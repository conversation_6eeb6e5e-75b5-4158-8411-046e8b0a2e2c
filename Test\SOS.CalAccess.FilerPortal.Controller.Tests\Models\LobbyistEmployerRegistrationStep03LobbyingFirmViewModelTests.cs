using SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Models;

namespace SOS.CalAccess.FilerPortal.Tests.Models;

[TestFixture]
public class LobbyistEmployerRegistrationStep03LobbyingFirmsViewModelTests
{
    [Test]
    public void LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel_ShouldSetAndGetPropertiesCorrectly()
    {
        // Arrange
        var gridModel = new DataGridModel();
        var action = FormAction.Continue;
        var firms = new List<LobbyingFirmRowDto>
    {
        new() { Id = "1", Name = "Alpha Lobbying Firm" }
    };

        var viewModel = new LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel
        {
            LobbyingFirmsGridModel = gridModel,
            Id = 999,
            Action = action,
            LobbyingFirms = firms,
            HasFirms = true
        };

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(viewModel.LobbyingFirmsGridModel, Is.EqualTo(gridModel));
            Assert.That(viewModel.Id, Is.EqualTo(999));
            Assert.That(viewModel.Action, Is.EqualTo(FormAction.Continue));
            Assert.That(viewModel.LobbyingFirms, Is.EqualTo(firms));
            Assert.That(viewModel.LobbyingFirms?.First().Name, Is.EqualTo("Alpha Lobbying Firm"));
            Assert.That(viewModel.HasFirms, Is.True);
        });
    }

    [Test]
    public void LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel_ShouldAllowNullValuesExceptHasFirms()
    {
        // Arrange
        var viewModel = new LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel
        {
            HasFirms = false
        };

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(viewModel.LobbyingFirmsGridModel, Is.Null);
            Assert.That(viewModel.Id, Is.Null);
            Assert.That(viewModel.Action, Is.Null);
            Assert.That(viewModel.LobbyingFirms, Is.Null);
            Assert.That(viewModel.HasFirms, Is.False);
        });
    }

}

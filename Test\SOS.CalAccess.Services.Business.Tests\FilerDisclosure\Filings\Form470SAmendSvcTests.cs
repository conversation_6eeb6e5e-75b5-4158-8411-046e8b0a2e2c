using NSubstitute;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Business.Tests.FilerDisclosure.Filings;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]

public class Form470SAmendSvcTests
{
    private IDecisionsSvc _decisionsSvcMock;
    private IRegistrationRepository _registrationRepositoryMock;
    private IAuthorizationSvc _authorizationSvcMock;
    private IFilerSvc _filerSvcMock;
    private INotificationSvc _notificationSvcMock;
    private IUserMaintenanceSvc _userMaintenanceSvcMock;
    private IFilingRepository _filingRepository;
    private Form470SvcDependencies _dependenices;
    private IForm470SSvc _form470SSvcMock;
    private Form470SAmendSvc _service;
    private IDateTimeSvc _dateTimeSvcMock;

    [SetUp]
    public void SetUp()
    {
        _decisionsSvcMock = Substitute.For<IDecisionsSvc>();
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();
        _filerSvcMock = Substitute.For<IFilerSvc>();
        _notificationSvcMock = Substitute.For<INotificationSvc>();
        _userMaintenanceSvcMock = Substitute.For<IUserMaintenanceSvc>();
        _registrationRepositoryMock = Substitute.For<IRegistrationRepository>();
        _filingRepository = Substitute.For<IFilingRepository>();
        _form470SSvcMock = Substitute.For<IForm470SSvc>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();

        _dependenices = new Form470SvcDependencies
        (
            _decisionsSvcMock,
            _authorizationSvcMock,
            _filerSvcMock,
            _notificationSvcMock,
            _userMaintenanceSvcMock,
            _dateTimeSvcMock,
            _registrationRepositoryMock,
            _filingRepository
        );
        _service = new Form470SAmendSvc(_dependenices, _form470SSvcMock);
    }

    [Test]
    public async Task Initialize470SAmendment_ShouldReturnResponse_WhenLatestFilingIsAccepted()
    {
        // Arrange
        var filerId = 1L;
        var filing = new Filing
        {
            Id = 100,
            FilingTypeId = FilingType.OfficeHolderCandidateSupplement.Id,
            StatusId = FilingStatus.Accepted.Id
        };
        var amendment = new Filing
        {
            Id = 200,
            StatusId = FilingStatus.Incomplete.Id
        };
        _filingRepository.GetAllByFilerId(filerId).Returns(new[] { filing });
        _filingRepository.CreateForm470SAmendment(filing.Id).Returns(amendment);

        // Act
        var result = await _service.Initialize470SAmendment(filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(amendment.Id));
            Assert.That(result.StatusId, Is.EqualTo(amendment.StatusId));
        });
    }

    [Test]
    public async Task Initialize470SAmendment_ShouldReturnError_WhenNoMatchingFilingFound()
    {
        // Arrange
        var filerId = 2L;
        var filing = new Filing
        {
            Id = 100,
            FilingTypeId = 999,
            StatusId = FilingStatus.Accepted.Id
        };
        _filingRepository.GetAllByFilerId(filerId).Returns(new[] { filing });

        // Act
        var result = await _service.Initialize470SAmendment(filerId);
        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.EqualTo(false));
            Assert.That(result.ErrorMessage, Is.EqualTo($"Filing not Found for Filer Id {filerId}"));
        });
    }

    [Test]
    public async Task Initialize470SAmendment_ShouldReturnError_WhenLatestFilingIsDraft()
    {
        // Arrange
        var filerId = 3L;
        var filing = new Filing
        {
            Id = 100,
            FilingTypeId = FilingType.OfficeHolderCandidateSupplement.Id,
            StatusId = FilingStatus.Draft.Id
        };
        _filingRepository.GetAllByFilerId(filerId).Returns(new[] { filing });

        // Act
        var result = await _service.Initialize470SAmendment(filerId);
        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.EqualTo(false));
            Assert.That(result.ErrorMessage, Is.EqualTo($"Draft already exists for this filer."));
        });
    }
}

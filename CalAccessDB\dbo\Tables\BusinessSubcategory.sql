﻿CREATE TABLE [dbo].[BusinessSubcategory] (
    [Id]                   BIGINT         IDENTITY (1, 1) NOT NULL,
    [Name]                 NVARCHAR (MAX) NOT NULL,
    [CreatedBy]            BIGINT         NOT NULL,
    [ModifiedBy]           BIGINT         NOT NULL,
    [AuditableResourceTag] NVARCHAR (450) NULL,
    CONSTRAINT [PK_BusinessSubcategory] PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_BusinessSubcategory_AuditableResourceTag]
    ON [dbo].[BusinessSubcategory]([AuditableResourceTag] ASC) WHERE ([AuditableResourceTag] IS NOT NULL);


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Unique resource tag.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'BusinessSubcategory', @level2type = N'COLUMN', @level2name = N'AuditableResourceTag';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'A string-serialized unique identifier used to link audit logs to arbitrary resource types.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'BusinessSubcategory', @level2type = N'COLUMN', @level2name = N'AuditableResourceTag';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'The identifier of the user who last modified this record.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'BusinessSubcategory', @level2type = N'COLUMN', @level2name = N'ModifiedBy';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'The identifier of the user who created this record.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'BusinessSubcategory', @level2type = N'COLUMN', @level2name = N'CreatedBy';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'BusinessSubcategory Name.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'BusinessSubcategory', @level2type = N'COLUMN', @level2name = N'Name';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'BusinessSubcategory Identifier.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'BusinessSubcategory', @level2type = N'COLUMN', @level2name = N'Id';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'BusinessSubcategory';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'This table holds BusinessSubcategorys.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'BusinessSubcategory';


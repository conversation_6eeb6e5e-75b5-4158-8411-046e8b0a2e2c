﻿CREATE TABLE [dbo].[PhoneNumberHistory] (
    [Id]                   BIGINT         NOT NULL,
    [InternationalNumber]  BIT            NOT NULL,
    [CountryCode]          NVARCHAR (10)  NULL,
    [Extension]            NVARCHAR (20)  NULL,
    [Number]               NVARCHAR (20)  NOT NULL,
    [Type]                 NVARCHAR (40)  NOT NULL,
    [PhoneNumberListId]    BIGINT         NOT NULL,
    [CreatedBy]            BIGINT         NOT NULL,
    [ModifiedBy]           BIGINT         NOT NULL,
    [AuditableResourceTag] NVARCHAR (450) NULL,
    [PeriodEnd]            DATETIME2 (7)  NOT NULL,
    [PeriodStart]          DATETIME2 (7)  NOT NULL,
    [CountryId]            BIGINT         NULL,
    [IsPrimaryPhoneNumber] BIT            NOT NULL
);














GO
CREATE CLUSTERED INDEX [ix_PhoneNumberHistory]
    ON [dbo].[PhoneNumberHistory]([PeriodEnd] ASC, [PeriodStart] ASC) WITH (DATA_COMPRESSION = PAGE);


GO



GO

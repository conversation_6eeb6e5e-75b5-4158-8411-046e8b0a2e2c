SET IDENTITY_INSERT BusinessSubCategory ON;
Go
 MERGE INTO BusinessSubCategory
 USING (VALUES
 (1, 'Entertainment / Recreation', 9999, 9999),
 (2, 'Finance / Insurance', 9999, 9999),
 (3, 'Lodging / Restaurant', 9999, 9999),
 (4, 'Manufacturing / Industrial', 9999, 9999),
 (5, 'Merchandise / Retail', 9999, 9999),
 (6, 'Oil and Gas', 9999, 9999),
 (7, 'Profession / Trade', 9999, 9999),
 (8, 'Real Estate', 9999, 9999),
 (9, 'Transportation', 9999, 9999),
 (10, 'Other', 9999, 9999)
 ) AS Source (Id, Name, CreatedBy, ModifiedBy)
 ON BusinessSubCategory.Id = source.Id
 WHEN MATCHED THEN
	UPDATE SET Name = Source.Name, ModifiedBy = Source.ModifiedBy
WHEN NOT MATCHED THEN
	INSERT (Id, Name, CreatedBy, ModifiedBy)
	VALUES (source.Id, source.Name, source.CreatedBy, source.ModifiedBy);
GO
 SET IDENTITY_INSERT BusinessSubCategory OFF;
 GO

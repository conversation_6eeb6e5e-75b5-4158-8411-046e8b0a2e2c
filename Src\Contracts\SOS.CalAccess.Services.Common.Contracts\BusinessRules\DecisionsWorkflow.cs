using SOS.CalAccess.Foundation.Utils;

namespace SOS.CalAccess.Services.Common.BusinessRules;

/// <summary>
/// An enum defining the Decisions workflows that can be accessed by CalAccess
///
/// Each enum must define an ExternalId attribute containing the id of the workflow in Decisions.
/// </summary>
public enum DecisionsWorkflow
{
    [ExternalId("01JG51MNDP0D92NHC6VX0DKJBT")]
    FR0005,
    //CandidateIntention
    [ExternalId("01JH3VJ6Y0V5HDX8RNK3GK7G8N")]
    CandidateInformationRuleset,
    [ExternalId("01JDQ58KRJ2DWYQFKA5VEBCMNG")]
    CandidateInformationCandidateSubmissionRuleset,
    [ExternalId("01JPQSN18E972ZJ3ZH4TKPXN79")]
    CandidateInformationNonCandidateSubmissionRuleset,
    [ExternalId("01JX063HRD5RW3J2DHJ101MA4J")]
    CandidateInformationElectionScreenValidationRuleset,
    [ExternalId("01JH67TJDXZF92RSXDX3HSA7J6")]
    CandidateInformationElectionRuleset,
    [ExternalId("01JM0BQQB0M11AJY2232D07BED")]
    CandidateInformationElectionLimitRuleset,

    //SmoRegistration
    [ExternalId("01JMYXF7V5M7PFYXP504786D0G")]
    SmoContactRuleSet,
    [ExternalId("01JNRZ68KFP28H8XGCEJ2PPN88")]
    SmoRegistrationsDetailsRuleSet,
    [ExternalId("01JQ4JRZY481NTAARTF9K0FDWX")]
    SmoTreasurerRuleSet,
    [ExternalId("01JR0RSZZDWQGPR6Z9DC9X6H61")]
    SmoOfficerRuleSet,
    [ExternalId("01JQVWDW8PQ1ZAK4AKN7VP4TE7")]
    SmoAuthorizerRuleSet,
    [ExternalId("01JR8VK9NQH2W7D488873AMPTK")]
    SmoPreAttestationRuleSet,
    [ExternalId("01JRG5M2NBF710CKF5AT3HF02W")]
    SmoPostSubmissionRuleSet,
    [ExternalId("01JRDJWDHZPSEZARRVSP3E922R")]
    SmoSendForAttestationRuleSet,
    [ExternalId("01JV7J3HSYKK0DQT8TEAX7D5CE")]
    SmoNoticeOfTerminationRuleSet,
    [ExternalId("01JV7Y502NEYHZQM3X5PQRT9KC")]
    SmoTerminationSubmitAttestationRuleSet,
    [ExternalId("01JV824D2ESVZW4BBJZ3MJN2VK")]
    SmoTerminationSendForAttestationRuleSet,
    [ExternalId("01JWEKTTNYF45J56JQ10NA0JA3")]
    SmoAttestationDelinkOfficersRuleSet,
    [ExternalId("01JWE8ATJDZ7C2QAXCMFF8MQGR")]
    SmoNoticeOfEditRuleSet,
    [ExternalId("01JWEMH4WR2P7HFNME7073YYY9")]
    SmoCancellationDelinkOfficersRuleSet,

    //SmoCampaignStatement
    [ExternalId("01JT3N2TRM4B6CSCEFS9AV0TYS")]
    SmoUnitemizedPaymentsReceivedRuleSet,
    [ExternalId("01JT0Z6XP6EZVN3P4ETEZBENJF")]
    SmoUnitemizedPaymentsMadeRuleSet,
    [ExternalId("01JT10G876X9KJ6KW71YCD26EP")]
    SmoPaymentsMadeBySmoOrAgentContractorRuleSet,
    [ExternalId("01JTNQ59D012W5DY1BXQ5S4J9X")]
    SmoPaymentReceivedInformationRuleSet,
    [ExternalId("01JTNQFTD5X1DFYP5G6G38PP39")]
    SmoPaymentReceivedAssociationRuleSet,
    [ExternalId("01JTRWCSM0BVV34ZGC7Z9JXFFW")]
    SmoCandidateOrMeasureNotListedRuleSet,
    [ExternalId("01JWRJQTJ7YGDG8ZD206SRAZGT")]
    SmoCandidateOrMeasureNotListedRuleP1Set,
    [ExternalId("01JT4ED6EJ4CY3BZNY95V1GE80")]
    TransactionPayorInformationRuleSet,
    [ExternalId("01JT961EPQHW0YSN3G4CG35C9Y")]
    TransactionPayeeInformationRuleSet,
    [ExternalId("01JTVH5MEPGA2E1QR0BQAYGPF9")]
    SmoCampaignStatementSendForAttestationRuleSet,
    [ExternalId("01JV53VY1PS48F6PEKG7NN5FVH")]
    SmoCampaignStatementAttestationRuleSet,
    [ExternalId("01JTVG3564TT4AEF5BP53F6228")]
    SmoCampaignStatementPostSubmissionRuleSet,
    [ExternalId("01JT10SWFMWCW4EMG21HY1QA4Y")]
    PersonsReceivingOfficerRuleSet,
    [ExternalId("01JT10TWEFKSGWJVB2EX4RHM97")]
    PersonsReceivingTransactionRuleSet,
    [ExternalId("01JWEQ8BWPC7ZMD9E1YHXM1MDD")]
    SmoCampaignStatementAmendmentExplanation,
    [ExternalId("01JWGPNHWMQFRTVJYHKJFMZPV5")]
    SmoCampaignStatementPostSubmissionAmendmentRuleSet,
    [ExternalId("01JWGTB0PY81HE32MRR73FGBN8")]
    SmoCampaignStatementSendForAttestationAmendmentRuleSet,

    [ExternalId("01JP60MZX8XJ711WJK2R8JS5XJ")]
    PayeeRuleset,
    [ExternalId("01JP60Q31GVRMKTCNDQKP5DA9R")]
    ActivityExpenseReportablePersonRuleset,
    [ExternalId("01JDJAQDVYCEGAPB7FNPSY6Z43")]
    ActivityExpenseTransactionRuleset,
    [ExternalId("01JPWP3NBT94JRKPHB808NAHQV")]
    PaymentMadeToLobbyingCoalitionRuleset,
    [ExternalId("01JPWVHMGM56W7Y5GGX1DTM7BV")]
    PaymentReceiveLobbyingCoalitionRuleset,
    [ExternalId("01JQPQ7K4KB44CP85WZNVJA5AV")]
    PaymentReceiveLobbyingCoalitionPaymentAmountRuleset,
    [ExternalId("01JQQ1S84MBKFA8T3VH8KHYRVJ")]
    PaymentReceiveLobbyingCoalitionMemberRuleset,
    [ExternalId("01JRB8Q9MQJZGC5HW90KBTWB8X")]
    PaymentMadeToLobbyingFirmsRuleset,
    [ExternalId("01JRB89EC9DETYPKZDVM38S1DT")]
    PaymentMadeToLobbyingFirmsContactRuleset,
    [ExternalId("01JQPTFAGTWG35XGMQZGK9F4EQ")]
    SubmitLobbyistReportRuleset,
    [ExternalId("01JT3PH6SFTSJ53AYGK09K0CW3")]
    SendForAttestationLobbyistReportRuleset,
    [ExternalId("01JV31KBS9CVS0DT96PG8PP0X0")]
    SubmitLobbyistEmployerReportRuleset,
    [ExternalId("01JVWY5D3SHXYN5MSF4ZTRW88E")]
    SendForAttestationLobbyistEmployerReportRuleset,
    [ExternalId("01JRJRG5GT2133FS4NB0DQG0M2")]
    LumpSumPaymentsRuleset,
    [ExternalId("01JT3S7CW15EWVT47JERMNT92Z")]
    SendForAttestationReport72HRuleset,
    [ExternalId("01JSPZTJ5VK4WP6TJP1JKKYMNM")]
    SubmitReport72HRuleSet,
    [ExternalId("01JR8JRHXYME9G571Q90YRJCTH")]
    ContributionsInExistingStatementsRuleset,

    //Address
    [ExternalId("01JRFWWTFXZ7KB8J4XPCQSFM53")]
    AddressValidationRuleSet,
    [ExternalId("01JRZGZEMH5WHZAQCJXQ932H8Y")]
    OtherPaymentsToInfluenceTransactionRuleset,

    //Form 470
    [ExternalId("01JRZTW7JQ3K3VGR5W7E6EHG8Y")]
    Form470AttestationRuleSet,
    [ExternalId("01JT8TM43BN8GX1W0AWJ6ZNFZT")]
    Form470SendForAttestationRuleSet,
    [ExternalId("01JSQ0Q3XXDD5S9Y41Q2B1877Q")]
    Form470SRuleSet,

    //72h Report
    [ExternalId("01JSM3C74XVEGZVH21MH596NQ0")]
    FDLOBFiling72HourReportActionsLobbiedBills,
    [ExternalId("01JSMC8RC8G0EYW1C6G46XQ4Z4")]
    FDLOBFiling72HourReportActionsLobbiedAgencies,
    [ExternalId("01JSMMNX8FJEYJJFQX1GR9MT6Z")]
    FDLOBFiling72HourReportActionsLobbied,
    [ExternalId("01JTK00P8XCM8F99A5P4BG0K21")]
    LobbyistEmployerReportActionsLobbiedRuleSet,
    [ExternalId("01JTK2HZBFR5XRR4JTBWCXVW4V")]
    LobbyistEmployerReportActionsLobbiedAgenciesRuleSet,
    [ExternalId("01JTKM8TJK170TP32JHWPV7YXB")]
    FDLOBTransactionLobbyistEmployerReportPUCActivity,
    [ExternalId("01JSF3BWVP55CWZVXJ12XSHKK8")]
    FDLOBFiling72HourReportLobbyingAdvertisement,
    [ExternalId("01JVARYGKNG6ZKX39897J75AXK")]
    FDLOBFiling72HourReportSubmitReportAmendment,
    [ExternalId("01JVASY0NKWYFX2R3RQ8CQM9QF")]
    FDLOBFiling72HourReportSendAttestationAmendment,

    //48H Report
    [ExternalId("01JSYK5RVFBSEJEJ3FMNKPKG9V")]
    FDLOBFiling48HourReportAddNewTransaction,
    [ExternalId("01JVCQ1VV6DE93HWB3DW2XRJZG")]
    FDLOBFiling48HourReportAddNewTransactionAdditionalData,
    [ExternalId("01JSZ0DG156D0NY7ESX4WM4FKY")]
    FDLOBFiling48HourReportSubmitReport,
    [ExternalId("01JV2WMPYJRGY6P8S13HJB9Z45")]
    FDLOBFiling48HourReportSendForAttestation,
    [ExternalId("01JX2M1AYWYC0GZ8T5JNK5DX5T")]
    FDLOBFiling48HourAmendReportSendForAttestation,
    [ExternalId("01JX2SN9FSS5AH20MDZNVAXCP5")]
    FDLOBFiling48HourAmendReportSubmission,
    [ExternalId("01JX09NHAQR4VTN9V1MSTR7F5H")]
    FDLOBLobbyistEmployer5000FirmAmendReport02AmendmentExplanation,
    [ExternalId("01JWVMYC22MP44GTJ6EPAYJS26")]
    FDLOBLobbyistEmployerAmendReportAmendmentExplanation,

    [ExternalId("01JTP28KHN7139BG04R6SZGSCM")]
    FRLOBFilingLobbyistStep1GeneralInfo,
    [ExternalId("01JTTWD76P1WD82CGMTTERR761")]
    FRLOBFilingLobbyistFinalSubmission,
    [ExternalId("01JTV4QQG5FWK77W5EZCHJDTTP")]
    FRLOBFilingLobbyistSendAttestation,
    [ExternalId("01JVWBXAS8AWDGYZ9NYX8V21QD")]
    FRLOBFilingAmendLobbyistFinalSubmission,
    [ExternalId("01JVZADZM6CPS2BM2QVW6689W7")]
    FRLOBFilingAmendLobbyistSendAttestation,
    [ExternalId("01JVQMNPD3PN8B0R35WY1W9SDX")]
    FRLOBFilingAmendRegistrationStep1GeneralInfo,
    [ExternalId("01JVZF123M7PEFS4X96T5HTFTS")]
    FRLOBFilingSubmitLobbyistRegistrationTermination,
    [ExternalId("01JWC20JW1CR1VWK8DSBWTNZ73")]
    FRLOBFilingTerminalRegistrationSendForAttestation,
    [ExternalId("01JWEDMMGZ9ERW3P3T8TDCQWH7")]
    FRLOBFilingLobbyistRenewalStep1GeneralInfo,
    [ExternalId("01JVZ3C403WDWJFTSXEDER6KH4")]
    LobbyistRegistrationSubmitWithdrawal,
    [ExternalId("01JW94RCTRKHZ89C8D7N2PF8PG")]
    LobbyistRegistrationSendForAttestationWithdrawal,
    [ExternalId("01JVQ5WD8NQZX6TKTTPZ0SR7FD")]
    LobbyistRegistrationEnterWithdrawal,

    [ExternalId("01JWVK03RBSBFNFWS5C2MXEH27")]
    FRLOBFilingLobbyistEmployerInfo,
    [ExternalId("01JWXHZ0RKQH7Q8QWB0MXVSAFT")]
    FRLOBFilingLobbyistEmployerStateAgenciesToBeInfluenced,
    [ExternalId("01JWXQZ05Q97ZY8PVBTX0KYV1X")]
    FRLOBFilingLobbyistEmployerLobbyingInterestsDescription,

    //LinkagesRequest
    [ExternalId("01JWE3F13TSFQMXEDJ7N09XMMG")]
    AcceptLinkageRequestRuleset,
    [ExternalId("01JWE5J2XWV2F232JYF05ZHPK4")]
    RejectLinkageRequestRuleset,
    [ExternalId("01JWEJSY11GCNBCEWW5P91VRAP")]
    TerminateLinkageRuleset,

    // Team 2
    [ExternalId("01JMHZ6Q35WCJ4K1VRYZ4VYD1M")]
    LobbyistEmployerCoalitionCampaignContributonRuleset,
    [ExternalId("01JMJGCRTW1JRDE7MT3X8YKTVP")]
    LobbyistCampaignContributionRuleset,
    [ExternalId("01JKZWD5WY2SWPYGR8VAF6KYTG")]
    PaymentsToInHouseLobbyistsRuleset,

    //CandidateIntentionStatementWithdrawal    
    [ExternalId("01JWXZ35HZTJTQPDK3REHAV6XC")]
    CISWithdrawalAttestationSubmitRuleset,
    [ExternalId("01JX30NJSEPMN0TJGB3FAF5AK9")]
    CISWithdrawalSendForAttestationRuleset,
}

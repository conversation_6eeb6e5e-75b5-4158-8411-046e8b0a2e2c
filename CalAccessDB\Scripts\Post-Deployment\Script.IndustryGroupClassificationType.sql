
/*
Populate the NatureAndInterestType table with default values
*/

SET IDENTITY_INSERT IndustryGroupClassificationType ON;

MERGE INTO IndustryGroupClassificationType
USING (VALUES 
    (1, 'Agriculture'),
    (2, 'Education'),
    (3, 'Government'),
    (4, 'Health'),
    (5, 'Labor Union'),
    (6, 'Legal'),
    (7, 'Public Employee'),
    (8, 'Political Organization'),
    (9, 'Utilities'),
    (10, 'Other')
) AS source (Id, Name)
ON IndustryGroupClassificationType.Id = source.Id
WHEN MATCHED THEN UPDATE SET Name = source.Name
WHEN NOT MATCHED THEN INSERT (Id, Name) VALUES (source.Id, source.Name);

SET IDENTITY_INSERT IndustryGroupClassificationType OFF;

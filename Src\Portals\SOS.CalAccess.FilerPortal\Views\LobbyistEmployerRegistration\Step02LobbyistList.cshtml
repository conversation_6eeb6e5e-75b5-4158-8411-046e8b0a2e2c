 @using SOS.CalAccess.FilerPortal.Models.Localization
 @inject IHtmlLocalizer<SharedResources> Localizer
 @model SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration.InHouseLobbyistListViewModel


@{
    var hasLobbyists = Model?.Lobbyists?.Count > 0;
    var progressItem1Name = ViewData["ProgressItem1Name"]?.ToString() ?? "";
    var progressItem2Name = ViewData["ProgressItem2Name"]?.ToString() ?? "";
    var progressItem3Name = ViewData["ProgressItem3Name"]?.ToString() ?? "";
    var progressItem4Name = ViewData["ProgressItem4Name"]?.ToString() ?? "";
    var progressItem5Name = ViewData["ProgressItem5Name"]?.ToString() ?? "";

    var progressBar = new ProgressBar(new List<ProgressItem>
    {
        new(progressItem1Name, true, false),
        new(progressItem2Name, false, true),
        new(progressItem3Name, false, false),
        new(progressItem4Name, false, false),
        new(progressItem5Name, false, false),
    });

    var buttonBar = new ButtonBarModel
    {
        LeftButtons = new List<ButtonConfig>
        {
            ButtonBarModel.DefaultPrevious,
            ButtonBarModel.DefaultContinue,
        },
        RightButtons = new List<ButtonConfig>
        {
            new ()
            {
                Type = ButtonType.Custom,
                HtmlContent = await Html.PartialAsync("_CancelDraftButton", (long?)null),
            },
            ButtonBarModel.DefaultSaveAndClose,
        }
    };
}
@Html.StepHeader(SharedLocalizer,ResourceConstants.LobbyistEmployerRegistrationTitle2)
<h3>@SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationTitle]</h3>
<partial name="_LayoutProgressbar" model="progressBar" />

@using (Html.BeginForm("Step02LobbyistList", "LobbyistEmployerRegistration", FormMethod.Post))
{
    <div class="d-flex flex-column gap-4 main-form">
        <b class="text-uppercase">
            @Localizer[ResourceConstants.Step].Value 2
        </b>

        @if (hasLobbyists)
        {
            <h3>@SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationPage05Title]</h3>
            @Html.TextBlock(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationPage05Body)
            <div class="mb-4">
                @await Html.PartialAsync("_SmallGrid", Model.InHouseLobbyistsGridModel)
            </div>
        }
        else
        {
            <h3>@SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationStep02LobbyistListTitle]</h3>
            @Html.TextBlock(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationPage05Body)
        }

        <div>
            <a asp-controller="LobbyistEmployerRegistration" asp-action="Step02AddLobbyist" asp-route-id="@Model?.Id"
               class="btn btn-outline-primary btn">
                <i class="bi bi-plus-circle-fill"></i>
                @SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationPage05Add]
            </a>
        </div>

    </div>

    <div class="main-button">
        <partial name="_ButtonBar" model="buttonBar" />
    </div>
}

<style>
    .main-form {
        border-top: 4px solid #397187;
        margin-top: 40px;
        padding: 40px;
    }

    .main-button {
        padding-left: 40px;
    }

    .e-content {
        position: unset !important;
    }
</style>

<script>
    function onRowDataBound(args) {
        if (args && args.data && args.row) {
            const isCertified = args.data.CertificationStatus === "Certified";

            // Highlight the IsCertified cell
            const cells = args.row.querySelectorAll("td");
            cells.forEach(cell => {
                if (cell.textContent.trim() === args.data.CertificationStatus) {
                    cell.style.color = isCertified ? "#198754" : "#DC3545";
                }
            });

            // Show .e-callAction only if IsCertified is NOT "Certified"
            const callActionButton = args.row.querySelector('.e-callAction');
            if (callActionButton) {
                const li = callActionButton.closest('li');
                if (li) {
                    li.style.display = isCertified ? 'none' : '';
                }
            }
        }
    }
</script>



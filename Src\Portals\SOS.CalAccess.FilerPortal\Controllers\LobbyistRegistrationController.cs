using System.ComponentModel.DataAnnotations;
using System.Globalization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistRegistration;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using static SOS.CalAccess.FilerPortal.Constants.RegistrationConstants;
using RegistrationResponseDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto;

namespace SOS.CalAccess.FilerPortal.Controllers;

public class LobbyistRegistrationController(
    ILobbyistRegistrationSvc lobbyistRegistrationSvc,
    ILobbyistRegistrationCtlSvc lobbyistRegistrationCtlSvc,
    IAccuMailValidatorService accuMailValidatorService,
    IStringLocalizer<SharedResources> localizer,
    IToastService toastService,
    IAuthorizationSvc authorizationSvc,
    ILogger<LobbyistRegistrationController> logger) : Controller
{
    /// <summary>
    /// Common logic to call before loading a view.
    /// </summary>
    /// <param name="context"></param>
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        SetCommonViewData();
    }

    public IActionResult Index()
    {
        return RedirectToAction(nameof(Page01));
    }

    /// <summary>
    /// Closes the form without changes and routes the user to the Dashboard.
    /// If FormAction is Cancel, then it will generate the same toast generated when form is cancelled.
    /// This covers the case where a new form is cancelled before saving (no ID to cancel with).
    /// </summary>
    /// <param name="action"></param>
    /// <returns></returns>
    public IActionResult Close(FormAction? action = null)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        if (action == FormAction.Cancel)
        {
            toastService.Success(localizer[CommonResourceConstants.ToastCancelled]);
        }
        return RedirectToDashboard();
    }

    /// <summary>
    /// Cancels the form and routes the user to the Dashboard.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<IActionResult> Cancel(
        [Required] long id,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            await lobbyistRegistrationSvc.CancelLobbyistRegistration(id);
            toastService.Success(localizer[CommonResourceConstants.ToastCancelled]);
            return RedirectToDashboard();
        }

        return new NotFoundResult();
    }

    public ActionResult RedirectToDashboard()
    {
        return RedirectToAction("Index", "Dashboard");
    }

    private void SetCommonViewData()
    {
        ViewData[LayoutConstants.Title] = localizer[ResourceConstants.LobbyistRegistrationTitle].Value;
        ViewData[LayoutConstants.Breadcrumbs] = new List<Breadcrumb>()
        {
            new("Filer Portal", "/FilerPortal"),
            new("Lobbyist", "/LobbyistRegistration"),
        };
        ViewData["ProgressItem1Name"] = "1. " + localizer[ResourceConstants.LobbyistRegistrationGeneralInformation].Value;
        ViewData["ProgressItem2Name"] = "2. " + localizer[ResourceConstants.LobbyistRegistrationPayFees].Value;
        ViewData["ProgressItem3Name"] = "3. " + localizer[ResourceConstants.LobbyistRegistrationSubmit].Value;

        // Withdraw Registration
        ViewData["WithdrawRegistrationStep1"] = "1. " + localizer[ResourceConstants.WithdrawLobbyistRegistrationStep01Title].Value;
        ViewData["WithdrawRegistrationStep2"] = "2. " + localizer[ResourceConstants.WithdrawLobbyistRegistrationStep02Title].Value;

        // Termination Registration
        ViewData["TerminationProgressItem1Name"] = "1. " + localizer[ResourceConstants.LobbyistRegistrationTerminationStep01Title].Value;
        ViewData["TerminationProgressItem2Name"] = "2. " + localizer[ResourceConstants.LobbyistRegistrationTerminationStep02Title].Value;
    }

    /// <summary>
    /// Handler for when user starts to edit a form.
    /// Redirects user to predetermined starting page.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public IActionResult Edit([Required] long id)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }
        return RedirectToAction(nameof(Page03), new { Id = id });
    }

    [HttpGet]
    /// <summary>
    /// Loader for confirmation
    /// </summary>
    /// <returns>Confirmation View</returns>
    public async Task<IActionResult> Confirmation([Required] long id, string registrationType,
    CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        ViewBag.RegistrationType = registrationType;
        var model = await lobbyistRegistrationCtlSvc.GetConfirmationViewModel(id);

        return View(model);
    }

    #region Page 01 - FR-LOB-Start
    public IActionResult Page01()
    {
        return View();
    }
    #endregion

    #region Page 02 - FR-LOB-LobbyistInformation
    [HttpGet]
    public async Task<IActionResult> Page02(long? id, [FromQuery] bool? isRenewal,
    [FromServices] ILobbyistApi lobbyistApi,
    CancellationToken cancellationToken = default)
    {
        var model = new LobbyistRegistrationDetailsStep01ViewModel
        {
            Id = id,
            IsRenewal = isRenewal.GetValueOrDefault()
        };

        if (ModelState.IsValid)
        {
            if (id.HasValue && id.Value != 0)
            {
                try
                {
                    var existingLobbyist = await lobbyistApi.GetLobbyistRegistration(id.Value, cancellationToken);
                    model.SelfRegister = existingLobbyist.SelfRegister.GetValueOrDefault();
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Lobbyist registration with Filer ID {Id} not found.", new { Id = id.Value });
                    return NotFound();
                }
            }
            return View(model);
        }
        else
        {
            return RedirectToAction(nameof(Page01));
        }
    }


    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public IActionResult Page02(LobbyistRegistrationDetailsStep01ViewModel? model)
    {
        if (ModelState.IsValid)
        {
            switch (model?.Action)
            {
                case FormAction.Continue:
                    return RedirectToAction(nameof(Page03), new { id = model.Id, selfRegister = model.SelfRegister, isRenewal = model.IsRenewal });
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page01));
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }
    #endregion

    #region Page 03 - FR-LOB-LobbyistInformation-2

    /// <summary>
    /// Loader for Page03
    /// </summary>
    /// <returns>Page03 View with model</returns>
    [HttpGet]
    [Route("[controller]/[action]/{id:long?}")]
    public async Task<IActionResult> Page03(
    [FromRoute] long? id,
    [FromQuery] bool? selfRegister,
    [FromQuery] bool? isRenewal,
    [FromServices] IReferenceDataApi referenceDataApi,
    [FromServices] IFilingsApi filingsApi,
    [FromServices] ILobbyistApi lobbyistApi,
    CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var model = await lobbyistRegistrationCtlSvc.GetPage03ViewModel(id, selfRegister.GetValueOrDefault(), cancellationToken)
                 ?? new LobbyistRegistrationStep01ViewModel();

        if (id.HasValue && id.Value != 0)
        {
            try
            {
                var existingLobbyist = await lobbyistApi.GetLobbyistRegistration(id.Value, cancellationToken);
                lobbyistRegistrationCtlSvc.MappingViewModelFromDto(model, existingLobbyist);
                model.IsRenewal = isRenewal.GetValueOrDefault();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Lobbyist registration with Filer ID {Id} not found.", new { Id = id.Value });
                return NotFound();
            }
        }

        model.IsNewCertification = !isRenewal;
        await PopulateReferenceData(model, referenceDataApi, filingsApi, lobbyistApi);

        return View(model);
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"</param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Page03(
        LobbyistRegistrationStep01ViewModel model,
        [FromServices] IReferenceDataApi referenceDataApi,
        [FromServices] IFilingsApi filingsApi,
        [FromServices] ILobbyistApi lobbyistApi)
    {
        await PopulateReferenceData(model, referenceDataApi, filingsApi, lobbyistApi);

        ModelState.Remove(nameof(model.ContactId));
        ModelState.Remove(nameof(model.RegistrationFilingId));

        if (!ModelState.IsValid)
        {
            return View(model);
        }

        if (model.IsSameAsBusinessAddress)
        {
            model.Addresses.Find(x => x.Purpose == "Mailing")?.Clear();
        }

        if (await accuMailValidatorService.AccuMailValidationHandler(model, ModelState, model.Action == FormAction.Cancel))
        {
            return View(model);
        }

        model.IsNewCertification = !model.IsRenewal;
        model.LegislativeSessionId = model.IsRenewal.GetValueOrDefault() ? model.LegislativeNextSessionId : model.LegislativeSessionId;

        switch (model.Action)
        {
            case FormAction.Continue:
                return await Page03ContinueAsync(model);
            case FormAction.SaveAndClose:
                return await Page03Save(model);
            case FormAction.Previous:
                return await Page03Previous(model);
            default:
                ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                break;
        }

        return View(model);
    }

    /// <summary>
    /// Handler for Page03 Previous Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    private async Task<IActionResult> Page03Previous(LobbyistRegistrationStep01ViewModel model)
    {
        var registrationId = await lobbyistRegistrationCtlSvc.Page03Submit(model, ModelState, false);

        if (registrationId.HasValue)
        {
            return RedirectToAction(nameof(Page02), new { Id = registrationId });
        }

        return View(nameof(Page03), model);
    }

    /// <summary>
    /// Handler for Page03 Save Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load after saving</returns>
    private async Task<IActionResult> Page03Save(LobbyistRegistrationStep01ViewModel model)
    {
        _ = await lobbyistRegistrationCtlSvc.Page03Submit(model, ModelState, true);
        if (ModelState.IsValid)
        {
            return RedirectToDashboard();
        }

        return View(nameof(Page03), model);
    }

    /// <summary>
    /// Handler for Page03 Continue Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private async Task<IActionResult> Page03ContinueAsync(LobbyistRegistrationStep01ViewModel model)
    {
        var registrationId = await lobbyistRegistrationCtlSvc.Page03Submit(model, ModelState, true);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page04), new { Id = registrationId });
        }

        return View(nameof(Page03), model);
    }
    #endregion

    #region Page 04 - FR-LOB-LobbyistInfo-Fee

    [HttpGet]
    /// <summary>
    /// Loader for Page04
    /// </summary>
    /// <returns>Page04 View</returns>
    public async Task<IActionResult> Page04([Required] long? id,
        [FromServices] ILobbyistApi lobbyistApi,
        CancellationToken cancellationToken = default)
    {
        if (!id.HasValue)
        {
            return BadRequest();
        }

        var existingLobbyist = await lobbyistApi.GetLobbyistRegistration(id.Value, cancellationToken);

        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = id.GetValueOrDefault(),
            Version = existingLobbyist.Version
        };

        if (ModelState.IsValid)
        {
            return View(model);
        }

        return NotFound();
    }

    /// <summary>
    /// Handler for all submissions to Page05
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public IActionResult Page04(
        LobbyistRegistrationStep02ViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Continue:
                    return RedirectToAction(nameof(Page05), new { model.Id });
                case FormAction.SaveAndClose:
                    return RedirectToDashboard();
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page03), new { id = model.Id });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }
    #endregion

    #region Page 05 - FR-LOB-LobbyistInfo-Fee-2

    [HttpGet]
    /// <summary>
    /// Loader for Page05
    /// </summary>
    /// <returns>Page05 View</returns>
    public async Task<IActionResult> Page05([Required] long? id,
        [FromServices] ILobbyistApi lobbyistApi,
        CancellationToken cancellationToken = default)
    {
        if (!id.HasValue)
        {
            return BadRequest();
        }

        var existingLobbyist = await lobbyistApi.GetLobbyistRegistration(id.Value, cancellationToken);

        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = id.GetValueOrDefault(),
            Version = existingLobbyist.Version
        };

        if (ModelState.IsValid)
        {
            return View(model);
        }

        return NotFound();
    }

    /// <summary>
    /// Handler for all submissions to Page05
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public IActionResult Page05(
        LobbyistRegistrationStep02ViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Continue:
                    return RedirectToAction(nameof(Page06), new { model.Id });
                case FormAction.SaveAndClose:
                    return RedirectToDashboard();
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page04), new { id = model.Id });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }
    #endregion

    #region Page 06 - FR-LOB-LobbyistInfoVerification

    [HttpGet]
    /// <summary>
    /// Loader for Page06
    /// </summary>
    /// <returns>Page06 View</returns>
    public async Task<IActionResult> Page06(
        [Required] long? id,
        [FromServices] ILobbyistApi lobbyistApi,
        CancellationToken cancellationToken = default)
    {
        if (!id.HasValue)
        {
            return BadRequest();
        }

        var existingLobbyist = await lobbyistApi.GetLobbyistRegistration(id.Value, cancellationToken);

        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = id.GetValueOrDefault(),
            Version = existingLobbyist.Version
        };

        if (ModelState.IsValid)
        {
            return View(model);
        }

        return NotFound();
    }

    /// <summary>
    /// Handler for all submissions to Page06
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public IActionResult Page06(
        LobbyistRegistrationStep02ViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Continue:
                    return RedirectToAction(nameof(Page07), new { model.Id });
                case FormAction.SaveAndClose:
                    return RedirectToDashboard();
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page05), new { id = model.Id });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }

    #endregion

    #region Page 07 - FR-LOB-LobbyistInfoVerification-2

    [HttpGet]
    /// <summary>
    /// Loader for Page07
    /// </summary>
    /// <returns>Page07 View</returns>
    public async Task<IActionResult> Page07(
        [Required] long id,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            var model = await lobbyistRegistrationCtlSvc.GetPage07ViewModel(id, cancellationToken);

            return View(model);

        }

        return NotFound();
    }

    /// <summary>
    /// Handler for all submissions to Page07
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public async Task<IActionResult> Page07(
        LobbyistRegistrationStep03ViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Submit:
                    return await Page07Submit(model);

                case FormAction.SaveAndClose:
                    return RedirectToDashboard();

                case FormAction.Previous:
                    return RedirectToAction(nameof(Page06), new { id = model.Id });

                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }

    /// <summary>
    /// Handler for Page07 Save Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load after saving</returns>
    private async Task<IActionResult> Page07Submit(LobbyistRegistrationStep03ViewModel model)
    {
        var response = await lobbyistRegistrationCtlSvc.SubmitLobbyistRegistration(model);

        if (response.ValidationErrors.Count != 0)
        {
            foreach (var error in response.ValidationErrors)
            {
                ModelState.AddModelError("", error.Message.Replace("{{Field Name}}", error.FieldName));
            }
            return View(model);
        }
        else
        {
            if (model.SelfRegister)
            {
                return RedirectToAction(nameof(Page08), new { id = model.Id });
            }
            else
            {
                toastService.Success(localizer[ResourceConstants.LobbyistSentForAttestationSuccess]);
                return RedirectToDashboard();
            }
        }
    }

    #endregion

    #region Page 08 - FR-LOB-LobbyistInfoVerification-3

    [HttpGet]
    /// <summary>
    /// Loader for Page08
    /// </summary>
    /// <returns>Page04 View</returns>
    public IActionResult Page08(long? id, CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            var viewModel = lobbyistRegistrationCtlSvc.Page08GetViewModel(id);
            return View(viewModel);
        }

        return NotFound();
    }

    [HttpPost]
    /// <summary>
    /// Displays success toast message after clicking Close
    /// </summary>
    /// <returns>Dashboard View</returns>
    public IActionResult Page08(ConfirmationViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        toastService.Success(localizer[CommonResourceConstants.ToastSaved]);
        return RedirectToDashboard();
    }

    #endregion

    #region Withdraw lobbyist Registration
    [HttpGet]
    public async Task<IActionResult> WithdrawRegistration(long id,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        var existingRegistration = await lobbyistRegistrationSvc.GetRegistrationById(id);
        if (!ModelState.IsValid || existingRegistration == null)
        {
            return NotFound();
        }
        var model = lobbyistRegistrationCtlSvc.MapWidthdrawLobbyistRegistrationViewModel(existingRegistration);

        var legislativeSessions = await filingsApi.GetLegistlativeSessions(cancellationToken);
        var currentSession = legislativeSessions.Sessions.FirstOrDefault(x => x.Id == model.LegislativeSessionId);
        model.LegislativeSessionOptions = new List<SelectListItem>()
        {
            new SelectListItem()
            {
                Text = $"{currentSession!.StartDate.Year} - {currentSession.EndDate.Year}",
                Value = currentSession.Id.ToString(CultureInfo.InvariantCulture)
            }
        };

        model.LobbyistFirmNameOrEmployer = existingRegistration.LobbyistEmployerOrLobbyingFirmName;
        return View(model);
    }

    [HttpPost]
    public async Task<IActionResult> WithdrawRegistration(LobbyistRegistrationWithdrawalViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        switch (model.Action)
        {
            case FormAction.Continue:
            case FormAction.SaveAndClose:
                var response = await lobbyistRegistrationSvc.WithdrawLobbyistRegistration(model.Id.GetValueOrDefault(),
                    new Services.Business.FilerRegistration.Registrations.Models.
                        WithdrawLobbyistRegistrationRequestDto()
                    {
                        WithdrawnAt = model.EffectiveDateOfWithdrawal,
                        StatusId = RegistrationStatus.Draft.Id,
                        LegislativeSessionId = model.LegislativeSessionId
                    });

                if (response.ValidationErrors.Count > 0)
                {
                    ApplyDecisionErrors(response.ValidationErrors, ModelState);
                    return View(nameof(WithdrawRegistration), model);
                }

                return model.Action == FormAction.SaveAndClose ? RedirectToDashboard() : RedirectToAction(nameof(Verification), new { response.Id });

            case FormAction.Cancel:
                await Cancel(model.Id.GetValueOrDefault());
                break;

            default:
                break;
        }

        return View(nameof(WithdrawRegistration), model);
    }
    #endregion

    #region Amendment

    [HttpGet]
    public async Task<IActionResult> Amend(
        [Required] long id,
        [FromServices] ILobbyistApi lobbyistApi,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            var model = new LobbyistRegistrationStep01ViewModel();

            try
            {
                var newRegistrationId = await lobbyistRegistrationCtlSvc.CreateAmendLobbyistRegistration(id);
                var amendLobbyist = await lobbyistApi.GetLobbyistRegistration(newRegistrationId, cancellationToken);
                lobbyistRegistrationCtlSvc.MappingViewModelFromDto(model, amendLobbyist);
                return RedirectToAction("Page02", new { id = newRegistrationId });
            }
            catch (ArgumentException ae)
            {
                toastService.Error(ae.Message);
                return RedirectToDashboard();
            }
        }
        return RedirectToDashboard();
    }

    #endregion

    #region Termination
    [HttpGet]
    /// <summary>
    /// Loader for termination verification
    /// </summary>
    /// <returns>Terminate Verification View</returns>
    public async Task<IActionResult> TerminationNotice([Required] long id,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var registration = await lobbyistRegistrationSvc.GetRegistrationById(id);
        if (registration == null)
        {
            return BadRequest();
        }

        var model = lobbyistRegistrationCtlSvc.MapTerminateLobbyistRegistrationViewModel(registration);
        var legislativeSessions = await filingsApi.GetLegistlativeSessions(cancellationToken);
        var selectedSession = legislativeSessions.Sessions?.FirstOrDefault(s => s.Id == model.LegislativeSessionId);
        model.LegislativeSessionOptions = [new SelectListItem
            {
                Text = $"{selectedSession?.StartDate.Year} - {selectedSession?.EndDate.Year}",
                Value = selectedSession?.Id.ToString(CultureInfo.InvariantCulture)
            }];

        return View(model);
    }

    [HttpPost]
    /// <summary>
    /// Loader for termination verification
    /// </summary>
    /// <returns>Terminate Verification View</returns>
    public async Task<IActionResult> TerminationNotice(LobbyistTerminationNoticeViewModel model,
    CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Continue:
                case FormAction.SaveAndClose:
                    if (model.EffectiveDateOfTermination == null)
                    {
                        ModelState.AddModelError(nameof(model.EffectiveDateOfTermination), localizer[ResourceConstants.TerminateLobbistRegistrationRequiredTerminatedAt]);
                        break;
                    }
                    var response = await lobbyistRegistrationSvc.SaveLobbyistRegistrationTermination(model.Id.GetValueOrDefault(), new Services.Business.FilerRegistration.Registrations.Models.LobbyistRegistrationTerminationRequestDto()
                    {
                        TerminatedAt = model.EffectiveDateOfTermination,
                        StatusId = RegistrationStatus.Draft.Id
                    });
                    return model.Action == FormAction.SaveAndClose ? RedirectToDashboard() : RedirectToAction(nameof(Verification), new { response.Id });
                case FormAction.Close:
                    return Close();
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }

    /// <summary>
    /// Loader for termination verification
    /// </summary>
    /// <returns>Terminate Verification View</returns>
    [HttpGet]
    public async Task<IActionResult> Verification([Required] long id,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var model = await lobbyistRegistrationCtlSvc.GetLobbyistVerificationViewModel(id);

            // Verify if user can have attest permission to do lobbyist registration
            var isAuthorized = await authorizationSvc.IsAuthorized(
                new Services.Business.Authorization.AuthorizationRequest(
                    CalAccess.Models.Authorization.Permission.Registration_Lobbyist_Attest, User, registrationId: id));
            model.IsUserAuthorizedToAttest = isAuthorized;

            return View(model);
        }
        catch (KeyNotFoundException e)
        {
            logger.LogError(e, "Lobbyist registration with ID {Id} not found.", new { Id = id });
            return BadRequest();
        }
    }

    /// <summary>
    /// Handler for all submissions to next page
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public async Task<IActionResult> Verification(
        LobbyistVerificationViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        return model.Action switch
        {
            FormAction.Continue => await HandleVerificationContinue(model),
            FormAction.Previous => HandleVerificationPrevious(model),
            FormAction.SaveAndClose => Close(),
            FormAction.Cancel => await Cancel(model.Id.GetValueOrDefault(), cancellationToken),
            _ => HandleVerificationDefault(model)
        };
    }
    #endregion

    #region Shared
    private static void ApplyDecisionErrors(List<CalAccess.Models.Common.WorkFlowError> errors, ModelStateDictionary modelState)
    {
        foreach (var error in errors)
        {
            // Errors to show at the bottom using Html.ValidationSummary
            string fieldName = "{{Field Name}}";
            if (error.Message.Contains(fieldName, StringComparison.Ordinal))
            {
                modelState.AddModelError("", error.Message.Replace(fieldName, error.FieldName, StringComparison.Ordinal));
            }
            else
            {
                modelState.AddModelError(error.FieldName, error.FieldName + ": " + error.Message);
            }
        }
    }

    private async Task<IActionResult> HandleVerificationContinue(LobbyistVerificationViewModel model)
    {
        var response = model.Type == RegistrationType.LobbyistWithdrawal
            ? await HandleSubmitSendForAttestWithdrawRegistration(model)
            : await HandleSubmitSendForAttestTerminationRegistration(model);

        if (response.Valid)
        {
            if (!model.IsUserAuthorizedToAttest)
            {
                toastService.Success(localizer[ResourceConstants.LobbyistSentForAttestationSuccess]);
                return RedirectToDashboard();
            }
            return RedirectToAction(nameof(Confirmation), new { model.Id, registrationType = model.Type });
        }

        ApplyDecisionErrors(response.ValidationErrors, ModelState);
        return View(model);
    }

    private IActionResult HandleVerificationPrevious(LobbyistVerificationViewModel model)
    {
        var previousPage = model.Type == RegistrationType.LobbyistWithdrawal
            ? nameof(WithdrawRegistration)
            : nameof(TerminationNotice);
        return RedirectToAction(previousPage, new { id = model.Id });
    }

    private IActionResult HandleVerificationDefault(LobbyistVerificationViewModel model)
    {
        ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
        return View(model);
    }

    private async Task<RegistrationResponseDto> HandleSubmitSendForAttestTerminationRegistration(
        LobbyistVerificationViewModel model)
    {
        var payload =
            new Services.Business.FilerRegistration.Registrations.Models.
                LobbyistRegistrationTerminationRequestDto()
            {
                LegislativeSessionId = model.LegislativeSessionId,
                TerminatedAt = model.EffectiveDateOfTermination,
                StatusId = model.IsUserAuthorizedToAttest
                        ? RegistrationStatus.Accepted.Id
                        : RegistrationStatus.Pending.Id,
                ExcuteOn = model.ExecutedOn
            };
        var response = await lobbyistRegistrationSvc.SendLobbyistRegistrationTermination(
                model.Id.GetValueOrDefault(), payload);
        return response;
    }

    private async Task<RegistrationResponseDto> HandleSubmitSendForAttestWithdrawRegistration(
        LobbyistVerificationViewModel model)
    {
        var payload =
            new Services.Business.FilerRegistration.Registrations.Models.
                WithdrawLobbyistRegistrationRequestDto()
            {
                WithdrawnAt = model.EffectiveDateOfWithdrawal,
                StatusId = model.IsUserAuthorizedToAttest
                        ? RegistrationStatus.Accepted.Id
                        : RegistrationStatus.Pending.Id,
                ExecutedOn = model.ExecutedOn,
                LegislativeSessionId = model.LegislativeSessionId
            };
        var response = await lobbyistRegistrationSvc.SendLobbyistRegistrationWithdrawal(
                model.Id.GetValueOrDefault(), payload);
        return response;
    }

    private static async Task<LobbyistRegistrationStep01ViewModel> PopulateReferenceData(
        LobbyistRegistrationStep01ViewModel model,
        IReferenceDataApi referenceDataApi,
        IFilingsApi filingsApi,
        ILobbyistApi lobbyistApi)
    {
        var agencies = await referenceDataApi.GetAllAgencies();
        model.Agencies = agencies!.ToDictionary(x => x.Id, x => x.Name);

        var legislativeSessions = await filingsApi.GetLegistlativeSessions();
        model.LegislativeSessionOptions = legislativeSessions.Sessions?
            .OrderBy(ls => ls.StartDate)
            .Select(ls => new SelectListItem
            {
                Value = ls.Id.ToString(CultureInfo.InvariantCulture),
                Text = $"{ls.StartDate.Year} - {ls.EndDate.Year}"
            })
            .ToList() ?? new List<SelectListItem>();

        if (model.LobbyistEmployerOrLobbyingFirmId.HasValue)
        {
            var lobbyistEmployerOrLobbyingFirm = await lobbyistApi.SearchLobbyistEmployerOrLobbyingFirmByIdOrName(model.LobbyistEmployerOrLobbyingFirmId.Value.ToString(CultureInfo.InvariantCulture), CancellationToken.None);

            model.Contact = new()
            {
                OrganizationName = lobbyistEmployerOrLobbyingFirm?.Count > 0
                ? lobbyistEmployerOrLobbyingFirm[0]?.Name
                : null
            };
        }
        return model;
    }

    [HttpGet("SearchLobbyistEmployerOrLobbyingFirmByIdOrName")]
    public async Task<JsonResult> SearchLobbyistEmployerOrLobbyingFirmByIdOrName(
    [FromServices] ILobbyistRegistrationSvc lobbyistRegistrationSvc,
    [FromQuery] string search)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { });
        }

        var data = await lobbyistRegistrationSvc.SearchLobbyistEmployerOrLobbyingFirmByIdOrName(search);
        return new JsonResult(data);
    }
    #endregion
}

@using Microsoft.AspNetCore.Html
@using Microsoft.AspNetCore.Mvc.Localization
@using SOS.CalAccess.Models.Common;
@using SOS.CalAccess.FilerPortal.Models.Localization;
@using SOS.CalAccess.FilerPortal.Models.Transactions
@using SOS.CalAccess.UI.Common.Constants;
@using SOS.CalAccess.UI.Common.Services;

@inject IHtmlLocalizer<SharedResources> Localizer
@model SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration.LobbyistEmployerRegistrationStep01Agencies;

@{
    var agencyOptions = (Model?.Agencies ?? new Dictionary<long, string>())
    .Select(a => new { Id = a.Key, Text = a.Value })
    .ToList();
}

@Html.HiddenFor(m => m.Id)
@Html.HiddenFor(m => m.IsRenewal)
@Html.HiddenFor(m => m.Version)

<div class="mb-3 col-lg-6">
    <p>
        @Localizer[ResourceConstants.LobbyistEmployerRegistration04StateAgenciesLobbied].Value
    </p>
    @Html.Raw(@Html.EJS().MultiSelect("SelectedAgencies")
            .Placeholder("Enter agency name")
            .DataSource(agencyOptions)
            .Fields(f => f.Value("Id").Text("Text"))
            .Value(Model?.SelectedAgencies)
            .AllowFiltering(true)
            .Mode(Syncfusion.EJ2.DropDowns.VisualMode.Box)
            .AllowCustomValue(false)
            .CssClass("multi-select rounded custom-border")
            .Render())

    <div class="mt-2">
        <a href="https://www.ca.gov/departments/list/" target="_blank">
            @Localizer[ResourceConstants.LobbyistEmployerRegistration04FullList].Value
        </a>
    </div>
    <div>
        @Html.SosValidationMessageFor(SharedLocalizer, m => m.SelectedAgencies)
    </div>
</div>
<div class="mb-3 col-lg-6">
    @Html.Label("StateLegislatureLobbied", Localizer[ResourceConstants.LobbyistEmployerRegistration04StateLegislatureLobbied].Value, new { @class = "form-label" })
    <div class="d-flex flex-column">
        <div>
            @Html.RadioButtonFor(m => m.StateLegislatureLobbied, true, new { @id = "isPlacementAgentYes" }) @Localizer["Common.Yes"]
        </div>
        <div>
            @Html.RadioButtonFor(m => m.StateLegislatureLobbied, false, new { @id = "isPlacementAgentNo" }) @Localizer["Common.No"]
        </div>
    </div>
    <div>
        @Html.SosValidationMessageFor(SharedLocalizer, m => m.StateLegislatureLobbied)
    </div>
</div>

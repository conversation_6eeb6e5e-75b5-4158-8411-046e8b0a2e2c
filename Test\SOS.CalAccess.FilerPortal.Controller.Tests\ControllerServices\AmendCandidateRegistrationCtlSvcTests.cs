using System;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using SOS.CalAccess.FilerPortal.ControllerServices.AmendCandidateRegistrationCtlSvc;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.Registrations.AmendCandidateRegistration;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business.FilerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Tests.ControllerServices;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(nameof(AmendCandidateRegistrationCtlSvc))]
internal sealed class AmendCandidateRegistrationCtlSvcTests
{
    private ICandidateIntentionRegistrationSvc _candidateIntentionRegistrationSvc;
    private ICandidateSvc _candidatSvc;
    private IUserMaintenanceSvc _userMaintenanceSvc;
    private IDecisionsValidationMapService _decisionsValidationMapService;
    private IStringLocalizer<SharedResources> _localizer;
    private AmendCandidateRegistrationCtlSvc _service;
    private IDateTimeSvc _mockDateTimeSvc;
    private TimeZoneInfo _timeZone;

    [SetUp]
    public void Setup()
    {
        _candidateIntentionRegistrationSvc = Substitute.For<ICandidateIntentionRegistrationSvc>();
        _candidatSvc = Substitute.For<ICandidateSvc>();
        _userMaintenanceSvc = Substitute.For<IUserMaintenanceSvc>();
        _decisionsValidationMapService = Substitute.For<IDecisionsValidationMapService>();
        _localizer = Substitute.For<IStringLocalizer<SharedResources>>();
        _mockDateTimeSvc = Substitute.For<IDateTimeSvc>();
        _timeZone = TimeZoneInfo.FindSystemTimeZoneById("Pacific Standard Time");
        _service = new AmendCandidateRegistrationCtlSvc(
            _candidateIntentionRegistrationSvc,
            _mockDateTimeSvc,
            _decisionsValidationMapService,
            _localizer);

        _localizer[Arg.Any<string>()].Returns(p => new LocalizedString((string)p[0], (string)p[0]));

    }

    [TearDown]
    public void TearDown()
    {
    }

    [Test]
    public void InstantiateCandidateRegistrationCtlSvc()
    {
        var output = _service.ReplaceFieldName("Test {{Field Name}}", "replacement");

        Assert.That(output, Is.EqualTo("Test replacement"));
    }

    #region Page01Submit
    [Test]
    public void Page01Submit_Update_ValidResponse()
    {
        ///Arrange
        var id = 1982;
        var model = new AmendCandidateRegistrationStep01ViewModel() { RegistrationId = id };
        var modelState = new ModelStateDictionary();
        var cancellationToken = CancellationToken.None;

        var response = new RegistrationResponseDto()
        {
            Valid = true,
            ValidationErrors = new() { },
            AddressValidationResult = new()
            {
                AddressResults = new List<AddressResult>
                {
                    new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                        Type = AddressType.Candidate
                    },
                     new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                        Type = AddressType.Mailing
                    },
                     new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                        Type = AddressType.Unknown
                    },
                     new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                    }
                }
            }
        };
        _candidateIntentionRegistrationSvc.UpdateCandidateIntentionStatement(id, Arg.Any<CandidateIntentionStatementRequest>()).Returns(response);

        //Act
        var result = _service.Page01Submit(model, modelState, cancellationToken, true);

        //Assert
        Assert.That(modelState.IsValid, Is.True);

        var errors = modelState.SelectMany(x => x.Value?.Errors!).Select(x => x.ErrorMessage).ToList();
        Assert.That(errors, Is.Not.Null);
        Assert.That(errors, Has.Count.EqualTo(0));
    }

    [Test]
    public void Page01Submit_Create_InvalidResponse()
    {
        ///Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel()
        {
            RegistrationId = 1,
            OriginalId = 1,
        };
        var modelState = new ModelStateDictionary();
        var cancellationToken = CancellationToken.None;

        var response = new RegistrationResponseDto()
        {
            Valid = false,
            ValidationErrors = new() {
                new("FirstName", "ErrorCode", "ErrorType", "message {{Field Name}}"),
            }
        };
        _candidateIntentionRegistrationSvc.CreateCandidateIntentionStatement(Arg.Any<CandidateIntentionStatementRequest>()).Returns(response);
        _decisionsValidationMapService
            .When(x => x.ApplyErrorsToModelState(Arg.Any<Dictionary<string, FieldProperty>>(), Arg.Any<List<CalAccess.Models.Common.WorkFlowError>>(), Arg.Any<ModelStateDictionary>()))
            .Do((callInfo) =>
            {
                var state = callInfo.Arg<ModelStateDictionary>();
                state.AddModelError("FirstName", "Error");
            });

        //Act
        var result = _service.Page01Submit(model, modelState, cancellationToken, true);

        //Assert
        Assert.That(modelState.IsValid, Is.False);

        var errors = modelState.SelectMany(x => x.Value?.Errors!).Select(x => x.ErrorMessage);
        Assert.That(errors, Is.Not.Null);
    }
    [Test]
    public void Page01Apply_Update_ValidResponse()
    {
        ///Arrange
        var id = 2000;
        //CandidateAddressConfirmation model 
        var model = new AmendCandidateRegistrationStep01ViewModel()
        {
            RegistrationId = id,
            CandidateAddressConfirmation = new AddressConfirmationModel { IsSuggested = true },
            MailingAddressConfirmation = new AddressConfirmationModel() { IsSuggested = true },
            AddressValidationResponse = new()
            {
                CandidateSuggestedAddress = new() { Street = "123", Street2 = "Main", City = "Dream City", State = "PA", Zip = "12345" },
                CandidateAddressValidationStatus = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                MailingSuggestedAddress = new() { Street = "123", Street2 = "Main", City = "Dream City", State = "PA", Zip = "12345" },
                MailingAddressValidationStatus = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges
            }
        };
        var modelState = new ModelStateDictionary();
        var cancellationToken = CancellationToken.None;

        var response = new RegistrationResponseDto()
        {
            Id = id,
            Valid = true,
            ValidationErrors = new() { },
            AddressValidationResult = new()
            {
                AddressResults = new List<AddressResult>
                {
                    new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                        Type = AddressType.Candidate
                    },
                     new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                        Type = AddressType.Mailing
                    },
                     new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                        Type = AddressType.Unknown
                    },
                     new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                    }
                }
            }
        };
        _candidateIntentionRegistrationSvc.UpdateCandidateIntentionStatement(id, Arg.Any<CandidateIntentionStatementRequest>()).Returns(response);

        //Act
        var result = _service.Page01Apply(model, modelState, cancellationToken);

        //Assert
        Assert.That(modelState.IsValid, Is.True);

        var errors = modelState.SelectMany(x => x.Value?.Errors!).Select(x => x.ErrorMessage).ToList();
        Assert.That(errors, Is.Not.Null);
        Assert.That(errors, Has.Count.EqualTo(0));
    }

    [Test]
    public void Page01Apply_Create_ValidResponse()
    {
        ///Arrange
        var id = 2025;
        //CandidateAddressConfirmation model 
        var model = new AmendCandidateRegistrationStep01ViewModel()
        {
            RegistrationId = id,
            OriginalId = id,
            CandidateAddressConfirmation = new AddressConfirmationModel { IsSuggested = true },
            MailingAddressConfirmation = new AddressConfirmationModel() { IsSuggested = true },
            AddressValidationResponse = new()
            {
                CandidateSuggestedAddress = new() { Street = "123", Street2 = "Main", City = "Dream City", State = "PA", Zip = "12345" },
                CandidateAddressValidationStatus = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                MailingSuggestedAddress = new() { Street = "123", Street2 = "Main", City = "Dream City", State = "PA", Zip = "12345" },
                MailingAddressValidationStatus = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges
            }
        };
        var modelState = new ModelStateDictionary();
        var cancellationToken = CancellationToken.None;

        var response = new RegistrationResponseDto()
        {
            Id = id,
            Valid = true,
            ValidationErrors = new() { },
            AddressValidationResult = new()
            {
                AddressResults = new List<AddressResult>
                {
                    new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                        Type = AddressType.Candidate
                    },
                     new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                        Type = AddressType.Mailing
                    },
                    new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                        Type = AddressType.Unknown
                    },
                    new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                        Type = (AddressType)999

                    }
                }
            }
        };
        _candidateIntentionRegistrationSvc.CreateCandidateIntentionStatement(Arg.Any<CandidateIntentionStatementRequest>()).Returns(response);

        //Act
        var result = _service.Page01Apply(model, modelState, cancellationToken);

        //Assert
        Assert.That(modelState.IsValid, Is.True);

        var errors = modelState.SelectMany(x => x.Value?.Errors!).Select(x => x.ErrorMessage).ToList();
        Assert.That(errors, Is.Not.Null);
        Assert.That(errors, Has.Count.EqualTo(0));
    }
    [Test]
    public void Page01Apply_Create_InValidResponse()
    {
        ///Arrange
        var id = 2025;
        //CandidateAddressConfirmation model 
        var model = new AmendCandidateRegistrationStep01ViewModel()
        {
            RegistrationId = id,
            OriginalId = id,
            CandidateAddressConfirmation = new AddressConfirmationModel { IsSuggested = true },
            MailingAddressConfirmation = new AddressConfirmationModel() { IsSuggested = true },
            AddressValidationResponse = new()
            {
                CandidateSuggestedAddress = new() { Street = "123", Street2 = "Main", City = "Dream City", State = "PA", Zip = "12345" },
                CandidateAddressValidationStatus = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                MailingSuggestedAddress = new() { Street = "123", Street2 = "Main", City = "Dream City", State = "PA", Zip = "12345" },
                MailingAddressValidationStatus = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges
            }
        };
        var modelState = new ModelStateDictionary();
        var cancellationToken = CancellationToken.None;

        var response = new RegistrationResponseDto()
        {
            Id = id,
            Valid = true,
            ValidationErrors = new() { },
            AddressValidationResult = new()
            {
                AddressResults = new List<AddressResult>
                {
                    new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                        Type = AddressType.Candidate
                    },
                    new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                        Type = AddressType.Mailing
                    },
                    new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                        Type = AddressType.Unknown
                    },
                    new()
                    {
                        Address = new AddressDto {Street = "123", Street2="Main", City="Dream City", State ="PA", Zip="12345" },
                        Status = CalAccess.Models.Common.AddressValidationStatus.SuggestedChanges,
                    }
                }
            }
        };
        _candidateIntentionRegistrationSvc.CreateCandidateIntentionStatement(Arg.Any<CandidateIntentionStatementRequest>()).Returns(new RegistrationResponseDto());

        //Act
        var result = _service.Page01Apply(model, modelState, cancellationToken);

        //Assert
        Assert.That(modelState.IsValid, Is.True);

        var errors = modelState.SelectMany(x => x.Value?.Errors!).Select(x => x.ErrorMessage).ToList();
        Assert.That(errors, Is.Not.Null);
        Assert.That(errors, Has.Count.EqualTo(0));
    }
    [Test]
    public async Task Page01Submit_CreatesNewStatement_WhenStatusIsApprovedAmendment()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            RegistrationId = 2,
            OriginalId = 1,
            StatusId = 3 // statusApproved
        };

        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto { Id = 10, Valid = true };
        var personalFundsOn = DateTime.Now;
        var previous = new CandidateIntentionStatementResponseDto
        {
            PoliticalPartyId = 99,
            ExpenditureLimitAccepted = true,
            ContributedPersonalExcessFundsOn = personalFundsOn
        };

        CandidateIntentionStatementRequest? capturedRequest = null;

        _candidateIntentionRegistrationSvc
            .GetCandidateIntentionStatement(2)
            .Returns(previous);

        _candidateIntentionRegistrationSvc
            .CreateCandidateIntentionStatement(Arg.Do<CandidateIntentionStatementRequest>(r => capturedRequest = r))
            .Returns(response);

        // Act
        var result = await _service.Page01Submit(model, modelState, CancellationToken.None);

        // Assert
        Assert.That(capturedRequest, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.EqualTo(10));
            Assert.That(capturedRequest.ParentId, Is.EqualTo(2));
            Assert.That(capturedRequest.PoliticalPartyId, Is.EqualTo(99));
            Assert.That(capturedRequest.ContributedPersonalExcessFundsOn, Is.EqualTo(personalFundsOn));
            Assert.That(capturedRequest.ExpenditureLimitAccepted, Is.True);
            Assert.That(capturedRequest.CheckRequiredFieldsFlag, Is.True);
        });
    }
    [Test]
    public async Task Page01Submit_WhenStatusApproved_AndContributedPersonalExcessFundsOnIsNull_DoesNotThrowAndSetsNull()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            RegistrationId = 123,
            OriginalId = 456,
            StatusId = 3 // statusApproved
        };

        _candidateIntentionRegistrationSvc
            .GetCandidateIntentionStatement(model.RegistrationId)
            .Returns((CandidateIntentionStatementResponseDto?)null);

        CandidateIntentionStatementRequest? capturedRequest = null;

        _candidateIntentionRegistrationSvc
            .CreateCandidateIntentionStatement(Arg.Do<CandidateIntentionStatementRequest>(r => capturedRequest = r))
            .Returns(new RegistrationResponseDto { Id = 999, Valid = true });

        // Act
        var result = await _service.Page01Submit(model, new ModelStateDictionary(), CancellationToken.None);

        // Assert
        Assert.That(capturedRequest, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(capturedRequest.ContributedPersonalExcessFundsOn, Is.Null);
            Assert.That(result, Is.EqualTo(999));
        });
    }
    [Test]
    public async Task Page01Submit_Should_CallCreate_When_OriginalFiling()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            RegistrationId = 1,
            OriginalId = 1,
            StatusId = 2
        };

        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(1)
            .Returns(new CandidateIntentionStatementResponseDto());

        _candidateIntentionRegistrationSvc.CreateCandidateIntentionStatement(Arg.Any<CandidateIntentionStatementRequest>())
            .Returns(new RegistrationResponseDto { Id = 123, Valid = true });

        var modelState = new ModelStateDictionary();

        // Act
        var result = await _service.Page01Submit(model, modelState, CancellationToken.None);

        // Assert
        Assert.That(result, Is.EqualTo(123));
    }
    [Test]
    public async Task Page01Submit_Should_CallCreate_When_ApprovedAmendment()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            RegistrationId = 2,
            OriginalId = 1,
            StatusId = 3
        };

        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(2)
            .Returns(new CandidateIntentionStatementResponseDto());

        _candidateIntentionRegistrationSvc.CreateCandidateIntentionStatement(Arg.Any<CandidateIntentionStatementRequest>())
            .Returns(new RegistrationResponseDto { Id = 456, Valid = true });

        var modelState = new ModelStateDictionary();

        // Act
        var result = await _service.Page01Submit(model, modelState, CancellationToken.None);

        // Assert
        Assert.That(result, Is.EqualTo(456));
        await _candidateIntentionRegistrationSvc.Received(1).CreateCandidateIntentionStatement(Arg.Any<CandidateIntentionStatementRequest>());
    }

    #endregion

    #region Page02
    [Test]
    public async Task Page02Submit_ShouldApplyModelErrors_WhenResponseIsInvalid()
    {
        // Arrange
        var registrationId = 123L;
        var model = new AmendCandidateRegistrationStep02ViewModel
        {
            SelectedDistrict = 2,
            SelectedElection = 3,
            SelectedElectionYear = "2026",
            SelectedJurisdiction = "state",
            SelectedOffice = 4,
            SelectedPartyAffiliation = 5
        };
        var modelState = new ModelStateDictionary();

        var errors = new List<WorkFlowError>
        {
            new("PoliticalParty", "ERR001", "Validation", "PoliticalParty is required")
        };

        var response = new RegistrationResponseDto(0, false, errors);

        _candidateIntentionRegistrationSvc
            .LinkElectionToCandidateIntentionStatement(registrationId, Arg.Any<UpdateRegistrationElectionRequest>())
            .Returns(response);

        // Act
        await _service.Page02Submit(registrationId, model, modelState, true);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(modelState.IsValid, Is.False);
            Assert.That(modelState.ContainsKey("SelectedPartyAffiliation"), Is.True);
            Assert.That(modelState["SelectedPartyAffiliation"].Errors.First().ErrorMessage, Is.EqualTo("PoliticalParty is required"));
        });
    }
    #endregion

    #region Page03

    [Test]
    public void Page03ValidatePersonalFundsDate_Should_Add_ModelError_When_Date_Is_Missing()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep03ViewModel
        {
            DidContributedPersonalExcessFunds = true,
            ContributedPersonalExcessFundsOn = null
        };

        var modelState = new ModelStateDictionary();

        // Act
        _service.Page03ValidatePersonalFundsDate(model, modelState);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(modelState.IsValid, Is.False);
            Assert.That(modelState.ContainsKey("ContributedPersonalExcessFundsOn"));
        });
    }

    [Test]
    public async Task Page03Submit_CallsUpdate_WithCorrectPayload()
    {
        // Arrange
        var expectedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0);

        var model = new AmendCandidateRegistrationStep03ViewModel
        {
            Id = 1,
            ExpenditureLimitAccepted = "true",
            ContributedPersonalExcessFundsOn = expectedDate,
            ExpenditureExceeded = true
        };

        var modelState = new ModelStateDictionary();

        CandidateIntentionStatementExpenditureLimitRequest? capturedPayload = null;

        _candidateIntentionRegistrationSvc
            .When(x => x.UpdateCandidateIntentionStatementExpenditureLimit(1, Arg.Any<CandidateIntentionStatementExpenditureLimitRequest>()))
            .Do(call => capturedPayload = call.Arg<CandidateIntentionStatementExpenditureLimitRequest>());

        // Act
        await _service.Page03Submit(model, modelState);

        // Assert
        Assert.That(capturedPayload, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(capturedPayload?.ExpenditureLimitAccepted, Is.True);
            Assert.That(capturedPayload?.ContributedPersonalExcessFundsOn?.Date, Is.EqualTo(expectedDate.Date));
            Assert.That(capturedPayload?.ExpenditureExceeded, Is.True);
        });
    }

    [Test]
    public void Page03Submit_WhenServiceThrowsException_ThrowsArgumentException()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep03ViewModel
        {
            Id = 1,
            ExpenditureLimitAccepted = "true",
            ContributedPersonalExcessFundsOn = null,
            ExpenditureExceeded = false
        };

        var modelState = new ModelStateDictionary();

        _candidateIntentionRegistrationSvc
            .UpdateCandidateIntentionStatementExpenditureLimit(Arg.Any<long>(), Arg.Any<CandidateIntentionStatementExpenditureLimitRequest>())
            .Throws(new ArgumentException("Test failure"));

        // Act & Assert
        var ex = Assert.ThrowsAsync<ArgumentException>(async () =>
            await _service.Page03Submit(model, modelState, true));

        Assert.That(ex.Message, Does.StartWith("Invalid request:"));
    }

    [Test]
    public async Task Page03GetPastRegistration_ReturnsNull_WhenBothIdsAreNull()
    {
        var result = await _service.Page03GetPastRegistration(null, null);
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task Page03GetPastRegistration_UsesParentId_WhenPresent()
    {
        var expectedDto = new CandidateIntentionStatementResponseDto();
        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(1).Returns(expectedDto);

        var result = await _service.Page03GetPastRegistration(1, 2);

        await _candidateIntentionRegistrationSvc.Received(1).GetCandidateIntentionStatement(1);
        Assert.That(result, Is.EqualTo(expectedDto));
    }

    [Test]
    public async Task Page03GetPastRegistration_UsesOriginalId_WhenParentIdIsNull()
    {
        var dto = new CandidateIntentionStatementResponseDto();
        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(2).Returns(dto);

        var result = await _service.Page03GetPastRegistration(null, 2);

        Assert.That(result, Is.EqualTo(dto));
        await _candidateIntentionRegistrationSvc.Received(1).GetCandidateIntentionStatement(2);
    }

    [Test]
    public async Task Page03GetViewModelBeforeOnDeadline_SimpleCase_NoLimitChanges()
    {
        var reg0 = new CandidateIntentionStatementResponseDto
        {
            ExpenditureLimitAccepted = true,
            ExpenditureExceeded = false,
            ContributedPersonalExcessFundsOn = null
        };

        var reg1 = new CandidateIntentionStatementResponseDto
        {
            ExpenditureLimitAccepted = true,
            ParentId = 1,
            OriginalId = 2
        };

        var reg2 = new CandidateIntentionStatementResponseDto
        {
            ExpenditureLimitAccepted = true,
            ParentId = 3,
            OriginalId = 4
        };

        var reg3 = new CandidateIntentionStatementResponseDto
        {
            ExpenditureLimitAccepted = true
        };

        _service.Page03GetPastRegistration(1, 2).Returns(reg2);
        _service.Page03GetPastRegistration(3, 4).Returns(reg3);

        var result = await _service.Page03GetViewModelBeforeOnDeadline(
            reg0, reg1, new DateTime(2025, 6, 1, 0, 0, 0, 0),
            5000m, false, 123);

        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(123));
            Assert.That(result.ExpenditureLimitAccepted, Is.EqualTo("true"));
            Assert.That(result.ExpenditureLimitAmount, Is.EqualTo(5000m));
            Assert.That(result.ExpenditureExceeded, Is.False);
            Assert.That(result.DidContributedPersonalExcessFunds, Is.False);
            Assert.That(result.ContributedPersonalExcessFundsOn, Is.Null);
            Assert.That(result.IsLimitRadioReadOnly, Is.False);
            Assert.That(result.IsLimitCheckboxReadOnly, Is.True);
            Assert.That(result.IsPersonalFundsSectionReadOnly, Is.False);
        });
    }

    [Test]
    public async Task Page03GetViewModelBeforeOnDeadline_MultipleLimitChanges_ReadOnlyTrue()
    {
        // Arrange
        var reg0 = new CandidateIntentionStatementResponseDto
        {
            ExpenditureLimitAccepted = false,
            ExpenditureExceeded = true,
            ContributedPersonalExcessFundsOn = new DateTime(2025, 1, 1, 0, 0, 0, 0)
        };

        var reg1 = new CandidateIntentionStatementResponseDto
        {
            ExpenditureLimitAccepted = false,
            ParentId = 1,
            OriginalId = 2
        };

        var reg2 = new CandidateIntentionStatementResponseDto
        {
            ExpenditureLimitAccepted = true,  // Change 1
            ParentId = 3,
            OriginalId = 4
        };

        var reg3 = new CandidateIntentionStatementResponseDto
        {
            ExpenditureLimitAccepted = false,
            Id = 2  // Match the originalId of reg1 to trigger the exit condition in the loop
        };

        // Mock the method that retrieves previous registrations
        _service.Page03GetPastRegistration(1, 2).Returns(reg2);  // reg2 is the first past registration
        _service.Page03GetPastRegistration(3, 4).Returns(reg3);  // reg3 is the second past registration

        // Act
        var result = await _service.Page03GetViewModelBeforeOnDeadline(
            reg0, reg1, new DateTime(2025, 6, 1, 0, 0, 0, 0),
            5000m, true, 456);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(456));
            Assert.That(result.ExpenditureLimitAccepted, Is.EqualTo("false"));
            Assert.That(result.IsLimitRadioReadOnly, Is.True);
            Assert.That(result.DidContributedPersonalExcessFunds, Is.True);
            Assert.That(result.IsPersonalFundsSectionReadOnly, Is.True);
        });
    }
    [Test]
    public async Task Page03BuildViewModel_BeforeOrOnFilingDeadline_ReturnsBeforeDeadlineModel()
    {
        var regId = 1L;

        var registration0 = new CandidateIntentionStatementResponseDto
        {
            ParentId = 10,
            OriginalId = 20,
            ExpenditureLimitAccepted = true,
            ExpenditureExceeded = false
        };

        var registration1 = new CandidateIntentionStatementResponseDto
        {
            ExpenditureLimitAccepted = true
        };

        var election = new Election
        {
            Name = "test",
            FilingDeadline = DateTime.Now.AddDays(1),
            ElectionDate = DateTime.Now.AddDays(30),
            ExpenditureLimitAcceptanceDeadline = 10
        };

        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatementElection(regId).Returns(election);
        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(regId).Returns(registration0);
        _service.Page03GetPastRegistration(10, 20).Returns(registration1);

        var result = await _service.Page03BuildViewModel(regId);

        Assert.That(result.IsLimitRadioReadOnly, Is.False);
    }
    [Test]
    public Task Page03BuildViewModel_BeforeOrOnFilingDeadline_ThrowsArgumentException()
    {
        var regId = 1L;

        var election = new Election
        {
            Name = "test",
            FilingDeadline = DateTime.Now.AddDays(30),
            ElectionDate = DateTime.Now,
            ExpenditureLimitAcceptanceDeadline = 10
        };

        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatementElection(regId).Returns(election);
        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(regId).Returns((CandidateIntentionStatementResponseDto?)null);
        _service.Page03GetPastRegistration(10, 20).Returns((CandidateIntentionStatementResponseDto?)null);

        Assert.ThrowsAsync<ArgumentException>(async () => await _service.Page03BuildViewModel(regId));
        return Task.CompletedTask;
    }
    [Test]
    public async Task Page03BuildViewModel_AfterFilingDeadline_BeforeElectionDate_ReturnsCorrectModel()
    {
        // Arrange
        var registrationId = 123L;

        var election = new Election
        {
            Name = "test",
            FilingDeadline = DateTime.Now.AddDays(-2),  // Filing deadline was 2 days ago
            ElectionDate = DateTime.Now.AddDays(2),     // Election date is two days from now
            ExpenditureLimitAcceptanceDeadline = 10        // 10 days after election
        };

        var registration0 = new CandidateIntentionStatementResponseDto
        {
            ExpenditureLimitAccepted = true,
            ExpenditureExceeded = true,
            ContributedPersonalExcessFundsOn = new DateTime(2025, 1, 1, 0, 0, 0, 0),
            ExpenditureCeilingAmount = 5000m,
            ParentId = 10,
            OriginalId = 20
        };

        var registration1 = new CandidateIntentionStatementResponseDto
        {
            ExpenditureLimitAccepted = false
        };

        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatementElection(Arg.Any<long>()).Returns(election);
        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(Arg.Any<long>()).Returns(registration0);
        _service.Page03GetPastRegistration(registration0.ParentId, registration0.OriginalId).Returns(registration1);
        _mockDateTimeSvc.GetCurrentDateTime().Returns(TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, _timeZone));

        // Act
        var result = await _service.Page03BuildViewModel(registrationId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(registrationId));
            Assert.That(result.IsLimitRadioReadOnly, Is.True);
            Assert.That(result.IsLimitCheckboxReadOnly, Is.True);
        });
    }
    [Test]
    public async Task Page03BuildViewModel_AfterFilingDeadline_BeforeElectionDate_ReturnsCorrectModel2()
    {
        // Arrange
        var registrationId = 123L;

        var election = new Election
        {
            Name = "test",
            FilingDeadline = DateTime.Now.AddDays(-2),  // Filing deadline was 2 days ago
            ElectionDate = DateTime.Now.AddDays(2),     // Election date is two days from now
            ExpenditureLimitAcceptanceDeadline = 10        // 10 days after election
        };

        var registration0 = new CandidateIntentionStatementResponseDto
        {
            ExpenditureLimitAccepted = false,
            ExpenditureExceeded = false,
            ContributedPersonalExcessFundsOn = null,
            ExpenditureCeilingAmount = 5000m,
            ParentId = 10,
            OriginalId = 20
        };

        var registration1 = new CandidateIntentionStatementResponseDto
        {
            ExpenditureLimitAccepted = true
        };

        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatementElection(Arg.Any<long>()).Returns(election);
        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(Arg.Any<long>()).Returns(registration0);
        _service.Page03GetPastRegistration(registration0.ParentId, registration0.OriginalId).Returns(registration1);
        _mockDateTimeSvc.GetCurrentDateTime().Returns(TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, _timeZone));

        // Act
        var result = await _service.Page03BuildViewModel(registrationId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(registrationId));
            Assert.That(result.IsLimitRadioReadOnly, Is.True);
            Assert.That(result.IsLimitCheckboxReadOnly, Is.True);
        });
    }
    [Test]
    public async Task Page03BuildViewModel_WithinExpenditureDeadline_ReturnsMidWindowModel()
    {
        var regId = 3L;

        var registration0 = new CandidateIntentionStatementResponseDto
        {
            ExpenditureLimitAccepted = true,
            ExpenditureExceeded = true,
            ContributedPersonalExcessFundsOn = new DateTime(2025, 1, 1)
        };

        var registration1 = new CandidateIntentionStatementResponseDto
        {
            ContributedPersonalExcessFundsOn = null
        };

        var election = new Election
        {
            Name = "test",
            FilingDeadline = DateTime.Now.AddDays(-5), // already passed
            ElectionDate = DateTime.Now.AddDays(-1),   // happened yesterday
            ExpenditureLimitAcceptanceDeadline = 10                      // window still open
        };

        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatementElection(regId).Returns(election);
        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(regId).Returns(registration0);
        _service.Page03GetPastRegistration(regId, null).Returns(registration1);
        _mockDateTimeSvc.GetCurrentDateTime().Returns(TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, _timeZone));

        var result = await _service.Page03BuildViewModel(regId);

        Assert.Multiple(() =>
        {
            Assert.That(result.IsLimitRadioReadOnly, Is.True);
            Assert.That(result.IsLimitCheckboxReadOnly, Is.False);
            Assert.That(result.IsPersonalFundsSectionReadOnly, Is.False);
        });
    }

    [Test]
    public async Task Page03BuildViewModel_PastExpenditureDeadline_ReturnsReadOnlyModel()
    {
        var regId = 4L;

        var registration0 = new CandidateIntentionStatementResponseDto
        {
            ParentId = null,
            OriginalId = null,
            ExpenditureLimitAccepted = false,
            ExpenditureExceeded = true,
            ContributedPersonalExcessFundsOn = new DateTime(2025, 2, 1)
        };

        var election = new Election
        {
            Name = "test",
            FilingDeadline = DateTime.Now.AddDays(-30), // long passed
            ElectionDate = DateTime.Now.AddDays(-20),   // also past
            ExpenditureLimitAcceptanceDeadline = 5                        // 5 days after election → also past
        };

        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatementElection(regId)
            .Returns(election);

        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(regId)
            .Returns(registration0);

        _candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(Arg.Any<long>())
            .Returns((CandidateIntentionStatementResponseDto?)null); // No prior registration

        _mockDateTimeSvc.GetCurrentDateTime().Returns(TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, _timeZone));

        var result = await _service.Page03BuildViewModel(regId);

        Assert.Multiple(() =>
        {
            Assert.That(result.IsLimitRadioReadOnly, Is.True);
            Assert.That(result.IsLimitCheckboxReadOnly, Is.True);
            Assert.That(result.IsPersonalFundsSectionReadOnly, Is.False);
            Assert.That(result.ExpenditureLimitAccepted, Is.EqualTo("false"));
        });
    }
    [Test]
    public void Page03BuildViewModelAfterDeadline_CoversAllConditions()
    {
        var election = new Election
        {
            Name = "test",
            ElectionDate = new DateTime(2025, 11, 5, 0, 0, 0, 0)
        };
        var limitAmount = 1000m;
        var registrationId = 123;

        var testCases = new[]
        {
        (prevAccepted: true,  prevExceeded: true,  hasExcessDate: true,  expectedAccepted: "true",  expectedExceeded: true,  isReadonly: true),
        (prevAccepted: true,  prevExceeded: false, hasExcessDate: false, expectedAccepted: "true",  expectedExceeded: false, isReadonly: false),
        (prevAccepted: false, prevExceeded: true,  hasExcessDate: false, expectedAccepted: "false", expectedExceeded: true,  isReadonly: true),
        (prevAccepted: false, prevExceeded: false, hasExcessDate: true,  expectedAccepted: "false", expectedExceeded: false, isReadonly: false),
        (prevAccepted: (bool?)null,  prevExceeded: true,  hasExcessDate: true,  expectedAccepted: "false", expectedExceeded: true,  isReadonly: true),
        (prevAccepted: true,  prevExceeded: (bool?)null,  hasExcessDate: false, expectedAccepted: "true",  expectedExceeded: false, isReadonly: false),
        (prevAccepted: (bool?)null,  prevExceeded: (bool?)null,  hasExcessDate: false, expectedAccepted: "false", expectedExceeded: false, isReadonly: true),
    };

        foreach (var (prevAccepted, prevExceeded, hasExcessDate, expectedAccepted, expectedExceeded, isReadonly) in testCases)
        {
            var previousAmendment = (prevAccepted != null || prevExceeded != null)
                ? new CandidateIntentionStatementResponseDto
                {
                    ExpenditureLimitAccepted = prevAccepted.GetValueOrDefault(),
                    ExpenditureExceeded = prevExceeded ?? false
                }
                : null;

            var latestAmendment = new CandidateIntentionStatementResponseDto
            {
                ContributedPersonalExcessFundsOn = hasExcessDate ? DateTime.Now : null
            };

            var result = _service.Page03BuildViewModelAfterDeadline(
                registrationId, election, previousAmendment, latestAmendment, limitAmount, isReadonly);

            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(registrationId));
                Assert.That(result.ExpenditureLimitAccepted, Is.EqualTo(expectedAccepted));
                Assert.That(result.ExpenditureLimitAmount, Is.EqualTo(limitAmount));
                Assert.That(result.ExpenditureExceeded, Is.EqualTo(expectedExceeded));
                Assert.That(result.DidContributedPersonalExcessFunds, Is.EqualTo(hasExcessDate));
                Assert.That(result.ContributedPersonalExcessFundsOn != null, Is.EqualTo(hasExcessDate));
                Assert.That(result.IsLimitRadioReadOnly, Is.True);
                Assert.That(result.IsLimitCheckboxReadOnly, Is.True);
                Assert.That(result.IsPersonalFundsSectionReadOnly, Is.EqualTo(isReadonly));
            });
        }
    }
    #endregion
}

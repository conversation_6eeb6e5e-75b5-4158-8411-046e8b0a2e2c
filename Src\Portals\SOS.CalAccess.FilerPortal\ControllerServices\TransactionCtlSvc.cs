using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.Extensions;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Contacts;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Transactions;
using SOS.CalAccess.UI.Common;

namespace SOS.CalAccess.FilerPortal.ControllerServices;

public class TransactionCtlSvc(
    IContactsApi contactsApi,
    ILobbyistEmployerApi lobbyistEmployerApi,
    ILobbyistEmployerCoalitionApi lobbyistEmployerCoalitionApi,
    ILobbyingAdvertisementApi lobbyingAdvertisementApi,
    IStringLocalizer<SharedResources> localizer) : ITransactionCtlSvc
{
    private static readonly string _businessAddressType = "Business";
    private static readonly string _phonePhoneNumberType = "Phone";
    private static readonly string _faxPhoneNumberType = "Fax";

    public async Task<PaymentMadeToLobbyingCoalitionViewModel> GetContactDetailForViewModel(
        PaymentMadeToLobbyingCoalitionViewModel model, CancellationToken cancellationToken = default)
    {
        if (model.ContactId.HasValue && model.ContactId != 0)
        {
            var contact = await contactsApi.GetContact(model.ContactId.Value, cancellationToken);
            var phoneNumber = contact.PhoneNumbers.FirstOrDefault(x => x.Type == _phonePhoneNumberType);
            var faxNumber = contact.PhoneNumbers.FirstOrDefault(x => x.Type == _faxPhoneNumberType);
            var emailAddress = contact.EmailAddresses.ElementAtOrDefault(0);
            model.LobbyingCoalitionName = contact.DisplayName();
            model.Contact = new GenericContactViewModel
            {
                PhoneNumberId = phoneNumber?.Id,
                PhoneNumber = phoneNumber?.Number ?? string.Empty,
                PhoneNumberCountryCode = phoneNumber?.CountryCode ?? string.Empty,
                FaxNumberId = faxNumber?.Id,
                FaxNumber = faxNumber?.Number ?? string.Empty,
                FaxNumberCountryCode = faxNumber?.CountryCode ?? string.Empty,
                EmailAddressId = emailAddress?.Id,
                EmailAddress = emailAddress?.Email ?? string.Empty,
                Country = contact.Country,
                Street = contact.Street,
                Street2 = contact.Street2,
                City = contact.City,
                State = contact.State,
                ZipCode = contact.ZipCode,
                AddressId = contact.AddressId,
            };
        }
        else if (model.RegistrationFilingId.HasValue && model.RegistrationFilingId != 0)
        {
            var contact = await lobbyistEmployerApi.GetLobbyistEmployer(model.RegistrationFilingId!.Value, cancellationToken);
            var phoneNumber = contact.PhoneNumbers.ElementAtOrDefault(0);
            var faxNumber = contact.PhoneNumbers.FirstOrDefault(x => x.Type == _faxPhoneNumberType);
            var address = contact.Addresses.ElementAtOrDefault(0);

            model.LobbyingCoalitionName = contact.Name;
            model.Contact = new GenericContactViewModel()
            {
                PhoneNumber = phoneNumber?.Number ?? string.Empty,
                PhoneNumberCountryCode = phoneNumber?.CountryCode ?? string.Empty,
                FaxNumber = faxNumber?.Number ?? string.Empty,
                FaxNumberCountryCode = faxNumber?.CountryCode ?? string.Empty,
                EmailAddress = contact.Email,
                Country = address?.Country ?? string.Empty,
                Street = address?.Street ?? string.Empty,
                Street2 = address?.Street2 ?? string.Empty,
                City = address?.City ?? string.Empty,
                State = address?.State ?? string.Empty,
                ZipCode = address?.Zip ?? string.Empty,
            };
        }

        return model;
    }

    public async Task<long> HandlePaymentToLobbyingCoalitionPage02Submit(PaymentMadeToLobbyingCoalitionViewModel model, CancellationToken cancellationToken = default)
    {
        var contactDetail = model.Contact;
        var addressDtoList = new List<AddressDto>()
        {
            new(
                contactDetail!.City ?? string.Empty,
                contactDetail!.Country ?? string.Empty,
                contactDetail?.AddressId ?? 0,
                string.Empty,
                contactDetail!.State ?? string.Empty,
                contactDetail!.Street ?? string.Empty,
                contactDetail!.Street2 ?? string.Empty,
                _businessAddressType,
                contactDetail!.ZipCode ?? string.Empty
            )
        };
        var emailAddressDtoList = new List<EmailAddressDto>()
        {
            new(contactDetail!.EmailAddress ?? string.Empty, contactDetail?.EmailAddressId ?? 0, string.Empty, string.Empty)
        };
        var phoneNumberDtoList = new List<PhoneNumberDto>()
        {
            new(contactDetail?.PhoneNumberCountryCode ?? string.Empty
            ,null
            , contactDetail?.PhoneNumberId ?? 0
            , false
            , contactDetail?.PhoneNumber ?? string.Empty
            , null
            , false
            , _phonePhoneNumberType),
            new(contactDetail?.FaxNumberCountryCode ?? string.Empty
            , null
            , contactDetail?.FaxNumberId ?? 0
            , false
            , contactDetail?.FaxNumber ?? string.Empty
            , null
            , false
            , _faxPhoneNumberType),
        };
        // Can replace blank string with optional UpsertFilerContactForm after refit refactor
        var upsertContactRequest = new UpsertOrganizationFilerContactRequest(
            addressDtoList,
            emailAddressDtoList,
            string.Empty,
            model!.LobbyingCoalitionName!.ToString(),
            phoneNumberDtoList);

        if (model.ContactId != null && model.ContactId.Value != 0)
        {
            await contactsApi.UpdateFilerContact(model.ContactId.Value, upsertContactRequest, cancellationToken);
        }
        else
        {
            var newContact = await contactsApi.CreateFilerContact(model!.FilerId!.Value, upsertContactRequest, cancellationToken);
            model.ContactId = newContact.Id;
        }
        return model.ContactId!.Value;
    }

    public async Task<TransactionResponseDto> HandlePaymentToLobbyingCoalitionPage03Submit(PaymentMadeToLobbyingCoalitionRequestDto requestDto, ModelStateDictionary modelState, CancellationToken cancellationToken = default)
    {
        TransactionResponseDto response;
        if (requestDto.TransactionId is not null)
        {
            response = await lobbyistEmployerCoalitionApi.EditPaymentMadeToLobbyingCoalition((long)requestDto.TransactionId, requestDto, cancellationToken);
        }
        else
        {
            response = await lobbyistEmployerCoalitionApi.CreatePaymentMadeToLobbyingCoalition(requestDto, cancellationToken);
        }

        if (!response.Valid)
        {
            var error = response.ValidationErrors.FirstOrDefault(x => x.FieldName == "PeriodAmount");
            if (error != null)
            {
                modelState.AddModelError("Amount", ReplaceFieldName(error.Message, localizer[ResourceConstants.CoalitionPaymentsAmount].Value));
            }
        }
        return response;
    }

    private static string ReplaceFieldName(string templateString, string replacement)
    {
        return templateString.Replace("{{Field Name}}", replacement, StringComparison.OrdinalIgnoreCase);
    }
}

using NSubstitute;
using SOS.CalAccess.Data.Contracts.UserAccountMaintenance;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;

namespace SOS.CalAccess.Services.Business.Contracts.Tests.FilerRegistration.Filers;

/// <summary>
/// Unit tests for the <see cref="UserMaintenanceSvc"/> class.
/// </summary>
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(UserMaintenanceSvc))]
public sealed class UserMaintenanceSvcTest
{
    private IAuthorizationSvc _authorizationSvcMock;
    private IUserRepository _userRepositoryMock;
    private UserMaintenanceSvc _userMaintenanceSvc;

    /// <summary>
    /// Sets up the unit tests for this fixture.
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();
        _userRepositoryMock = Substitute.For<IUserRepository>();
        _userMaintenanceSvc = new UserMaintenanceSvc
            (
                _authorizationSvcMock,
                _userRepositoryMock
            );
    }

    [Test]
    public async Task GetListUsersByUserNameAsync_Found_ShouldReturnResult()
    {
        // Act
        var userNames = new List<string> { "Test" };
        var expected = new List<User>
        {
            new()
            {
                Id = 1,
                FirstName = "Test",
                LastName = "Test",
                EmailAddress = "Test",
                UserName = "Test",
                EntraOid = "TestOid"
            }
        };

        _userRepositoryMock.FindUsersByUserNames(Arg.Any<List<string>>()).Returns(Task.FromResult(expected));

        // Arrange
        var result = await _userMaintenanceSvc.GetListUsersByUserNameAsync(userNames);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(expected));
        });
    }

    /// <summary>
    /// Get user by id
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task GetUserById_Found_ShouldReturnResult()
    {
        // Act
        var userId = 1;
        var expected = new User
        {
            Id = 1,
            FirstName = "Test",
            LastName = "Test",
            EmailAddress = "Test",
            UserName = "Test",
            EntraOid = "TestOid"
        };

        _userRepositoryMock.FindById(Arg.Any<long>()).Returns(expected);

        // Arrange
        var result = await _userMaintenanceSvc.GetUserById(userId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(expected));
        });
    }

    /// <summary>
    /// Get user account details by user id
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task GetUserAccountDetailsByUserId_Found_ShouldReturnResult()
    {
        // Act
        var userId = 1;
        var expected = new User
        {
            Id = userId,
            FirstName = "Test first name",
            LastName = "Test last name",
            EmailAddress = "Test email",
            UserName = "Test user name",
            EntraOid = "TestOid"
        };

        var address = new Address
        {
            Street = "Test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Purpose = "Office",
            Type = "Cell",
        };
        var phoneNumber = new PhoneNumber
        {
            Number = "Test number",
            Extension = "Test extn",
            CountryCode = "Test code",
            Type = "Cell",
            InternationalNumber = true,
        };
        var addressList = new AddressList
        {
            Addresses = new List<Address> { address }
        };
        var phoneNumberList = new PhoneNumberList
        {
            PhoneNumbers = new List<PhoneNumber> { phoneNumber }
        };
        var user = new User
        {
            Id = 1,
            FirstName = "Test first name",
            LastName = "Test last name",
            EmailAddress = "Test email address",
            UserName = "Test user name",
            EntraOid = "Test Oid",
            AddressList = addressList,
            PhoneNumberList = phoneNumberList,
        };

        var userDetails = _userRepositoryMock.GetUserAccountDetailsByUserId(Arg.Any<long>()).Returns(user);

        // Arrange
        var result = await _userMaintenanceSvc.GetUserAccountDetailsByUserId(user.Id);
        var addresses = result.AddressDto;
        var phoneNumbers = result.PhoneNumberDto!.ToList();

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.FirstName, Is.EqualTo("Test first name"));
            Assert.That(result.LastName, Is.EqualTo("Test last name"));
            Assert.That(result.Email, Is.EqualTo("Test email address"));
            Assert.That(result.UserName, Is.EqualTo("Test user name"));
            Assert.That(addresses, Is.Not.Null);
            Assert.That(addresses!.Street, Is.EqualTo("Test street"));
            Assert.That(addresses!.City, Is.EqualTo("Test city"));
            Assert.That(addresses!.State, Is.EqualTo("Test state"));
            Assert.That(addresses!.Zip, Is.EqualTo("Test zip"));
            Assert.That(addresses!.Country, Is.EqualTo("Test country"));
            Assert.That(addresses!.Type, Is.EqualTo("Cell"));
            Assert.That(addresses!.Purpose, Is.EqualTo("Office"));
            Assert.That(addresses!.Street2, Is.Null);
            Assert.That(phoneNumbers[0], Is.Not.Null);
            Assert.That(phoneNumbers[0].Number, Is.EqualTo("Test number"));
            Assert.That(phoneNumbers[0].Extension, Is.EqualTo("Test extn"));
            Assert.That(phoneNumbers[0].CountryCode, Is.EqualTo("Test code"));
            Assert.That(phoneNumbers[0].Type, Is.EqualTo("Cell"));
            Assert.That(phoneNumbers[0].InternationalNumber, Is.EqualTo(true));
            Assert.That(phoneNumbers[0].SetAsPrimaryPhoneNumber, Is.False);
            Assert.That(phoneNumbers[0].SelectedCountry, Is.Null);
        });
    }

    /// <summary>
    /// Get user account details by id returns null
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task GetUserAccountDetailsByUserId_NotFound_ShouldReturnNull()
    {
        // Act
        var userId = 2;
        var expected = new User
        {
            Id = userId,
            FirstName = "first name",
            LastName = "last name",
            EmailAddress = "email",
            UserName = "user name",
            EntraOid = "TestOid"
        };

        var address = new Address
        {
            Street = "Test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Purpose = "Office",
            Type = "Cell",
        };
        var phoneNumber = new PhoneNumber
        {
            Number = "Test number",
            Extension = "Test extn",
            CountryCode = "Test code",
            Type = "Cell",
            InternationalNumber = true,
        };
        var addressList = new AddressList
        {
            Addresses = new List<Address> { address }
        };
        var phoneNumberList = new PhoneNumberList
        {
            PhoneNumbers = new List<PhoneNumber> { phoneNumber }
        };

        var user = new User
        {
            Id = 1,
            FirstName = "Test first name",
            LastName = "Test last name",
            EmailAddress = "Test email address",
            UserName = "Test user name",
            EntraOid = "Test Oid",
            AddressList = addressList,
            PhoneNumberList = phoneNumberList,
        };

        var userDetails = _userRepositoryMock.GetUserAccountDetailsByUserId(Arg.Any<long>()).Returns(expected);

        // Arrange
        var result = await _userMaintenanceSvc.GetUserAccountDetailsByUserId(user.Id);
        var addresses = result.AddressDto;
        var phoneNumbers = result.PhoneNumberDto!.ToList();


        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.FirstName, Is.Not.EqualTo("Test first name"));
            Assert.That(result.LastName, Is.Not.EqualTo("Test last name"));
            Assert.That(result.Email, Is.Not.EqualTo("Test email address"));
            Assert.That(result.UserName, Is.Not.EqualTo("Test user name"));
            Assert.That(addresses, Is.Not.Null);
            Assert.That(addresses!.Street, Is.Null);
            Assert.That(addresses!.City, Is.Null);
            Assert.That(addresses!.State, Is.Null);
            Assert.That(addresses!.Zip, Is.Null);
            Assert.That(addresses!.Country, Is.Null);
            Assert.That(addresses!.Type, Is.Null);
            Assert.That(addresses!.Purpose, Is.Null);
            Assert.That(addresses!.Street2, Is.Null);
            Assert.That(phoneNumbers, Is.Empty);
        });
    }

    /// <summary>
    /// Update user without phone numbers
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task UpdateUser_WithoutPhoneNumbers()
    {
        // Act
        var userId = 1;
        var expected = new User
        {
            Id = userId,
            FirstName = "Test first name",
            LastName = "Test last name",
            EmailAddress = "Test email",
            UserName = "Test user name",
            EntraOid = "TestOid"
        };

        AddressDto addressDto = new()
        {
            Street = "Test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Type = "Test type"
        };

        List<PhoneNumberDto> phoneNumberDto = new()
            {
                new(){
                CountryCode = "Test country code",
                Extension = "Test extension",
                Id = userId,
                InternationalNumber = true,
                Number = "Test number",
                SelectedCountry = 1,
                SetAsPrimaryPhoneNumber = true,
                Type = "Test type",
                }
            };

        UserAccountDetailsDto userDto = new()
        {
            Id = 1,
            Email = "Test email",
            FirstName = "Test first name",
            LastName = "Test last name",
            UserName = "Test user name",
            AddressDto = addressDto,
            PhoneNumberDto = phoneNumberDto
        };
        _userRepositoryMock.GetUserAccountDetailsByUserId(Arg.Any<long>()).Returns(expected);
        _userRepositoryMock.Update(Arg.Any<User>()).Returns(expected);
        await _userRepositoryMock.SaveChangesAsync();

        // Arrange
        var result = await _userMaintenanceSvc.GetUserAccountDetailsByUserId(userDto.Id);
        AddressDto? addressToUpdate = result.AddressDto;
        var newAddress = userDto.AddressDto;
        addressToUpdate!.Street ??= newAddress.Street!;
        addressToUpdate.Street2 ??= newAddress.Street2;
        addressToUpdate.City ??= newAddress.City!;
        addressToUpdate.State ??= newAddress.State!;
        addressToUpdate.Zip ??= newAddress.Zip!;
        addressToUpdate.Country ??= newAddress.Country!;
        addressToUpdate.Type ??= newAddress.Type!;

        var update = _userMaintenanceSvc.UpdateUser(userDto);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.FirstName, Is.EqualTo(userDto.FirstName));
            Assert.That(result.LastName, Is.EqualTo(userDto.LastName));
            Assert.That(result.Email, Is.EqualTo(userDto.Email));
            Assert.That(result.UserName, Is.EqualTo(userDto.UserName));
            Assert.That(addressToUpdate, Is.Not.Null);
            Assert.That(addressToUpdate!.Street, Is.Not.Null);
            Assert.That(addressToUpdate!.City, Is.Not.Null);
            Assert.That(addressToUpdate!.State, Is.Not.Null);
            Assert.That(addressToUpdate!.Zip, Is.Not.Null);
            Assert.That(addressToUpdate!.Country, Is.Not.Null);
        });
    }

    /// <summary>
    /// Update user
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task UpdateUser_WithoutAddressAndPhoneDto()
    {
        // Act
        var userId = 1;

        AddressDto addressDto = new()
        {
            Street = "Test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Type = "Test type"
        };

        List<PhoneNumberDto> phoneNumberDto = new()
            {
                new(){
                CountryCode = "Test country code",
                Extension = "Test extension",
                Id = userId,
                InternationalNumber = true,
                Number = "Test number",
                SelectedCountry = 1,
                SetAsPrimaryPhoneNumber = true,
                Type = "Test type",
                }
            };

        UserAccountDetailsDto userDto = new()
        {
            Id = userId,
            Email = "Test email address",
            FirstName = "Test first name",
            LastName = "Test last name",
            UserName = "Test user name",
            AddressDto = addressDto,
            PhoneNumberDto = phoneNumberDto
        };

        var address = new Address
        {
            Street = "Test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Purpose = "Office",
            Type = "Cell",
        };
        var phoneNumbers = new PhoneNumber
        {
            Number = "Test number",
            Extension = "Test extn",
            CountryCode = "Test code",
            Type = "Cell",
        };
        var addressList = new AddressList
        {
            Addresses = new List<Address> { address }
        };
        var phoneNumberList = new PhoneNumberList
        {
            PhoneNumbers = new List<PhoneNumber> { phoneNumbers }
        };
        var expectedUser = new User
        {
            Id = userId,
            FirstName = "Test first name",
            LastName = "Test last name",
            EmailAddress = "Test email address",
            UserName = "Test user name",
            EntraOid = "Test Oid",
            AddressList = addressList,
            PhoneNumberList = phoneNumberList,
        };

        // Arrange
        _userRepositoryMock.GetUserAccountDetailsByUserId(Arg.Any<long>()).Returns(expectedUser);
        Address? addressToUpdate = null;
        var newAddress = userDto.AddressDto;
        if (addressToUpdate == null)
        {
            addressToUpdate = new Address
            {
                Street = newAddress.Street!,
                Street2 = newAddress.Street2,
                City = newAddress.City!,
                State = newAddress.State!,
                Zip = newAddress.Zip!,
                Country = newAddress.Country!,
                Type = newAddress.Type!,
                Purpose = newAddress.Purpose!,
            };
            expectedUser.AddressList.Addresses.Add(addressToUpdate);
        }
        else
        {
            addressToUpdate!.Street = newAddress.Street!;
            addressToUpdate.Street2 = newAddress.Street2;
            addressToUpdate.City = newAddress.City!;
            addressToUpdate.State = newAddress.State!;
            addressToUpdate.Zip = newAddress.Zip!;
            addressToUpdate.Country = newAddress.Country!;
            addressToUpdate.Type = newAddress.Type!;
        }
        var existingPhoneNumberList = expectedUser.PhoneNumberList;
        var existPhoneNumbers = existingPhoneNumberList.PhoneNumbers;
        var existingbyId = existPhoneNumbers.Where(p => p.Id > 0).ToDictionary(p => p.Id);
        var seenIds = new HashSet<long>();

        foreach (var dto in userDto.PhoneNumberDto)
        {
            if (dto.Id.HasValue && dto.Id.Value > 0 && existingbyId.TryGetValue(dto.Id.Value, out var phoneEntity))
            {
                seenIds.Add(dto.Id.Value);
                phoneEntity.Number = dto.Number!;
                phoneEntity.Extension = dto.Extension!;
                phoneEntity.Type = dto.Type!;
                phoneEntity.IsPrimaryPhoneNumber = dto.SetAsPrimaryPhoneNumber!;
                phoneEntity.CountryCode = dto.CountryCode!;
            }
            else
            {
                var newPhone = new PhoneNumber
                {
                    Number = dto.Number!,
                    Extension = dto.Extension!,
                    Type = dto.Type!,
                    IsPrimaryPhoneNumber = dto.SetAsPrimaryPhoneNumber!,
                    InternationalNumber = dto.SetAsPrimaryPhoneNumber!,
                    CountryCode = dto.CountryCode!
                };
                existingPhoneNumberList.PhoneNumbers.Add(newPhone);
            }
        }

        var toRemove = existPhoneNumbers.Where(p => p.Id > 0 && !seenIds.Contains(p.Id)).ToList();
        foreach (var orphen in toRemove)
        {
            existingPhoneNumberList.PhoneNumbers.Remove(orphen);
        }
        _userRepositoryMock.Update(Arg.Any<User>()).Returns(expectedUser);
        await _userRepositoryMock.SaveChangesAsync();

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(expectedUser, Is.Not.Null);
            Assert.That(expectedUser.FirstName, Is.EqualTo(userDto.FirstName));
            Assert.That(expectedUser.LastName, Is.EqualTo(userDto.LastName));
            Assert.That(expectedUser.EmailAddress, Is.EqualTo(userDto.Email));
            Assert.That(expectedUser.UserName, Is.EqualTo(userDto.UserName));
            Assert.That(addressToUpdate, Is.Not.Null);
            Assert.That(addressToUpdate!.Street, Is.EqualTo("Test street"));
            Assert.That(addressToUpdate!.City, Is.EqualTo("Test city"));
            Assert.That(addressToUpdate!.State, Is.EqualTo("Test state"));
            Assert.That(addressToUpdate!.Zip, Is.EqualTo("Test zip"));
            Assert.That(addressToUpdate!.Country, Is.EqualTo("Test country"));
        });
    }

    /// <summary>
    /// Update user with address and phone dto
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task UpdateUser_WithAddressAndPhoneDto()
    {
        // Act
        var userId = 1;

        AddressDto addressDto = new()
        {
            Street = "Test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Type = "Test type"
        };

        List<PhoneNumberDto> phoneNumberDto = new()
            {
                new(){
                CountryCode = "Test country code",
                Extension = "Test extension",
                Id = userId,
                InternationalNumber = true,
                Number = "Test number",
                SelectedCountry = 1,
                SetAsPrimaryPhoneNumber = true,
                Type = "Test type",
                }
            };

        UserAccountDetailsDto userDto = new()
        {
            Email = "Test email address",
            FirstName = "Test first name",
            LastName = "Test last name",
            UserName = "Test user name",
            AddressDto = addressDto,
            PhoneNumberDto = phoneNumberDto
        };

        var address = new Address
        {
            Street = "Test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Purpose = "Office",
            Type = "Cell",
        };
        var phoneNumbers = new PhoneNumber
        {
            Id = 1,
            Number = "Test number",
            Extension = "Test extn",
            CountryCode = "Test code",
            Type = "Cell",
        };
        var addressList = new AddressList
        {
            Addresses = new List<Address> { address }
        };
        var phoneNumberList = new PhoneNumberList
        {
            PhoneNumbers = new List<PhoneNumber> { phoneNumbers }
        };
        var expectedUser = new User
        {
            FirstName = "Test first name",
            LastName = "Test last name",
            EmailAddress = "Test email address",
            UserName = "Test user name",
            EntraOid = "Test Oid",
            AddressList = addressList,
            PhoneNumberList = phoneNumberList,
        };

        // Arrange
        _userRepositoryMock.GetUserAccountDetailsByUserId(Arg.Any<long>()).Returns(expectedUser);
        Address? addressToUpdate = expectedUser.AddressList.Addresses.FirstOrDefault();
        var newAddress = userDto.AddressDto;
        if (addressToUpdate == null)
        {
            addressToUpdate = new Address
            {
                Street = newAddress.Street!,
                Street2 = newAddress.Street2,
                City = newAddress.City!,
                State = newAddress.State!,
                Zip = newAddress.Zip!,
                Country = newAddress.Country!,
                Type = newAddress.Type!,
                Purpose = newAddress.Purpose!,
            };
            expectedUser.AddressList.Addresses.Add(addressToUpdate);
        }
        else
        {
            addressToUpdate.Street = newAddress.Street!;
            addressToUpdate.Street2 = newAddress.Street2;
            addressToUpdate.City = newAddress.City!;
            addressToUpdate.State = newAddress.State!;
            addressToUpdate.Zip = newAddress.Zip!;
            addressToUpdate.Country = newAddress.Country!;
            addressToUpdate.Type = newAddress.Type!;
        }
        var existingPhoneNumberList = expectedUser.PhoneNumberList;
        var existPhoneNumbers = existingPhoneNumberList.PhoneNumbers;
        var existingbyId = existPhoneNumbers.Where(p => p.Id > 0).ToDictionary(p => p.Id);
        var seenIds = new HashSet<long>();

        foreach (var dto in userDto.PhoneNumberDto)
        {
            if (dto.Id.HasValue && dto.Id.Value > 0 && existingbyId.TryGetValue(dto.Id.Value, out var phoneEntity))
            {
                seenIds.Add(dto.Id.Value);
                phoneEntity.Number = dto.Number!;
                phoneEntity.Extension = dto.Extension!;
                phoneEntity.Type = dto.Type!;
                phoneEntity.IsPrimaryPhoneNumber = dto.SetAsPrimaryPhoneNumber!;
                phoneEntity.CountryCode = dto.CountryCode!;
            }
            else
            {
                var newPhone = new PhoneNumber
                {
                    Number = dto.Number!,
                    Extension = dto.Extension!,
                    Type = dto.Type!,
                    IsPrimaryPhoneNumber = dto.SetAsPrimaryPhoneNumber!,
                    InternationalNumber = dto.SetAsPrimaryPhoneNumber!,
                    CountryCode = dto.CountryCode!
                };
                existingPhoneNumberList.PhoneNumbers.Add(newPhone);
            }
        }

        var toRemove = existPhoneNumbers.Where(p => p.Id > 0 && !seenIds.Contains(p.Id)).ToList();
        foreach (var orphen in toRemove)
        {
            existingPhoneNumberList.PhoneNumbers.Remove(orphen);
        }
        _userRepositoryMock.Update(Arg.Any<User>()).Returns(expectedUser);
        await _userRepositoryMock.SaveChangesAsync();

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(expectedUser, Is.Not.Null);
            Assert.That(expectedUser.FirstName, Is.EqualTo(userDto.FirstName));
            Assert.That(expectedUser.LastName, Is.EqualTo(userDto.LastName));
            Assert.That(expectedUser.EmailAddress, Is.EqualTo(userDto.Email));
            Assert.That(expectedUser.UserName, Is.EqualTo(userDto.UserName));
            Assert.That(addressToUpdate, Is.Not.Null);
            Assert.That(addressToUpdate!.Street, Is.EqualTo("Test street"));
            Assert.That(addressToUpdate!.City, Is.EqualTo("Test city"));
            Assert.That(addressToUpdate!.State, Is.EqualTo("Test state"));
            Assert.That(addressToUpdate!.Zip, Is.EqualTo("Test zip"));
            Assert.That(addressToUpdate!.Country, Is.EqualTo("Test country"));
        });
    }
}

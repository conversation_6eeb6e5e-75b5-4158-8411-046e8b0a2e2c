using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Models;

namespace SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration;

public class InHouseLobbyistListViewModel
{
    public DataGridModel? InHouseLobbyistsGridModel { get; set; }
    public long? Id { get; set; }
    public FormAction? Action { get; set; }
    public List<InHouseLobbyistResponseDto>? Lobbyists { get; set; }
}

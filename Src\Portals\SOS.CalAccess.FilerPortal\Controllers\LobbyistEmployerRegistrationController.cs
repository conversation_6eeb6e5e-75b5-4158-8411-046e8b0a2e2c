using System.ComponentModel.DataAnnotations;
using System.Globalization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration;
using SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Controllers;

public class LobbyistEmployerRegistrationController(
    ILobbyistEmployerRegistrationCtlSvc lobbyistEmployerRegistrationCtlSvc,
    IAccuMailValidatorService accuMailValidatorService,
    IStringLocalizer<SharedResources> localizer,
    ILobbyistEmployerRegistrationSvc lobbyistEmployerRegistrationSvc,
    IToastService toastService) : Controller
{
    #region Shared

    public override void OnActionExecuting(ActionExecutingContext context)
    {
        InitializeViewData();
    }

    public IActionResult Index()
    {
        return RedirectToAction("Page01");
    }

    public IActionResult Close(FormAction? action = null)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        if (action == FormAction.Cancel)
        {
            toastService.Success(localizer[CommonResourceConstants.ToastCancelled]);
        }
        return RedirectToDashboard();
    }

    public async Task<IActionResult> Cancel(
        [Required] long id,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            await lobbyistEmployerRegistrationSvc.CancelLobbyistEmployerRegistration(id);
            toastService.Success(localizer[CommonResourceConstants.ToastCancelled]);
            return RedirectToDashboard();
        }

        return new NotFoundResult();
    }

    public IActionResult Edit([Required] long id)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }
        return RedirectToAction("Page03", new { Id = id });
    }

    public ActionResult RedirectToDashboard()
    {
        return RedirectToAction("Index", "Dashboard");
    }

    public IActionResult UnderContruction()
    {
        return RedirectToAction("UnderConstruction", "Home");
    }

    private void InitializeViewData()
    {
        ViewData[LayoutConstants.Title] = localizer[ResourceConstants.LobbyistEmployerRegistrationTitle].Value;
        ViewData[LayoutConstants.Breadcrumbs] = new List<Breadcrumb>()
        {
            new("Filer Portal", "/FilerPortal"),
            new("Lobbyist Employer", "/LobbyistEmployerRegistration"),
        };
        ViewData["ProgressItem1Name"] = "1. " + localizer[ResourceConstants.LobbyistEmployerRegistrationGeneralInformation].Value;
        ViewData["ProgressItem2Name"] = "2. " + localizer[ResourceConstants.LobbyistEmployerRegistrationInHouseLobbyists].Value;
        ViewData["ProgressItem3Name"] = "3. " + localizer[ResourceConstants.LobbyistEmployerRegistrationLobbyingFirms].Value;
        ViewData["ProgressItem4Name"] = "4. " + localizer[ResourceConstants.LobbyistEmployerRegistrationResponsibleOfficers].Value;
        ViewData["ProgressItem5Name"] = "5. " + localizer[ResourceConstants.LobbyistEmployerRegistrationSubmit].Value;
    }

    #endregion

    #region Page 01 - FR-LEC-Start

    /// <summary>
    /// Loader for Page 01.
    /// </summary>
    /// <param name="id"></param>
    /// <returns>The first page to start the lobbyist employer/coalition registration</returns>
    public IActionResult Page01()
    {
        return View();
    }

    #endregion

    #region Page 02 - FR-LEC-FilerInformation-1

    [HttpGet]
    public IActionResult Page02(long? id)
    {
        var model = new LobbyistEmployerRegistrationBase
        {
            Id = id.GetValueOrDefault()
        };

        if (ModelState.IsValid)
        {
            return View(model);
        }
        else
        {
            return RedirectToAction(nameof(Page01));
        }
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public IActionResult Page02(LobbyistEmployerRegistrationBase? model)
    {
        if (ModelState.IsValid)
        {
            switch (model?.Action)
            {
                case FormAction.Continue:
                    return RedirectToAction(nameof(Page03), new { id = model.Id });
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page01));
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    #endregion

    #region Page 03 - FR-LEC-FilerInformation-2

    [HttpGet]
    public async Task<IActionResult> Page03(long? id, CancellationToken cancellationToken)
    {
        var model = await lobbyistEmployerRegistrationCtlSvc.GetStep01GenInfoViewModel(id.GetValueOrDefault(), cancellationToken);

        if (ModelState.IsValid)
        {
            return View(model);
        }
        else
        {
            return RedirectToAction(nameof(Page02));
        }
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public async Task<IActionResult> Page03([FromServices] IFilingsApi filingsApi, LobbyistEmployerRegistrationStep01GenInfo model)
    {
        if (ModelState.IsValid)
        {
            if (await accuMailValidatorService.AccuMailValidationHandler(model, ModelState, model.Action == FormAction.Cancel))
            {
                await lobbyistEmployerRegistrationCtlSvc.PopulateStep01GenInfoReferenceData(model, filingsApi);
                return View(model);
            }

            switch (model.Action)
            {
                case FormAction.Continue:
                    return await Page03ContinueAsync(model);
                case FormAction.SaveAndClose:
                    return await Page03Save(model);
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page02));
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    /// <summary>
    /// Handler for Page03 Continue Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private async Task<IActionResult> Page03ContinueAsync(LobbyistEmployerRegistrationStep01GenInfo model)
    {
        var registration = await lobbyistEmployerRegistrationCtlSvc.SubmitStep01GenInfoViewModel(model, ModelState, true);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page04), new { id = registration.Id });
        }

        return View(nameof(Page03), model);
    }

    /// <summary>
    /// Handler for Page03 Save Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load after saving</returns>
    private async Task<IActionResult> Page03Save(LobbyistEmployerRegistrationStep01GenInfo model)
    {
        _ = await lobbyistEmployerRegistrationCtlSvc.SubmitStep01GenInfoViewModel(model, ModelState, true);
        if (ModelState.IsValid)
        {
            return RedirectToDashboard();
        }

        return View(nameof(Page03), model);
    }

    #endregion

    #region Page 04 - FR-LEC-StateAgencies

    [HttpGet]
    public async Task<IActionResult> Page04(long? id, CancellationToken cancellationToken)
    {
        var model = await lobbyistEmployerRegistrationCtlSvc.GetStep01AgenciesViewModel(id);

        if (ModelState.IsValid)
        {
            return View(model);
        }
        else
        {
            return RedirectToAction(nameof(Page03));
        }
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public async Task<IActionResult> Page04(LobbyistEmployerRegistrationStep01Agencies? model)
    {
        if (ModelState.IsValid)
        {
            switch (model?.Action)
            {
                case FormAction.Continue:
                    return await Page04ContinueAsync(model);
                case FormAction.SaveAndClose:
                    return await Page04Save(model);
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page03), new { id = model.Id });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    /// <summary>
    /// Handler for Page04 Continue Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private async Task<IActionResult> Page04ContinueAsync(LobbyistEmployerRegistrationStep01Agencies model)
    {
        var registration = await lobbyistEmployerRegistrationCtlSvc.SubmitStep01AgenciesViewModel(model, ModelState, true);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page05), new { id = registration.Id });
        }

        return View(nameof(Page04), model);
    }

    /// <summary>
    /// Handler for Page04 Save Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load after saving</returns>
    private async Task<IActionResult> Page04Save(LobbyistEmployerRegistrationStep01Agencies model)
    {
        _ = await lobbyistEmployerRegistrationCtlSvc.SubmitStep01AgenciesViewModel(model, ModelState, true);
        if (ModelState.IsValid)
        {
            return RedirectToDashboard();
        }

        return View(nameof(Page04), model);
    }


    #endregion

    #region Page 05 - FR-LEC-Lobbying Interests

    [HttpGet]
    public async Task<IActionResult> Page05(long? id, CancellationToken cancellationToken)
    {
        var model = await lobbyistEmployerRegistrationCtlSvc.GetStep01NatureInterestsViewModel(id);

        if (ModelState.IsValid)
        {
            return View(model);
        }
        else
        {
            return RedirectToAction(nameof(Page04));
        }
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public async Task<IActionResult> Page05(LobbyistEmployerRegistrationStep01NatureInterests? model)
    {
        if (ModelState.IsValid)
        {
            switch (model?.Action)
            {
                case FormAction.Continue:
                    return await Page05ContinueAsync(model);
                case FormAction.SaveAndClose:
                    return await Page05Save(model);
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page04), new { id = model.Id });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    /// <summary>
    /// Handler for Page05 Continue Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private async Task<IActionResult> Page05ContinueAsync(LobbyistEmployerRegistrationStep01NatureInterests model)
    {
        var registration = await lobbyistEmployerRegistrationCtlSvc.SubmitStep01NatureInterestViewModel(model, ModelState, true);

        if (ModelState.IsValid)
        {
            return RedirectToAction("Step02LobbyistList", "LobbyistEmployerRegistration", new { id = registration.Id });
            //TD: Open route when completed
            //return RedirectToAction(nameof(Page06), new { id = registration.Id });
        }

        return View(nameof(Page05), model);
    }

    /// <summary>
    /// Handler for Page05 Save Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load after saving</returns>
    private async Task<IActionResult> Page05Save(LobbyistEmployerRegistrationStep01NatureInterests model)
    {
        _ = await lobbyistEmployerRegistrationCtlSvc.SubmitStep01NatureInterestViewModel(model, ModelState, true);
        if (ModelState.IsValid)
        {
            return RedirectToDashboard();
        }

        return View(nameof(Page05), model);
    }

    #endregion

    #region Page 06 - FR-LEC-NatureAndInterests

    #endregion

    #region Step 02 - In-House Lobbyists List

    [HttpGet]
    public async Task<IActionResult> Step02LobbyistList(long? id)
    {
        var model = await lobbyistEmployerRegistrationCtlSvc.GetInHouseLobbyistListViewModel(id);

        if (ModelState.IsValid)
        {
            return View(model);
        }
        else
        {
            return RedirectToAction(nameof(Page03));
        }
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public IActionResult Step02LobbyistList(InHouseLobbyistListViewModel? model)
    {
        if (ModelState.IsValid)
        {
            switch (model?.Action)
            {
                case FormAction.Continue:
                    return RedirectToAction(nameof(Step03LobbyingFirms), new { id = model.Id });
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page03));
                case FormAction.SaveAndClose:
                    toastService.Success(localizer[CommonResourceConstants.ToastSaved]);
                    return RedirectToDashboard();
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    public IActionResult Step02LobbyistListEdit()
    {
        return UnderContruction();
    }

    public IActionResult Step02LobbyistListCertification()
    {
        return UnderContruction();
    }

    #region Initiate Certify in-house Lobbyist
    /// <summary>
    /// Lobbyist Certification Statement, Get method
    /// </summary>
    /// <param name="id"></param>
    /// <param name="referenceDataApi"></param>
    /// <param name="filingsApi"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> Step02InitiateCertification(
    long id,
    [FromServices] IReferenceDataApi referenceDataApi,
    [FromServices] IFilingsApi filingsApi,
    CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            var model = await lobbyistEmployerRegistrationCtlSvc.GetLobbyistCertificationViewModel(id, cancellationToken);
            await PopulateReferenceData(model, referenceDataApi, filingsApi);
            return View(model);
        }
        return BadRequest();
    }

    /// <summary>
    ///  Lobbyist Certification Statement, Post method
    /// </summary>
    /// <param name="model"></param>
    /// <param name="referenceDataApi"></param>
    /// <param name="filingsApi"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> Step02InitiateCertification(
    LobbyistCertificationViewModel model,
    [FromServices] IReferenceDataApi referenceDataApi,
    [FromServices] IFilingsApi filingsApi)
    {
        await PopulateReferenceData(model, referenceDataApi, filingsApi);

        if (!ModelState.IsValid)
        {
            return View(model);
        }

        if (model.IsSameAsBusinessAddress)
        {
            model.Addresses.Find(x => x.Purpose == "Mailing")?.Clear();
        }

        if (await accuMailValidatorService.AccuMailValidationHandler(model, ModelState, model.Action == FormAction.Cancel))
        {
            return View(model);
        }

        model.IsNewCertification = true;
        switch (model.Action)
        {
            case FormAction.Continue:
            case FormAction.SaveAndClose:
            case FormAction.Previous:
                var response = await lobbyistEmployerRegistrationCtlSvc.SaveLobbyistCertificationViewModel(model);
                if (response.Valid)
                {
                    toastService.Success("Saved");
                }
                if (model.Action == FormAction.Continue)
                {
                    // TD: Continue page
                }
                return RedirectToAction(nameof(Step02LobbyistList), new { id = model.LobbyistEmployerOrLobbyingFirmId });
            default:
                break;
        }

        return View(model);
    }
    #endregion

    /// <summary>
    /// Creates or update a new Inhouse Lobbyist Registration
    /// </summary>
    /// <param name="id">Lobbyist employer registration id</par am>
    /// <returns></returns>
    [HttpGet("/[controller]/{id:long}/Lobbyist/{lobbyistRegistrationId:long?}")]
    public async Task<IActionResult> LobbyistCreationOrModification(long id, long? lobbyistRegistrationId)
    {
        if (ModelState.IsValid)
        {
            var model = await lobbyistEmployerRegistrationCtlSvc.GetInHouseLobbyistViewModel(id, lobbyistRegistrationId);

            return View(model);
        }

        return NotFound();
    }

    /// <summary>
    /// Creates or update a new Lobbyist Registration
    /// </summary>
    /// <param name="model">Lobbyist creation view model</param>
    /// <returns></returns>
    [HttpPost("/[controller]/{id}/Lobbyist/{lobbyistRegistrationId:long?}")]
    public async Task<IActionResult> LobbyistCreationOrModification(long id, long? lobbyistRegistrationId, [FromForm] InHouseLobbyistViewModel model)
    {
        if (ModelState.IsValid)
        {
            await lobbyistEmployerRegistrationCtlSvc.CreateOrUpdateInHouseLobbyist(model);

            return RedirectToAction(nameof(Step02LobbyistList), new { id });
        }

        return View(model);
    }

    [HttpGet]
    public IActionResult Step02AddLobbyist(long id)
    {
        var model = new LobbyistEmployerRegistrationStep02AddLobbyistViewModel();
        if (ModelState.IsValid)
        {
            return View(model);
        }
        else
        {
            return RedirectToAction(nameof(Page02));
        }
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public IActionResult Step02AddLobbyist(LobbyistEmployerRegistrationStep02AddLobbyistViewModel model)
    {
        if (ModelState.IsValid)
        {
            switch (model?.Action)
            {
                case FormAction.Continue:
                    return RedirectToAction("Step02LobbyistList", "LobbyistEmployerRegistration", new { id = model.Id });
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page03));
                case FormAction.SaveAndClose:
                    toastService.Success(localizer[CommonResourceConstants.ToastSaved]);
                    return RedirectToDashboard();
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    [HttpGet]
    public async Task<JsonResult> SearchLobbyistByIdOrName(
    [FromServices] ILobbyistEmployerRegistrationSvc lobbyistEmployerRegistrationSvc,
    [FromQuery] string search)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { });
        }

        var data = await lobbyistEmployerRegistrationSvc.SearchLobbyistByIdOrName(search);
        return new JsonResult(data);
    }

    #endregion

    #region private
    private static async Task PopulateReferenceData(
    LobbyistRegistrationStep01ViewModel model,
    IReferenceDataApi referenceDataApi,
    IFilingsApi filingsApi)
    {
        var listLegislativeSession = await filingsApi.GetLegistlativeSessions();
        model.LegislativeSessionOptions = listLegislativeSession.Sessions?
            .OrderBy(ls => ls.StartDate)
            .Select(ls => new SelectListItem
            {
                Text = $"{ls.StartDate.Year} - {ls.EndDate.Year}",
                Value = ls.Id.ToString(CultureInfo.InvariantCulture),
            })
            .ToList() ?? new List<SelectListItem>();

        var listAgency = await referenceDataApi.GetAllAgencies();
        model.Agencies = listAgency!.ToDictionary(x => x.Id, x => x.Name);

        if (model.LobbyistEmployerOrLobbyingFirmId.HasValue && !string.IsNullOrEmpty(model.LobbyistEmployerOrLobbyingFirmName))
        {
            model.Contact = new()
            {
                OrganizationName = model.LobbyistEmployerOrLobbyingFirmName
            };
        }
    }
    #endregion

    #region Step 03 - Lobbying Firms

    [HttpGet]
    public IActionResult Step03LobbyingFirms(long? id)
    {
        var model = lobbyistEmployerRegistrationCtlSvc.GetLobbyistEmployerRegistrationStep03LobbyingFirmsViewModel(id);

        if (ModelState.IsValid)
        {
            return View(model);
        }
        else
        {
            return RedirectToAction(nameof(Step02LobbyistList));
        }
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public IActionResult Step03LobbyingFirms(LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel? model)
    {
        if (ModelState.IsValid)
        {
            switch (model?.Action)
            {
                case FormAction.Continue:
                    return UnderContruction();
                case FormAction.Previous:
                    return RedirectToAction(nameof(Step02LobbyistList), new { id = model.Id });
                case FormAction.SaveAndClose:
                    toastService.Success(localizer[CommonResourceConstants.ToastSaved]);
                    return RedirectToDashboard();
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    #endregion
}

﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SOS.CalAccess.Data.EntityFramework.Migrations
{
    /// <inheritdoc />
    public partial class FrLobEmpNatureAndInterestDataDelta : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "BusinessSubcategoryId",
                table: "RegistrationFiling",
                type: "bigint",
                nullable: true)
                .Annotation("Documentation:Description", "Foreign Key reference to the Business Subcategory Id for the registration.")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "RegistrationFilingHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null)
                .Annotation("SqlServer:TemporalPeriodEndColumnName", "PeriodEnd")
                .Annotation("SqlServer:TemporalPeriodStartColumnName", "PeriodStart");

            migrationBuilder.AddColumn<string>(
                name: "BusinessSubcategoryOtherText",
                table: "RegistrationFiling",
                type: "nvarchar(max)",
                nullable: true)
                .Annotation("Documentation:Description", "Business Subcategory Other Text.")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "RegistrationFilingHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null)
                .Annotation("SqlServer:TemporalPeriodEndColumnName", "PeriodEnd")
                .Annotation("SqlServer:TemporalPeriodStartColumnName", "PeriodStart");

            migrationBuilder.AddColumn<bool>(
                name: "IsFiftyMembersOrLess",
                table: "RegistrationFiling",
                type: "bit",
                nullable: true)
                .Annotation("Documentation:Description", "Gets or sets a value indicating threshold of members.")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "RegistrationFilingHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null)
                .Annotation("SqlServer:TemporalPeriodEndColumnName", "PeriodEnd")
                .Annotation("SqlServer:TemporalPeriodStartColumnName", "PeriodStart");

            migrationBuilder.CreateIndex(
                name: "IX_RegistrationFiling_BusinessSubcategoryId",
                table: "RegistrationFiling",
                column: "BusinessSubcategoryId");

            migrationBuilder.AddForeignKey(
                name: "FK_RegistrationFiling_BusinessSubcategory_BusinessSubcategoryId",
                table: "RegistrationFiling",
                column: "BusinessSubcategoryId",
                principalTable: "BusinessSubcategory",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RegistrationFiling_BusinessSubcategory_BusinessSubcategoryId",
                table: "RegistrationFiling");

            migrationBuilder.DropIndex(
                name: "IX_RegistrationFiling_BusinessSubcategoryId",
                table: "RegistrationFiling");

            migrationBuilder.DropColumn(
                name: "BusinessSubcategoryId",
                table: "RegistrationFiling")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "RegistrationFilingHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null)
                .Annotation("SqlServer:TemporalPeriodEndColumnName", "PeriodEnd")
                .Annotation("SqlServer:TemporalPeriodStartColumnName", "PeriodStart");

            migrationBuilder.DropColumn(
                name: "BusinessSubcategoryOtherText",
                table: "RegistrationFiling")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "RegistrationFilingHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null)
                .Annotation("SqlServer:TemporalPeriodEndColumnName", "PeriodEnd")
                .Annotation("SqlServer:TemporalPeriodStartColumnName", "PeriodStart");

            migrationBuilder.DropColumn(
                name: "IsFiftyMembersOrLess",
                table: "RegistrationFiling")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "RegistrationFilingHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null)
                .Annotation("SqlServer:TemporalPeriodEndColumnName", "PeriodEnd")
                .Annotation("SqlServer:TemporalPeriodStartColumnName", "PeriodStart");
        }
    }
}

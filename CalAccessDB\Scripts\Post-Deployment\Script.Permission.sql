SET IDENTITY_INSERT [dbo].[Permission] ON;
GO

MERGE INTO Permission
USING (VALUES 
    (1, 'ReferenceData_Retrieve', 'Allow user to retrieve reference data such as elections, offices, etc'),
    (2, 'Candidate_SearchByName', 'Allow user to retrieve candidate information based on search of candidates name'),
    (3, 'Committee_SearchByIdOrName', 'Allow user to retrieve committee information based on search of committee ID or name'),
    (500, 'Filer_AuthorizedUsers_View', 'Allow user to view the linkage dashboard'),
    (501, 'Filer_AuthorizedUsers_Manage', 'Allow user to terminate existing linkages, send linkage requests, and approve/reject incoming requests for the filer'),

    (1001, 'Registration_Candidate_Create ', 'Allow the user to create a draft candidate intention statement'),
    (1002, 'Registration_Candidate_Edit ', 'Allow the user to edit a saved draft candidate intention statement'),
    (1003, 'Registration_Candidate_Amend', 'Allow the user to create an amendment to a candidate intention statement'),
    (1004, 'Registration_Candidate_Attest ', 'Allow the user to attest to a candidate intention statement'),
    (1005, 'Registration_Candidate_Terminate ', 'Allow the user to terminate a candidate intention statement'),
    (1006, 'Registration_Candidate_Withdraw ', 'Allow the user to withdraw a candidate intention statement'),

    (2001, 'Registration_SlateMailerOrganization_Create', 'Allow the user to create a draft slate mailer organization registration'),
    (2002, 'Registration_SlateMailerOrganization_Search', 'Allow the user to search slate mailer organization registration'),
    (2003, 'Registration_SlateMailerOrganization_View', 'Allow the user to view a slate mailer organization registration'),
    (2004, 'Registration_SlateMailerOrganization_Edit', 'Allow the user to edit a saved draft slate mailer organization registration'),
    (2005, 'Registration_SlateMailerOrganization_Amend', 'Allow the user to create an amendment to a slate mailer organization registration'),
    (2006, 'Registration_SlateMailerOrganization_Terminate', 'Allow the user to terminate a slate mailer organization registration'),
    (2007, 'Registration_SlateMailerOrganization_Reinstate', 'Allow the user to reinstate a terminated slate mailer organization registration'),
    (2008, 'Registration_SlateMailerOrganization_Notify', 'Allow the user to notify other users linked to a slate mailer organization registration'),
    (2009, 'Registration_SlateMailerOrganization_CompleteTreasurerAcknowledgment', 'Allow the user to complete the treasurer acknowledgment of a slate mailer organization registration'),
    (2010, 'Registration_SlateMailerOrganization_Attest', 'Allow the user to attest a slate mailer organization registration'),

    (2501, 'Disclosure_401_Notify', 'Allow the user to notify other users linked to a slate mailer organization campaign statement'),
    (2502, 'Disclosure_401_Attest', 'Allow the user to attest a slate mailer organization campaign statement'),
    (2503, 'Disclosure_Lobbying48H_Notify', 'Allow the user to notify other users linked to a 48H Report'),
    (2504, 'Disclosure_Lobbying48H_Attest', 'Allow the user to attest a 48H Report'),
    (2505, 'Disclosure_Lobbying72H_Notify', 'Allow the user to notify other users linked to a 72H Report'),
    (2506, 'Disclosure_Lobbying72H_Attest', 'Allow the user to attest a 72H Report'),
    (2507, 'Disclosure_LobbyistEmployer_Notify', 'Allow the user to notify other users linked to a Lobbyist Employer Report'),
    (2508, 'Disclosure_LobbyistEmployer_Attest', 'Allow the user to attest a Lobbyist Employer Report'),
    
    (3001, 'Registration_Lobbyist_Create ', 'Allow the user to create a draft lobbyist registration'),
    (3002, 'Registration_Lobbyist_Edit ', 'Allow the user to edit a saved draft lobbyist registration'),
    (3003, 'Registration_Lobbyist_Amend', 'Allow the user to create an amendment to a lobbyist registration'),
    (3004, 'Registration_Lobbyist_Attest ', 'Allow the user to attest to a lobbyist registration'),
    (3005, 'Registration_Lobbyist_Terminate ', 'Allow the user to terminate a lobbyist registration'),

    (3501, 'Disclosure_Lobbyist_Notify', 'Allow the user to notify other users linked to a Lobbyist Report'),
    (3502, 'Disclosure_Lobbyist_Attest', 'Allow the user to attest a Lobbyist Report'),

    (9001, 'Admin_NotificationTemplate_Create ', 'Allow the user to create a notification template'),
    (9002, 'Admin_NotificationTemplate_Delete ', 'Allow the user to delete a notification template'),
    (9003, 'Admin_NotificationTemplate_Edit ', 'Allow the user to edit an existing notification template')
) AS source (Id, Name, Description)
ON Permission.Id = source.Id
WHEN MATCHED THEN 
    UPDATE SET
        Name = source.Name,
        Description = source.Description
WHEN NOT MATCHED THEN 
    INSERT (Id, Name, Description)
    VALUES (source.Id, source.Name, source.Description);
GO

SET IDENTITY_INSERT [dbo].[Permission] OFF;
GO

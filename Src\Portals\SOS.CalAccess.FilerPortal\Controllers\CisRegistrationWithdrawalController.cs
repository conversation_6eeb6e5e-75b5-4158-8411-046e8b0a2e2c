using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.ControllerServices.CisRegistrationWithdrawalCtlSvc;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations.CisRegistrationWithdrawal;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Controllers;

/// <summary>
/// Withdraw original and amend Candidate Intention Statement
/// </summary>
public class CisRegistrationWithdrawalController(
     ICisRegistrationWithdrawalCtlSvc cisRegistrationWithdrawalCtlSvc,
     IStringLocalizer<SharedResources> localizer,
     IAuthorizationSvc authorizationSvc,
     IToastService toastService
    ) : Controller
{
    /// <summary>
    /// Common logic to call before loading a view.
    /// </summary>
    /// <param name="context"></param>
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        SetCommonViewData();
    }

    /// <summary>
    /// Redirect to Dashboard
    /// </summary>
    /// <returns>Dashboard View</returns>
    public ActionResult RedirectToDashboard()
    {
        return RedirectToAction("Index", "Dashboard");
    }

    /// <summary>
    /// Redirect to Page01
    /// </summary>
    /// <param name="id">registration id</param>
    /// <returns>Dashboard View</returns>
    [HttpGet]
    public async Task<ActionResult> Index([Required] long id)
    {
        //await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: id));

        var result = await cisRegistrationWithdrawalCtlSvc.InitializeCisWithdrawal(id);
        if (result.IsOk())
        {
            return RedirectToAction(nameof(Page01), new { Id = result.Unwrap() });
        }
        else
        {
            toastService.Error(result!.Error!.Message);
            return RedirectToDashboard();
        }
    }

    /// <summary>
    /// Cancel withdrawal
    /// </summary>
    /// <param name="id">registration id</param>
    /// <returns>Dashboard View</returns>
    [HttpPost]
    public async Task<ActionResult> Cancel(
    [Required] long id,
    CancellationToken cancellationToken = default)
    {
        //await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: id));

        if (!ModelState.IsValid)
        {
            return RedirectToDashboard();
        }

        var result = await cisRegistrationWithdrawalCtlSvc.CancelCisWithdrawal(id);
        if (result.IsOk())
        {
            toastService.Success(localizer[CommonResourceConstants.ToastCanceled]);
            return RedirectToDashboard();
        }
        else
        {
            toastService.Error(result!.Error!.Message);
            return RedirectToDashboard();
        }
    }

    /// <summary>
    /// Handler for when user starts to edit a form.
    /// Redirects user to predetermined starting page.
    /// </summary>
    /// <param name="id">registration id</param>
    /// <returns></returns>
    public async Task<IActionResult> Edit([Required] long id)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: id));

        if (!ModelState.IsValid)
        {
            return RedirectToDashboard();
        }
        return RedirectToAction(nameof(Page01), new { Id = id });
    }

    /// <summary>
    /// Handler for when user wants to attest a withdrawal.
    /// Redirects user to predetermined starting page.
    /// </summary>
    /// <param name="id">registration id</param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> CompleteAttestation([Required] long id)
    {
        var isAuthorized = await authorizationSvc.IsAuthorized(
            new AuthorizationRequest(Permission.Registration_Candidate_Attest, User, registrationId: id));

        if (!ModelState.IsValid)
        {
            return RedirectToDashboard();
        }

        if (isAuthorized)
        {
            return RedirectToAction(nameof(Page02), new { Id = id });
        }

        toastService.Error(localizer[CommonResourceConstants.UnauthorizedActionToast]);
        return RedirectToDashboard();
    }

    #region Page01
    /// <summary>
    /// Loads view for Page01
    /// </summary>
    /// <returns>Page01 View</returns>
    [HttpGet]
    public async Task<IActionResult> Page01(
        [Required] long id,
        CancellationToken cancellation = default)
    {
        //await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: id));

        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var result = await cisRegistrationWithdrawalCtlSvc.Page01GetViewModel(id);
        if (result.IsOk())
        {
            return View(result.Unwrap());
        }

        return View();

    }

    [HttpPost]
    public async Task<IActionResult> Page01(CisRegistrationWithdrawalPage01ViewModel model)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: model.Id));

        if (!ModelState.IsValid)
        {
            return View(model);
        }

        switch (model.Action)
        {
            case FormAction.Continue:
                return Page01Continue(model);
            case FormAction.SaveAndClose:
                return Page01SaveAndClose(model);
            default:
                ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                return View(model);
        }
    }

    private IActionResult Page01Continue(CisRegistrationWithdrawalPage01ViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        return RedirectToAction(nameof(Page02), new { model.Id });
    }

    private IActionResult Page01SaveAndClose(CisRegistrationWithdrawalPage01ViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        return RedirectToDashboard();
    }
    #endregion

    #region Page02
    /// <summary>
    /// Loads view for Page02
    /// </summary>
    /// <returns>Page02 View</returns>
    [HttpGet]
    public async Task<IActionResult> Page02(
        [Required] long id,
        CancellationToken cancellation = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: id));

        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var result = await cisRegistrationWithdrawalCtlSvc.Page02GetViewModel(id);
        if (result.IsOk())
        {
            return View(result.Unwrap());
        }

        return BadRequest();
    }

    [HttpPost]
    public async Task<IActionResult> Page02(CisRegistrationWithdrawalPage02ViewModel model)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: model.Id));

        if (!ModelState.IsValid)
        {
            return View(model);
        }

        switch (model.Action)
        {
            case FormAction.Previous:
                return RedirectToAction(nameof(Page01), new { model.Id });
            case FormAction.Continue:
                return await Page02Continue(model);
            case FormAction.SaveAndClose:
                return Page02SaveAndClose(model);
            default:
                ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                return View(model);
        }
    }

    private async Task<IActionResult> Page02Continue(CisRegistrationWithdrawalPage02ViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        var result = await cisRegistrationWithdrawalCtlSvc.Page02Submit(model.Id!.Value);
        if (result.IsOk())
        {
            return RedirectToAction(nameof(Page03), new { model.Id });
        }

        return View(model);
    }

    private IActionResult Page02SaveAndClose(CisRegistrationWithdrawalPage02ViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        return RedirectToDashboard();
    }
    #endregion

    #region Page03
    /// <summary>
    /// Loads view for Page03
    /// </summary>
    /// <returns>Page03 View</returns>
    [HttpGet]
    public async Task<IActionResult> Page03(
        [Required] long id,
        CancellationToken cancellation = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: id));

        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var result = await cisRegistrationWithdrawalCtlSvc.Page03GetViewModel(id);

        if (result.IsOk())
        {
            return View(result.Unwrap());
        }

        return View();

    }

    [HttpPost]
    public async Task<IActionResult> Page03(CisRegistrationWithdrawalPage03ViewModel model)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: model.Id));

        if (!ModelState.IsValid)
        {
            return View(model);
        }

        switch (model.Action)
        {
            case FormAction.Close:
                return RedirectToDashboard();
            default:
                ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                return View(model);
        }
    }
    #endregion

    #region Private
    /// <summary>
    /// Sets UI data that is common to all pages in this form.
    /// </summary>
    private void SetCommonViewData()
    {
        ViewData[LayoutConstants.Title] = localizer[ResourceConstants.WithdrawalCisTitle].Value;
        ViewData[LayoutConstants.Breadcrumbs] = new List<Breadcrumb>()
        {
            new(localizer[ResourceConstants.FilerPortalTitle].Value, "/FilerPortal"),
            new(localizer[ResourceConstants.WithdrawalCisBreadcrumb], "/Candidate"),
        };
        ViewData["ProgressItem1Name"] = "1. " + localizer[ResourceConstants.WithdrawalCisStep01].Value;
        ViewData["ProgressItem2Name"] = "2. " + localizer[ResourceConstants.WithdrawalCisStep02].Value;
    }
    #endregion

}

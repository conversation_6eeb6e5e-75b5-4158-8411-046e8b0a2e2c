using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;


namespace SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;
public class UserAccountDetailsDto
{
    /// <inheritdoc />
    public long Id { get; set; }
    /// <summary>
    /// Gets or sets OrganizationName
    /// </summary>
    public required string UserName { get; set; }
    /// <summary>
    /// Gets or sets Email.
    /// </summary>
    public required string Email { get; set; }

    /// <summary>
    /// Gets or sets First name
    /// </summary>
    public required string FirstName { get; set; }

    /// <summary>
    /// Gets or sets Last name
    /// </summary>
    public required string LastName { get; set; }

    /// <summary>
    /// Gets or sets Middle name
    /// </summary>
    public string? MiddleName { get; set; }

    /// <summary>
    /// Address dto object
    /// </summary>
    public AddressDto? AddressDto { get; set; }

    /// <summary>
    /// Phone number dto object
    /// </summary>
    public List<PhoneNumberDto>? PhoneNumberDto { get; set; }

}

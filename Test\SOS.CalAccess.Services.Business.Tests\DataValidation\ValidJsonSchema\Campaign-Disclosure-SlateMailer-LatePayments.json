{"type": "object", "title": "Form 498 Schema", "description": "SlateMailer LatePayments", "additionalProperties": false, "required": ["submittedDate", "amendment", "latePaymentReceived"], "properties": {"submittedDate": {"type": "string", "format": "date"}, "amendment": {"type": "object", "additionalProperties": false, "required": ["isAmendment"], "properties": {"isAmendment": {"type": "boolean"}, "supercededFilingId": {"type": "string"}, "descriptionOfAmendment": {"type": "string"}}}, "latePaymentReceived": {"type": "array", "items": {"type": "object", "additionalProperties": false, "required": ["transactionDate", "address", "telephoneNumber", "amount", "candidateOrMeasureDetails"], "anyOf": [{"required": ["firstName", "lastName"]}, {"required": ["orgName"]}], "properties": {"firstName": {"type": "string"}, "middleName": {"type": "string"}, "lastName": {"type": "string"}, "orgName": {"type": "string"}, "idNumber": {"type": "string"}, "transactionDate": {"type": "string", "format": "date"}, "address": {"type": "object", "additionalProperties": false, "required": ["street", "city", "state", "zipCode", "country", "county", "type"], "properties": {"street": {"type": "string"}, "street2": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "zipCode": {"type": "string"}, "county": {"type": "string"}, "country": {"type": "string"}, "type": {"type": "string"}}}, "mailingAddress": {"type": "object", "additionalProperties": false, "required": ["street", "city", "state", "zipCode", "country", "county", "type"], "properties": {"street": {"type": "string"}, "street2": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "zipCode": {"type": "string"}, "county": {"type": "string"}, "country": {"type": "string"}, "type": {"type": "string"}}}, "telephoneNumber": {"type": "object", "additionalProperties": false, "required": ["countryCode", "number"], "properties": {"countryCode": {"type": "string", "description": "Country code of the telephone number."}, "number": {"type": "string", "description": "Telephone number."}, "extension": {"type": "string", "description": "Extension of the telephone number."}}}, "faxNumber": {"type": "object", "additionalProperties": false, "required": ["countryCode", "number"], "properties": {"countryCode": {"type": "string", "description": "Country code of the telephone number."}, "number": {"type": "string", "description": "Telephone number."}, "extension": {"type": "string", "description": "Extension of the telephone number."}}}, "email": {"type": "string", "format": "email"}, "amount": {"type": "number"}, "occupation": {"type": "string"}, "employer": {"type": "string"}, "candidateOrMeasureDetails": {"type": "array", "items": {"type": "object", "additionalProperties": false, "required": ["officeSought", "jurisdiction", "position", "amountAttributed"], "anyOf": [{"required": ["firstName", "lastName"]}, {"required": ["name"]}], "properties": {"firstName": {"type": "string"}, "middleName": {"type": "string"}, "lastName": {"type": "string"}, "name": {"type": "string"}, "officeSought": {"type": "string"}, "jurisdiction": {"type": "string"}, "position": {"type": "string"}, "amountAttributed": {"type": "number"}}}}}}}}}
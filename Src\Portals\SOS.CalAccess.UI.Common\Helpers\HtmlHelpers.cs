using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Linq.Expressions;
using System.Text.Encodings.Web;
using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Localization;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.Routing;
using Microsoft.Extensions.DependencyInjection;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Extensions;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using Syncfusion.EJ2;

namespace SOS.CalAccess.UI.Common.Helpers;

[ExcludeFromCodeCoverage]
public static class HtmlHelpers
{
    private const string CheckedAttribute = "checked";
    private const string AriaRequiredAttribute = "aria-required";
    private const string ValueAttribute = "value";
    private const int TextAreaSize = 5;
    private const string FormLabelClass = "form-label";
    private const string TextDangerClass = "text-danger";
    private const string FormControlClass = "form-control";
    private const string FormCheckInput = "form-check-input";
    private const string FormCheckLabel = "form-check-label";
    private const string FormCheckCssClass = "form-check";
    private const string FormGroupCssClass = "form-group";
    private const string PlainTextFormControlCssClass = "form-control-plaintext border rounded px-2 py-1 disabled bg-light text-muted";
    private const string DataValAttribute = "data-val";
    private const string DataValRequiredAttribute = "data-val-required";
    private const string InputTag = "input";
    private const string OptionTag = "option";
    private const string SelectedAttribute = "selected";
    private const string ClassAttribute = "class";
    private const string StyleAttribute = "style";
    private const string ReadOnlyLabelCssClass = "readonly-label";
    private const string FileUploaderId = "UploadFiles";
    /// <summary>
    /// HTML Helper for TextBox control
    /// </summary>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="name"></param>
    /// <param name="label"></param>
    /// <param name="required"></param>
    /// <param name="value"></param>
    /// <param name="placeholder"></param>
    /// <param name="readOnly"></param>
    /// <returns></returns>
#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent TextField(this IHtmlHelper htmlHelper, IHtmlLocalizer localizer, string name, string label, bool required = false, string value = "", string placeholder = "", bool readOnly = false)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        var divTag = new TagBuilder("div");
        var labelTag = GenerateLabelTag(localizer, name, label, required, readOnly);
        if (labelTag != null)
        {
            _ = divTag.InnerHtml.AppendHtml(labelTag);
        }

        if (readOnly)
        {
            var spanTag = new TagBuilder("span");
            spanTag.Attributes["id"] = name;
            spanTag.Attributes["aria-labelledby"] = $"{name}_label";
            spanTag.AddCssClass(PlainTextFormControlCssClass);
            _ = spanTag.InnerHtml.SetContent(value ?? "");
            _ = divTag.InnerHtml.AppendHtml(spanTag);
        }
        else
        {
            var inputTag = new TagBuilder(InputTag) { TagRenderMode = TagRenderMode.SelfClosing };
            inputTag.Attributes["type"] = "text";
            inputTag.Attributes["id"] = name;
            inputTag.Attributes["name"] = name;
            inputTag.Attributes[ValueAttribute] = value;
            inputTag.Attributes["placeholder"] = placeholder;
            inputTag.AddCssClass(FormControlClass);


            if (required)
            {
                inputTag.Attributes[AriaRequiredAttribute] = "true";
                inputTag.Attributes[DataValAttribute] = "true";
                inputTag.Attributes[DataValRequiredAttribute] = string.Format(CultureInfo.InvariantCulture, localizer[CommonResourceConstants.FieldIsRequired].Value, localizer[label].Value);
            }

            _ = divTag.InnerHtml.AppendHtml(inputTag);
        }

        AddValidationTag(name, divTag);

        return divTag;
    }

    /// <summary>
    /// HTML Helper for a TextField that uses lambda expression to set model and value
    /// Builds a complete set of text input with label and validation error section.
    /// </summary>
    /// <typeparam name="TModel"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="expression"></param>
    /// <param name="labelKey"></param>
    /// <param name="required"></param>
    /// <param name="placeholderKey"></param>
    /// <param name="captionKey"></param>
    /// <param name="disabled"></param>
    /// <param name="readOnly"></param>
    /// <returns></returns>
#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent PhoneNumberFor<TModel>(
        this IHtmlHelper<TModel> htmlHelper,
        IReferenceDataSvc referenceDataSvc,
        IHtmlLocalizer localizer,
        Expression<Func<TModel, PhoneNumberDto>> expression,
        string? labelKey = null,
        string countryCodeLabel = CommonResourceConstants.CountryCode,
        string phoneNumberLabel = CommonResourceConstants.TelephoneNumber,
        string phoneExtensionLabel = CommonResourceConstants.Extension,
        bool? required = false,
        string placeholderKey = "",
        string? captionKey = null,
        bool disabled = false, bool readOnly = false)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        //extract dto from model
        var model = htmlHelper.ViewData.Model;
        PhoneNumberDto? phoneDto = expression.Compile().Invoke(model);

        //create DTO instance if null
        phoneDto ??= new PhoneNumberDto();

        //setup country code options
        var countryCodes = referenceDataSvc.GetAllCountryCodes().Result.ToList();

        //default Selected Country
        if (phoneDto.SelectedCountry == null)
        {
            if (string.IsNullOrEmpty(phoneDto.CountryCode))
            {
                //default to USA
                phoneDto.SelectedCountry = Country.UnitedStatesId;
            }
            else
            {
                //add option country code without country name
                countryCodes.Add(new() { Id = -1, PhoneCountryCode = phoneDto.CountryCode, CountryName = string.Empty });
                phoneDto.SelectedCountry = 1;
            }
        }

        var initialMask = countryCodes.Where(c => c.Id == phoneDto.SelectedCountry).Select(c => c.PhoneMask).FirstOrDefault() ?? Country.DefaultPhoneMask;
        //format country codes as select options
        var options = countryCodes.Select(c => new SelectListItemWithData
        {
            Text = c.CountryName + "  (" + c.PhoneCountryCode + ")",
            Value = c.Id.ToString(CultureInfo.InvariantCulture),
            Selected = c.Id == phoneDto.SelectedCountry,
            AdditionalDataAttributes = new() { { "mask", c.PhoneMask ?? Country.DefaultPhoneMask } }
        });

        //render editor template
        var editor = htmlHelper.EditorFor(expression, additionalViewData: new
        {
            LegendLabel = labelKey,
            CountryCodes = options,
            CountryCodeLabel = countryCodeLabel,
            PhoneMask = initialMask,
            PhoneNubmerLabel = phoneNumberLabel,
            PhoneExtensionLabel = phoneExtensionLabel,
            Required = required,
            Disabled = disabled,
            ReadOnly = readOnly,
        });

        return editor;
    }

    /// <summary>
    /// HTML Helper for a TextField that uses lambda expression to set model and value
    /// Builds a complete set of text input with label and validation error section.
    /// </summary>
    /// <typeparam name="TModel"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="expression"></param>
    /// <param name="labelKey"></param>
    /// <param name="required"></param>
    /// <param name="placeholderKey"></param>
    /// <param name="captionKey"></param>
    /// <param name="disabled"></param>
    /// <param name="readOnly"></param>
    /// <param name="optionalLabel"></param>
    /// <returns></returns>
#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent TextFieldFor<TModel, TValue>(
    this IHtmlHelper<TModel> htmlHelper,
    IHtmlLocalizer localizer,
    Expression<Func<TModel, TValue>> expression,
    string labelKey,
    bool? required = false,
    string placeholderKey = "",
    string? captionKey = null,
    bool disabled = false,
    bool readOnly = false,
    bool isMasked = false,
    string maskPattern = "",
    string? optionalLabel = null)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        var divTag = new TagBuilder("div");
        divTag.AddCssClass("mb-3");

        var labelTag = SosLabelFor(htmlHelper, localizer, expression, labelKey, required, readOnly, optionalLabel);
        _ = divTag.InnerHtml.AppendHtml(labelTag);

        var id = htmlHelper.IdFor(expression).ToString();

        if (captionKey is not null)
        {
            var captionTag = new TagBuilder("span");
            captionTag.AddCssClass("form-caption");
            _ = captionTag.InnerHtml.SetContent(localizer[captionKey].Value);
            _ = divTag.InnerHtml.AppendHtml(captionTag);
        }

        if (readOnly)
        {
            var model = htmlHelper.ViewData.Model;
            TValue? modelValue = !EqualityComparer<TModel>.Default.Equals(model, default!)
            ? expression.Compile()(model)
            : default;
            var displayValue = modelValue?.ToString() ?? "";

            var spanTag = new TagBuilder("span");
            spanTag.Attributes["id"] = id;
            spanTag.Attributes["aria-labelledby"] = $"{id}_label";
            spanTag.AddCssClass(PlainTextFormControlCssClass);
            _ = spanTag.InnerHtml.SetContent(displayValue);
            _ = divTag.InnerHtml.AppendHtml(spanTag);

            divTag.InnerHtml.AppendHtml(htmlHelper.HiddenFor(expression));
        }
        else
        {
            if (isMasked)
            {
                _ = divTag.InnerHtml.AppendHtml(SosMaskedTextBoxFor(htmlHelper, localizer, expression, labelKey, maskPattern, required: required, disabled: disabled));
                _ = divTag.InnerHtml.AppendHtml(htmlHelper.HiddenFor(expression));
                var onCreateScript = new TagBuilder("script");
                onCreateScript.InnerHtml.SetHtmlContent($"var {id}; function OnCreate_{id}() {{ {id} = this; this.element.name = '{id}_Masked'}}; function OnChange_{id}() {{ document.getElementById('{id}').value = this.value;}}");
                _ = divTag.InnerHtml.AppendHtml(onCreateScript);
            }
            else
            {
                _ = divTag.InnerHtml.AppendHtml(SosTextBoxFor(htmlHelper, localizer, expression, labelKey, required, placeholderKey, disabled));
            }

            var validation = htmlHelper.ValidationMessageFor(expression, "", new { @class = TextDangerClass });
            var validationDiv = new TagBuilder("div");
            _ = validationDiv.InnerHtml.AppendHtml(validation);
            _ = divTag.InnerHtml.AppendHtml(validationDiv);
        }

        return divTag;
    }

    /// <summary>
    /// HTML Helper that wraps LabelFor to apply SOS specific configurations.
    /// </summary>
    /// <typeparam name="TModel"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="expression"></param>
    /// <param name="labelKey"></param>
    /// <param name="required"></param>
    /// <param name="optionalLabel"></param>
    /// <returns></returns>
    public static IHtmlContent SosLabelFor<TModel, TValue>(
    this IHtmlHelper<TModel> htmlHelper,
    IHtmlLocalizer localizer,
    Expression<Func<TModel, TValue>> expression,
    string labelKey,
    bool? required = false,
    bool readOnly = false,
    string? optionalLabel = null)
    {
        // Set default value for optionalLabel if no input of optionalLabel
        optionalLabel ??= localizer[CommonResourceConstants.Optional].Value;

        var labelText = localizer[labelKey].Value;
        if (required == false && optionalLabel != string.Empty)
        {
            labelText += string.Format(CultureInfo.InvariantCulture, " ({0})", optionalLabel);
        }

        var id = htmlHelper.IdFor(expression).ToString();

        TagBuilder labelTag;

        if (readOnly)
        {
            labelTag = new TagBuilder("span");
            labelTag.Attributes["id"] = $"{id}_label";
            labelTag.AddCssClass(ReadOnlyLabelCssClass);
        }
        else
        {
            labelTag = new TagBuilder("label");
            labelTag.Attributes["for"] = id;
            labelTag.AddCssClass(FormLabelClass);
        }

        _ = labelTag.InnerHtml.Append(labelText);

        return labelTag;
    }

    /// <summary>
    /// HTML Helper that wraps TextBoxFor to apply SOS specific configurations.
    /// </summary>
    /// <typeparam name="TModel"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="expression"></param>
    /// <param name="labelKey"></param>
    /// <param name="required"></param>
    /// <param name="placeholderKey"></param>
    /// <returns></returns>
    public static IHtmlContent SosTextBoxFor<TModel, TValue>(
        this IHtmlHelper<TModel> htmlHelper,
        IHtmlLocalizer localizer,
        Expression<Func<TModel, TValue>> expression,
        string labelKey,
        bool? required = false,
        string placeholderKey = "",
        bool disabled = false)
    {
        var attributes = new Dictionary<string, object>
        {
            { ClassAttribute, FormControlClass },
            { "placeholder", localizer[placeholderKey].Value }
        };

        if (required == true)
        {
            attributes.Add("ariaRequired", "true");
            attributes.Add("dataVal", "true");
            attributes.Add("required", "true");
            attributes.Add("dataValRequired", string.Format(CultureInfo.InvariantCulture, localizer[CommonResourceConstants.FieldIsRequired].Value, localizer[labelKey].Value));
        }
        if (disabled)
        {
            attributes.Add("disabled", "disabled");
        }

        return htmlHelper.TextBoxFor(expression, attributes);
    }

    /// <summary>
    /// HTML Helper that wraps Syncfusion MaskedTextBoxFor to apply SOS specific configurations.
    /// </summary>
    /// <typeparam name="TModel"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="expression"></param>
    /// <param name="mask"></param>
    /// <param name="labelKey"></param>
    /// <param name="required"></param>
    /// <param name="placeholderKey"></param>
    /// <param name="disabled"></param>
    /// <returns></returns>
#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent SosMaskedTextBoxFor<TModel, TValue>(
        this IHtmlHelper<TModel> htmlHelper,
        IHtmlLocalizer localizer,
        Expression<Func<TModel, TValue>> expression,
        string labelKey,
        string mask,
        string promptChar = "_",
        bool? required = false,
        bool disabled = false
        )
#pragma warning restore S107 // Methods should not have too many parameters
    {
        var id = htmlHelper.IdFor(expression).ToString();
        var attributes = new Dictionary<string, object>
        {
            { "id", id+"_masked" },
            { ClassAttribute, FormControlClass },
        };

        if (required == true)
        {
            attributes.Add("ariaRequired", "true");
            attributes.Add("dataVal", "true");
            attributes.Add("required", "true");
            attributes.Add("dataValRequired", string.Format(CultureInfo.InvariantCulture, localizer[CommonResourceConstants.FieldIsRequired].Value, localizer[labelKey].Value));
        }
        if (disabled)
        {
            attributes.Add("disabled", "disabled");
        }
        var syncfusionTextBox = htmlHelper.EJS().MaskedTextBoxFor(expression)
            .Mask(mask)
            .PromptChar(promptChar)
            .Created("OnCreate_" + id)
            .Change("OnChange_" + id)
            .HtmlAttributes(attributes);
        var result = syncfusionTextBox.Render();
        return result;
    }

    /// <summary>
    /// HTML Helper that wraps TextAreaFor to apply SOS specific configurations.
    /// </summary>
    /// <typeparam name="TModel"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="expression"></param>
    /// <param name="labelKey"></param>
    /// <param name="required"></param>
    /// <param name="placeholderKey"></param>
    /// <param name="disabled"></param>
    /// <returns></returns>
    public static IHtmlContent SosTextAreaFor<TModel, TValue>(
        this IHtmlHelper<TModel> htmlHelper,
        IHtmlLocalizer localizer,
        Expression<Func<TModel, TValue>> expression,
        string labelKey,
        bool required = false,
        string placeholderKey = "",
        bool disabled = false)
    {
        var attributes = new Dictionary<string, object>();
        attributes.Add(ClassAttribute, FormControlClass);
        attributes.Add("placeholder", localizer[placeholderKey].Value);

        if (required)
        {
            attributes.Add("ariaRequired", "true");
            attributes.Add("dataVal", "true");
            attributes.Add("dataValRequired", string.Format(CultureInfo.CurrentCulture, localizer[CommonResourceConstants.FieldIsRequired].Value, localizer[labelKey].Value));
        }
        if (disabled)
        {
            attributes.Add("disabled", "disabled");

        }

        return htmlHelper.TextAreaFor(expression, attributes);
    }

    /// <summary>
    /// HTML Helper that wraps ValidationMessageFor to apply SOS specific configurations.
    /// </summary>
    /// <typeparam name="TModel"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="expression"></param>
    /// <returns></returns>
    public static IHtmlContent SosValidationMessageFor<TModel, TValue>(
        this IHtmlHelper<TModel> htmlHelper,
        IHtmlLocalizer localizer,
        Expression<Func<TModel, TValue>> expression)
    {
        return htmlHelper.ValidationMessageFor(expression, null, new { @class = TextDangerClass });
    }

    /// <summary>
    /// HTML Helper for Input control
    /// </summary>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="name"></param>
    /// <param name="required"></param>
    /// <param name="value"></param>
    /// <param name="placeholder"></param>
    /// <returns></returns>
#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent InputField(
        this IHtmlHelper htmlHelper,
        IHtmlLocalizer localizer,
        string labelKey,
        string name,
        bool required = false,
        string value = "",
        string placeholder = "", bool readOnly = false)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        if (readOnly)
        {
            var spanTag = new TagBuilder("span");
            spanTag.AddCssClass(PlainTextFormControlCssClass);
            _ = spanTag.InnerHtml.SetContent(value ?? "");
            return spanTag;
        }

        var inputTag = new TagBuilder(InputTag) { TagRenderMode = TagRenderMode.SelfClosing };
        inputTag.Attributes["type"] = "text";
        inputTag.Attributes["id"] = name;
        inputTag.Attributes["name"] = name;
        inputTag.Attributes[ValueAttribute] = value;
        inputTag.Attributes["placeholder"] = placeholder;
        inputTag.AddCssClass(FormControlClass);

        if (required)
        {
            inputTag.Attributes[AriaRequiredAttribute] = "true";
            inputTag.Attributes[DataValAttribute] = "true";
            inputTag.Attributes[DataValRequiredAttribute] = string.Format(CultureInfo.InvariantCulture, localizer[CommonResourceConstants.FieldIsRequired].Value, localizer[labelKey].Value);
        }

        return inputTag;
    }


    private static TagBuilder? GenerateLabelTag(IHtmlLocalizer localizer, string name, string labelKey, bool? required, bool readOnly = false)
    {
        if (string.IsNullOrEmpty(labelKey))
        {
            return null;
        }

        var labelText = localizer[labelKey].Value;
        if (required == false)
        {
            labelText += string.Format(CultureInfo.InvariantCulture, " ({0})", localizer[CommonResourceConstants.Optional].Value);
        }
        TagBuilder labelTag;

        if (readOnly)
        {
            // Use span instead of label for read-only mode
            labelTag = new TagBuilder("span");
            labelTag.Attributes["id"] = $"{name}_label";
            labelTag.AddCssClass(ReadOnlyLabelCssClass);
        }
        else
        {
            labelTag = new TagBuilder("label");
            labelTag.Attributes["for"] = name;
            labelTag.AddCssClass(FormLabelClass);
        }

        _ = labelTag.InnerHtml.Append(labelText);

        if (required == true && !readOnly)
        {
            var spanTag = new TagBuilder("span");
            spanTag.AddCssClass(TextDangerClass);
            _ = labelTag.InnerHtml.AppendHtml(spanTag);
        }


        return labelTag;
    }

    /// <summary>
    /// HTML Helper for Label with localizer
    /// </summary>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="name"></param>
    /// <param name="label"></param>
    /// <param name="required"></param>
    /// <returns></returns>
    public static TagBuilder InputLabel(this IHtmlHelper htmlHelper, IHtmlLocalizer localizer, string name, string label, bool required)
    {
        var labelTag = GenerateLabelTag(localizer, name, label, required);
        if (labelTag == null)
        {
            return new TagBuilder("");
        }
        return labelTag;
    }

    /// <summary>
    /// HTML Helper for Dropdown control
    /// </summary>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="name"></param>
    /// <param name="label"></param>
    /// <param name="options"></param>
    /// <param name="selectedValue"></param>
    /// <param name="placeHolder"></param>
    /// <param name="required"></param>
    /// <param name="disabled"></param>
    /// <param name="onChange"></param>
    /// <param name="readOnly"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent Dropdown(
        this IHtmlHelper htmlHelper,
        IHtmlLocalizer localizer,
        string name,
        string label,
        Dictionary<string, string> options,
        string selectedValue = "",
        string placeHolder = "",
        bool? required = false,
        bool disabled = false,
        string onChange = "",
        bool readOnly = false)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        if (options == null || options.Count == 0)
        {
            throw new ArgumentException("The 'options' dictionary cannot be null or empty.", nameof(options));
        }

        var divTag = new TagBuilder("div");

        var labelTag = GenerateLabelTag(localizer, name, label, required, readOnly);
        if (labelTag != null)
        {
            _ = divTag.InnerHtml.AppendHtml(labelTag);
        }

        if (readOnly)
        {
            _ = divTag.InnerHtml.AppendHtml(BuildDropdownReadOnlySpan(name, options, selectedValue, placeHolder));
        }
        else
        {
            _ = divTag.InnerHtml.AppendHtml(BuildDropdownSelectTag(localizer, name, label, options, selectedValue, placeHolder, required, disabled, onChange));
        }

        AddValidationTag(name, divTag);
        return divTag;
    }

    private static TagBuilder BuildDropdownReadOnlySpan(
    string id,
    Dictionary<string, string> options,
    string selectedValue,
    string placeHolder)
    {
        var selectedLabel = options.TryGetValue(selectedValue, out var labelText)
            ? labelText
            : placeHolder;

        var spanTag = new TagBuilder("span");
        spanTag.Attributes["id"] = id;
        spanTag.AddCssClass(PlainTextFormControlCssClass);
        _ = spanTag.InnerHtml.SetContent(selectedLabel ?? "");

        return spanTag;
    }

#pragma warning disable S107 // Methods should not have too many parameters
    private static TagBuilder BuildDropdownSelectTag(
    IHtmlLocalizer localizer,
    string name,
    string label,
    Dictionary<string, string> options,
    string selectedValue,
    string placeHolder,
    bool? required,
    bool disabled,
    string onChange)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        var selectTag = new TagBuilder("select");
        selectTag.Attributes["id"] = name;
        selectTag.Attributes["name"] = name;
        selectTag.AddCssClass("form-select");

        if (!string.IsNullOrEmpty(onChange))
        {
            selectTag.Attributes["onchange"] = onChange;
        }

        if (required == true)
        {
            selectTag.Attributes[AriaRequiredAttribute] = "true";
            selectTag.Attributes[DataValAttribute] = "true";
            selectTag.Attributes[DataValRequiredAttribute] = string.Format(
                CultureInfo.InvariantCulture,
                localizer[CommonResourceConstants.FieldIsRequired].Value,
                localizer[label].Value);
        }

        if (disabled)
        {
            selectTag.Attributes["disabled"] = "true";
        }

        if (string.IsNullOrEmpty(selectedValue))
        {
            var placeholderOptionTag = new TagBuilder(OptionTag);
            placeholderOptionTag.Attributes[ValueAttribute] = "";
            _ = placeholderOptionTag.InnerHtml.Append(placeHolder);
            _ = selectTag.InnerHtml.AppendHtml(placeholderOptionTag);
        }

        foreach (var option in options)
        {
            var optionTag = new TagBuilder(OptionTag);
            optionTag.Attributes[ValueAttribute] = option.Key;
            if (option.Key == selectedValue)
            {
                optionTag.Attributes[SelectedAttribute] = "selected";
            }
            _ = optionTag.InnerHtml.Append(option.Value);
            _ = selectTag.InnerHtml.AppendHtml(optionTag);
        }

        return selectTag;
    }

    private static void AddValidationTag(string name, TagBuilder tag)
    {
        var validationTag = new TagBuilder("span");
        validationTag.AddCssClass(TextDangerClass);
        validationTag.Attributes["id"] = $"{name}-validation";
        validationTag.Attributes["data-valmsg-for"] = name;
        validationTag.Attributes["data-valmsg-replace"] = "true";
        validationTag.Attributes["aria-live"] = "polite";
        _ = tag.InnerHtml.AppendHtml(validationTag);
    }

    /// <summary>
    ///  HTML Helper for a Dropdown that uses lambda expression to set model and value
    /// Builds a complete set of text input with label and validation error section.
    /// </summary>
    /// <typeparam name="TModel"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="expression"></param>
    /// <param name="labelKey"></param>
    /// <param name="options"></param>
    /// <param name="placeHolder"></param>
    /// <param name="required"></param>
    /// <param name="disabled"></param>
    /// <param name="readOnly"></param>
    /// <param name="onChange"></param>
    /// <returns></returns>
#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent DropdownFor<TModel, TValue>(
        this IHtmlHelper<TModel> htmlHelper,
        IHtmlLocalizer localizer,
        Expression<Func<TModel, TValue>> expression,
        string labelKey,
        IEnumerable<SelectListItem> options,
        string placeHolder = "",
        bool required = false,
        bool disabled = false,
        bool readOnly = false,
        string onChange = "")
#pragma warning restore S107 // Methods should not have too many parameters
    {
        var divTag = new TagBuilder("div");
        divTag.AddCssClass("mb-3");

        _ = divTag.InnerHtml.AppendHtml(SosLabelFor(htmlHelper, localizer, expression, labelKey, required, readOnly));

        var model = htmlHelper.ViewData.Model;
        TValue? selectedValue = !EqualityComparer<TModel>.Default.Equals(model, default!)
            ? expression.Compile()(model)
            : default;
        string selectedKey = selectedValue?.ToString() ?? "";

        if (readOnly)
        {
            var id = htmlHelper.IdFor(expression).ToString();
            _ = divTag.InnerHtml.AppendHtml(BuildReadOnlySpan(id, options, selectedKey, placeHolder));
        }
        else
        {
            _ = divTag.InnerHtml.AppendHtml(BuildSelect(htmlHelper, localizer, expression, options, selectedKey, placeHolder, required, disabled, onChange, labelKey));
            _ = divTag.InnerHtml.AppendHtml(htmlHelper.ValidationMessageFor(expression, "", new { @class = TextDangerClass }));
        }

        return divTag;
    }

    private static TagBuilder BuildReadOnlySpan(
        string id,
        IEnumerable<SelectListItem> options,
        string selectedKey,
        string placeHolder)
    {
        var selectedItem = options.FirstOrDefault(opt => opt.Value == selectedKey);
        var displayText = selectedItem?.Text ?? placeHolder;

        var spanTag = new TagBuilder("span");
        spanTag.Attributes["id"] = id;
        spanTag.AddCssClass(PlainTextFormControlCssClass);
        _ = spanTag.InnerHtml.SetContent(displayText ?? "");

        return spanTag;
    }

#pragma warning disable S107 // Methods should not have too many parameters
    private static TagBuilder BuildSelect<TModel, TValue>(
        IHtmlHelper<TModel> htmlHelper,
        IHtmlLocalizer localizer,
        Expression<Func<TModel, TValue>> expression,
        IEnumerable<SelectListItem> options,
        string selectedKey,
        string placeHolder,
        bool required,
        bool disabled,
        string onChange, string labelKey)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        var selectTag = new TagBuilder("select");
        selectTag.Attributes["id"] = htmlHelper.IdFor(expression).ToString();
        selectTag.Attributes["name"] = htmlHelper.NameFor(expression).ToString();
        selectTag.AddCssClass("form-select");

        if (!string.IsNullOrEmpty(onChange))
        {
            selectTag.Attributes["onchange"] = onChange;
        }

        if (required)
        {
            selectTag.Attributes["aria-required"] = "true";
            selectTag.Attributes[DataValAttribute] = "true";
            selectTag.Attributes[DataValRequiredAttribute] = string.Format(CultureInfo.InvariantCulture, localizer[CommonResourceConstants.FieldIsRequired].Value, localizer[labelKey].Value);
        }

        if (disabled)
        {
            selectTag.Attributes["disabled"] = "true";
        }

        var placeholderValue = localizer[placeHolder].Value;
        if (!string.IsNullOrEmpty(placeholderValue))
        {
            var placeholderOption = new TagBuilder(OptionTag);
            placeholderOption.Attributes[ValueAttribute] = "";
            _ = placeholderOption.InnerHtml.Append(placeholderValue);
            _ = selectTag.InnerHtml.AppendHtml(placeholderOption);
        }

        foreach (var option in options)
        {
            TagBuilder optionTag = BuildSelectOption(selectedKey, option);
            _ = selectTag.InnerHtml.AppendHtml(optionTag);
        }

        return selectTag;
    }

    private static TagBuilder BuildSelectOption(string selectedKey, SelectListItem option)
    {
        var optionTag = new TagBuilder(OptionTag);
        optionTag.Attributes[ValueAttribute] = option.Value;

        if (option.Value == selectedKey || option.Selected)
        {
            optionTag.Attributes[SelectedAttribute] = "selected";
        }

        if (option.Disabled)
        {
            optionTag.Attributes["disabled"] = "disabled";
        }

        if (option is SelectListItemWithData optionData && optionData.AdditionalDataAttributes != null)
        {
            foreach (var dataAttribute in optionData.AdditionalDataAttributes)
            {
                if (!string.IsNullOrWhiteSpace(dataAttribute.Key))
                {
                    optionTag.Attributes["data-" + dataAttribute.Key] = dataAttribute.Value;
                }
            }
        }

        _ = optionTag.InnerHtml.Append(option.Text);
        return optionTag;
    }



    /// <summary>
    /// HTML Helper for Radio Group control
    /// </summary>
    /// <param name="htmlHelper"></param>
    /// <param name="name"></param>
    /// <param name="label"></param>
    /// <param name="options"></param>
    /// <param name="selectedValue"></param>
    /// <param name="required"></param>
    /// <param name="disabled"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent Radio(this IHtmlHelper htmlHelper, IHtmlLocalizer localizer, string name, string label, Dictionary<string, string> options, string selectedValue = "", bool required = false, bool disabled = false, string? captionKey = null, bool readOnly = false)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        if (options == null || options.Count == 0)
        {
            throw new ArgumentException("The 'options' dictionary cannot be null or empty.", nameof(options));
        }

        // Create a fieldset container for the group
        var fieldset = new TagBuilder("fieldset");
        fieldset.AddCssClass(FormGroupCssClass);

        // Create <legend> for the group label
        var legend = new TagBuilder("legend");
        legend.AddCssClass(FormLabelClass);
        legend.Attributes["id"] = $"{name}_label";
        _ = legend.InnerHtml.Append(localizer[label].Value);

        _ = fieldset.InnerHtml.AppendHtml(legend);

        if (captionKey is not null)
        {
            var captionTag = new TagBuilder("span");
            captionTag.AddCssClass("form-caption");
            _ = captionTag.InnerHtml.SetContent(localizer[captionKey].Value);
            _ = fieldset.InnerHtml.AppendHtml(captionTag);
        }

        // Add radio buttons
        foreach (var option in options)
        {
            var radioDivTag = new TagBuilder("div");
            radioDivTag.AddCssClass(FormCheckCssClass);

            var inputTag = new TagBuilder(InputTag) { TagRenderMode = TagRenderMode.SelfClosing };
            inputTag.Attributes["type"] = "radio";
            inputTag.Attributes["name"] = name;
            inputTag.Attributes["id"] = $"{name}_{option.Key}";
            inputTag.Attributes[ValueAttribute] = option.Key;
            inputTag.AddCssClass(FormCheckInput);

            if (required)
            {
                inputTag.Attributes[AriaRequiredAttribute] = "true";
                inputTag.Attributes[DataValAttribute] = "true";
                inputTag.Attributes[DataValRequiredAttribute] = string.Format(CultureInfo.InvariantCulture, localizer[CommonResourceConstants.FieldIsRequired].Value, localizer[label].Value);
            }

            bool isSelected = option.Key == selectedValue;
            if (isSelected)
            {
                inputTag.Attributes[CheckedAttribute] = CheckedAttribute;
            }

            if (disabled || (readOnly && !isSelected))
            {
                inputTag.Attributes["disabled"] = "true";
            }

            var optionLabelTag = new TagBuilder("label");
            optionLabelTag.Attributes["for"] = $"{name}_{option.Key}";
            optionLabelTag.AddCssClass(FormCheckLabel);
            _ = optionLabelTag.InnerHtml.Append(option.Value);

            _ = radioDivTag.InnerHtml.AppendHtml(inputTag);
            _ = radioDivTag.InnerHtml.AppendHtml(optionLabelTag);

            _ = fieldset.InnerHtml.AppendHtml(radioDivTag);
        }
        AddValidationTag(name, fieldset);

        return fieldset;
    }

#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent RadioFor<TModel, TValue>(
    this IHtmlHelper<TModel> htmlHelper,
    IHtmlLocalizer localizer,
    Expression<Func<TModel, TValue>> expression,
    string labelKey,
    IEnumerable<SelectListItem> options,
    bool required = false,
    bool disabled = false,
    bool readOnly = false,
    string? captionKey = null)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        var name = htmlHelper.NameFor(expression).ToString();
        var selectedValue = htmlHelper.ValueFor(expression).ToString();

        var fieldset = new TagBuilder("fieldset");
        fieldset.AddCssClass(FormGroupCssClass);

        // Group label
        var labelText = localizer[labelKey].Value;
        if (!required)
        {
            labelText += $" ({localizer[CommonResourceConstants.Optional].Value})";
        }

        var legend = new TagBuilder("legend");
        legend.AddCssClass(FormLabelClass);
        legend.Attributes["id"] = $"{name}_label";
        if (string.IsNullOrEmpty(labelText))
        {
            legend.Attributes.Add(StyleAttribute, "display: none");
        }
        _ = legend.InnerHtml.Append(labelText);

        _ = fieldset.InnerHtml.AppendHtml(legend);

        // Optional caption
        if (!string.IsNullOrEmpty(captionKey))
        {
            var captionTag = new TagBuilder("span");
            captionTag.AddCssClass("form-caption");
            _ = captionTag.InnerHtml.SetContent(localizer[captionKey].Value);
            _ = fieldset.InnerHtml.AppendHtml(captionTag);
        }

        foreach (var option in options)
        {
            var radioDiv = new TagBuilder("div");
            radioDiv.AddCssClass(FormCheckCssClass);

            var inputTag = new TagBuilder(InputTag) { TagRenderMode = TagRenderMode.SelfClosing };
            inputTag.AddCssClass(FormCheckInput);
            inputTag.Attributes["type"] = "radio";
            inputTag.Attributes["name"] = name;
            inputTag.Attributes["id"] = $"{name}_{option.Value}";
            inputTag.Attributes[ValueAttribute] = option.Value;

            // Apply selection
            bool isSelected = option.Selected || option.Value == selectedValue;
            if (isSelected)
            {
                inputTag.Attributes[CheckedAttribute] = CheckedAttribute;
            }

            // Disable
            if (disabled || (readOnly && !isSelected))
            {
                inputTag.Attributes["disabled"] = "disabled";
            }

            // Required
            if (required)
            {
                inputTag.Attributes[DataValAttribute] = "true";
                inputTag.Attributes[DataValRequiredAttribute] = string.Format(CultureInfo.InvariantCulture, localizer[CommonResourceConstants.FieldIsRequired].Value, labelText);
            }

            // Option label
            var optionLabel = new TagBuilder("label");
            optionLabel.AddCssClass(FormCheckLabel);
            optionLabel.Attributes["for"] = $"{name}_{option.Value}";
            _ = optionLabel.InnerHtml.SetContent(option.Text);

            _ = radioDiv.InnerHtml.AppendHtml(inputTag);
            _ = radioDiv.InnerHtml.AppendHtml(optionLabel);

            _ = fieldset.InnerHtml.AppendHtml(radioDiv);
        }

        // Validation message
        var validation = htmlHelper.ValidationMessageFor(expression, "", new { @class = TextDangerClass });
        _ = fieldset.InnerHtml.AppendHtml(validation);
        AddValidationTag(name, fieldset);

        return fieldset;
    }

    /// <summary>
    /// HTML Helper for TextArea Control
    /// </summary>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="name"></param>
    /// <param name="label"></param>
    /// <param name="required"></param>
    /// <param name="value"></param>
    /// <param name="placeholder"></param>
    /// <param name="readOnly"></param>
    /// <returns></returns>
#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent TextArea(this IHtmlHelper htmlHelper, IHtmlLocalizer localizer, string name, string label, bool required = false, string value = "", string placeholder = "", bool readOnly = false)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        var divTag = new TagBuilder("div");
        divTag.AddCssClass(FormGroupCssClass);

        var labelTag = GenerateLabelTag(localizer, name, label, required, readOnly);
        if (labelTag != null)
        {
            _ = divTag.InnerHtml.AppendHtml(labelTag);
        }

        if (readOnly)
        {
            var spanTag = new TagBuilder("span");
            spanTag.AddCssClass(PlainTextFormControlCssClass);
            _ = spanTag.InnerHtml.SetContent(value ?? "");
            _ = divTag.InnerHtml.AppendHtml(spanTag);
        }
        else
        {
            var textAreaTag = new TagBuilder("textarea");
            textAreaTag.Attributes["id"] = name;
            textAreaTag.Attributes["name"] = name;
            textAreaTag.Attributes["placeholder"] = placeholder;
            textAreaTag.Attributes["rows"] = TextAreaSize.ToString(CultureInfo.InvariantCulture);
            textAreaTag.AddCssClass(FormControlClass);

            if (!string.IsNullOrEmpty(value))
            {
                _ = textAreaTag.InnerHtml.Append(value);
            }

            if (required)
            {
                textAreaTag.Attributes[AriaRequiredAttribute] = "true";
                textAreaTag.Attributes[DataValAttribute] = "true";
                textAreaTag.Attributes[DataValRequiredAttribute] = string.Format(CultureInfo.InvariantCulture, localizer[CommonResourceConstants.FieldIsRequired].Value, localizer[label].Value);
            }

            _ = divTag.InnerHtml.AppendHtml(textAreaTag);
        }

        AddValidationTag(name, divTag);

        return divTag;
    }

    /// <summary>
    /// HTML Helper for a TextArea that uses lambda expression to set model and value
    /// Builds a complete set of text input with label and validation error section.
    /// </summary>
    /// <typeparam name="TModel"></typeparam>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="expression"></param>
    /// <param name="labelKey"></param>
    /// <param name="placeholder"></param>
    /// <param name="required"></param>
    /// <param name="readOnly"></param>
    /// <param name="optionalLabel"></param>
    /// <returns></returns>
#pragma warning disable S107 // A new optionalLabel property for a custom optional label for input field
    public static IHtmlContent TextAreaFor<TModel>(
    this IHtmlHelper<TModel> htmlHelper,
    IHtmlLocalizer localizer,
    Expression<Func<TModel, string>> expression,
    string labelKey,
    string placeholder = "",
    bool? required = false,
    bool readOnly = false,
    string? optionalLabel = null)
#pragma warning restore S107 // A new optionalLabel property for a custom optional label for input field
    {
        var divTag = new TagBuilder("div");
        divTag.AddCssClass("mb-3"); // Adds spacing below field

        var labelTag = SosLabelFor(htmlHelper, localizer, expression, labelKey, required, readOnly, optionalLabel);
        _ = divTag.InnerHtml.AppendHtml(labelTag);

        // Get current value from model
        var model = htmlHelper.ViewData.Model;
        string? fieldValue = !EqualityComparer<TModel>.Default.Equals(model, default!) ? expression.Compile()(model) : null;

        if (readOnly)
        {
            var spanTag = new TagBuilder("span");
            spanTag.AddCssClass(PlainTextFormControlCssClass);
            _ = spanTag.InnerHtml.SetContent(fieldValue ?? "");
            _ = divTag.InnerHtml.AppendHtml(spanTag);
        }
        else
        {
            var attributes = new Dictionary<string, object>
        {
            { "placeholder", placeholder },
            { "rows", TextAreaSize.ToString(CultureInfo.InvariantCulture) },
            { ClassAttribute, FormControlClass }
        };

            if (required == true)
            {
                attributes["aria-required"] = "true";
                attributes[DataValAttribute] = "true";
                attributes[DataValRequiredAttribute] = string.Format(
                    CultureInfo.InvariantCulture,
                    localizer[CommonResourceConstants.FieldIsRequired].Value,
                    localizer[labelKey].Value
                );
            }

            var textArea = htmlHelper.TextAreaFor(expression, attributes);
            _ = divTag.InnerHtml.AppendHtml(textArea);

            var validation = htmlHelper.ValidationMessageFor(expression, "", new { @class = TextDangerClass });
            _ = divTag.InnerHtml.AppendHtml(validation);
        }

        return divTag;
    }


    /// <summary>
    /// HTML Helper for Button
    /// </summary>
    /// <param name="htmlHelper"></param>
    /// <param name="text"></param>
    /// <param name="id"></param>
    /// <param name="type"></param>
    /// <param name="cssClass"></param>
    /// <param name="onClick"></param>
    /// <param name="ariaLabel"></param>
    /// <returns></returns>
#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent Button(this IHtmlHelper htmlHelper, IHtmlLocalizer localizer, string text, string? id = null, string type = "submit", string? onClick = null, string? ariaLabel = null, string cssClass = "btn btn-primary")
#pragma warning disable S107 // Methods should not have too many parameters
    {
        var buttonTag = new TagBuilder("button");
        buttonTag.Attributes["type"] = type;
        var buttonText = localizer[text].Value;
        if (!string.IsNullOrEmpty(id))
        {
            buttonTag.Attributes["id"] = id;
        }
        buttonTag.AddCssClass(cssClass);
        if (!string.IsNullOrEmpty(onClick))
        {
            buttonTag.Attributes["onclick"] = onClick;
        }

        if (!string.IsNullOrEmpty(ariaLabel))
        {
            buttonTag.Attributes["aria-label"] = ariaLabel;
        }

        _ = buttonTag.InnerHtml.Append(buttonText);

        return buttonTag;
    }

    /// <summary>
    /// HTML Helper for generating a link styled as a button with route parameters.
    /// </summary>
    /// <param name="htmlHelper">The HTML helper instance.</param>
    /// <param name="localizer">Localizer for text translation.</param>
    /// <param name="controller">The target controller for the link.</param>
    /// <param name="action">The target action for the link.</param>
    /// <param name="routeValues">The route values to include in the link.</param>
    /// <param name="textKey">The localization key for the button text.</param>
    /// <param name="cssClass">The CSS class for styling the button.</param>
    /// <returns>Returns the HTML content for the link styled as a button.</returns>
    public static IHtmlContent LinkButton(
        this IHtmlHelper htmlHelper,
        IHtmlLocalizer localizer,
        string textKey,
        string? controller,
        string? action,
        object? routeValues,
        string cssClass = "btn btn-outline-primary btn-sm ms-auto")
    {
        // Use the Url property from the ViewContext
        var urlHelper = htmlHelper.ViewContext.HttpContext.RequestServices.GetService<IUrlHelperFactory>()?.GetUrlHelper(htmlHelper.ViewContext);

        // Generate the URL
        var generatedUrl = urlHelper?.Action(action, controller, routeValues);

        // Fallback to "#" if URL generation fails
        generatedUrl ??= "#";

        var anchorTag = new TagBuilder("a") { Attributes = { ["href"] = generatedUrl } };
        anchorTag.AddCssClass(cssClass);
        anchorTag.InnerHtml.Append(localizer[textKey].Value);
        anchorTag.Attributes["role"] = "button";

        return anchorTag;
    }

    /// <summary>
    /// HTML Helper for a single Checkbox control
    /// </summary>
    /// <param name="htmlHelper">The HTML helper instance.</param>
    /// <param name="name">The name and ID of the checkbox.</param>
    /// <param name="label">The label text.</param>
    /// <param name="isChecked">Whether the checkbox should be checked by default.</param>
    /// <returns>Returns the HTML content for a checkbox.</returns>
    public static IHtmlContent Checkbox(this IHtmlHelper htmlHelper, IHtmlLocalizer localizer, string name, string label, bool isChecked = false, bool disabled = false, bool readOnly = false)
    {
        var container = new TagBuilder("div");
        container.AddCssClass("mb-3");

        var checkDiv = new TagBuilder("div");
        checkDiv.AddCssClass(FormCheckCssClass);

        // Generate the checkbox input
        var inputTag = new TagBuilder(InputTag) { TagRenderMode = TagRenderMode.SelfClosing };
        inputTag.Attributes["type"] = "checkbox";
        inputTag.Attributes["id"] = name;
        inputTag.Attributes["name"] = name;
        inputTag.Attributes[ValueAttribute] = "true";
        inputTag.AddCssClass(FormCheckInput);

        if (isChecked)
        {
            inputTag.Attributes["checked"] = "checked";
        }

        if (disabled)
        {
            inputTag.Attributes["disabled"] = "disabled";
        }

        if (readOnly)
        {
            inputTag.Attributes["onclick"] = "return false";
        }

        // Generate the label
        var labelTag = new TagBuilder("label");
        labelTag.AddCssClass(FormCheckLabel);
        labelTag.Attributes["for"] = name;
        _ = labelTag.InnerHtml.Append(localizer[label].Value);

        _ = checkDiv.InnerHtml.AppendHtml(inputTag);
        _ = checkDiv.InnerHtml.AppendHtml(labelTag);
        _ = container.InnerHtml.AppendHtml(checkDiv);

        return container;
    }

    /// <summary>
    /// HTML Helper of Checkbox Control group
    /// </summary>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="name"></param>
    /// <param name="label"></param>
    /// <param name="options"></param>
    /// <param name="selectedValues"></param>
    /// <param name="required"></param>
    /// <param name="readOnly"></param>
    /// <returns></returns>
#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent Checkbox(this IHtmlHelper htmlHelper, IHtmlLocalizer localizer, string name, string label, Dictionary<string, string> options, List<string>? selectedValues = null, bool required = false, bool readOnly = false)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        selectedValues ??= new List<string>();

        // Create a container div for the checkbox group
        var fieldset = new TagBuilder("fieldset");
        fieldset.AddCssClass(FormGroupCssClass);

        // Add the group label
        var labelText = localizer[label].Value;
        if (!required)
        {
            labelText += $" ({localizer[CommonResourceConstants.Optional].Value})";
        }

        var legend = new TagBuilder("legend");
        legend.AddCssClass(FormLabelClass);
        legend.Attributes["id"] = $"{name}_label";
        _ = legend.InnerHtml.Append(labelText);

        if (required)
        {
            var asterisk = new TagBuilder("span");
            asterisk.AddCssClass(TextDangerClass);
            _ = legend.InnerHtml.AppendHtml(asterisk);
        }

        _ = fieldset.InnerHtml.AppendHtml(legend);

        foreach (var option in options)
        {
            var checkboxDivTag = new TagBuilder("div");
            checkboxDivTag.AddCssClass(FormCheckCssClass);

            var inputTag = new TagBuilder(InputTag) { TagRenderMode = TagRenderMode.SelfClosing };
            inputTag.Attributes["type"] = "checkbox";
            inputTag.Attributes["name"] = name;
            inputTag.Attributes["id"] = $"{name}_{option.Key}";
            inputTag.Attributes[ValueAttribute] = option.Key;
            inputTag.AddCssClass(FormCheckInput);

            if (required)
            {
                inputTag.Attributes[DataValAttribute] = "true";
                inputTag.Attributes[DataValRequiredAttribute] = "Please select at least one option.";
            }

            if (readOnly)
            {
                inputTag.Attributes["onclick"] = "return false";
            }

            if (selectedValues.Contains(option.Key))
            {
                inputTag.Attributes[CheckedAttribute] = CheckedAttribute;
            }

            var optionLabelTag = new TagBuilder("label");
            optionLabelTag.Attributes["for"] = $"{name}_{option.Key}";
            optionLabelTag.AddCssClass(FormCheckLabel);
            _ = optionLabelTag.InnerHtml.Append(option.Value);

            _ = checkboxDivTag.InnerHtml.AppendHtml(inputTag);
            _ = checkboxDivTag.InnerHtml.AppendHtml(optionLabelTag);

            _ = fieldset.InnerHtml.AppendHtml(checkboxDivTag);
        }

        AddValidationTag(name, fieldset);

        return fieldset;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="TModel"></typeparam>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="expression"></param>
    /// <param name="labelKey"></param>
    /// <param name="disabled"></param>
    /// <param name="readOnly"></param>
    /// <returns></returns>
    public static IHtmlContent CheckBoxFor<TModel>(
    this IHtmlHelper<TModel> htmlHelper,
    IHtmlLocalizer localizer,
    Expression<Func<TModel, bool>> expression,
    string labelKey,
    bool disabled = false,
    bool readOnly = false)
    {
        var container = new TagBuilder("div");
        container.AddCssClass("mb-3");

        var checkDiv = new TagBuilder("div");
        checkDiv.AddCssClass(FormCheckCssClass);

        var htmlAttributes = new Dictionary<string, object>
        {
            { ClassAttribute, FormCheckInput }
        };

        if (disabled)
        {
            htmlAttributes["disabled"] = "disabled";
        }

        if (readOnly)
        {
            htmlAttributes["onclick"] = "return false";
        }

        // Generate checkbox input
        var checkbox = htmlHelper.CheckBoxFor(expression, htmlAttributes);

        // Localize label text
        var labelText = localizer[labelKey].Value;

        // Generate label
        var label = htmlHelper.LabelFor(expression, labelText, new { @class = FormCheckLabel });

        _ = checkDiv.InnerHtml.AppendHtml(checkbox);
        _ = checkDiv.InnerHtml.AppendHtml(label);
        _ = container.InnerHtml.AppendHtml(checkDiv);

        // Append validation message below checkbox
        var validation = htmlHelper.ValidationMessageFor(expression, "", new { @class = TextDangerClass });
        _ = container.InnerHtml.AppendHtml(validation);

        return container;
    }

    /// <summary>
    /// HTML Helper for DatePicker
    /// </summary>
    /// <typeparam name="TModel"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="html"></param>
    /// <param name="localizer"></param>
    /// <param name="expression"></param>
    /// <param name="labelResourceKey"></param>
    /// <param name="placeholderResourceKey"></param>
    /// <param name="minDate"></param>
    /// <param name="maxDate"></param>
    /// <param name="format"></param>
    /// <param name="isRequired"></param>
    /// <param name="isReadOnly"></param>
    /// <param name="cssClass"></param>
    /// <returns></returns>
#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent DatePickerFor<TModel, TValue>(
        this IHtmlHelper<TModel> html,
        IHtmlLocalizer localizer,
        Expression<Func<TModel, TValue>> expression,
        string labelResourceKey,
        string placeholderResourceKey = CommonResourceConstants.DatePlaceHolder,
        DateTime? minDate = null,
        DateTime? maxDate = null,
        string format = "MM/dd/yyyy",
        bool isRequired = false,
        bool isReadOnly = false,
        string cssClass = "datepicker-container") where TModel : class
#pragma warning restore S107 // Methods should not have too many parameters
    {

        var containerDiv = new TagBuilder("div");
        containerDiv.AddCssClass(FormGroupCssClass);

        // Label
        var labelTag = SosLabelFor(html, localizer, expression, labelResourceKey, isRequired, isReadOnly);

        var labelDiv = new TagBuilder("div");
        _ = labelDiv.InnerHtml.AppendHtml(labelTag);

        var contentDiv = new TagBuilder("div");

        if (isReadOnly)
        {
            // Extract value from model
            var model = html.ViewData.Model;
            TValue? value = model != null ? expression.Compile().Invoke(model) : default;

            string displayValue = "";

            if (value is DateTime dt && dt > DateTime.MinValue)
            {
                displayValue = dt.ToString(format, CultureInfo.InvariantCulture);
            }
            else if (value is DateTime dto && dto > DateTime.MinValue)
            {
                displayValue = dto.ToString(format, CultureInfo.InvariantCulture);
            }
            else if (!EqualityComparer<TValue>.Default.Equals(value, default!))
            {
                displayValue = value?.ToString() ?? "";
            }

            var spanTag = new TagBuilder("span");
            spanTag.Attributes["id"] = html.IdFor(expression).ToString();
            spanTag.AddCssClass(PlainTextFormControlCssClass);
            _ = spanTag.InnerHtml.SetContent(displayValue);

            _ = contentDiv.InnerHtml.AppendHtml(spanTag);
        }
        else
        {
            var datePicker = html.EJS().DatePickerFor(expression)
                .Placeholder(localizer[placeholderResourceKey].Value)
                .Format(format)
                .Min(minDate ?? DateTime.MinValue)
                .Max(maxDate ?? DateTime.MaxValue)
                .CssClass(cssClass);

            _ = contentDiv.InnerHtml.AppendHtml(datePicker.Render());
        }

        var validation = html.ValidationMessageFor(expression, "", new { @class = TextDangerClass });
        var validationDiv = new TagBuilder("div");
        _ = validationDiv.InnerHtml.AppendHtml(validation);

        _ = containerDiv.InnerHtml.AppendHtml(labelDiv);
        _ = containerDiv.InnerHtml.AppendHtml(contentDiv);
        _ = containerDiv.InnerHtml.AppendHtml(validationDiv);

        return containerDiv;
    }

    /// <summary>
    /// Formatted currency input ($1,000.00). Set widthInEmUnits=null to accept the width of the parent column.
    /// </summary>
    /// <typeparam name="TModel"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="httpContext"></param>
    /// <param name="expression"></param>
    /// <param name="value"></param>
    /// <param name="labelKey"></param>
    /// <param name="minValue"></param>
    /// <param name="maxValue"></param>
    /// <param name="widthInEmUnits"></param>
    /// <param name="placeholderKey"></param>
    /// <returns></returns>
#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent CurrencyInputFor<TModel, TValue>(
        this IHtmlHelper<TModel> htmlHelper,
        IHtmlLocalizer localizer,
        Expression<Func<TModel, TValue>> expression,
        string labelKey,
        bool? required = false,
        bool displayCurrencySymbol = false,
        bool isAlignRight = false,
        double? widthInEmUnits = 6.0,
        float minValue = 0,
        string placeholderKey = "",
        bool disabled = false)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        // Init Container
        var divContainerWrapperTag = new TagBuilder("div");
        divContainerWrapperTag.Attributes.Add(StyleAttribute, $"display: flex; flex-direction: column;");

        // Label for Currency
        if (!string.IsNullOrEmpty(labelKey))
        {
            var labelTag = isAlignRight
            ? htmlHelper.LabelFor(expression, localizer[labelKey].Value, new { style = "line-height:2.5em" })
            : SosLabelFor(htmlHelper, localizer, expression, labelKey, required);

            _ = divContainerWrapperTag.InnerHtml.AppendHtml(labelTag);
        }

        // Currency
        var currencyWrapper = new TagBuilder("div");

        var builder = htmlHelper.EJS()
                .NumericTextBoxFor(expression)
                .Decimals(2)
                .Format(displayCurrencySymbol ? "$#,###.00" : "n2")
                .StrictMode(true)
                .Min(minValue)
                .ShowSpinButton(false)
                .Placeholder(placeholderKey != "" ? localizer[placeholderKey].Value : "")
                .Enabled(!disabled)
                .HtmlAttributes(new Dictionary<string, object>
                {
                    { "class", PlainTextFormControlCssClass },
                    { "style", "box-sizing: border-box;" }
                });

        // Wrapper to apply the width and align position
        var currencyInputWrapper = new TagBuilder("div");

        var width = widthInEmUnits is null ? "auto" : $"{widthInEmUnits}em;";
        currencyInputWrapper.Attributes.Add(StyleAttribute, $"width:{width}");
        currencyInputWrapper.AddCssClass(isAlignRight ? "ms-auto" : "");

        // Add currency input to currency wrapper
        _ = currencyInputWrapper.InnerHtml.AppendHtml(builder.Render());
        _ = currencyWrapper.InnerHtml.AppendHtml(currencyInputWrapper);

        // Validation
        var validationWrapper = new TagBuilder("div");

        var validationClass = TextDangerClass;
        var validation = htmlHelper.ValidationMessageFor(expression, "", new { @class = validationClass });

        // Apply align position
        validationWrapper.AddCssClass(isAlignRight ? "ms-auto" : "");

        // Add validation message to validation wrapper
        _ = validationWrapper.InnerHtml.AppendHtml(validation);

        // Add Currency and Validation to Container
        _ = divContainerWrapperTag.InnerHtml.AppendHtml(currencyWrapper);
        _ = divContainerWrapperTag.InnerHtml.AppendHtml(validationWrapper);

        return divContainerWrapperTag;
    }

    /// <summary>
    /// HTML Helper for generating the sub-header text on a form workflow.
    /// Enables global control over styling of the content header across all pages.
    /// </summary>
    /// <param name="htmlHelper">The HTML helper instance.</param>
    /// <param name="localizer">Localizer</param>
    /// <param name="text">Localized resource key or text to use as the header</param>
    /// <returns>Returns the HTML for the header.</returns>
    public static IHtmlContent StepHeader(this IHtmlHelper htmlHelper, IHtmlLocalizer localizer, string text)
    {
        var tag = new TagBuilder("h2");
        _ = tag.InnerHtml.SetContent(localizer[text]?.Value ?? text ?? "");
        return tag;
    }

    /// <summary>
    /// HTML Helper for generating content text blocks within the content section.
    /// Enables global control over styling of the content text across all pages.
    /// </summary>
    /// <param name="htmlHelper">The HTML helper instance.</param>
    /// <param name="localizer">Localizer</param>
    /// <param name="text">Localized resource key or text to use as the header</param>
    /// <returns>Returns the HTML for a section of content text.</returns>
    public static IHtmlContent TextBlock(this IHtmlHelper htmlHelper, IHtmlLocalizer localizer, string text)
    {
        var pTag = new TagBuilder("p");
        _ = pTag.InnerHtml.SetContent(localizer[text]?.Value ?? text ?? "");
        return pTag;
    }

    public static IHtmlContent RenderValidationError(Dictionary<string, ValidationMessage>? validations, string validationKey, string displayName)
    {
        if (validations != null &&
            validations.TryGetValue(validationKey, out var validation))
        {
            return UtilityService.DecisionsErrorDisplay(validation.Message, displayName);
        }
        return new HtmlString(string.Empty);
    }

    public static IHtmlContent CampaignContributionsTransactionDateInput(this IHtmlHelper htmlHelper, IHtmlLocalizer localizer, string text)
    {
        var container = new TagBuilder("div");
        _ = container.InnerHtml.AppendHtml(htmlHelper.Label("TransactionDate", localizer[text].Value, new { @class = FormLabelClass }));
        _ = container.InnerHtml.AppendHtml(htmlHelper.TextBox("TransactionDate", localizer[text].Value, new { @class = FormControlClass, type = "date" }));
        return container;
    }

    public static IHtmlContent CampaignContributionsTextBox(this IHtmlHelper htmlHelper, IHtmlLocalizer localizer, string text, string expression)
    {
        var container = new TagBuilder("div");
        _ = container.InnerHtml.AppendHtml(htmlHelper.Label(expression, localizer[text].Value, new { @class = FormLabelClass }));
        _ = container.InnerHtml.AppendHtml(htmlHelper.ValidationMessage(expression, null, new { @class = TextDangerClass }));
        _ = container.InnerHtml.AppendHtml(htmlHelper.TextBox(expression, string.Empty, new { @class = FormControlClass }));
        return container;
    }

    public static string GetFilingSummaryStatusColorByStatusName(string? statusName)
    {
        if (string.IsNullOrEmpty(statusName))
        {
            return "#5E9967";
        }
        return statusName switch
        {
            var status when status == FilingSummaryStatus.InProgress.Name => "#9F8A2B",
            var status when status == FilingSummaryStatus.NotStarted.Name => "#C76D6D",
            _ => "#5E9967"
        };
    }

    public static string GetFilingSummaryButtonTextByStatusName(string? statusName, IHtmlLocalizer localizer)
    {
        if (string.IsNullOrEmpty(statusName))
        {
            return localizer["FilerPortal.Disclosure.Dashboard.View"].Value;
        }
        return statusName switch
        {
            var status when status == FilingSummaryStatus.InProgress.Name => localizer["Common.Edit"].Value,
            var status when status == FilingSummaryStatus.NothingToReport.Name => localizer["Common.Edit"].Value,
            var status when status == FilingSummaryStatus.NotStarted.Name => localizer["FilerPortal.Disclosure.Dashboard.Start"].Value,
            _ => localizer["FilerPortal.Disclosure.Dashboard.View"].Value
        };
    }

    /// <summary>
    /// Formats address for display in the specified mode
    /// </summary>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="address">AddressViewModel</param>
    /// <param name="mode">Horizontal or Vertical display</param>
    /// <returns></returns>
    public static IHtmlContent AddressDisplay(this IHtmlHelper htmlHelper, IHtmlLocalizer localizer, AddressViewModel address, AddressDisplayMode mode)
    {
        var tag = new TagBuilder("span");
        if (string.IsNullOrEmpty(address.Street))
        {
            tag.InnerHtml.Append(localizer[CommonResourceConstants.AddressNotRecognized].Value);
        }
        else if (mode == AddressDisplayMode.Horizontal)
        {
            if (string.IsNullOrEmpty(address.Street2))
            {
                tag.InnerHtml.Append($"{address.Street}, {address.City}, {address.State} {address.Zip}");
            }
            else
            {
                tag.InnerHtml.Append($"{address.Street}, {address.Street2}, {address.City}, {address.State} {address.Zip}");
            }
        }
        else if (mode == AddressDisplayMode.Vertical)
        {
            tag.InnerHtml.AppendLine(address.Street);

            if (!string.IsNullOrEmpty(address.Street2))
            {
                tag.InnerHtml.AppendLine(address.Street2);
            }

            tag.InnerHtml.AppendLine($"{address.City}, {address.State} {address.Zip}");
            tag.Attributes.Add(StyleAttribute, "white-space: pre-line");
        }

        return tag;
    }

    public static IHtmlContent DetailTable(this IHtmlHelper htmlHelper, IHtmlLocalizer localizer, List<DetailTableRecord> details)
    {
        var tag = new TagBuilder("div");
        tag.AddCssClass("sos-detail-table");
        for (var i = 0; i < details.Count; i++)
        {
            var item = details[i];
            if (i == 0)
            {
                tag.InnerHtml.AppendHtml(new TagBuilder("hr") { TagRenderMode = TagRenderMode.SelfClosing });
            }
            var row = new TagBuilder("div");
            row.AddCssClass("row");
            var leftCol = new TagBuilder("div");
            leftCol.AddCssClass("col-sm-6");
            leftCol.AddCssClass("fw-bold");
            leftCol.AddCssClass("align-self-center");
            if (item.Label is not null)
            {
                leftCol.InnerHtml.AppendHtml(item.Label);
            }

            var rightCol = new TagBuilder("div");
            rightCol.AddCssClass("col-sm-6");
            rightCol.AddCssClass("align-right");
            if (item.Value is not null)
            {
                rightCol.InnerHtml.AppendHtml(item.Value);
            }

            row.InnerHtml.AppendHtml(leftCol);
            row.InnerHtml.AppendHtml(rightCol);

            tag.InnerHtml.AppendHtml(row);
            if (i <= details.Count)
            {
                tag.InnerHtml.AppendHtml(new TagBuilder("hr") { TagRenderMode = TagRenderMode.SelfClosing });
            }
        }
        return tag;
    }

    public static IHtmlContent StringHtml(this IHtmlHelper htmlHelper, IHtmlLocalizer localizer, string? valueOrKey)
    {
        return new HtmlString(HtmlEncoder.Default.Encode(localizer[valueOrKey ?? ""]?.Value ?? ""));
    }
    public static IHtmlContent StringHtml(this IHtmlHelper htmlHelper, string? value)
    {
        return new HtmlString(HtmlEncoder.Default.Encode(value ?? ""));
    }

    /// <summary>
    /// HtmlHelper for the Syncfusion Uploader
    /// </summary>
    /// <param name="htmlHelper"></param>
    /// <param name="localizer"></param>
    /// <param name="settings"></param>
    /// <returns></returns>
#pragma warning disable S107 // Methods should not have too many parameters
    public static IHtmlContent FileUploader(this IHtmlHelper htmlHelper,
        IHtmlLocalizer localizer,
        string? allowedFileExtensions = null,
        long? relationshipId = null,
        string? relationshipType = null,
        string titleResourceKey = CommonResourceConstants.UploadFileTitle,
        string? instructionsResourceKey = null,
        string? onUploadSuccess = null,
        string? onUploadFailure = null,
        bool handleMultipleFiles = true,
        int? maxMegabyteSize = 5,
        bool required = false)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        var divTag = new TagBuilder("div");
        var labelTag = GenerateLabelTag(localizer, FileUploaderId, titleResourceKey, required, readOnly: false);
        if (labelTag != null)
        {
            _ = divTag.InnerHtml.AppendHtml(labelTag);
        }

        if (instructionsResourceKey is not null)
        {
            var instructions = new TagBuilder("div");
            instructions.InnerHtml.AppendHtml(localizer[instructionsResourceKey]);
            _ = divTag.InnerHtml.AppendHtml(instructions);
        }

        // Use the Url property from the ViewContext
        var urlHelper = htmlHelper.ViewContext.HttpContext.RequestServices.GetService<IUrlHelperFactory>()?.GetUrlHelper(htmlHelper.ViewContext);

        var saveUrl = urlHelper!.Action(action: "UploadFiles", controller: "Upload", new { relationshipId, relationshipType });
        var removeUrl = urlHelper!.Action(action: "RemoveFiles", controller: "Upload", new { relationshipId, relationshipType });

        /* To do: add customizations for the Upload button reference and selected files list */

        var settings = new FileUploaderViewModel
        {
            SaveUrl = saveUrl!,
            RemoveUrl = removeUrl!,
            Title = titleResourceKey is not null ? localizer[titleResourceKey].Value : titleResourceKey,
            Instructions = instructionsResourceKey is not null ? localizer[instructionsResourceKey].Value : instructionsResourceKey,
            HandleMultipleFiles = handleMultipleFiles,
            MaxMegabyteSize = maxMegabyteSize,
            AllowedFileExtensions = allowedFileExtensions,
            OnUploadSuccess = onUploadSuccess,
            OnUploadFailure = onUploadFailure
        };

        var uploader = htmlHelper.Partial("_FileUploader", settings);
        _ = divTag.InnerHtml.AppendHtml(uploader);

        return divTag;
    }

    public static IHtmlContent DisabledTextFieldWithHidden<TModel, TValue>(
        this IHtmlHelper<TModel> htmlHelper,
        IHtmlLocalizer localizer,
        Expression<Func<TModel, TValue>> expression,
        string labelKey,
        bool? required = false,
        string placeholderKey = "",
        string? captionKey = null
    )
    {
        var content = new HtmlContentBuilder();
        content.AppendHtml(htmlHelper.TextFieldFor(localizer, expression, labelKey, required, placeholderKey, captionKey, disabled: true));
        content.AppendHtml(htmlHelper.HiddenFor(expression));

        return content;
    }
}

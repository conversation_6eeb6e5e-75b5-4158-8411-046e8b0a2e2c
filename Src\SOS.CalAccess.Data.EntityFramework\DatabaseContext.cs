// <copyright file="DatabaseContext.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Internal;
using SOS.CalAccess.Models.Auditing;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.DataValidation;
using SOS.CalAccess.Models.Efile;
using SOS.CalAccess.Models.Email;
using SOS.CalAccess.Models.FilerDisclosure;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;
using SOS.CalAccess.Models.FilerRegistration;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Lobbyists;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Models.SmsMessaging;
using SOS.CalAccess.Models.System;
using SOS.CalAccess.Models.UserAccountMaintenance;
using LobbyingFirm = SOS.CalAccess.Models.FilerRegistration.Lobbyists.LobbyingFirm;

namespace SOS.CalAccess.Data.EntityFramework;

/// <summary>
/// Context class for the Disclosure System.
/// This encapsulates our data access layer and communication
/// with the database.
/// </summary>
/// <param name="options">Options passed to configure the context.</param>
public sealed class DatabaseContext(
    DbContextOptions<DatabaseContext> options) : DbContext(options)
{
    /// <summary>
    /// Gets or sets the set of actors that
    /// are registered in the system and which actions
    /// can be attributed to.
    /// </summary>
    public DbSet<Actor> Actors { get; set; }

    /// <summary>
    /// Gets or sets the set of filers in the system.
    /// </summary>
    public DbSet<Filer> Filers { get; set; }

    /// <summary>
    /// Gets or sets the set of filer contact types in the system.
    /// </summary>
    public DbSet<FilerContactType> FilerContactTypes { get; set; }

    /// <summary>
    /// Gets or sets the set of filer contacts in the system.
    /// </summary>
    public DbSet<FilerContact> FilerContacts { get; set; }

    /// <summary>
    /// Gets or sets the set of transaction reportable persons in the system.
    /// </summary>
    public DbSet<TransactionReportablePerson> TransactionReportablePersons { get; set; }

    /// <summary>
    /// Gets or sets the set of filer individual contacts in the system.
    /// </summary>
    public DbSet<IndividualContact> IndividualContacts { get; set; }

    /// <summary>
    /// Gets or sets the set of filer committee contacts in the system.
    /// </summary>
    public DbSet<CommitteeContact> CommitteeContacts { get; set; }

    /// <summary>
    /// Gets or sets the set of filer organization contacts in the system.
    /// </summary>
    public DbSet<OrganizationContact> OrganizationContacts { get; set; }

    /// <summary>
    /// Gets or sets the set of users that
    /// are registered and can interact with the system.
    /// </summary>
    /// <remarks>
    /// Users are a form of actors.
    /// </remarks>
    public DbSet<User> Users { get; set; }

    /// <summary>
    /// Gets or sets the set of system actors that
    /// are registered and can interact with the system.
    /// </summary>
    /// <remarks>
    /// This includes the system itself.
    /// </remarks>
    public DbSet<SystemActor> Systems { get; set; }

    /// <summary>
    /// Gets or sets the set of registrations in the system.
    /// </summary>
    public DbSet<Registration> Registrations { get; set; }

    /// <summary>
    /// Gets or sets the set of registration statuses in the system.
    /// </summary>
    public DbSet<RegistrationStatus> RegistrationStatuses { get; set; }

    /// <summary>
    /// Gets or sets the set of transaction types in the system.
    /// </summary>
    public DbSet<TransactionType> TransactionTypes { get; set; }

    /// <summary>
    /// Gets or sets the set of contribution types in the system.
    /// </summary>
    public DbSet<ContributionType> ContributionTypes { get; set; }

    /// <summary>
    /// Gets or sets the set of monetary types for contributions in the system.
    /// </summary>
    public DbSet<MonetaryType> MonetaryTypes { get; set; }

    /// <summary>
    /// Gets or sets the set of activity expense types in the system.
    /// </summary>
    public DbSet<ActivityExpenseType> ActivityExpenseTypes { get; set; }

    /// <summary>
    /// Gets or sets the transaction set in the system.
    /// </summary>
    public DbSet<Transaction> Transactions { get; set; }

    /// <summary>
    /// Gets or sets the contribution set in the system.
    /// </summary>
    public DbSet<Contribution> Contributions { get; set; }

    /// <summary>
    /// Gets or sets the in-kind contribution set in the system.
    /// </summary>
    public DbSet<InKindContribution> InKindContributions { get; set; }

    /// <summary>
    /// Gets or sets the monetary contribution set in the system.
    /// </summary>
    public DbSet<MonetaryContribution> MonetaryContributions { get; set; }

    /// <summary>
    /// Gets or sets the expenditure set in the system.
    /// </summary>
    public DbSet<Expenditure> Expenditures { get; set; }

    /// <summary>
    /// Gets or sets the set of available filing statuses in the system.
    /// </summary>
    public DbSet<FilingStatus> FilingStatuses { get; set; }

    /// <summary>
    /// Gets or sets the set of filings in the system.
    /// </summary>
    public DbSet<Filing> Filings { get; set; }

    /// <summary>
    /// Gets or sets the set of candidates in the system.
    /// </summary>
    public DbSet<Candidate> Candidates { get; set; }

    /// <summary>
    /// Gets or sets the set of slate mailer organization registrations in the system.
    /// </summary>
    public DbSet<SlateMailerOrganization> SlateMailerOrganizations { get; set; }

    //TODO: Use correct LobbyingFirm e.g. Lobbyists or Registrations
    /// <summary>
    /// Gets or sets the set of lobbying firm registrations in the system.
    /// </summary>
    public DbSet<LobbyingFirm> LobbyingFirms { get; set; }

    /// <summary>
    /// Gets or sets the set of unregistered lobbying firms in the system.
    /// </summary>
    public DbSet<UnregisteredLobbyingFirm> UnregisteredLobbyingFirms { get; set; }

    /// <summary>
    /// Gets or sets the set of lobbyist registrations in the system.
    /// </summary>
    public DbSet<Lobbyist> Lobbyists { get; set; }

    /// <summary>
    /// Gets or sets the set of lobbyist registrations in the system.
    /// </summary>
    public DbSet<LobbyistWithdrawal> LobbyistWithdrawals { get; set; }

    /// <summary>
    /// Gets or sets the set of lobbyist registrations terminaltions in the system.
    /// </summary>
    public DbSet<LobbyistTermination> LobbyistTerminations { get; set; }

    /// <summary>
    /// Gets or sets the set of lobbyist employer registrations in the system.
    /// </summary>
    public DbSet<LobbyistEmployer> LobbyistEmployers { get; set; }

    /// <summary>
    /// Gets or sets the set of candidate intention statements in the system.
    /// </summary>
    public DbSet<CandidateIntentionStatement> CandidateIntentionStatements { get; set; }

    /// <summary>
    /// Gets or sets the set of candidate intention statements withdrawals in the system.
    /// </summary>
    public DbSet<CisWithdrawal> CisWithdrawals { get; set; }

    /// <summary>
    /// Gets or sets the set of campaign organization registrations in the system, which groups slate mailers and committees.
    /// </summary>
    public DbSet<CampaignOrganization> CampaignOrganizations { get; set; }

    /// <summary>
    /// Gets or sets the set of committee registrations in the system.
    /// </summary>
    public DbSet<Committee> Committees { get; set; }

    /// <summary>
    /// Gets or sets the set of general purpose committee registrations in the system.
    /// </summary>
    public DbSet<GeneralPurposeCommittee> GeneralPurposeCommittees { get; set; }

    /// <summary>
    /// Gets or sets the set of primarily formed committee registrations in the system.
    /// </summary>
    public DbSet<PrimarilyFormedCommittee> PrimarilyFormedCommittees { get; set; }

    /// <summary>
    /// Gets or sets the set of candidate-controlled committees in the system.
    /// </summary>
    public DbSet<CandidateControlledCommittee> CandidateControlledCommittees { get; set; }

    /// <summary>
    /// Gets or sets the linking table for support or opposition of candidates by registrations.
    /// </summary>
    public DbSet<RegistrationStanceOnCandidate> RegistrationsCandidates { get; set; }

    /// <summary>
    /// Gets or sets the linking table for support or opposition of candidates by registrations.
    /// </summary>
    public DbSet<RegistrationStanceOnBallotMeasure> RegistrationsBallotMeasures { get; set; }

    /// <summary>
    /// Gets or sets the set of auditing logs
    /// that represent actions performed by system actors
    /// on system resources.
    /// </summary>
    public DbSet<AuditLog> AuditLogs { get; set; }

    /// <summary>
    /// Gets or sets the MultipurposeOrganizations.
    /// </summary>
    public DbSet<MultipurposeOrganization> MultipurposeOrganizations { get; set; }

    /// <summary>
    /// Gets or sets the Agencies.
    /// </summary>
    public DbSet<Agency> Agencies { get; set; }

    /// <summary>
    /// Gets or sets the Bills.
    /// </summary>
    public DbSet<Bill> Bills { get; set; }

    /// <summary>
    /// Gets or sets the Bills.
    /// </summary>
    public DbSet<BillHouse> BillHouses { get; set; }

    /// <summary>
    /// Gets or sets the PaymentCodes.
    /// </summary>
    public DbSet<PaymentCode> PaymentCodes { get; set; }

    /// <summary>
    /// Gets or sets the BusinessSubcategories.
    /// </summary>
    public DbSet<BusinessSubcategory> BusinessSubcategories { get; set; }

    /// <summary>
    /// Gets or sets the PaymentCodes.
    /// </summary>
    public DbSet<AdvertisementDistributionMethod> AdvertisementDistributionMethods { get; set; }

    /// <summary>
    /// Gets or sets the set of elections in the system.
    /// </summary>
    public DbSet<Election> Elections { get; set; }

    /// <summary>
    /// Gets or sets BallotMeasures.
    /// </summary>
    public DbSet<BallotMeasure> BallotMeasures { get; set; }

    /// <summary>
    /// Gets or sets the filer statuses.
    /// </summary>
    public DbSet<FilerStatus> FilerStatuses { get; set; }

    /// <summary>
    /// Gets or sets the set of addresses in the system.
    /// </summary>
    public DbSet<Address> Addresses { get; set; }

    /// <summary>
    /// Gets or sets the set of phone numbers in the system.
    /// </summary>
    public DbSet<PhoneNumber> PhoneNumbers { get; set; }

    /// <summary>
    /// Gets or sets the set of filing periods in the system.
    /// </summary>
    public DbSet<FilingPeriod> FilingPeriods { get; set; }

    /// <summary>
    /// Gets or sets the actions lobbied in the system.
    /// </summary>
    public DbSet<ActionsLobbied> ActionsLobbied { get; set; }

    /// <summary>
    /// Gets or sets the set of filing types in the system.
    /// </summary>
    public DbSet<FilingType> FilingTypes { get; set; }

    /// <summary>
    /// Gets or sets the set of filing transactions in the system.
    /// </summary>
    public DbSet<FilingTransaction> FilingsTransactions { get; set; }

    /// <summary>
    /// Gets or sets the set of registration contacts in the system.
    /// </summary>
    public DbSet<RegistrationContact> RegistrationContacts { get; set; }

    /// <summary>
    /// Gets or sets the set of registration - registration contacts relationship in the system.
    /// </summary>
    public DbSet<RegistrationRegistrationContact> RegistrationsRegistrationContacts { get; set; }

    /// <summary>
    /// Gets or sets the set of registration - lobbying client relationship in the system.
    /// </summary>
    public DbSet<RegistrationLobbyingClient> RegistrationsLobbyingClients { get; set; }

    /// <summary>
    /// Gets or sets the set of registration lobbying client - agency relationship in the system.
    /// </summary>
    public DbSet<RegistrationLobbyingClientAgency> RegistrationsLobbyingClientAgencies { get; set; }

    /// <summary>
    /// Gets or sets the set of registration lobbying client - lobbying interest relationship in the system.
    /// </summary>
    public DbSet<RegistrationLobbyingClientLobbyingInterest> RegistrationsLobbyingClientsLobbyingInterests { get; set; }

    /// <summary>
    /// Gets or sets the set of links between registrations and agencies in the system.
    /// </summary>
    public DbSet<RegistrationAgency> RegistrationAgencies { get; set; }

    /// <summary>
    /// Gets or sets the set of address lists in the system.
    /// </summary>
    public DbSet<AddressList> AddressLists { get; set; }

    /// <summary>
    /// Gets or sets the set of phone number lists in the system.
    /// </summary>
    public DbSet<PhoneNumberList> PhoneNumberLists { get; set; }

    /// <summary>
    /// Gets or sets the set of lobbying clients in the system.
    /// </summary>
    public DbSet<LobbyingClient> LobbyingClients { get; set; }

    /// <summary>
    /// Gets or sets the details of APIRequest in the system.
    /// </summary>
    public DbSet<ApiRequest> ApiRequest { get; set; }
    /// <summary>
    /// Gets or sets the details of APIRequest in the system.
    /// </summary>
    public DbSet<ApiRequestStatus> ApiRequestStatus { get; set; }

    /// <summary>
    /// Gets or sets the API related Errors in the system.
    /// </summary>
    public DbSet<ApiError> ApiError { get; set; }

    /// <summary>
    /// Describes error messages used in the Decisions system.
    /// </summary>
    public DbSet<DecisionsErrorMessage> DecisionsErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the group details
    /// </summary>
    public DbSet<AuthorizationGroup> Groups { get; set; }

    /// <summary>
    /// Gets or sets Authorization GRoups and its permissions
    /// </summary>
    public DbSet<AuthorizationGroupPermission> AuthorizationGroupPermission { get; set; }

    /// <summary>
    /// Gets or sets the permission details
    /// </summary>
    public DbSet<Permission> Permissions { get; set; }

    /// <summary>
    /// Gets or sets the filer role details
    /// </summary>
    public DbSet<FilerRole> FilerRoles { get; set; }

    /// <summary>
    /// Gets or sets the filer user details
    /// </summary>
    public DbSet<FilerRolePermission> FilerRolePermissions { get; set; }

    /// <summary>
    /// Gets or sets the filer user details
    /// </summary>
    public DbSet<FilerUser> FilerUsers { get; set; }

    /// <summary>
    /// Gets or sets the sms messages
    /// </summary>
    public DbSet<SmsMessage> SmsMessages { get; set; }

    /// <summary>
    /// Gets or sets the Dirty Data Metadata
    /// </summary>
    public DbSet<DirtyDataMetadata> DirtyDataMetadata { get; set; }

    /// <summary>
    /// Gets or sets the filer user details
    /// </summary>
    public DbSet<EmailMessage> EmailMessages { get; set; }

    /// <summary>
    /// Gets or sets the attestations
    /// </summary>
    public DbSet<Attestation> Attestations { get; set; }
    /// <summary>
    /// Gets or sets the JsonSchema
    /// </summary>
    public DbSet<JsonSchemaReference> JsonSchemaReference { get; set; }

    /// <summary>
    /// Gets or sets the NotificationMessages
    /// </summary>
    public DbSet<NotificationMessage> NotificationMessages { get; set; }

    /// <summary>
    /// Gets or sets the NotificationTemplate
    /// </summary>
    public DbSet<NotificationTemplate> NotificationTemplates { get; set; }

    /// <summary>
    /// Gets or sets the NotificationTemplateTranslation
    /// </summary>
    public DbSet<NotificationTemplateTranslation> NotificationTemplateTranslations { get; set; }

    /// <summary>
    /// Gets or sets the NotificationTypes
    /// </summary>
    public DbSet<NotificationType> NotificationTypes { get; set; }

    /// <summary>
    /// Gets or sets the NotificationTypes
    /// </summary>
    public DbSet<UserNotificationPreference> UserNotificationPreferences { get; set; }

    /// <summary>
    /// Gets or sets the FilerTypes
    /// </summary>
    public DbSet<FilerType> Filertypes { get; set; }

    /// <summary>
    /// Gets or sets the set of filer links in the system.
    /// </summary>
    public DbSet<FilerLink> FilerLinks { get; set; }

    /// <summary>
    /// Gets or sets the ElectionRaces
    /// </summary>
    public DbSet<ElectionRace> ElectionRaces { get; set; }

    /// <summary>
    /// Gets or sets the LLCDetails
    /// </summary>
    public DbSet<LlcDetail> LlcDetails { get; set; }
    /// <summary>
    /// Gets or sets the CapitalContributions
    /// </summary>
    public DbSet<CapitalContribution> CapitalContributions { get; set; }

    /// <summary>
    /// Gets or sets Lobbying Coalition Payment Transactions
    /// </summary>
    public DbSet<PaymentMadeToLobbyingCoalition> PaymentMadeToLobbyingCoalition { get; set; }

    /// <summary>
    /// Gets or sets Other Influence Payments Transactions
    /// </summary>
    public DbSet<OtherPaymentsToInfluence> OtherPaymentsToInfluence { get; set; }

    /// <summary>
    /// Gets or sets Lobbying Firm Payment Transactions
    /// </summary>
    public DbSet<PaymentMadeToLobbyingFirms> PaymentMadeToLobbyingFirms { get; set; }

    /// <summary>
    /// Gets or sets the UnregisteredBallotMeasures
    /// </summary>
    public DbSet<UnRegisteredBallotMeasure> UnRegisteredBallotMeasures { get; set; }

    /// <summary>
    /// Gets or sets the UnregisteredCandidates
    /// </summary>
    public DbSet<UnRegisteredCandidate> UnRegisteredCandidates { get; set; }

    /// <summary>
    /// Gets or sets Payments Received by Lobbying Coalitions Transactions
    /// </summary>
    public DbSet<PaymentReceiveLobbyingCoalition> PaymentReceiveLobbyingCoalition { get; set; }

    /// <summary>
    /// Gets or sets Official Positions look-up
    /// </summary>
    public DbSet<OfficialPosition> OfficialPositions { get; set; }


    /// <summary>
    /// Gets or sets filers related to a filing
    /// </summary>
    public DbSet<FilingRelatedFiler> FilingRelatedFiler { get; set; }


    /// <summary>
    /// Class that represents the different statuses of filing summary that can be submitted in the system.
    /// </summary>
    public DbSet<FilingSummaryStatus> FilingSummaryStatuses { get; set; }

    /// <summary>
    /// Class that represents the Nature And Interest Type that can be submitted in the system.
    /// </summary>
    public DbSet<NatureAndInterestType> NatureAndInterestTypes { get; set; }

    /// <summary>
    /// Class that represents  filing contact summary types used as look up.
    /// </summary>
    public DbSet<FilingContactSummaryType> FilingContactSummaryTypes { get; set; }

    /// <summary>
    /// Class that represents the Filing contact summarys submitted in the system.
    /// </summary>
    public DbSet<FilingContactSummary> FilingContactSummarys { get; set; }
    /// <summary>
    /// Class that represents the Industry Group Classification Type that can be submitted in the system.
    /// </summary>
    public DbSet<IndustryGroupClassificationType> IndustryGroupClassificationTypes { get; set; }

    /// <summary>
    /// Gets or sets country codes
    /// </summary>
    public DbSet<Country> Country { get; set; }

    /// <summary>
    /// Class that represents the Committee Type that can be submitted in the system.
    /// </summary>
    public DbSet<CommitteeType> CommitteeTypes { get; set; }

    /// <summary>
    /// Class that represents the Banner Message that can be submitted in the system
    /// </summary>
    public DbSet<BannerMessage> BannerMessage { get; set; }

    /// <summary>
    /// Class that represents the Banner Message Filer Type that can be submitted in the system
    /// </summary>
    public DbSet<BannerMessageFilerType> BannerMessageFilerType { get; set; }

    /// <summary>
    /// Class that represents the Committee Filer Contact Type.
    /// </summary>
    public DbSet<FilerCommitteeContact> FilerCommitteeContact { get; set; }

    /// <summary>
    /// Class that represents the Candidate Filer Contact Type.
    /// </summary>
    public DbSet<CandidateContact> CandidateContact { get; set; }

    /// <summary>
    /// Class that represents the Payment Made Transaction Type.
    /// </summary>
    public DbSet<PaymentMade> PaymentMade { get; set; }

    /// <summary>
    /// Class that represents the Payment Received Transaction Type.
    /// </summary>
    public DbSet<PaymentReceived> PaymentReceived { get; set; }

    /// <summary>
    /// Class that represents the Person recevied 1000 or more Transaction Type.
    /// </summary>
    public DbSet<PersonReceiving1000OrMore> PersonReceiving1000OrMore { get; set; }

    /// <summary>
    /// Gets or sets the linking table for support or opposition of candidates by disclosure transactions.
    /// </summary>
    public DbSet<DisclosureStanceOnCandidate> DisclosureStanceOnCandidate { get; set; }

    /// <summary>
    /// Gets or sets the linking table for support or opposition of ballot measures by disclosure transactions.
    /// </summary>
    public DbSet<DisclosureStanceOnBallotMeasure> DisclosureStanceOnBallotMeasure { get; set; }

    /// <summary>
    /// Gets or sets the table for LegacyIdMappings.
    /// </summary>
    public DbSet<LegacyIdMapping> LegacyIdMappings { get; set; }

    /// <summary>
    /// Gets or sets the table for DisclosureWithoutPaymentReceived.
    /// </summary>
    public DbSet<DisclosureWithoutPaymentReceived> DisclosureWithoutPaymentReceived { get; set; }

    /// <summary>
    /// Gets or sets the table for LinkageStatus.
    /// </summary>
    public DbSet<LinkageStatus> LinkageStatuses { get; set; }

    /// <summary>
    /// Gets ir sets the table for LinkageRequest.
    /// </summary>
    public DbSet<LinkageRequest> LinkageRequests { get; set; }

    /// <summary>
    /// Gets or sets the table for SystemParameter.
    /// </summary>
    public DbSet<SystemParameter> SystemParameter { get; set; }

    /// <summary>
    /// Gets or sets the table for EndOfSessionLobbying.
    /// </summary>
    public DbSet<EndOfSessionLobbying> EndOfSessionLobbying { get; set; }

    /// <summary>
    /// Class that represents expenditure codes used as look up.
    /// </summary>
    public DbSet<ExpenditureCode> ExpenditureCodes { get; set; }

    public DbSet<UploadedFile> UploadedFiles { get; set; }

    /// <inheritdoc />
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AuditLog>()
            .DisableAudits()
            .HasIndex(a => a.ResourceId);

        modelBuilder.ApplyConfigurationsFromAssembly(typeof(DatabaseContext).Assembly);

        modelBuilder.ConfigureAudits();

        modelBuilder.ConfigureDocumentationAnnotations();

        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            foreach (var foreignKey in entityType.GetForeignKeys())
            {
                if (foreignKey.PrincipalEntityType.ClrType == typeof(Actor) && (foreignKey.DependentToPrincipal?.Name == "CreatedByReference" || foreignKey.DependentToPrincipal?.Name == "ModifiedByReference"))
                {
                    foreignKey.DeleteBehavior = DeleteBehavior.NoAction;
                }
            }
        }
    }
}

﻿CREATE TABLE [dbo].[PhoneNumber] (
    [Id]                   BIGINT                                             IDENTITY (1, 1) NOT NULL,
    [InternationalNumber]  BIT                                                NOT NULL,
    [CountryCode]          NVARCHAR (10)                                      NULL,
    [Extension]            NVARCHAR (20)                                      NULL,
    [Number]               NVARCHAR (20)                                      NOT NULL,
    [Type]                 NVARCHAR (40)                                      NOT NULL,
    [PhoneNumberListId]    BIGINT                                             NOT NULL,
    [CreatedBy]            BIGINT                                             NOT NULL,
    [ModifiedBy]           BIGINT                                             NOT NULL,
    [AuditableResourceTag] NVARCHAR (450)                                     NULL,
    [PeriodEnd]            DATETIME2 (7) GENERATED ALWAYS AS ROW END HIDDEN   NOT NULL,
    [PeriodStart]          DATETIME2 (7) GENERATED ALWAYS AS ROW START HIDDEN NOT NULL,
    [CountryId]            BIGINT                                             NULL,
    [IsPrimaryPhoneNumber] BIT                                                DEFAULT (CONVERT([bit],(0))) NOT NULL,
    CONSTRAINT [PK_PhoneNumber] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_PhoneNumber_Country_CountryId] FOREIGN KEY ([CountryId]) REFERENCES [dbo].[Country] ([Id]),
    CONSTRAINT [FK_PhoneNumber_PhoneNumberList_PhoneNumberListId] FOREIGN KEY ([PhoneNumberListId]) REFERENCES [dbo].[PhoneNumberList] ([Id]) ON DELETE CASCADE,
    PERIOD FOR SYSTEM_TIME ([PeriodStart], [PeriodEnd])
)
WITH (SYSTEM_VERSIONING = ON (HISTORY_TABLE=[dbo].[PhoneNumberHistory], DATA_CONSISTENCY_CHECK=ON));


















GO
CREATE NONCLUSTERED INDEX [IX_PhoneNumber_PhoneNumberListId]
    ON [dbo].[PhoneNumber]([PhoneNumberListId] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_PhoneNumber_AuditableResourceTag]
    ON [dbo].[PhoneNumber]([AuditableResourceTag] ASC) WHERE ([AuditableResourceTag] IS NOT NULL);


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Unique resource tag.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber', @level2type = N'COLUMN', @level2name = N'AuditableResourceTag';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'A string-serialized unique identifier used to link audit logs to arbitrary resource types.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber', @level2type = N'COLUMN', @level2name = N'AuditableResourceTag';


GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'The identifier of the user who last modified this record.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber', @level2type = N'COLUMN', @level2name = N'ModifiedBy';


GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'The identifier of the user who created this record.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber', @level2type = N'COLUMN', @level2name = N'CreatedBy';


GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'List to which this phone number is linked.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber', @level2type = N'COLUMN', @level2name = N'PhoneNumberListId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to a phone number list.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber', @level2type = N'COLUMN', @level2name = N'PhoneNumberListId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Intended purpose of the phone number when linked to other entities.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber', @level2type = N'COLUMN', @level2name = N'Type';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Phone number.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber', @level2type = N'COLUMN', @level2name = N'Number';


GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'The actual dialed Country code. Should include the preceding + character', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber', @level2type = N'COLUMN', @level2name = N'CountryCode';




GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates if this phone number is international.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber', @level2type = N'COLUMN', @level2name = N'InternationalNumber';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Phone number identifier.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber', @level2type = N'COLUMN', @level2name = N'Id';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'This table holds system phone numbers.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber';


GO
CREATE NONCLUSTERED INDEX [IX_PhoneNumber_CountryId]
    ON [dbo].[PhoneNumber]([CountryId] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Foreign key reference to the country table.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber', @level2type = N'COLUMN', @level2name = N'CountryId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Extension code.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber', @level2type = N'COLUMN', @level2name = N'Extension';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Is Primary PhoneNumber.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'PhoneNumber', @level2type = N'COLUMN', @level2name = N'IsPrimaryPhoneNumber';


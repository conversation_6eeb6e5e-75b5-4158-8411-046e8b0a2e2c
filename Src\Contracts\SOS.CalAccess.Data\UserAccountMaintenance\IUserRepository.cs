using SOS.CalAccess.Models.UserAccountMaintenance;

namespace SOS.CalAccess.Data.Contracts.UserAccountMaintenance;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// Defines database interactions for User table
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// Every entity has an Id field containing a unique identifier that can be used to retrieve a single record
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>UA-01: Create a User Account</li>
/// <li>UA-02: Update a User Account</li>
/// <li>UA-03: Search for a User/Entity</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// |                                |                                 |                                             |
#endregion
public interface IUserRepository : IRepository<User, long>
{
    /// <summary>
    /// Find all User records that match the supplied criteria
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    Task<List<User>> SearchUsers(UserSearchCriteria criteria);

    /// <summary>
    /// Find the Id of the User record by the provided username
    /// </summary>
    /// <param name="username"></param>
    /// <returns>null if username not found</returns>
    Task<long?> FindUserIdByUsername(string username);

    /// <summary>
    /// Find Users by list of user name
    /// </summary>
    /// <param name="userNames">List of user name</param>
    /// <returns>List of user</returns>
    Task<List<User>> FindUsersByUserNames(List<string> userNames);

    /// <summary>
    /// Get User by Entra Oid
    /// </summary>
    /// <param name="entraOid">Unique Entra OId of the user </param>
    /// <returns></returns>
    Task<User?> GetUserByEntraOId(string entraOid);

    /// <summary>
    /// Get user account details by user id
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    Task<User> GetUserAccountDetailsByUserId(long userId);

    /// <summary>
    /// Save changes to database
    /// </summary>
    /// <returns></returns>
    Task SaveChangesAsync();
}

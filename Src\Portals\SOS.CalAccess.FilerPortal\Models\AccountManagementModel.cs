using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Models;

namespace SOS.CalAccess.FilerPortal.Models;

public class AccountManagementModel : IAccuMailValidation
{
    /// <inheritdoc />
    public required long Id { get; set; }
    /// <summary>
    /// Gets or sets OrganizationName
    /// </summary>
    public required string UserName { get; set; }
    /// <summary>
    /// Gets or sets Email.
    /// </summary>
    public required string Email { get; set; }
    /// <summary>
    /// Gets or sets First name.
    /// </summary>
    public required string FirstName { get; set; }
    /// <summary>
    /// Gets or sets Last name.
    /// </summary>
    public required string LastName { get; set; }
    /// <summary>
    /// Gets or sets Middle name.
    /// </summary>
    public string? MiddleName { get; set; }
    /// <summary>
    /// Gets or sets Full name.
    /// </summary>
    public string? FullName { get; set; }
    /// <summary>
    /// Gets or sets Action.
    /// </summary>
    public FormAction? Action { get; set; }

    /// <summary>
    /// Gets or sets addresses
    /// </summary>
    public List<AddressViewModel> Addresses { get; set; } = new();

    /// <summary>
    /// Gets or sets AccuMail suggestions
    /// </summary>
    public List<AccuMailSuggestion> Suggestions { get; set; } = new();
    /// <summary>
    /// Phone number dto object
    /// </summary>
    public List<PhoneNumberDto>? PhoneNumberDto { get; set; }
}


using Refit;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Services.Common.FileSystem.Models;

namespace SOS.CalAccess.Services.Common.FileSystem;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// IUploadFileSvc manages data about files uploaded to Azure Blob Storage.
/// </p>
/// <p>
/// In terms of Architecture Design this translates to backend common application layer service invoked by the business services layer
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this service is to support the storage and retrieval of uploaded images, large json documents, pdfs, and other file based data in a persistent file store outside the database
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>BCS8- File System</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                   |
/// | ------------------------------ | ------------------------------ | ----------------------------- |
/// | UploadedFileRepository         | CreateUploadedFile             | Create an UploadedFile record |
/// | UploadedFileRepository         | GetUploadsByRelationshipId     | Gets a set of related uploads |
/// 
#endregion
public interface IUploadFileSvc
{
    /// <summary>
    /// Create an UploadedFile record 
    /// </summary>
    /// <paramref name="uploadedFile"/>
    /// <returns>Task<IEnumerable<BannerMessage>></returns>
    /// \msc
    /// Actor, IUploadFileSvc, UploadedFileRepository;
    /// Actor => IUploadFileSvc [label="CreateUploadedFile() "];
    /// IUploadFileSvc => UploadedFileRepository [label="CreateUploadedFile()"];
    /// \endmsc
    [Post("/" + CreateUploadedFilePath)]
    Task<UploadedFile> CreateUploadedFile(UploadedFile uploadedFile);
    const string CreateUploadedFilePath = "api/uploads/CreateUploadedFile";

    /// <summary>
    /// Find an UploadedFile record by the filename Guid. 
    /// </summary>
    /// <paramref name="fileNameGuid"/>
    /// <returns>Task<<UploadedFile></returns>
    /// \msc
    /// Actor, IUploadFileSvc, UploadedFileRepository;
    /// Actor => IUploadFileSvc [label="FindUploadByFileNameGuid() "];
    /// IUploadFileSvc => UploadedFileRepository [label="FindUploadByFileNameGuid()"];
    /// UploadedFileRepository >> IUploadFileSvc [label="\nreturn UploadedFile"];
    /// IUploadFileSvc >> Actor [label="\nreturn UploadedFile"];
    /// \endmsc
    [Get("/" + FindUploadByFileNameGuidPath)]
    Task<UploadedFile?> FindUploadByFileNameGuid(string fileNameGuid);
    const string FindUploadByFileNameGuidPath = "api/uploads/FindUploadByFileNameGuid/{fileNameGuid}";

    /// <summary>
    /// Get the set of uploads by RelationshipId. 
    /// </summary>
    /// <paramref name="relationshipId"/>
    /// <returns>Task<IEnumerable<UploadedFile>></returns>
    /// \msc
    /// Actor, IUploadFileSvc, UploadedFileRepository;
    /// Actor => IUploadFileSvc [label="GetUploadsByRelationshipId() "];
    /// IUploadFileSvc => UploadedFileRepository [label="GetUploadsByRelationshipId()"];
    /// UploadedFileRepository >> IUploadFileSvc [label="\nreturn IEnumerable<UploadedFile>"];
    /// IUploadFileSvc >> Actor [label="\nreturn IEnumerable<UploadedFile>"];
    /// \endmsc
    [Get("/" + GetUploadsByRelationshipIdPath)]
    Task<IEnumerable<UploadedFile>> GetUploadsByRelationshipId(long relationshipId);
    const string GetUploadsByRelationshipIdPath = "api/uploads/GetUploadsByRelationshipId/{relationshipId}";

    /// <summary>
    /// Update an UploadedFile record
    /// </summary>
    /// <param name="uploadedFile"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, IUploadFileSvc, UploadedFileRepository;
    /// Actor => IUploadFileSvc [label="UpdateUploadedFile() "];
    /// IUploadFileSvc => UploadedFileRepository [label="UpdateUploadedFile()"];
    /// \endmsc
    [Post("/" + UpdateUploadedFilePath)]
    Task<UploadedFile> UpdateUploadedFile(UploadedFile uploadedFile);
    const string UpdateUploadedFilePath = "api/uploads/UpdateUploadedFile";

    /// <summary>
    /// Delete an UploadedFile record
    /// </summary>
    /// <param name="uploadedFile"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, IUploadFileSvc, UploadedFileRepository;
    /// Actor => IUploadFileSvc [label="DeleteUploadedFile() "];
    /// IUploadFileSvc => UploadedFileRepository [label="DeleteUploadedFile()"];
    /// \endmsc
    [Post("/" + DeleteUploadedFilePath)]
    Task DeleteUploadedFile(UploadedFile uploadedFile);
    const string DeleteUploadedFilePath = "api/uploads/DeleteUploadedFile";

    /// <summary>
    /// Updates the relationships between uploaded files and their associated entity.
    /// </summary>
    /// <param name="request">The request object containing details of uploaded files and their entity links.</param>
    /// <returns></returns>
    [Put("/" + UpdateUploadedFileRelationshipsAsyncPath)]
    Task UpdateUploadedFileRelationshipsAsync(UpdateUploadedFileRelationshipsRequest request);
    const string UpdateUploadedFileRelationshipsAsyncPath = "api/Uploads/Relationships";

}

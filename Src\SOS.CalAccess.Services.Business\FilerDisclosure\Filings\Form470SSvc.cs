using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Common.Utility;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Filings;

#pragma warning disable S107 // Methods should not have too many parameters
/// <inheritdoc /> 
public class Form470SSvc(IFilingRepository filingRepository,
    IRegistrationRepository registrationRepository,
    IDecisionsSvc decisionsSvc,
    INotificationSvc notificationSvc,
    IAuthorizationSvc authorizationSvc,
    IUserMaintenanceSvc userMaintenanceSvc,
    IDateTimeSvc dateTimeSvc,
    IFilerSvc filerSvc) : IForm470SSvc
#pragma warning restore S107 // Methods should not have too many parameters
{
    private readonly IFilingRepository _filingRepository = filingRepository;
    private readonly IRegistrationRepository _registrationRepository = registrationRepository;
    private readonly IDecisionsSvc _decisionsSvc = decisionsSvc;
    private readonly INotificationSvc _notificationSvc = notificationSvc;
    private readonly IAuthorizationSvc _authorizationSvc = authorizationSvc;
    private readonly IFilerSvc _filerSvc = filerSvc;

    /// <inheritdoc /> 
    public async Task<CandidateIntentionStatement470S> GetCisWithElection470SByFilerId(long id)
    {
        var candidateIntentionStatement = await _registrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(id);

        if (candidateIntentionStatement is null)
        {
            return new CandidateIntentionStatement470S();
        }

        return MapToCandidateIntentionStatement470S(candidateIntentionStatement);
    }

    /// <inheritdoc />
    public async Task<Form470SOverviewResponse> GetForm470SOverview(long id)
    {
        var filing = await _filingRepository.GetForm470SById(id);
        if (filing == null)
        {
            return new Form470SOverviewResponse();
        }

        var candidateIntentionStatement = await _registrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filing.FilerId);
        if (candidateIntentionStatement == null)
        {
            return new Form470SOverviewResponse();
        }

        return new Form470SOverviewResponse
        {
            CandidateIntentionStatement470S = MapToCandidateIntentionStatement470S(candidateIntentionStatement),
            Form470SFiling = MapToForm470SFiling(filing)
        };
    }

    public async Task<ValidatedForm470SResponseDto> SubmitCandidateSupplementForEfile(CandidateStatementSupplementSubmissionDto submission)
    {
        var validationErrors = new List<WorkFlowError> { };

        // dtb: test for attestation
        var filing = submission.CandidateStatementSupplement!;

        if (submission.Attestation == null)
        {
            throw new InvalidOperationException(nameof(submission.Attestation));
        }

        // Call Decisions to validate business rule
        DecisionsForm470S decisionsForm470S = new()
        {
            ContributionsOrExpenditureOverOn = filing.ContributionsOrExpenditureOverOn
        };

        var response = await _decisionsSvc.InitiateWorkflow<DecisionsForm470S, DecisionsForm470SResponse>
            (DecisionsWorkflow.Form470SRuleSet, decisionsForm470S, checkRequiredFields: submission.IsSubmission);

        validationErrors.AddRange(response?.Result ?? new List<WorkFlowError> { });

        if (validationErrors.Count > 0)
        {
            return new ValidatedForm470SResponseDto(null, false, validationErrors, FilingStatus.Cancelled.Id);
        }
        else
        {
            await _filingRepository.Create(filing);

            filing.OriginalId = filing.Id;
            filing.ApprovedAt = DateTime.UtcNow.ToDefaultTimeZone();

            await _filingRepository.Update(filing);
        }

        return new ValidatedForm470SResponseDto(filing.Id, true, validationErrors, filing.StatusId);
    }

    /// <inheritdoc />
    public async Task<ValidatedForm470SResponseDto> CreateOfficeHolderCandidateSupplementFormFiling(Form470SFilingRequest form470SFilingRequest)
    {
        var validationErrors = new List<WorkFlowError> { };

        // Call Decisions to validate business rule
        DecisionsForm470S decisionsForm470S = new()
        {
            ContributionsOrExpenditureOverOn = form470SFilingRequest.ContributionsOrExpenditureOverOn
        };

        var decisionResponse = await _decisionsSvc.InitiateWorkflow<DecisionsForm470S, DecisionsForm470SResponse>(DecisionsWorkflow.Form470SRuleSet, decisionsForm470S, checkRequiredFields: form470SFilingRequest.IsSubmission);
        validationErrors.AddRange(decisionResponse?.Result ?? new List<WorkFlowError> { });

        if (validationErrors.Count > 0)
        {
            return new ValidatedForm470SResponseDto(validationErrors);
        }

        var filing = new OfficeHolderCandidateSupplementForm
        {
            FilerId = form470SFilingRequest.FilerId,
            ContributionsOrExpenditureOverOn = form470SFilingRequest.ContributionsOrExpenditureOverOn,
            Version = 0,
            StatusId = FilingStatus.Draft.Id
        };

        if (form470SFilingRequest.IsSubmission)
        {
            filing.StatusId = FilingStatus.Accepted.Id;
            filing.SubmittedDate = dateTimeSvc.GetCurrentDateTime();
            filing.ApprovedAt = dateTimeSvc.GetCurrentDateTime();
        }

        var createdEntity = await _filingRepository.Create(filing);

        createdEntity.OriginalId = createdEntity.Id;

        await _filingRepository.Update(filing);

        if (decisionResponse?.Result?.Count == 0 && createdEntity.Id > 0)
        {
            await _notificationSvc.SendDecisionsNotifications(decisionResponse.Notifications, form470SFilingRequest.FilerId);
        }

        return new ValidatedForm470SResponseDto(createdEntity.Id, true, validationErrors, createdEntity.StatusId);

    }

    /// <inheritdoc />
    public async Task<ValidatedForm470SResponseDto> UpdateOfficeHolderCandidateSupplementFormFiling(Form470SFilingRequest form470SFilingRequest)
    {
        var validationErrors = new List<WorkFlowError> { };

        var existing470SFiling = await _filingRepository.GetForm470SById(form470SFilingRequest.FilingId!.Value)
            ?? throw new InvalidOperationException($"Unable to find filing record to update. FilingId = {form470SFilingRequest.FilingId}");

        // Get Filer User of the current user to send notifications.
        var userId = await _authorizationSvc.GetInitiatingUserId();
        var filerUser = await _filerSvc.GetFilerUserByUserIdAsync(form470SFilingRequest.FilerId, userId.GetValueOrDefault())
            ?? throw new InvalidOperationException($"No filer user found with ID {form470SFilingRequest.FilerId}.");

        // Call Decisions to validate business rule.
        DecisionsForm470S decisionsForm470S = new()
        {
            ContributionsOrExpenditureOverOn = form470SFilingRequest.ContributionsOrExpenditureOverOn
        };

        var decisionResponse = await _decisionsSvc.InitiateWorkflow<DecisionsForm470S, DecisionsForm470SResponse>(DecisionsWorkflow.Form470SRuleSet, decisionsForm470S, checkRequiredFields: form470SFilingRequest.IsSubmission);
        validationErrors.AddRange(decisionResponse?.Result ?? new List<WorkFlowError> { });

        if (validationErrors.Count > 0)
        {
            return new ValidatedForm470SResponseDto(validationErrors);
        }

        existing470SFiling.ContributionsOrExpenditureOverOn = form470SFilingRequest.ContributionsOrExpenditureOverOn;

        if (form470SFilingRequest.IsSubmission)
        {
            existing470SFiling.StatusId = FilingStatus.Accepted.Id;
            existing470SFiling.SubmittedDate = dateTimeSvc.GetCurrentDateTime();
            existing470SFiling.ApprovedAt = dateTimeSvc.GetCurrentDateTime();
        }

        var updatedEntity = await _filingRepository.Update(existing470SFiling);

        if (decisionResponse?.Result?.Count == 0 && updatedEntity.Id > 0)
        {
            await _notificationSvc.SendDecisionsNotifications(decisionResponse.Notifications, form470SFilingRequest.FilerId);
        }

        return new ValidatedForm470SResponseDto(updatedEntity.Id, true, validationErrors, updatedEntity.StatusId);
    }

    /// <inheritdoc />
    public async Task<long> GetFilerIdOfLatestAcceptedCis()
    {
        var user = await userMaintenanceSvc.GetCurrentUser();
        var filer = await registrationRepository.GetFilerOfLatestAcceptedCisRegistration(user.Id);
        if (filer is null)
        {
            return 0;
        }
        return filer.Id;
    }

    /// <inheritdoc />
    public async Task<long> GetLatestRegistrationIdByFilerId(long id)
    {
        var cis = await registrationRepository.GetLatestAcceptedCisRegistrationByFilerId(id);
        if (cis is null)
        {
            return 0;
        }
        return cis.Id;
    }
    #region Private

    private static Form470SFiling? MapToForm470SFiling(OfficeHolderCandidateSupplementForm officeHolderCandidateSupplementForm)
    {
        return new Form470SFiling
        {
            Id = officeHolderCandidateSupplementForm.Id,
            FilerId = officeHolderCandidateSupplementForm.FilerId,
            Status = officeHolderCandidateSupplementForm.StatusId,
            ContributionsOrExpenditureOverOn = officeHolderCandidateSupplementForm.ContributionsOrExpenditureOverOn,
            Submitted = officeHolderCandidateSupplementForm.SubmittedDate,
            Version = officeHolderCandidateSupplementForm.Version,
        };
    }

    private static CandidateIntentionStatement470S MapToCandidateIntentionStatement470S(CandidateIntentionStatement candidateIntentionStatement)
    {
        var phoneNumber = Form470Shared.GetPhoneNumber(candidateIntentionStatement.PhoneNumberList);
        var faxNumber = Form470Shared.GetFaxNumber(candidateIntentionStatement.PhoneNumberList);

        var response = new CandidateIntentionStatement470S
        {
            RegistrationId = candidateIntentionStatement.Id,
            FilerId = candidateIntentionStatement.FilerId,
            Name = candidateIntentionStatement.Name,
            EmailAddress = candidateIntentionStatement.Email,
            OfficeSought = candidateIntentionStatement.ElectionOfficeSought,
            Jurisdiction = candidateIntentionStatement.ElectionJurisdiction,
            DistrictNumber = candidateIntentionStatement.ElectionDistrictNumber,
            ElectionDate = candidateIntentionStatement.ElectionRace?.Election?.ElectionDate,
            AddressDto = Form470Shared.SetCandidateAddress(candidateIntentionStatement.AddressList),
            PhoneNumber = phoneNumber,
            FaxNumber = faxNumber
        };

        return response;
    }
    #endregion
}

namespace SOS.CalAccess.FilerPortal.Models.Transactions;

/// <summary>
/// This class is used as the model for the lobbying entity search partial view
/// </summary>
public class LobbyistSearchViewModel
{
    /// <summary>
    /// Gets or sets FilerId
    /// Used in edit/prefilling cases.
    /// </summary>
    public long FilerId { get; set; }

    /// <summary>
    /// Gets or sets ContactId
    /// Used in edit/prefilling cases.
    /// Used for setting the selected contact ID (hidden input), which identifies the exact
    /// contact selected during the search.
    /// </summary>
    public long? ContactId { get; set; }

    /// <summary>
    /// Gets or sets RegistrationFilingId
    /// Used in edit/prefilling cases.
    /// Used for setting the selected contact ID (hidden input), which identifies the exact
    /// contact selected during the search.
    /// </summary>
    public long? RegistrationFilingId { get; set; }

    /// <summary>
    /// Gets or sets ContactName
    /// Used in edit/prefilling cases.
    /// Used for setting the text within the ContactSearch input (visible text).
    /// </summary>
    public string? ContactName { get; set; }

    /// <summary>
    /// Gets or sets Lobbying Employer or Firm Id
    /// Used lobbyist registration process.
    /// </summary>
    public long? LobbyistId { get; set; }

    /// <summary>
    /// Gets or sets Required
    /// Used for configuring the component to set the field as required.
    /// </summary>
    public bool Required { get; set; }

    /// <summary>
    /// Gets or sets the action name used for search
    /// </summary>
    public string SearchActionName { get; set; } = "SearchLobbyistByIdOrName";

    /// <summary>
    /// Gets or sets the search div ID
    /// </summary>
    public string SearchDivId { get; set; } = "lobbyistSearch";

    /// <summary>
    /// Gets or sets the search label key for localization
    /// </summary>
    public string SearchLabelKey { get; set; } = "LobbyistSearch";

    /// <summary>
    /// Gets or sets the search label text
    /// </summary>
    public string SearchLabelText { get; set; } = "Look up lobbyist by ID# or name";

    /// <summary>
    /// Gets or sets the search input key for localization
    /// </summary>
    public string SearchInputKey { get; set; } = "LobbyistSearch";

    /// <summary>
    /// Gets or sets the search input label
    /// </summary>
    public string SearchInputLabel { get; set; } = "Lobbyist Search";
}

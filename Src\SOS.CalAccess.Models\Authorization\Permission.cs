using System.Diagnostics.CodeAnalysis;
using SOS.CalAccess.Models.Common;

namespace SOS.CalAccess.Models.Authorization;

[SuppressMessage("Naming", "CA1711:Identifiers should not have incorrect suffix", Justification = "Permission used in domain context")]
public class Permission : IIdentifiable<long>
{
#pragma warning disable CA1707 // Identifiers should not contain underscores

    #region Common Permissions
    public static readonly Permission ReferenceData_Retrieve = new() { Id = 1, Name = nameof(ReferenceData_Retrieve), Description = "Allow user to retrieve reference data such as elections, offices, etc" };
    public static readonly Permission Candidate_SearchByName = new() { Id = 2, Name = nameof(Candidate_SearchByName), Description = "Allow user to retrieve candidate information based on search of candidates name" };
    public static readonly Permission Committee_SearchByIdOrName = new() { Id = 3, Name = nameof(Committee_SearchByIdOrName), Description = "Allow user to retrieve committee information based on search of committee ID or name" };
    public static readonly Permission Filer_AuthorizedUsers_View = new() { Id = 500, Name = nameof(Filer_AuthorizedUsers_View), Description = "Allow user to view the linkage dashboard" };
    public static readonly Permission Filer_AuthorizedUsers_Manage = new() { Id = 501, Name = nameof(Filer_AuthorizedUsers_Manage), Description = "Allow user to terminate existing linkages, send linkage requests, and approve/reject incoming requests for the filer" };
    #endregion

    // Registration Permissions
    #region Registration Permissions - Candidate (501)
    public static readonly Permission Registration_Candidate_Create = new() { Id = 1001, Name = nameof(Registration_Candidate_Create), Description = "Allow the user to create new a draft candidate registration" };
    public static readonly Permission Registration_Candidate_Edit = new() { Id = 1002, Name = nameof(Registration_Candidate_Edit), Description = "Allow the user to edit a draft candidate registration or amendment" };
    public static readonly Permission Registration_Candidate_Amend = new() { Id = 1003, Name = nameof(Registration_Candidate_Amend), Description = "Allow the user to create an amendment to a candidate registration for the filer" };
    public static readonly Permission Registration_Candidate_Attest = new() { Id = 1004, Name = nameof(Registration_Candidate_Attest), Description = "Allow the user to attest to a candidate registration" };
    public static readonly Permission Registration_Candidate_Terminate = new() { Id = 1005, Name = nameof(Registration_Candidate_Terminate), Description = "Allow the user to terminate a candidate registration for the filer" };
    public static readonly Permission Registration_Candidate_Withdraw = new() { Id = 1006, Name = nameof(Registration_Candidate_Withdraw), Description = "Allow the user to withdraw a candidate registration for the filer" };
    #endregion

    #region Registration Permissions - Slate Mailer Organization (400)
    public static readonly Permission Registration_SlateMailerOrganization_Create = new() { Id = 2001, Name = nameof(Registration_SlateMailerOrganization_Create), Description = "Allow the user to create a draft slate mailer organization registration" };
    public static readonly Permission Registration_SlateMailerOrganization_Search = new() { Id = 2002, Name = nameof(Registration_SlateMailerOrganization_Search), Description = "Allow the user to search slate mailer organization registration" };
    public static readonly Permission Registration_SlateMailerOrganization_View = new() { Id = 2003, Name = nameof(Registration_SlateMailerOrganization_View), Description = "Allow the user to view a slate mailer organization registration" };
    public static readonly Permission Registration_SlateMailerOrganization_Edit = new() { Id = 2004, Name = nameof(Registration_SlateMailerOrganization_Edit), Description = "Allow the user to edit a saved draft slate mailer organization registration" };
    public static readonly Permission Registration_SlateMailerOrganization_Amend = new() { Id = 2005, Name = nameof(Registration_SlateMailerOrganization_Amend), Description = "Allow the user to create an amendment to a slate mailer organization registration" };
    public static readonly Permission Registration_SlateMailerOrganization_Terminate = new() { Id = 2006, Name = nameof(Registration_SlateMailerOrganization_Terminate), Description = "Allow the user to terminate a slate mailer organization registration" };
    public static readonly Permission Registration_SlateMailerOrganization_Reinstate = new() { Id = 2007, Name = nameof(Registration_SlateMailerOrganization_Reinstate), Description = "Allow the user to reinstate a terminated slate mailer organization registration" };
    public static readonly Permission Registration_SlateMailerOrganization_Notify = new() { Id = 2008, Name = nameof(Registration_SlateMailerOrganization_Notify), Description = "Allow the user to notify other users linked to a slate mailer organization registration" };
    public static readonly Permission Registration_SlateMailerOrganization_CompleteTreasurerAcknowledgment = new() { Id = 2009, Name = nameof(Registration_SlateMailerOrganization_CompleteTreasurerAcknowledgment), Description = "Allow the user to complete the treasurer acknowledgment of a slate mailer organization registration" };
    public static readonly Permission Registration_SlateMailerOrganization_Attest = new() { Id = 2010, Name = nameof(Registration_SlateMailerOrganization_Attest), Description = "Allow the user to attest a slate mailer organization registration" };
    #endregion

    #region Registration Permissions - Lobbyist
    public static readonly Permission Registration_Lobbyist_Attest = new() { Id = 3004, Name = nameof(Registration_Lobbyist_Attest), Description = "Allow the user to attest a lobbyist registration" };
    #endregion

    // Disclosure Permissions
    #region Disclosure 401 Permissions - SlateMailerOrganization
    public static readonly Permission Disclosure_401_Notify = new() { Id = 2501, Name = nameof(Disclosure_401_Notify), Description = "Allow the user to notify other users linked to a slate mailer organization campaign statement" };
    public static readonly Permission Disclosure_401_Attest = new() { Id = 2502, Name = nameof(Disclosure_401_Attest), Description = "Allow the user to attest a slate mailer organization campaign statement" };

    #endregion

    //Administrator Permissions
    public static readonly Permission Admin_NotificationTemplate_Create = new() { Id = 9001, Name = nameof(Admin_NotificationTemplate_Create), Description = "Allow the user to create a notification template" };
    public static readonly Permission Admin_NotificationTemplate_Delete = new() { Id = 9002, Name = nameof(Admin_NotificationTemplate_Delete), Description = "Allow the user to delete a notification template" };
    public static readonly Permission Admin_NotificationTemplate_Edit = new() { Id = 9003, Name = nameof(Admin_NotificationTemplate_Edit), Description = "Allow the user to edit an existing notification template" };

#pragma warning restore CA1707 // Identifiers should not contain underscores

    [Documentation("Permission identifier.")]
    public required long Id { get; set; }

    [Documentation("Permission name.")]
    public required string Name { get; set; }

    [Documentation("Permission description.")]
    public required string Description { get; set; }

}

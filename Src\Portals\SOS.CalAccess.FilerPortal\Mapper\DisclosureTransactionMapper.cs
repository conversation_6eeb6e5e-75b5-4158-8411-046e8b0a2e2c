using SOS.CalAccess.FilerPortal.Models.Disclosure.SmoCampaignStatement;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using static SOS.CalAccess.FilerPortal.Constants.DisclosureConstants;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Mapper;
public static class DisclosureTransactionMapper
{
    public static PaymentReceivedRequest MapModelToPaymentReceivedRequest(SmoCampaignStatementTransactionEntryViewModel model)
    {
        var request = new PaymentReceivedRequest
        {
            ContactId = model.ContactId.GetValueOrDefault(),
            Position = model.Position ?? string.Empty,
            Amount = model.TransactionAmount.GetValueOrDefault(),
            TransactionDate = model.TransactionDate,
            Notes = model.Notes,
            Jurisdiction = model.Jurisdiction ?? string.Empty,
            AttachedFileGuidsJson = model.AttachedFileGuidsJson,
            UnitemizedAmount = model.UnitemizedAmount,
            IsCumulativeAmount = model.IsIncludePreviouslyUnitemizedAmount,
            CumulativeAmountToDate = model.CumulativeAmountToDate
        };

        if (model.PertainsTo == CandidateOrMeasure.Candidate)
        {
            request.StanceOnCandidate = MapModelToStanceOnCandidate(model);
        }
        else
        {
            request.StanceOnBallotMeasure = MapModelToStanceOnBallotMeasure(model);
        }

        return request;
    }

    public static PaymentMadeRequest MapModelToPaymentMadeRequest(SmoCampaignStatementTransactionEntryViewModel model)
    {
        if (model.IsPaidByAgentOrContractor)
        {
            return new PaymentMadeByAgentOrIndependentContractorRequest
            {
                ContactId = model.ContactId.GetValueOrDefault(),
                Amount = model.TransactionAmount.GetValueOrDefault(),
                TransactionDate = model.TransactionDate,
                Notes = model.Notes,
                CodeId = model.CodeId.GetValueOrDefault(),
                Description = model.Description ?? string.Empty,
                IsPaidByAgentOrIndependentContractor = model.IsPaidByAgentOrContractor,
                AgentOrIndependentContractorName = model.AgentOrIndependentContractorName ?? string.Empty,
                AttachedFileGuidsJson = model.AttachedFileGuidsJson,
            };
        }

        return new PaymentMadeRequest
        {
            ContactId = model.ContactId.GetValueOrDefault(),
            Amount = model.TransactionAmount.GetValueOrDefault(),
            TransactionDate = model.TransactionDate,
            Notes = model.Notes,
            CodeId = model.CodeId.GetValueOrDefault(),
            Description = model.Description ?? string.Empty,
            AttachedFileGuidsJson = model.AttachedFileGuidsJson,
        };
    }

    public static PersonReceiving1000OrMoreRequest MapModelToPersonReceivingRequest(SmoCampaignStatementTransactionEntryViewModel model)
    {
        return new PersonReceiving1000OrMoreRequest
        {
            ContactId = model.ContactId.GetValueOrDefault(),
            Notes = model.Notes,
            Amount = model.TransactionAmount.GetValueOrDefault(),
            AttachedFileGuidsJson = model.AttachedFileGuidsJson,
        };
    }

    public static DisclosureWithoutPaymentReceivedDto MapModelToDisclosureWithoutPaymentReceivedDto(SmoCampaignStatementTransactionEntryViewModel model)
    {
        var dto = new DisclosureWithoutPaymentReceivedDto
        {
            Id = model.DisclosureWithoutPaymentId,
            Position = model.Position ?? string.Empty,
            Jurisdiction = model.Jurisdiction ?? string.Empty,
        };

        if (model.PertainsTo == CandidateOrMeasure.Candidate)
        {
            dto.StanceOnCandidate = MapModelToStanceOnCandidate(model);
        }
        else
        {
            dto.StanceOnBallotMeasure = MapModelToStanceOnBallotMeasure(model);
        }

        return dto;
    }

    public static StanceOnCandidateDto MapModelToStanceOnCandidate(SmoCampaignStatementTransactionEntryViewModel model)
    {
        return new StanceOnCandidateDto
        {
            CandidateId = model.CandidateId,
            FirstName = model.CandidateFirstName,
            LastName = model.CandidateLastName,
            MiddleName = model.CandidateMiddleName,
            OfficeSought = model.OfficeSought,
            Jurisdiction = model.JurisdictionName,
            District = model.District,
        };
    }

    public static StanceOnBallotMeasureDto MapModelToStanceOnBallotMeasure(SmoCampaignStatementTransactionEntryViewModel model)
    {
        return new StanceOnBallotMeasureDto
        {
            BallotMeasureId = model.BallotMeasureId,
            Jurisdiction = model.JurisdictionName,
            BallotLetter = model.BallotLetter,
            Title = model.BallotMeasureTitle,
        };
    }
}

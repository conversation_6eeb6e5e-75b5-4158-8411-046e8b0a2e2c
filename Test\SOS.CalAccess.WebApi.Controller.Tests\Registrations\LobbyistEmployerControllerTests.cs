using Microsoft.AspNetCore.Authorization;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.WebApi.Registrations;

namespace SOS.CalAccess.WebApi.Tests.Registrations;

[TestFixture]
public class LobbyistEmployerControllerTests
{
    private IAuthorizationService _mockAuthorizationService;
    private ILobbyistEmployerRegistrationSvc _mockLobbyistEmployerRegistrationSvc;
    private LobbyistEmployerController _controller;

    [SetUp]
    public void SetUp()
    {
        _mockAuthorizationService = Substitute.For<IAuthorizationService>();
        _mockLobbyistEmployerRegistrationSvc = Substitute.For<ILobbyistEmployerRegistrationSvc>();
        _controller = new LobbyistEmployerController(
            _mockAuthorizationService,
            _mockLobbyistEmployerRegistrationSvc);
    }

    [Test]
    public async Task GetLobbyistEmployer_ReturnsLobbyistEmployerResponseDto_WhenFound()
    {
        // Arrange
        long id = 1;
        var lobbyistEmployer = new LobbyistEmployer
        {
            Id = id,
            Name = "Test Lobbyist Employer",
            Email = "<EMAIL>",
            StatusId = 1,
            FilerId = 101,
            Version = 1,
            EmployerName = "Test Employer",
            EmployerType = "Business",
            BusinessActivity = "Lobbying",
            BusinessDescription = "Professional lobbying services",
            InterestType = "Business",
            IndustryDescription = "Consulting",
            IndustryPortion = "Government Relations",
            NumberOfMembers = 5,
            StateLegislatureLobbying = true,
            DateQualified = DateTime.Now,
            LegislativeSessionId = 2023
        };

        var expectedResponse = new LobbyistEmployerResponseDto(lobbyistEmployer);

        _mockLobbyistEmployerRegistrationSvc.GetLobbyistEmployer(id).Returns(expectedResponse);

        // Act
        var result = await _controller.GetLobbyistEmployer(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EqualTo(expectedResponse));
        await _mockLobbyistEmployerRegistrationSvc.Received(1).GetLobbyistEmployer(id);
    }

    [Test]
    public async Task GetLobbyistEmployer_ReturnsNull_WhenNotFound()
    {
        // Arrange
        long id = 999;
        _ = _mockLobbyistEmployerRegistrationSvc.GetLobbyistEmployer(id).Returns((LobbyistEmployerResponseDto?)null);

        // Act
        var result = await _controller.GetLobbyistEmployer(id);

        // Assert
        Assert.That(result, Is.Null);
        _ = await _mockLobbyistEmployerRegistrationSvc.Received(1).GetLobbyistEmployer(id);
    }

    [Test]
    public async Task GetLobbyistEmployer_PropagatesException_WhenServiceThrows()
    {
        // Arrange
        long id = 1;
        var expectedException = new KeyNotFoundException("Lobbyist Employer registration not found");

        _ = _mockLobbyistEmployerRegistrationSvc
            .GetLobbyistEmployer(id)
            .ThrowsForAnyArgs(expectedException);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _controller.GetLobbyistEmployer(id));

        Assert.That(ex.Message, Is.EqualTo(expectedException.Message));
        _ = await _mockLobbyistEmployerRegistrationSvc.Received(1).GetLobbyistEmployer(id);
    }

    [Test]
    public async Task GetLobbyistEmployerByFilerId_ReturnsLobbyistEmployerResponseDto_WhenFound()
    {
        // Arrange
        long filerId = 101;
        var lobbyistEmployer = new LobbyistEmployer
        {
            Id = 1,
            Name = "Test Lobbyist Employer",
            Email = "<EMAIL>",
            StatusId = 1,
            FilerId = filerId,
            Version = 1,
            EmployerName = "Test Employer",
            EmployerType = "Business",
            BusinessActivity = "Lobbying",
            BusinessDescription = "Professional lobbying services",
            InterestType = "Business",
            IndustryDescription = "Consulting",
            IndustryPortion = "Government Relations",
            NumberOfMembers = 5,
            StateLegislatureLobbying = true,
            DateQualified = DateTime.Now,
            LegislativeSessionId = 2023
        };

        var expectedResponse = new LobbyistEmployerResponseDto(lobbyistEmployer);

        _ = _mockLobbyistEmployerRegistrationSvc.GetLobbyistEmployerByFilerId(filerId).Returns(expectedResponse);

        // Act
        var result = await _controller.GetLobbyistEmployerByFilerId(filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EqualTo(expectedResponse));

        _ = await _mockLobbyistEmployerRegistrationSvc.Received(1).GetLobbyistEmployerByFilerId(filerId);
    }

    [Test]
    public async Task GetLobbyistEmployerByFilerId_ReturnsNull_WhenNotFound()
    {
        // Arrange
        long filerId = 999;
        _ = _mockLobbyistEmployerRegistrationSvc.GetLobbyistEmployerByFilerId(filerId).Returns((LobbyistEmployerResponseDto?)null);

        // Act
        var result = await _controller.GetLobbyistEmployerByFilerId(filerId);

        // Assert
        Assert.That(result, Is.Null);
        _ = await _mockLobbyistEmployerRegistrationSvc.Received(1).GetLobbyistEmployerByFilerId(filerId);
    }

    [Test]
    public async Task GetLobbyistEmployerByFilerId_PropagatesException_WhenServiceThrows()
    {
        // Arrange
        long filerId = 101;
        var expectedException = new KeyNotFoundException($"Lobbyist Employer registration with Filer ID {filerId} not found.");

        _ = _mockLobbyistEmployerRegistrationSvc
            .GetLobbyistEmployerByFilerId(filerId)
            .ThrowsForAnyArgs(expectedException);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _controller.GetLobbyistEmployerByFilerId(filerId));

        Assert.That(ex.Message, Is.EqualTo(expectedException.Message));
        _ = await _mockLobbyistEmployerRegistrationSvc.Received(1).GetLobbyistEmployerByFilerId(filerId);
    }

    [Test]
    public async Task GetLobbyistEmployerByName_ReturnsLobbyistEmployerResponseDto()
    {
        // Arrange
        var name = "Test Lobbyist Employer";
        var lobbyistEmployer = new LobbyistEmployer
        {
            Id = 999,
            Name = name,
            Email = "<EMAIL>",
            StatusId = 1,
            FilerId = 101,
            Version = 1,
            EmployerName = "Test Employer",
            EmployerType = "Business",
            BusinessActivity = "Lobbying",
            BusinessDescription = "Professional lobbying services",
            InterestType = "Business",
            IndustryDescription = "Consulting",
            IndustryPortion = "Government Relations",
            NumberOfMembers = 5,
            StateLegislatureLobbying = true,
            DateQualified = DateTime.Now,
            LegislativeSessionId = 2023
        };

        var expectedResponse = new LobbyistEmployerResponseDto(lobbyistEmployer);

        _mockLobbyistEmployerRegistrationSvc.GetLobbyistEmployerByName(name).Returns(expectedResponse);

        // Act
        var result = await _controller.GetLobbyistEmployerByName(name);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EqualTo(expectedResponse));

        await _mockLobbyistEmployerRegistrationSvc.Received(1).GetLobbyistEmployerByName(name);
    }

    [Test]
    public void CreateLobbyistEmployerRegistration_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var request = new LobbyistEmployerGeneralInfoRequest
        {
            EmployerName = "Test Name",
            FaxNumber = new PhoneNumberDto(),
        };

        _ = _mockLobbyistEmployerRegistrationSvc.CreateLobbyistEmployerRegistrationPage03(request);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _controller.CreateLobbyistEmployerRegistrationPage03(request));
    }

    [Test]
    public void UpdateLobbyistEmployerRegistration_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;
        var request = new LobbyistEmployerGeneralInfoRequest
        {
            EmployerName = "Test Name",
            FaxNumber = new PhoneNumberDto(),
        };

        _ = _mockLobbyistEmployerRegistrationSvc.UpdateLobbyistEmployerRegistrationPage03(id, request);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _controller.UpdateLobbyistEmployerRegistrationPage03(id, request));
    }

    [Test]
    public void UpdateLobbyistEmployerRegistrationPage04_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;
        var request = new LobbyistEmployerStateAgenciesRequest
        {
            Agencies = new(),
            IsLobbyingStateLegislature = true
        };

        _ = _mockLobbyistEmployerRegistrationSvc.UpdateLobbyistEmployerRegistrationPage04(id, request);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _controller.UpdateLobbyistEmployerRegistrationPage04(id, request));
    }

    [Test]
    public void UpdateLobbyistEmployerRegistrationPage05_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;
        var request = new LobbyistEmployerLobbyingInterestsRequest
        {
            NatureAndPurpose = "Some lobbying interest"
        };

        _ = _mockLobbyistEmployerRegistrationSvc.UpdateLobbyistEmployerRegistrationPage05(id, request);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _controller.UpdateLobbyistEmployerRegistrationPage05(id, request));
    }


    [Test]
    public void CanceLobbyistEmployerRegistration_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;

        _ = _mockLobbyistEmployerRegistrationSvc.CancelLobbyistEmployerRegistration(id);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _controller.CancelLobbyistEmployerRegistration(id));
    }

    [Test]
    public async Task SearchLobbyistByIdOrName_ReturnsExpectedResults()
    {
        // Arrange
        var query = "Alpha";
        var expectedResults = new List<LobbyistSearchResultDto>
    {
        new() {
            Id = 1,
            Name = "Test Lobbyist Employer Alpha",
            IsDisabled = false
        },
        new() {
            Id = 2,
            Name = "Test Lobbying Firm Alpha",
            IsDisabled = false
        }
    };

        _ = _mockLobbyistEmployerRegistrationSvc.SearchLobbyistByIdOrName(query)
            .Returns(expectedResults);

        // Act
        var result = await _controller.SearchLobbyistByIdOrName(query);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(expectedResults.Count));
            Assert.That(result.Select(r => r.Id), Is.EquivalentTo(expectedResults.Select(e => e.Id)));
            Assert.That(result.Select(r => r.Name), Is.EquivalentTo(expectedResults.Select(e => e.Name)));
            Assert.That(result.Select(r => r.IsDisabled), Is.EquivalentTo(expectedResults.Select(e => e.IsDisabled)));

        });
    }
}

using SOS.CalAccess.Models.Common;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Phone number information.
/// </summary>
public sealed class PhoneNumberDto
{
    /// <summary>
    /// Constructor
    /// </summary>
    public PhoneNumberDto() { }

    public PhoneNumberDto(PhoneNumber? phoneNumber)
    {
        if (phoneNumber != null)
        {
            Id = phoneNumber.Id;
            InternationalNumber = phoneNumber.InternationalNumber;
            CountryCode = phoneNumber.CountryCode;
            SelectedCountry = phoneNumber.CountryId;
            Number = phoneNumber.Number;
            Extension = phoneNumber.Extension;
            Type = phoneNumber.Type;
        }
    }

    public PhoneNumber CreateModel(IReferenceDataSvc referenceDataSvc)
    {
        var model = new PhoneNumber() { Number = Number ?? string.Empty, Type = Type ?? string.Empty, CountryCode = CountryCode ?? string.Empty };
        UpdateModel(referenceDataSvc, model);
        return model;
    }

    public void UpdateModel(IReferenceDataSvc referenceDataSvc, PhoneNumber model)
    {
        model.InternationalNumber = InternationalNumber ?? false;
        if (SelectedCountry == -1)
        {
            model.CountryId = null;
            //leave phoneNumber.CountryCode set to current value
        }
        else if (SelectedCountry == null)
        {
            model.CountryId = null;
            model.CountryCode = null;
        }
        else
        {
            var country = referenceDataSvc.GetCountryById(SelectedCountry.Value).Result;
            if (country is not null)
            {
                model.CountryId = SelectedCountry;
                model.CountryCode = country?.PhoneCountryCode;
            }
        }

        model.Number = Number ?? string.Empty;
        model.Extension = Extension;
        model.Type = Type ?? string.Empty;
    }

    public override string ToString()
    {
        if (Id is null)
        {
            return string.Empty;
        }
        else
        {
            return $"{CountryCode}{Number}";
        }
    }

    /// <summary>
    /// Gets or sets the unique identifier of the phone number.
    /// </summary>
    public long? Id { get; set; }

    /// <summary>
    /// Gets or sets the International Number.
    /// </summary>
    public bool? InternationalNumber { get; set; }

    /// <summary>
    /// Gets or sets the Country Code.
    /// </summary>
    public string? CountryCode { get; set; }

    /// <summary>
    /// Gets or sets the Phone Number.
    /// </summary>
    public string? Number { get; set; }

    /// <summary>
    /// Gets or sets the Extension Number.
    /// </summary>
    public string? Extension { get; set; }

    /// <summary>
    /// Gets or sets the Type.
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// Gets or sets the selected country code for this record.
    /// </summary>
    public long? SelectedCountry { get; set; }

    /// <summary>
    /// Gets or sets the Country Id
    /// </summary>
    public long? CountryId { get; set; }

    /// <summary>
    /// Gets or sets the primary phone number
    /// </summary>
    public bool SetAsPrimaryPhoneNumber { get; set; } = true;

}

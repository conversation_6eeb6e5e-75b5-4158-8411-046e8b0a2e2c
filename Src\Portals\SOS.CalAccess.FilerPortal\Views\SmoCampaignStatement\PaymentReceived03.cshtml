@using SOS.CalAccess.Models.Common
@using SOS.CalAccess.UI.Common
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.FilerPortal.Models.Disclosure.SmoCampaignStatement
@using SOS.CalAccess.UI.Common.Enums
@using SOS.CalAccess.UI.Common.Localization;
@using SOS.CalAccess.UI.Common.Constants;
@inject IHtmlLocalizer<SharedResources> Localizer

@model SmoCampaignStatementTransactionEntryViewModel
@{
    // FR-CF-PaymentReceived-3
    var buttonConfig = new ButtonBarModel
            {
                LeftButtons = new List<ButtonConfig>
        {
            ButtonBarModel.DefaultPrevious,
            ButtonBarModel.DefaultContinue,
        },
                RightButtons = new List<ButtonConfig>
        {
            new ()
            {
                Type = ButtonType.Custom,
                HtmlContent = await Html.PartialAsync("_CancelPaymentEntryButton"),
            },
        },
            };
    var allowedFileExtension = DisclosureConstants.Transaction.AllowedFileExtension;
}

<div class="p-5">
    @Html.StepHeader(SharedLocalizer, ResourceConstants.SmoCampaignStatementEnterTransaction)
    <div class="p-5">
        <div class="mb-5">
            @Html.StepHeader(SharedLocalizer, ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentTitle)

            @Html.TextBlock(SharedLocalizer, ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentBody)
        </div>

        @using (Html.BeginForm("PaymentReceived03", "SmoCampaignStatement", FormMethod.Post))
        {
            @Html.AntiForgeryToken()
            @Html.HiddenFor(m => m.ContactId)
            @Html.HiddenFor(m => m.TransactionId)
            @Html.HiddenFor(m => m.AttachedFileGuidsJson)

            <partial name="_SmoDisclosureStance" model="@Model" />

            <div class="mb-3 col-sm-6">
                @Html.CurrencyInputFor(SharedLocalizer, m => m.TransactionAmount, ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentAmountReceived, true, true, widthInEmUnits: null)
            </div>

            <div class="mb-3 col-sm-6">
                @Html.DatePickerFor(
                         SharedLocalizer,
                         m => m.TransactionDate,
                         Localizer[ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentDateReceived].Value,
                         minDate: null, maxDate: DateTime.Now,
                         format: "MM/dd/yyyy",
                         isRequired: true,
                         isReadOnly: false,
                         cssClass: "w-100",
                         placeholderResourceKey: "MM/DD/YYYY"
                         )
            </div>

            <div class="mb-3 col-sm-6">
                @Html.FileUploader(
                         SharedLocalizer,
                         allowedFileExtension,
                         relationshipId: Model.TransactionId,
                         relationshipType: RelationshipType.DisclosureTransaction.ToString(),
                         titleResourceKey: ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentAttachFiles,
                         instructionsResourceKey: null,
                         onUploadSuccess: "handlePaymentReceivedUploadedFile",
                         handleMultipleFiles: true
                         )
            </div>

            <div class="mb-3 col-sm-6">
                @Html.TextAreaFor(Localizer, m => m.Notes!, ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentNotes)
            </div>

            <div class="mt-5">
                <partial name="_ButtonBar" model="buttonConfig" />
            </div>
        }
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const transactionAmountField = document.getElementById('@Html.IdFor(m => m.TransactionAmount)');

        function disableListener(e) {
            e.stopImmediatePropagation();
        }

        // Disable mousewheel to changing value
        transactionAmountField.addEventListener("mousewheel", disableListener);
    })

    function handlePaymentReceivedUploadedFile(args) {
        try {
            // Validate response value
            let paymentReceivedOriginalFileName = args.file.name;
            if (!args) return;

            const event = args.e;
            if (!event) return;

            // Get current uploaded files
            let paymentReceivedAttachmentFiles = [];
            const paymentReceivedAttachedFileElement = document.getElementById('@Html.IdFor(m => m.AttachedFileGuidsJson)');
            if (paymentReceivedAttachedFileElement.value) {
                paymentReceivedAttachmentFiles = JSON.parse(paymentReceivedAttachedFileElement.value);
            }
            let paymentReceivedFileName = '';

            // Add or remove uploaded file base on action
            switch (args.operation) {
                case "@CommonConstants.FileAction.Upload":
                    paymentReceivedFileName = event.target.response;
                    if (!paymentReceivedFileName) return;
                    if (!paymentReceivedAttachmentFiles.includes(paymentReceivedFileName)) {
                        paymentReceivedAttachmentFiles.push(paymentReceivedFileName);
                    }
                    break;

                case "@CommonConstants.FileAction.Remove":
                    paymentReceivedFileName = paymentReceivedOriginalFileName;
                    if (!paymentReceivedFileName) return;
                    paymentReceivedAttachmentFiles = paymentReceivedAttachmentFiles.filter(f => f !== paymentReceivedFileName);
                    break;

                default:
                    break;
                }

            // Update current uploaded files
            paymentReceivedAttachedFileElement.value = JSON.stringify(paymentReceivedAttachmentFiles);

        } catch (e) {

        }
    }
</script>

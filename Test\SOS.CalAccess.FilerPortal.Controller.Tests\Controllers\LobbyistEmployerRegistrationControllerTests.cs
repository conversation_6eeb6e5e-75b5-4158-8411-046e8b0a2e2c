using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Localization;
using Moq;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;
public class LobbyistEmployerRegistrationControllerTests
{
    private Mock<IStringLocalizer<SharedResources>> _localizerMock;
    private Mock<ILobbyistEmployerRegistrationCtlSvc> _lobbyistEmployerRegistrationCtlSvcMock;
    private Mock<ILobbyistEmployerRegistrationSvc> _lobbyistEmployerRegistrationSvcMock;
    private Mock<ILobbyistRegistrationSvc> _lobbyistRegistrationSvcMock;
    private Mock<IToastService> _toastServiceMock;
    private Mock<IFilingsApi> _filingsApiMock;
    private Mock<IAccuMailValidatorService> _accuMailValidatorServiceMock;
    private Mock<IReferenceDataApi> _referenceDataApiMock;
    private LobbyistEmployerRegistrationController _controller;

    [SetUp]
    public void Setup()
    {
        _localizerMock = new Mock<IStringLocalizer<SharedResources>>();
        _toastServiceMock = new Mock<IToastService>();
        _lobbyistEmployerRegistrationCtlSvcMock = new Mock<ILobbyistEmployerRegistrationCtlSvc>();
        _lobbyistEmployerRegistrationSvcMock = new Mock<ILobbyistEmployerRegistrationSvc>();
        _lobbyistRegistrationSvcMock = new Mock<ILobbyistRegistrationSvc>();
        _filingsApiMock = new Mock<IFilingsApi>();
        _accuMailValidatorServiceMock = new Mock<IAccuMailValidatorService>();
        _referenceDataApiMock = new Mock<IReferenceDataApi>();

        _localizerMock.Setup(x => x[It.IsAny<string>()])
            .Returns((string key) => new LocalizedString(key, key));

        _controller = new LobbyistEmployerRegistrationController(
            _lobbyistEmployerRegistrationCtlSvcMock.Object,
            _accuMailValidatorServiceMock.Object,
            _localizerMock.Object,
            _lobbyistEmployerRegistrationSvcMock.Object,
            _toastServiceMock.Object
        );
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    #region Other

    [Test]
    public void Index_ShouldRedirectToPage01()
    {
        // Act
        var result = _controller.Index() as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page01"));
    }

    [Test]
    public void Close_WithCancelAction_ShouldShowToastAndRedirect()
    {
        var result = _controller.Close(FormAction.Cancel) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Cancel_WithValidModelState_ShouldCallServiceAndRedirect()
    {
        long id = 1;
        var result = await _controller.Cancel(id) as RedirectToActionResult;

        _lobbyistEmployerRegistrationSvcMock.Verify(s => s.CancelLobbyistEmployerRegistration(id), Times.Once);
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Cancel_WithInvalidModelState_ShouldReturnNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid state");

        // Act
        var result = await _controller.Cancel(1);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void OnActionExecuting_ShouldInitializeViewData()
    {
        // Arrange
        var httpContext = new DefaultHttpContext();
        var routeData = new RouteData();
        var actionDescriptor = new ActionDescriptor();
        var actionContext = new ActionContext(httpContext, routeData, actionDescriptor);

        var context = new ActionExecutingContext(
            actionContext,
            new List<IFilterMetadata>(),
            new Dictionary<string, object?>(),
            _controller
        );

        // Act
        _controller.OnActionExecuting(context);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(_controller.ViewData.ContainsKey(LayoutConstants.Title), Is.True);
            Assert.That(_controller.ViewData[LayoutConstants.Title], Is.Not.Null);
        });
    }

    [Test]
    public void Close_WithInvalidModelState_ShouldReturnBadRequest()
    {
        // Arrange
        _controller.ModelState.AddModelError("error", "some error");

        // Act
        var result = _controller.Close(FormAction.Cancel);

        // Assert
        Assert.That(result, Is.InstanceOf<BadRequestResult>());
    }

    [Test]
    public void Edit_WhenModelStateIsInvalid_ShouldReturnNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Id", "Required");

        // Act
        var result = _controller.Edit(123);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void Edit_WhenModelStateIsValid_ShouldRedirectToPage03()
    {
        // Act
        var result = _controller.Edit(456) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page03"));
            Assert.That(result.RouteValues["Id"], Is.EqualTo(456));
        });
    }

    #endregion

    #region Page 01

    [Test]
    public void Page01_ShouldReturnView()
    {
        // Act
        var result = _controller.Page01();

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    #endregion

    #region Page 02

    [Test]
    public void Page02_Get_WithValidModelState_ShouldReturnViewWithModel()
    {
        // Act
        var result = _controller.Page02(123) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<LobbyistEmployerRegistrationBase>());
    }

    [Test]
    public void Page02_Get_WithInvalidModelState_ShouldRedirectToPage01()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid state");

        // Act
        var result = _controller.Page02(123) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page01"));
    }

    [Test]
    public void Page02_Post_WithContinueAction_ShouldRedirectToPage03()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationBase
        {
            Id = 789,
            Action = FormAction.Continue
        };

        // Act
        var result = _controller.Page02(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page03"));
        });
    }

    [Test]
    public void Page02_Post_PreviousAction_ShouldRedirectToPage01()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationBase
        {
            Id = 789,
            Action = FormAction.Previous
        };

        // Act
        var result = _controller.Page02(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page01"));
    }

    [Test]
    public void Page02_Post_InvalidAction_ShouldReturnViewWithError()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationBase
        {
            Id = 123,
            Action = (FormAction)999
        };

        // Act
        var result = _controller.Page02(model) as ViewResult;

        // Arrange
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.IsValid, Is.False);
        });
    }

    #endregion

    #region Page 03

    [Test]
    public async Task Page03_Get_WithValidModelState_ShouldReturnViewWithModel()
    {
        // Arrange
        var id = 123;

        var model = new LobbyistEmployerRegistrationStep01GenInfo
        {
            Id = id,
            Addresses = new List<AddressViewModel>
            {
                new() { Purpose = "Business", Country = "United States" },
                new() { Purpose = "Mailing" }
            },
            LegislativeSessionOptions = []
        };

        _ = _lobbyistEmployerRegistrationCtlSvcMock.Setup(x => x.GetStep01GenInfoViewModel(It.IsAny<long?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(model);

        // Act
        var result = await _controller.Page03(id, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<LobbyistEmployerRegistrationStep01GenInfo>());
    }

    [Test]
    public async Task Page03_Get_WithInvalidModelState_ShouldRedirectToPage02()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid state");

        // Act
        var result = await _controller.Page03(123, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page02"));
    }

    [Test]
    public async Task Page03_Post_WithContinueAction_ShouldRedirectToPage04()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep01GenInfo
        {
            Id = 789,
            Action = FormAction.Continue
        };

        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
            ValidationErrors = new List<CalAccess.Models.Common.WorkFlowError>()
        };

        _ = _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
            {
                new
                (
                    id: 1,
                    name: "Test Session",
                    startDate : DateTime.Now,
                    endDate: DateTime.Now.AddYears(1)
                )
            }));

        _ = _lobbyistEmployerRegistrationCtlSvcMock
                .Setup(api => api.SubmitStep01GenInfoViewModel(It.IsAny<LobbyistEmployerRegistrationStep01GenInfo>(), It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
                .ReturnsAsync(response);

        // Act
        var result = await _controller.Page03(_filingsApiMock.Object, model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page04"));
        });
    }

    [Test]
    public async Task Page03_Post_WithSaveAndCloseAction_ShouldRedirectToUnderConstruction()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep01GenInfo
        {
            Id = 789,
            Action = FormAction.SaveAndClose,
            BusinessAddress = new AddressViewModel(),
            MailingAddress = new AddressViewModel(),
            Email = "<EMAIL>",
            Name = "sample lobbyist employer",
            IsLobbyingCoalition = true,
            IsSameAsBusinessAddress = true,
            LegislativeSessionId = 1,
            LegislativeSessionOptions = [],
            FaxNumber = new(),
            PhoneNumber = new(),
            QualificationDate = DateTime.Now,
            Suggestions = [],
        };

        _filingsApiMock
        .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
        .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
        {
                           new
                           (
                               id: 1,
                               name: "Test Session",
                               startDate : DateTime.Now,
                               endDate: DateTime.Now.AddYears(1)
                           )
        }));

        // Act
        var result = await _controller.Page03(_filingsApiMock.Object, model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Page03_Post_PreviousAction_ShouldRedirectToPage02()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep01GenInfo
        {
            Id = 789,
            Action = FormAction.Previous
        };

        _filingsApiMock
        .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
        .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
        {
                           new
                           (
                               id: 1,
                               name: "Test Session",
                               startDate : DateTime.Now,
                               endDate: DateTime.Now.AddYears(1)
                           )
        }));

        // Act
        var result = await _controller.Page03(_filingsApiMock.Object, model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page02"));
    }

    [Test]
    public async Task Page03_Post_InvalidAction_ShouldReturnViewWithError()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep01GenInfo
        {
            Id = 123,
            Action = (FormAction)999
        };

        _filingsApiMock
        .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
        .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
        {
                   new
                   (
                       id: 1,
                       name: "Test Session",
                       startDate : DateTime.Now,
                       endDate: DateTime.Now.AddYears(1)
                   )
        }));

        // Act
        var result = await _controller.Page03(_filingsApiMock.Object, model) as ViewResult;

        // Arrange
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.IsValid, Is.False);
        });
    }

    #endregion

    #region Page 04

    [Test]
    public async Task Page04_Get_WithValidModelState_ShouldReturnViewWithModel()
    {
        // Arrange
        var id = 123;

        var model = new LobbyistEmployerRegistrationStep01Agencies
        {
            Id = id,
            Agencies = new Dictionary<long, string>(),
        };

        _ = _lobbyistEmployerRegistrationCtlSvcMock.Setup(x => x.GetStep01AgenciesViewModel(It.IsAny<long?>()))
            .ReturnsAsync(model);

        // Act
        var result = await _controller.Page04(id, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<LobbyistEmployerRegistrationStep01Agencies>());
    }

    [Test]
    public async Task Page04_Get_WithInvalidModelState_ShouldRedirectToPage03()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid state");

        // Act
        var result = await _controller.Page04(123, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page03"));
    }

    [Test]
    public async Task Page04_Post_PreviousAction_ShouldRedirectToPage03()
    {
        // Arrange
        var id = 789;
        var model = new LobbyistEmployerRegistrationStep01Agencies
        {
            Id = id,
            Action = FormAction.Previous,
            Agencies = [],
            StateLegislatureLobbied = false,
        };

        var response = new LobbyistEmployerRegistrationStep01GenInfo
        {
            Id = id,
            Addresses = new List<AddressViewModel>
            {
                new() { Purpose = "Business", Country = "United States" },
                new() { Purpose = "Mailing" }
            },
            LegislativeSessionOptions = []
        };

        _ = _lobbyistEmployerRegistrationCtlSvcMock.Setup(x => x.GetStep01GenInfoViewModel(It.IsAny<long?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.Page04(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page03"));
    }

    [Test]
    public async Task Page04_Post_ContinueAction_ShouldRedirectToPage05()
    {
        // Arrange
        var id = 789;
        var model = new LobbyistEmployerRegistrationStep01Agencies
        {
            Id = id,
            Action = FormAction.Continue
        };

        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
            ValidationErrors = new List<CalAccess.Models.Common.WorkFlowError>()
        };

        _ = _lobbyistEmployerRegistrationCtlSvcMock
        .Setup(api => api.SubmitStep01AgenciesViewModel(It.IsAny<LobbyistEmployerRegistrationStep01Agencies>(), It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
        .ReturnsAsync(response);

        // Act
        var result = await _controller.Page04(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page05"));
        });
    }

    [Test]
    public async Task Page04_Post_SaveAndCloseAction_ShouldRedirectToDashboard()
    {
        // Arrange
        var id = 789;
        var model = new LobbyistEmployerRegistrationStep01Agencies
        {
            Id = id,
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = await _controller.Page04(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Page04_Post_InvalidAction_ShouldReturnViewWithError()
    {
        // Arrange
        var id = 789;
        var model = new LobbyistEmployerRegistrationStep01Agencies
        {
            Id = id,
            Action = (FormAction)999
        };

        // Act
        var result = await _controller.Page04(model) as ViewResult;

        // Arrange
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.IsValid, Is.False);
        });
    }

    #endregion

    #region Page 05

    [Test]
    public async Task Page05_Get_WithValidModelState_ShouldReturnViewWithModel()
    {
        // Arrange
        var id = 123;

        var model = new LobbyistEmployerRegistrationStep01NatureInterests
        {
            Id = id,
            FilingInterestsDescription = null,
        };

        _ = _lobbyistEmployerRegistrationCtlSvcMock.Setup(x => x.GetStep01NatureInterestsViewModel(It.IsAny<long?>()))
            .ReturnsAsync(model);

        // Act
        var result = await _controller.Page05(id, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<LobbyistEmployerRegistrationStep01NatureInterests>());
    }

    [Test]
    public async Task Page05_Get_WithInvalidModelState_ShouldRedirectToPage04()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid state");

        // Act
        var result = await _controller.Page05(123, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page04"));
    }

    [Test]
    public async Task Page05_Post_PreviousAction_ShouldRedirectToPage04()
    {
        // Arrange
        var id = 789;
        var model = new LobbyistEmployerRegistrationStep01NatureInterests
        {
            Id = id,
            Action = FormAction.Previous,
        };

        // Act
        var result = await _controller.Page05(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page04"));
    }

    [Test]
    public async Task Page05_Post_ContinueAction_ShouldRedirectToUnderConstruction()
    {
        // Arrange
        var id = 789;
        var model = new LobbyistEmployerRegistrationStep01NatureInterests
        {
            Id = id,
            Action = FormAction.Continue
        };

        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
            ValidationErrors = new List<CalAccess.Models.Common.WorkFlowError>()
        };

        _ = _lobbyistEmployerRegistrationCtlSvcMock
        .Setup(api => api.SubmitStep01NatureInterestViewModel(It.IsAny<LobbyistEmployerRegistrationStep01NatureInterests>(), It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
        .ReturnsAsync(response);

        // Act
        var result = await _controller.Page05(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Step02LobbyistList"));
            Assert.That(result.ControllerName, Is.EqualTo("LobbyistEmployerRegistration"));
        });
    }

    [Test]
    public async Task Page05_Post_SaveAndCloseAction_ShouldRedirectToUnderConstruction()
    {
        // Arrange
        var id = 789;
        var model = new LobbyistEmployerRegistrationStep01NatureInterests
        {
            Id = id,
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = await _controller.Page05(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Page05_Post_InvalidAction_ShouldReturnViewWithError()
    {
        // Arrange
        var id = 789;
        var model = new LobbyistEmployerRegistrationStep01NatureInterests
        {
            Id = id,
            Action = (FormAction)999
        };

        // Act
        var result = await _controller.Page05(model) as ViewResult;

        // Arrange
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.IsValid, Is.False);
        });
    }

    #endregion

    #region In-house lobbyist
    [Test]
    public async Task LobbyistCreationOrModification_InvalidModalState_ShouldReturnNotFound()
    {
        // Arrange
        long id = 1;

        var model = new InHouseLobbyistViewModel
        {
            LobbyistEmployerRegistrationId = id
        };

        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.LobbyistCreationOrModification(id, null) as RedirectToActionResult;

        // Arrange
        Assert.That(result, Is.Null);
        Assert.Multiple(() =>
        {
            Assert.That(_controller.ModelState.IsValid, Is.False);
        });
    }

    [Test]
    public async Task LobbyistCreation_ValidModalState_ShouldReturnViewModel()
    {
        // Arrange
        long id = 1;

        var model = new InHouseLobbyistViewModel
        {
            LobbyistEmployerRegistrationId = id
        };

        // Act
        _lobbyistEmployerRegistrationCtlSvcMock.Setup(x => x.GetInHouseLobbyistViewModel(id, null))
                                                .ReturnsAsync(model);
        var result = await _controller.LobbyistCreationOrModification(id, null) as ViewResult;

        // Arrange
        Assert.That(result, Is.Not.Null);
        var resultModel = result.Model as InHouseLobbyistViewModel;
        Assert.Multiple(() =>
        {
            Assert.That(resultModel, Is.Not.Null);
            Assert.That(result.Model, Is.TypeOf<InHouseLobbyistViewModel>());
            Assert.That(model.LobbyistEmployerRegistrationId, Is.EqualTo(resultModel!.LobbyistEmployerRegistrationId));
        });
    }

    [Test]
    public async Task LobbyistModification_ValidModalState_ShouldReturnViewModel()
    {
        // Arrange
        long id = 1;
        long lobbyistRegistrationId = 123;
        var model = new InHouseLobbyistViewModel
        {
            LobbyistEmployerRegistrationId = id,
            LobbyistRegistrationId = lobbyistRegistrationId,
            FirstName = "Test",
            LastName = "Unit",
            Email = "<EMAIL>",
            IsNotAllowToEdit = true
        };

        // Act
        _lobbyistEmployerRegistrationCtlSvcMock.Setup(x => x.GetInHouseLobbyistViewModel(id, lobbyistRegistrationId))
                                                .ReturnsAsync(model);

        var result = await _controller.LobbyistCreationOrModification(id, lobbyistRegistrationId) as ViewResult;

        // Arrange
        Assert.That(result, Is.Not.Null);
        var resultModel = result.Model as InHouseLobbyistViewModel;
        Assert.Multiple(() =>
        {
            Assert.That(resultModel, Is.Not.Null);
            Assert.That(result.Model, Is.TypeOf<InHouseLobbyistViewModel>());
            Assert.That(model.LobbyistEmployerRegistrationId, Is.EqualTo(resultModel!.LobbyistEmployerRegistrationId));
            Assert.That(model.LobbyistRegistrationId, Is.EqualTo(resultModel!.LobbyistRegistrationId));
            Assert.That(model.FirstName, Is.EqualTo(resultModel!.FirstName));
            Assert.That(model.IsNotAllowToEdit, Is.EqualTo(resultModel!.IsNotAllowToEdit));
        });
    }

    [Test]
    public async Task LobbyistCreationOrUpdate_Post_InvalidState_ShouldStayPage()
    {
        // Arrange
        long id = 1;
        _controller.ModelState.AddModelError("Error", "Invalid model state");
        var model = new InHouseLobbyistViewModel
        {
            LobbyistEmployerRegistrationId = id,
            FirstName = "Test",
            LastName = "Unit",
            Action = FormAction.Submit,
            Email = "<EMAIL>"
        };

        // Act
        var result = await _controller.LobbyistCreationOrModification(id,null, model) as ViewResult;

        // Arrange
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(_controller.ModelState.IsValid, Is.False);
            Assert.That(result.Model, Is.TypeOf<InHouseLobbyistViewModel>());
        });
    }

    [Test]
    public async Task LobbyistCreation_Post_ShouldRedirectToUnderConstruction()
    {
        // Arrange
        long id = 1;

        var model = new InHouseLobbyistViewModel
        {
            LobbyistEmployerRegistrationId = id,
            FirstName = "Test",
            LastName = "Unit",
            Action = FormAction.Submit,
            Email = "<EMAIL>"
        };

        // Act
        var result = await _controller.LobbyistCreationOrModification(id, null, model) as RedirectToActionResult;

        // Arrange
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Step02LobbyistList"));
        });
    }

    [Test]
    public async Task LobbyistUpdate_Post_ShouldRedirectToUnderConstruction()
    {
        // Arrange
        long id = 1;

        var model = new InHouseLobbyistViewModel
        {
            LobbyistEmployerRegistrationId = id,
            LobbyistRegistrationId = 100,
            FirstName = "Test",
            LastName = "Unit",
            Action = FormAction.Submit,
            Email = "<EMAIL>"
        };

        // Act
        var result = await _controller.LobbyistCreationOrModification(id, model.LobbyistRegistrationId, model) as RedirectToActionResult;

        // Arrange
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Step02LobbyistList"));
        });
    }


    [Test]
    public async Task Step02LobbyistList_ModelStateIsValid_ReturnsViewWithCorrectModel()
    {
        // Arrange
        var mockModel = new InHouseLobbyistListViewModel
        {
            Id = 123,
            Lobbyists = new List<InHouseLobbyistResponseDto>
            {
                new() { Name = "Jane Smith", Id = 123, StatusId = 3 },
                new() { Name = "John Doe", Id = 456, StatusId = 1 }
            },
            InHouseLobbyistsGridModel = new SmallDataGridModel
            {
                GridId = "InHouseLobbyistsGrid",
                DataSource = new List<InHouseLobbyistResponseDto>()
            }
        };

        _lobbyistEmployerRegistrationCtlSvcMock
            .Setup(s => s.GetInHouseLobbyistListViewModel(123L))
            .ReturnsAsync   (mockModel);

        // Act
        var result = await _controller.Step02LobbyistList(123L) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var model = result.Model as InHouseLobbyistListViewModel;
        Assert.That(model, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(123));
            Assert.That(model.Lobbyists, Has.Count.EqualTo(2));
            Assert.That(model.InHouseLobbyistsGridModel?.GridId, Is.EqualTo("InHouseLobbyistsGrid"));
        });

        _lobbyistEmployerRegistrationCtlSvcMock.Verify(
            s => s.GetInHouseLobbyistListViewModel(123L),
            Times.Once
        );
    }

    [Test]
    public async Task Step02LobbyistList_ModelStateIsInvalid_RedirectsToPage03()
    {
        // Arrange
        _controller.ModelState.AddModelError("SomeField", "Some error");

        var mockModel = new InHouseLobbyistListViewModel
        {
        };
        _lobbyistEmployerRegistrationCtlSvcMock
            .Setup(s => s.GetInHouseLobbyistListViewModel(null))
            .ReturnsAsync(mockModel);

        // Act
        var result = await ((Func<long?, Task<IActionResult>>)_controller.Step02LobbyistList).Invoke(null);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.That(redirectResult.ActionName, Is.EqualTo("Page03"));

        _lobbyistEmployerRegistrationCtlSvcMock.Verify(
            s => s.GetInHouseLobbyistListViewModel(null),
            Times.Once
        );
    }


    [Test]
    public void Step02LobbyistList_ContinueAction_RedirectsToUnderConstruction()
    {
        // Arrange
        var model = new InHouseLobbyistListViewModel
        {
            Action = FormAction.Continue
        };

        // Act
        var result = _controller.Step02LobbyistList(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Step03LobbyingFirms"));
    }


    [Test]
    public void Step02LobbyistList_PreviousAction_RedirectsToPage03()
    {
        // Arrange
        var model = new InHouseLobbyistListViewModel
        {
            //HasLobbyists = true,
            Action = FormAction.Previous
        };

        // Act
        var result = _controller.Step02LobbyistList(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page03"));
    }

    [Test]
    public void Step02LobbyistList_SaveAndClose_ShowsToastAndRedirectsToDashboard()
    {
        // Arrange
        var model = new InHouseLobbyistListViewModel
        {
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = _controller.Step02LobbyistList(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
    }

    [Test]
    public void Step02LobbyistList_InvalidAction_AddsModelError_AndReturnsView()
    {
        // Arrange
        var model = new InHouseLobbyistListViewModel
        {
            Action = null // or invalid value
        };

        // Act
        var result = _controller.Step02LobbyistList(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState?[string.Empty]?.Errors, Has.Exactly(1).Items);
        });
    }


    [Test]
    public void Step02LobbyistList_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        var model = new InHouseLobbyistListViewModel
        {
            Action = FormAction.Continue
        };
        _controller.ModelState.AddModelError("Any", "Some error");

        // Act
        var result = _controller.Step02LobbyistList(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    [Test]
    public void Step02LobbyistListEdit_RedirectsToUnderConstruction()
    {
        // Act
        var result = _controller.Step02LobbyistListEdit() as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("UnderConstruction"));
            Assert.That(result.ControllerName, Is.EqualTo("Home"));
        });
    }

    [Test]
    public void Step02LobbyistListCertification_RedirectsToUnderConstruction()
    {
        // Act
        var result = _controller.Step02LobbyistListCertification() as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("UnderConstruction"));
            Assert.That(result.ControllerName, Is.EqualTo("Home"));
        });
    }

    [Test]
    public void Step02AddLobbyist_ValidModelState_ShouldReturnViewModel()
    {
        // Arrange
        long id = 1;

        // Act
        var result = _controller.Step02AddLobbyist(id) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.TypeOf<LobbyistEmployerRegistrationStep02AddLobbyistViewModel>());
        });
    }

    [Test]
    public void Step02AddLobbyist_InvalidModelState_ShouldRedirectToPage02()
    {
        // Arrange
        long id = 1;
        _controller.ModelState.AddModelError("TestError", "Invalid");

        // Act
        var result = _controller.Step02AddLobbyist(id) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page02"));
    }

    [Test]
    public void Step02AddLobbyist_ContinueAction_ValidModelState_ShouldRedirectToLobbyistList()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep02AddLobbyistViewModel
        {
            Id = 1,
            Action = FormAction.Continue
        };

        // Act
        var result = _controller.Step02AddLobbyist(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Step02LobbyistList"));
            Assert.That(result.ControllerName, Is.EqualTo("LobbyistEmployerRegistration"));
            Assert.That(result.RouteValues?["id"], Is.EqualTo(1));
        });
    }

    [Test]
    public void Step02AddLobbyist_PreviousAction_ValidModelState_ShouldRedirectToPage02()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep02AddLobbyistViewModel
        {
            Id = 1,
            Action = FormAction.Previous
        };

        // Act
        var result = _controller.Step02AddLobbyist(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page03"));
    }

    [Test]
    public void Step02AddLobbyist_SaveAndCloseAction_ValidModelState_ShouldRedirectToDashboard()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep02AddLobbyistViewModel
        {
            Id = 1,
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = _controller.Step02AddLobbyist(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
            Assert.That(result.ActionName, Is.EqualTo("Index"));
        });
    }

    [Test]
    public void Step02AddLobbyist_InvalidAction_ValidModelState_ShouldReturnViewWithModelError()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep02AddLobbyistViewModel
        {
            Id = 1,
            Action = (FormAction)999
        };

        // Act
        var result = _controller.Step02AddLobbyist(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState?[string.Empty]?.Errors, Has.Count.GreaterThan(0));
        });
    }

    [Test]
    public void Step02AddLobbyist_InvalidModelState_ShouldReturnView()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep02AddLobbyistViewModel();
        _controller.ModelState.AddModelError("TestError", "Invalid");

        // Act
        var result = _controller.Step02AddLobbyist(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task SearchLobbyistByIdOrName_WithInvalidModelState_ReturnsEmptyJsonResult()
    {
        // Arrange
        var search = "test";
        _controller.ModelState.AddModelError("Error", "Invalid state");

        // Act
        var result = await _controller.SearchLobbyistByIdOrName(_lobbyistEmployerRegistrationSvcMock.Object, search);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());

        _lobbyistEmployerRegistrationSvcMock.Verify(
            svc => svc.SearchLobbyistByIdOrName(It.IsAny<string>()),
            Times.Never);
    }

    [Test]
    public async Task SearchLobbyistByIdOrName_WithEmptySearchString_CallsServiceWithEmptyString()
    {
        // Arrange
        var search = "test";
        var expectedData = new List<LobbyistSearchResultDto>()
        {
            new() {
                Id = 1,
                Name = "Test Firm",
                Email = "<EMAIL>",
                IsDisabled = false,
            },
        };

        _lobbyistEmployerRegistrationSvcMock
            .Setup(svc => svc.SearchLobbyistByIdOrName(It.IsAny<string>()))
            .ReturnsAsync(expectedData);

        // Act
        var result = await _controller.SearchLobbyistByIdOrName(_lobbyistEmployerRegistrationSvcMock.Object, search);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Value, Is.EqualTo(expectedData));
    }


    [Test]
    public void Step03LobbyingFirms_ModelStateIsValid_ReturnsViewWithCorrectModel()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel
        {
            Id = 123,
            HasFirms = true,
            LobbyingFirms = new List<LobbyingFirmRowDto>
        {
            new() { Id = "ID# 1", Name = "Jane Smith", Email = "<EMAIL>" }
        },
            LobbyingFirmsGridModel = new SmallDataGridModel { GridId = "LobbyingFirmsGrid" }
        };

        _lobbyistEmployerRegistrationCtlSvcMock
            .Setup(s => s.GetLobbyistEmployerRegistrationStep03LobbyingFirmsViewModel(123L))
            .Returns(model);

        // Act
        var result = _controller.Step03LobbyingFirms(123L) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var viewModel = result.Model as LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel;
        Assert.That(viewModel, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewModel.Id, Is.EqualTo(123));
            Assert.That(viewModel.HasFirms, Is.True);
            Assert.That(viewModel.LobbyingFirms, Has.Count.EqualTo(1));
            Assert.That(viewModel.LobbyingFirmsGridModel?.GridId, Is.EqualTo("LobbyingFirmsGrid"));
        });

        _lobbyistEmployerRegistrationCtlSvcMock.Verify(
            s => s.GetLobbyistEmployerRegistrationStep03LobbyingFirmsViewModel(123L),
            Times.Once
        );
    }

    [Test]
    public void Step03LobbyingFirms_ModelStateInvalid_RedirectsToStep02LobbyistList()
    {
        // Arrange
        _controller.ModelState.AddModelError("SomeField", "Some error");

        _lobbyistEmployerRegistrationCtlSvcMock
            .Setup(s => s.GetLobbyistEmployerRegistrationStep03LobbyingFirmsViewModel(null))
            .Returns(new LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel { HasFirms = false });

        // Act
        var result = ((Func<long?, IActionResult>)_controller.Step03LobbyingFirms).Invoke(null);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.That(redirectResult.ActionName, Is.EqualTo("Step02LobbyistList"));
    }

    [Test]
    public void Step03LobbyingFirms_ContinueAction_RedirectsToUnderConstruction()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel
        {
            HasFirms = true,
            Action = FormAction.Continue
        };

        // Act
        var result = _controller.Step03LobbyingFirms(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("UnderConstruction"));
            Assert.That(result.ControllerName, Is.EqualTo("Home"));
        });
    }

    [Test]
    public void Step03LobbyingFirms_PreviousAction_RedirectsToStep02LobbyistList()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel
        {
            Id = 456,
            HasFirms = true,
            Action = FormAction.Previous
        };

        // Act
        var result = _controller.Step03LobbyingFirms(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Step02LobbyistList"));
            Assert.That(result?.RouteValues?["id"], Is.EqualTo(456));
        });
    }

    [Test]
    public void Step03LobbyingFirms_SaveAndClose_ShowsToastAndRedirectsToDashboard()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel
        {
            HasFirms = true,
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = _controller.Step03LobbyingFirms(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
    }

    [Test]
    public void Step03LobbyingFirms_InvalidAction_AddsModelError_AndReturnsView()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel
        {
            HasFirms = true,
            Action = null
        };

        // Act
        var result = _controller.Step03LobbyingFirms(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState[string.Empty]?.Errors, Has.Count.EqualTo(1));
        });
    }

    [Test]
    public void Step03LobbyingFirms_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        var model = new LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel
        {
            HasFirms = true,
            Action = FormAction.Continue
        };
        _controller.ModelState.AddModelError("Any", "Some error");

        // Act
        var result = _controller.Step03LobbyingFirms(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    #endregion

    #region Step02InitiateCertification Tests

    [Test]
    public async Task Step02InitiateCertification_Get_ValidId_ShouldReturnViewWithModel()
    {
        // Arrange
        long id = 123;
        var model = new LobbyistCertificationViewModel
        {
            Id = id,
            FirstName = "John",
            LastName = "Cinema",
            Email = "<EMAIL>"
        };

        _lobbyistEmployerRegistrationCtlSvcMock
            .Setup(x => x.GetLobbyistCertificationViewModel(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(model);

        _referenceDataApiMock
            .Setup(x => x.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>());

        _filingsApiMock
            .Setup(x => x.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>()));

        // Act
        var result = await _controller.Step02InitiateCertification(id, _referenceDataApiMock.Object, _filingsApiMock.Object, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<LobbyistCertificationViewModel>());
        var resultModel = result.Model as LobbyistCertificationViewModel;
        Assert.Multiple(() =>
        {
            Assert.That(resultModel, Is.Not.Null);
            Assert.That(resultModel!.Id, Is.EqualTo(id));
            Assert.That(resultModel.FirstName, Is.EqualTo("John"));
            Assert.That(resultModel.LastName, Is.EqualTo("Cinema"));
            Assert.That(resultModel.Email, Is.EqualTo("<EMAIL>"));
        });

        _lobbyistEmployerRegistrationCtlSvcMock.Verify(
            x => x.GetLobbyistCertificationViewModel(id, It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Test]
    public async Task Step02InitiateCertification_Get_InvalidModelState_ShouldReturnBadRequest()
    {
        // Arrange
        long id = 123;
        _controller.ModelState.AddModelError("Error", "Invalid state");

        // Act
        var result = await _controller.Step02InitiateCertification(id, _referenceDataApiMock.Object, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.TypeOf<BadRequestResult>());
        _lobbyistEmployerRegistrationCtlSvcMock.Verify(
            x => x.GetLobbyistCertificationViewModel(It.IsAny<long>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Test]
    [TestCase(FormAction.SaveAndClose)]
    [TestCase(FormAction.Previous)]
    [TestCase(FormAction.Continue)]
    public async Task Step02InitiateCertification_Post_ValidModel_ShouldRedirectToStep02LobbyistList(FormAction action)
    {
        // Arrange
        var model = new LobbyistCertificationViewModel
        {
            Id = 123,
            FirstName = "John",
            LastName = "Cinema",
            Email = "<EMAIL>",
            Action = action,
            IsSameAsBusinessAddress = true,
            Addresses = new List<AddressViewModel>
            {
                new() { Purpose = "Business", Street = "123 Business St" },
                new() { Purpose = "Mailing", Street = "456 Mailing Ave" }
            },
            LobbyistEmployerOrLobbyingFirmId = 789,
            LobbyistEmployerOrLobbyingFirmName = "test",
            IsDisableToSelectEmployerOrFirm = true
        };

        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto()
        {
            Id = 123,
            Valid = true,
            FilerId = 456,
            ValidationErrors = new List<CalAccess.Models.Common.WorkFlowError>()
        };

        _lobbyistEmployerRegistrationCtlSvcMock
            .Setup(x => x.SaveLobbyistCertificationViewModel(It.IsAny<LobbyistCertificationViewModel>()))
            .ReturnsAsync(response);

        _referenceDataApiMock
            .Setup(x => x.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>() { new(1, 1, 1, "test", null!) });

        _filingsApiMock
            .Setup(x => x.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>()
            { new(DateTime.Now, 1, "test", DateTime.Now) }));

        _accuMailValidatorServiceMock
            .Setup(x => x.AccuMailValidationHandler(It.IsAny<LobbyistCertificationViewModel>(), It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.Step02InitiateCertification(model, _referenceDataApiMock.Object, _filingsApiMock.Object) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ControllerName, Is.Null);
            Assert.That(result.ActionName, Is.EqualTo("Step02LobbyistList"));
            Assert.That(result.RouteValues, Is.Not.Null);
        });

        _lobbyistEmployerRegistrationCtlSvcMock.Verify(
            x => x.SaveLobbyistCertificationViewModel(It.IsAny<LobbyistCertificationViewModel>()),
            Times.Once);
    }

    [Test]
    public async Task Step02InitiateCertification_Post_ValidModel_AccumailCheck()
    {
        // Arrange
        var model = new LobbyistCertificationViewModel
        {
            Id = 123,
            FirstName = "John",
            LastName = "Cinema",
            Email = "<EMAIL>",
            Action = FormAction.SaveAndClose,
            LobbyistEmployerOrLobbyingFirmId = 789
        };

        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto()
        {
            Id = 123,
            Valid = true,
            FilerId = 456,
            ValidationErrors = new List<CalAccess.Models.Common.WorkFlowError>()
        };

        _referenceDataApiMock
            .Setup(x => x.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>() { new(1, 1, 1, "test", null!) });

        _lobbyistEmployerRegistrationCtlSvcMock
            .Setup(x => x.SaveLobbyistCertificationViewModel(It.IsAny<LobbyistCertificationViewModel>()))
            .ReturnsAsync(response);

        _filingsApiMock
            .Setup(x => x.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>()
            { new(DateTime.Now, 1, "test", DateTime.Now) }));

        _accuMailValidatorServiceMock
            .Setup(x => x.AccuMailValidationHandler(It.IsAny<LobbyistCertificationViewModel>(), It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.Step02InitiateCertification(model, _referenceDataApiMock.Object, _filingsApiMock.Object) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Step02InitiateCertification_Post_InvalidAction()
    {
        // Arrange
        var model = new LobbyistCertificationViewModel
        {
            Id = 123,
            FirstName = "John",
            LastName = "Cinema",
            Email = "<EMAIL>",
            Action = FormAction.Close,
            LobbyistEmployerOrLobbyingFirmId = 789
        };

        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto()
        {
            Id = 123,
            Valid = true,
            FilerId = 456,
            ValidationErrors = new List<CalAccess.Models.Common.WorkFlowError>()
        };

        _lobbyistEmployerRegistrationCtlSvcMock
            .Setup(x => x.SaveLobbyistCertificationViewModel(It.IsAny<LobbyistCertificationViewModel>()))
            .ReturnsAsync(response);

        _referenceDataApiMock
            .Setup(x => x.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>() { });

        _filingsApiMock
            .Setup(x => x.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>() { }));

        _accuMailValidatorServiceMock
            .Setup(x => x.AccuMailValidationHandler(It.IsAny<LobbyistCertificationViewModel>(), It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.Step02InitiateCertification(model, _referenceDataApiMock.Object, _filingsApiMock.Object) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Step02InitiateCertification_Post_InvalidModelState_ShouldReturnViewWithModel()
    {
        // Arrange
        var model = new LobbyistCertificationViewModel
        {
            Id = 123,
            FirstName = "John",
            LastName = "Cinema"
        };

        _controller.ModelState.AddModelError("Email", "Email is required");

        _referenceDataApiMock
            .Setup(x => x.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>());

        _filingsApiMock
            .Setup(x => x.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>()));

        // Act
        var result = await _controller.Step02InitiateCertification(model, _referenceDataApiMock.Object, _filingsApiMock.Object) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
        _lobbyistEmployerRegistrationCtlSvcMock.Verify(
            x => x.SaveLobbyistCertificationViewModel(It.IsAny<LobbyistCertificationViewModel>()),
            Times.Never);
    }
    #endregion

}

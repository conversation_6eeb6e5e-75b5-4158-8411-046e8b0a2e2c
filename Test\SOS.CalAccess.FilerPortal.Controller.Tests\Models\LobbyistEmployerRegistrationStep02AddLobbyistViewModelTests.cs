using SOS.CalAccess.FilerPortal.Models.Contacts;
using SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration;

namespace SOS.CalAccess.FilerPortal.Tests.Models;

[TestFixture]
public class LobbyistEmployerRegistrationStep02AddLobbyistViewModelTests
{
    [Test]
    public void LobbyistEmployerRegistrationStep02AddLobbyistViewModel_ShouldSetAndGetPropertiesCorrectly()
    {
        // Arrange
        var contact = new GenericContactViewModel
        {
            FirstName = "Jane",
            LastName = "Lobbyist"
        };

        var viewModel = new LobbyistEmployerRegistrationStep02AddLobbyistViewModel
        {
            ContactId = 123,
            RegistrationFilingId = 456,
            Contact = contact,
            LobbyistId = 789
        };

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(viewModel.ContactId, Is.EqualTo(123));
            Assert.That(viewModel.RegistrationFilingId, Is.EqualTo(456));
            Assert.That(viewModel.Contact, Is.EqualTo(contact));
            Assert.That(viewModel.Contact?.FirstName, Is.EqualTo("Jane"));
            Assert.That(viewModel.LobbyistId, Is.EqualTo(789));
        });
    }

    [Test]
    public void LobbyistEmployerRegistrationStep02AddLobbyistViewModel_ShouldAllowNullValues()
    {
        // Arrange
        var viewModel = new LobbyistEmployerRegistrationStep02AddLobbyistViewModel();

        // Assert default (null) values
        Assert.Multiple(() =>
        {
            Assert.That(viewModel.ContactId, Is.Null);
            Assert.That(viewModel.RegistrationFilingId, Is.Null);
            Assert.That(viewModel.Contact, Is.Null);
            Assert.That(viewModel.LobbyistId, Is.Null);
        });
    }
}

{"profiles": {"http": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "AZURE_TENANT_ID": "0252a9dd-e773-4879-87a9-7fb1b1215fc1", "AZURE_CLIENT_ID": "40af65b5-3339-494a-965e-0925e22cd5f1", "AZURE_CLIENT_SECRET": "**********************************"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5262"}, "https": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "AZURE_TENANT_ID": "0252a9dd-e773-4879-87a9-7fb1b1215fc1", "AZURE_CLIENT_ID": "40af65b5-3339-494a-965e-0925e22cd5f1", "AZURE_CLIENT_SECRET": "**********************************"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7280;http://localhost:5262"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "AZURE_TENANT_ID": "0252a9dd-e773-4879-87a9-7fb1b1215fc1", "AZURE_CLIENT_ID": "40af65b5-3339-494a-965e-0925e22cd5f1", "AZURE_CLIENT_SECRET": "**********************************"}}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:33039", "sslPort": 44338}}}
using NSubstitute;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.WebApi.Registrations;
using Microsoft.AspNetCore.Authorization;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.WebApi.Tests.Registrations;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(CisRegistrationWithdrawalController))]
public class CisRegistrationWithdrawalControllerTests
{
    private IAuthorizationService _authorizationService;
    private ICisRegistrationWithdrawalSvc _service;

    private CisRegistrationWithdrawalController _controller;
    /// <summary>
    /// Sets up the unit tests for this fixture.
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _authorizationService = Substitute.For<IAuthorizationService>();

        _service = Substitute.For<ICisRegistrationWithdrawalSvc>();

        _controller = new CisRegistrationWithdrawalController(
         _authorizationService,
         _service);
    }

    #region CreateCisWithdrawal
    [Test]
    public async Task CreateCisWithdrawal_Success()
    {
        // Arrange
        var id = 1;
        _service.CreateCisWithdrawal(Arg.Any<long>()).Returns(id);
        await _controller.CreateCisWithdrawal(id);

        // Act & Assert
        await _service.Received(1).CreateCisWithdrawal(Arg.Any<long>());
    }
    #endregion

    #region GetCisWithdrawal
    [Test]
    public async Task GetCisWithdrawal_Success()
    {
        // Arrange
        var id = 1;

        _service.CreateCisWithdrawal(Arg.Any<long>()).Returns(2);

        await _controller.GetCisWithdrawal(id);

        // Act & Assert
        await _service.Received(1).GetCisWithdrawal(Arg.Any<long>());
    }
    #endregion

    #region CancelCisWithdrawal
    [Test]
    public async Task CancelCisWithdrawal_Success()
    {
        // Arrange
        var id = 1;
        _service.CancelCisWithdrawal(Arg.Any<long>()).Returns(Task.CompletedTask);
        await _controller.CancelCisWithdrawal(id);

        // Act & Assert
        await _service.Received(1).CancelCisWithdrawal(Arg.Any<long>());
    }
    #endregion

    #region AttestationCisWithdrawal

    [Test]
    public async Task AttestationCisWithdrawal_ShouldCallServiceAndReturnResponse()
    {
        // Arrange
        var dto = new CisRegistrationAttestationWithdrawalDto { RegistrationId = 123, IsCertified = true };
        var expectedResponse = new RegistrationResponseDto(123, true, null, 1, new AddressValidationResult());

        _service.AttestationCisWithdrawal(dto).Returns(expectedResponse);

        // Act
        var result = await _controller.AttestationCisWithdrawal(dto);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EqualTo(expectedResponse));
        await _service.Received(1).AttestationCisWithdrawal(dto);
    }

    #endregion

    #region SendForAttestationCisWithdrawal
    [Test]
    public async Task SendForAttestationCisWithdrawal_ShouldCallServiceAndReturnResponse()
    {
        // Arrange
        var id = 1;
        var expectedResponse = new RegistrationResponseDto(123, true, null, 1, new AddressValidationResult());

        _service.SendForAttestationCisWithdrawal(Arg.Any<long>()).Returns(expectedResponse);

        // Act
        var result = await _controller.SendForAttestationCisWithdrawal(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EqualTo(expectedResponse));
        await _service.Received(1).SendForAttestationCisWithdrawal(id);
    }
    #endregion

    #region GetPendingItems
    [Test]
    public async Task GetPendingItems_ShouldCallServiceAndReturnResponse()
    {
        // Arrange
        var id = 1;
        var expectedResponse = new List<PendingItemDto> { };

        _service.GetPendingItems(Arg.Any<long>()).Returns(expectedResponse);

        // Act
        var result = await _controller.GetPendingItems(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EqualTo(expectedResponse));
        await _service.Received(1).GetPendingItems(id);
    }
    #endregion
}


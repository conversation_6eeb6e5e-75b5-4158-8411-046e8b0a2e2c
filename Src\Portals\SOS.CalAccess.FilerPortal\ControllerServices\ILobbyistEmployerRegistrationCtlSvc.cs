using Microsoft.AspNetCore.Mvc.ModelBinding;
using SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using IFilingsApi = SOS.CalAccess.FilerPortal.Generated.IFilingsApi;

namespace SOS.CalAccess.FilerPortal.ControllerServices;

public interface ILobbyistEmployerRegistrationCtlSvc
{
    /// <summary>
    /// Gets view model data for the Step 01 general info page
    /// </summary>
    /// <param name="id"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<LobbyistEmployerRegistrationStep01GenInfo> GetStep01GenInfoViewModel(long? id, CancellationToken cancellationToken);

    /// <summary>
    /// Gets view model data for the Step 01 state agencies page
    /// </summary>
    /// <param name="id"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<LobbyistEmployerRegistrationStep01Agencies> GetStep01AgenciesViewModel(long? id);

    Task<RegistrationResponseDto> SubmitStep01AgenciesViewModel(LobbyistEmployerRegistrationStep01Agencies model, ModelStateDictionary modelState, bool isSubmission = true);

    /// <summary>
    /// Gets view model data for the Step 01 description of interests and nature of interests pages
    /// </summary>
    /// <param name="id"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<LobbyistEmployerRegistrationStep01NatureInterests> GetStep01NatureInterestsViewModel(long? id);

    Task<RegistrationResponseDto> SubmitStep01NatureInterestViewModel(LobbyistEmployerRegistrationStep01NatureInterests model, ModelStateDictionary modelState, bool isSubmission = true);

    Task<RegistrationResponseDto> SubmitStep01GenInfoViewModel(LobbyistEmployerRegistrationStep01GenInfo model, ModelStateDictionary modelState, bool isSubmission = true);

    Task<InHouseLobbyistListViewModel> GetInHouseLobbyistListViewModel(long? id);

    /// <summary>
    /// Get inhouse lobbyist creation view model
    /// </summary>
    /// <param name="lobbyistEmployerRegistrationId">Lobbyist employer registration id</param>
    /// <param name="lobbyistRegistrationId">Lobbyist registration id</param>
    /// <returns>ILobbyist creation view model</returns>
    Task<InHouseLobbyistViewModel> GetInHouseLobbyistViewModel(long lobbyistEmployerRegistrationId, long? lobbyistRegistrationId);

    /// <summary>
    /// Create or update inhouse lobbyist
    /// </summary>
    /// <param name="model">Inhouse lobbyist view model</param>
    /// <returns></returns>
    Task<RegistrationResponseDto> CreateOrUpdateInHouseLobbyist(InHouseLobbyistViewModel model);

    Task PopulateStep01GenInfoReferenceData(
LobbyistEmployerRegistrationStep01GenInfo model,
IFilingsApi filingsApi);

    LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel GetLobbyistEmployerRegistrationStep03LobbyingFirmsViewModel(long? id);

    /// <summary>
    /// Get in house lobbyist certification view model
    /// </summary>
    /// <param name="id"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<LobbyistCertificationViewModel> GetLobbyistCertificationViewModel(long id, CancellationToken cancellationToken);

    /// <summary>
    /// Save in house Lobbyist Certification
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<RegistrationResponseDto> SaveLobbyistCertificationViewModel(LobbyistCertificationViewModel model);
}

using SOS.CalAccess.FilerPortal.Models.Disclosure;

namespace SOS.CalAccess.FilerPortal.Tests.Models;

[TestFixture]
public class DisclosureReportTests
{
    [Test]
    public void Constructor_SetsPropertiesCorrectly()
    {
        // Arrange
        long expectedId = 1;
        string expectedName = "Test Report";
        string expectedViewName = "TestView";
        string expectedStatus = "Active";
        long expectedFilingSummaryTypeId = 1;

        // Act
        var report = new DisclosureReport(expectedId, expectedName, expectedViewName, expectedStatus, expectedFilingSummaryTypeId);
        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(report.Id, Is.EqualTo(expectedId));
            Assert.That(report.Name, Is.EqualTo(expectedName));
            Assert.That(report.ViewName, Is.EqualTo(expectedViewName));
            Assert.That(report.Status, Is.EqualTo(expectedStatus));
            Assert.That(report.FilingSummaryTypeId, Is.EqualTo(expectedFilingSummaryTypeId));
        });
    }
}

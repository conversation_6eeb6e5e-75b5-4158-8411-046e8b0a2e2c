using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.Services.Business.FilerRegistration;
public class CandidateSvc(
    ICandidateRepository candidateRepository,
    IRegistrationRepository registrationRepository,
    IDateTimeSvc dateTimeSvc
    ) : ICandidateSvc
{
    /// <inheritdoc />
    public async Task<CandidateDto?> GetCandidateById(long id)
    {
        var data = await candidateRepository.FindCandidateById(id);
        if (data == null)
        {
            // candidate id not found
            return null;
        }
        var registration = data.Registrations?.LastOrDefault();

        Address? candidateAddress = null;
        Address? mailingAddress = null;
        foreach (Address address in registration?.AddressList?.Addresses ?? Enumerable.Empty<Address>())
        {
            if (address.Purpose.Equals("Candidate", StringComparison.OrdinalIgnoreCase))
            {
                candidateAddress = address;
                continue;
            }
            if (address.Purpose.Equals("Mailing", StringComparison.OrdinalIgnoreCase))
            {
                mailingAddress = address;
            }
        }

        PhoneNumber? candidatePhone = null;
        PhoneNumber? fax = null;
        foreach (PhoneNumber phone in registration?.PhoneNumberList?.PhoneNumbers ?? Enumerable.Empty<PhoneNumber>())
        {
            if (phone.Type.Equals("Phone", StringComparison.OrdinalIgnoreCase))
            {
                candidatePhone = phone;
                continue;
            }
            if (phone.Type.Equals("Fax", StringComparison.OrdinalIgnoreCase))
            {
                fax = phone;
            }
        }

        var candidate = new CandidateDto
        {
            Id = data.Id,
            Email = registration?.Email,
            FirstName = registration?.FirstName ?? string.Empty,
            MiddleInitial = registration?.MiddleName,
            LastName = registration?.LastName ?? string.Empty,
        };

        if (candidateAddress != null)
        {
            candidate.CandidateAddress = new AddressDto
            {
                Street = candidateAddress.Street,
                Street2 = candidateAddress.Street2,
                City = candidateAddress.City,
                State = candidateAddress.State,
                Zip = candidateAddress.Zip,
                Type = candidateAddress.Type,
                Country = candidateAddress.Country,
                Purpose = candidateAddress.Purpose,
            };
        }
        if (mailingAddress != null)
        {
            candidate.MailingAddress = new AddressDto
            {
                Street = mailingAddress.Street,
                Street2 = mailingAddress.Street2,
                City = mailingAddress.City,
                State = mailingAddress.State,
                Zip = mailingAddress.Zip,
                Type = mailingAddress.Type,
                Country = mailingAddress.Country,
                Purpose = mailingAddress.Purpose,
            };
        }
        if (candidatePhone != null)
        {
            candidate.Phone = new PhoneNumberDto(candidatePhone);
        }
        if (fax != null)
        {
            candidate.Fax = new PhoneNumberDto(fax);
        }

        return candidate;
    }


    /// <inheritdoc />
    public async Task<IEnumerable<CandidateSearchResultDto>> SearchCandidateByName(string q)
    {
        var candidacies = await registrationRepository.FindCandidateRegistrationsWithElectionByIdOrName(q) ?? throw new KeyNotFoundException("not found.");
        var data = new Dictionary<long, CandidateSearchResultDto>();
        foreach (CandidateIntentionStatement candidacy in candidacies)
        {
            // If new Candidate, add to data
            if (!data.TryGetValue(candidacy.CandidateId!.Value, out CandidateSearchResultDto? candidateSearchResult))
            {
                candidateSearchResult = new CandidateSearchResultDto
                {
                    Id = (long)candidacy.CandidateId,
                    Name = candidacy.Name,
                    LastName = candidacy.LastName!,
                    LastElectionDate = candidacy.ElectionRace?.Election?.ElectionDate ?? dateTimeSvc.GetCurrentDateTime(),
                    LastElection = candidacy.ElectionRace?.Election?.Name ?? "",
                    Registrations = new List<CandidateSearchRegistrationResultDto>()
                };
                data.Add(candidacy.CandidateId.Value, candidateSearchResult);
            }

            // Add Election
            if (candidateSearchResult != null && candidacy.ElectionRace != null)
            {
                var registration = new CandidateSearchRegistrationResultDto()
                {
                    Office = candidacy.ElectionRace.Office!.Name,
                    Election = candidacy.ElectionRace.Election!.Name,
                    ElectionDate = candidacy.ElectionRace.Election.ElectionDate
                };
                candidateSearchResult.Registrations.Add(registration);

                if (candidateSearchResult.LastElectionDate < candidacy.ElectionRace.Election.ElectionDate)
                {
                    candidateSearchResult.LastElectionDate = candidacy.ElectionRace.Election.ElectionDate;
                    candidateSearchResult.LastElection = candidacy.ElectionRace.Election.Name;
                }
            }
        }

        foreach (var result in data.Values)
        {
            result.Registrations = result.Registrations
                                         .OrderByDescending(r => r.ElectionDate)
                                         .Take(2)
                                         .ToList();
        }

        var sortedResult = data.Values.OrderBy(c => c.LastName).ThenBy(c => c.Name);

        return sortedResult;
    }
}

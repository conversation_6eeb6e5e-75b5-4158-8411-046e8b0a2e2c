using Microsoft.Extensions.Logging;
using NSubstitute;
using SOS.CalAccess.Foundation.Utils;

namespace SOS.CalAccess.Services.Business.Tests.SystemAdministration;

[TestFixture]
public class DateTimeSvcTests()
{
    private ILogger<DateTimeSvc> _logger;
    private const string HawaiiTimzoneId = "Hawaiian Standard Time";

    [SetUp]
    public void SetUp()
    {
        _logger = Substitute.For<ILogger<DateTimeSvc>>();
    }
    [Ignore("Temporarily disabling this test")]
    [Test]
    public async Task EnableOverride()
    {
        //Arrange
        var start = DateTime.Now;
        var overrideDate = new DateTime(2020, 3, 19, 16, 15, 14, DateTimeKind.Local);

        var service = new DateTimeSvc(_logger, new(false, null, null));

        //Act
        var initial = service.GetCurrentDateTime();

        await service.EnableOverride(overrideDate);

        var afterOverride = service.GetCurrentDateTime();

        await service.DisableOverride();

        var afterDisable = service.GetCurrentDateTime();

        Assert.Multiple(() =>
        {
            //Assert
            Assert.That(initial, Is.GreaterThanOrEqualTo(start));
            Assert.That(initial, Is.Not.EqualTo(overrideDate));
            Assert.That(afterOverride, Is.EqualTo(overrideDate));
            Assert.That(afterDisable, Is.GreaterThanOrEqualTo(start));
            Assert.That(afterDisable, Is.Not.EqualTo(overrideDate));
        });
    }
    [Ignore("Temporarily disabling this test")]
    [Test]
    public async Task EnableOverrideConstructor()
    {
        //Arrange
        var start = DateTime.Now;
        var overrideDate = new DateTime(2020, 3, 19, 16, 15, 14, DateTimeKind.Local);

        var service = new DateTimeSvc(_logger, new(true, overrideDate, null));

        //Act
        var initial = service.GetCurrentDateTime();

        await service.DisableOverride();

        var afterDisable = service.GetCurrentDateTime();

        Assert.Multiple(() =>
        {
            //Assert
            Assert.That(initial, Is.EqualTo(overrideDate));
            Assert.That(afterDisable, Is.GreaterThanOrEqualTo(start));
            Assert.That(afterDisable, Is.Not.EqualTo(overrideDate));
        });
    }
    [Ignore("Temporarily disabling this test")]
    [Test]
    public void EnableOverrideConstructorMissingDatetime()
    {
        //Arrange
        var start = DateTime.Now;

        var service = new DateTimeSvc(_logger, new(true, null, null));

        //Act
        var initial = service.GetCurrentDateTime();

        Assert.Multiple(() =>
        {
            //Assert
            Assert.That(initial, Is.GreaterThanOrEqualTo(start));
        });
    }

    [Test]
    public void ConvertDateTimeToUtcDefaultTimezoneDaylightSavings()
    {
        //Arrange
        var overrideDate = new DateTime(2020, 3, 19, 16, 15, 14, DateTimeKind.Unspecified);

        var service = new DateTimeSvc(_logger, new(false, null, null));

        //Act
        var result = service.ConvertDateTimeToUtc(overrideDate);

        Assert.Multiple(() =>
        {
            //Assert 7 hour difference from UTC to PDT
            Assert.That(result, Is.EqualTo(new DateTime(2020, 3, 19, 23, 15, 14, DateTimeKind.Utc)));
        });
    }

    [Test]
    public void ConvertDateTimeToUtcDefaultTimezone()
    {
        //Arrange
        var overrideDate = new DateTime(2020, 1, 19, 16, 15, 14, DateTimeKind.Unspecified);

        var service = new DateTimeSvc(_logger, new(false, null, null));

        //Act
        var result = service.ConvertDateTimeToUtc(overrideDate);

        Assert.Multiple(() =>
        {
            //Assert 8 hour difference from UTC to PST
            Assert.That(result, Is.EqualTo(new DateTime(2020, 1, 20, 0, 15, 14, DateTimeKind.Utc)));
        });
    }

    [Test]
    public void ConvertDateTimeToUtcSpecifiedTimezone()
    {
        //Arrange
        var overrideDate = new DateTime(2020, 3, 19, 16, 15, 14, DateTimeKind.Unspecified);

        var service = new DateTimeSvc(_logger, new(false, null, HawaiiTimzoneId));

        //Act
        var result = service.ConvertDateTimeToUtc(overrideDate);

        Assert.Multiple(() =>
        {
            //Assert
            Assert.That(result, Is.EqualTo(new DateTime(2020, 3, 20, 2, 15, 14, DateTimeKind.Utc)));
        });
    }

}

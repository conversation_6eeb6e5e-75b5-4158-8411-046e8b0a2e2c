using Newtonsoft.Json;
using SOS.CalAccess.Data.Contracts.Common;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Services.Common.FileSystem.Models;

namespace SOS.CalAccess.Services.Common.FileSystem;
public class UploadFileSvc(IUploadedFileRepository repository) : IUploadFileSvc
{
    public async Task<UploadedFile> CreateUploadedFile(UploadedFile uploadedFile)
    {
        return await repository.Create(uploadedFile);
    }

    public async Task DeleteUploadedFile(UploadedFile uploadedFile)
    {
        await repository.DeleteUploadedFile(uploadedFile);
    }

    public async Task<UploadedFile?> FindUploadByFileNameGuid(string fileNameGuid)
    {
        return await repository.FindUploadByFileNameGuid(fileNameGuid);
    }

    public async Task<IEnumerable<UploadedFile>> GetUploadsByRelationshipId(long relationshipId)
    {
        return await repository.GetUploadsByRelationshipId(relationshipId);
    }

    public async Task<UploadedFile> UpdateUploadedFile(UploadedFile uploadedFile)
    {
        return await repository.UpdateUploadedFile(uploadedFile);
    }

    /// <inheritdoc/>
    public async Task UpdateUploadedFileRelationshipsAsync(UpdateUploadedFileRelationshipsRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.AttachedFileGuidsJson))
        {
            return;
        }

        // Parse file name GUID from the request
        List<string> fileNameGuidsFromJson = JsonConvert.DeserializeObject<List<string>>(request.AttachedFileGuidsJson) ?? new List<string>();
        var fileNameGuids = fileNameGuidsFromJson.Where(x => !string.IsNullOrWhiteSpace(x)).Select(Guid.Parse).ToList();

        var uploadedFiles = await repository.FindUploadFilesByFileNameGuids(fileNameGuids);
        foreach (var file in uploadedFiles)
        {
            file.RelationshipId = request.RelationshipId;
        }

        await repository.UpdateRelationships(uploadedFiles);
    }
}

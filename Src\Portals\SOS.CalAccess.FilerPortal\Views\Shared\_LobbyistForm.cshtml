@using Microsoft.AspNetCore.Html
@using Microsoft.AspNetCore.Mvc.Localization
@using SOS.CalAccess.Models.Common;
@using SOS.CalAccess.FilerPortal.Models.Localization;
@using SOS.CalAccess.FilerPortal.Models.Transactions
@using SOS.CalAccess.UI.Common.Constants;
@using SOS.CalAccess.UI.Common.Services;

@inject IHtmlLocalizer<SharedResources> Localizer
@model SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistRegistration.LobbyistRegistrationStep01ViewModel;

@{
    var accuMailValidateModalModel = new AccuMailValidationModalModel { Suggestions = Model?.Suggestions ?? new(), Action = Model?.Action };

    var legislativeSessionOptions = Model?.LegislativeSessionOptions ?? new List<SelectListItem>();

    if (Model != null && Model.IsRenewal.GetValueOrDefault() && Model.LegislativeSessionId != null)
    {
        Model.LegislativeNextSessionId = Model.LegislativeSessionId.Value + 1;
    }

    var agencyOptions = (Model?.Agencies ?? new Dictionary<long, string>())
        .Select(a => new { Id = a.Key, Text = a.Value })
        .ToList();

    var firmSearchViewModel = new LobbyingEntitySearchViewModel
    {
        FilerId = 0,
        ContactId = Model?.ContactId,
        RegistrationFilingId = Model?.RegistrationFilingId,
        LobbyistEmployerOrLobbyingFirmId = Model?.LobbyistEmployerOrLobbyingFirmId,
        Required = false,
        SearchActionName = "SearchLobbyistEmployerOrLobbyingFirmByIdOrName",
        SearchDivId = "lobbyingFirmSearch",
        SearchLabelKey = "LobbyingFirmSearch",
        SearchLabelText = "Search for your employer by Filer ID# or name",
        SearchInputKey = "LobbyingFirmSearch",
        SearchInputLabel = "Lobbying Firm Search",
        ContactName = Model?.Contact?.OrganizationName,
        IsRenewal = Model?.IsRenewal
    };
}

@Html.HiddenFor(m => m.Id)
@Html.HiddenFor(m => m.IsRenewal)
@Html.HiddenFor(m => m.SelfRegister)
@Html.HiddenFor(m => m.Version)
@Html.HiddenFor(m => m.UploadedFilename, new { id = "uploadedFilename" })

@if(Model?.Version > 0)
{
    <div class="mb-3 col-sm-6">
        @Html.DatePickerFor(Localizer, m => m.EffectiveDateOfChanges, ResourceConstants.LobbyistRegistration03EffectiveDateOfChanges, isRequired: true, cssClass: "FormControlClass")
    </div>
}

<div class="col-sm-6">
    @Html.TextFieldFor(Localizer, m => m.FirstName, ResourceConstants.LobbyistRegistration03FirstName, true, "", null)

    @Html.TextFieldFor(Localizer, m => m.MiddleName, ResourceConstants.LobbyistRegistration03MiddleName, false, "", null)

    @Html.TextFieldFor(Localizer, m => m.LastName, ResourceConstants.LobbyistRegistration03LastName, true, "", null)

    @Html.TextFieldFor(Localizer, m => m.Email, ResourceConstants.LobbyistRegistration03Email, true, "", null)
</div>
<div class="mb-3 col-sm-6">
    @Html.SosLabelFor(Localizer, m => m.PhoneNumber, ResourceConstants.LobbyistRegistration03PhoneNumber, true)
    <partial name="_ContactNumberInput" model='new ContactNumberInputModel
    {
        Name = "Phone Number",
        CountryCodeName = "PhoneNumberCountryCode",
        CountryCodeValue = Model?.PhoneNumberCountryCode ?? "",
        ContactNumberName = "PhoneNumber",
        ContactNumber = Model?.PhoneNumber ?? "",
        // HasValidationError = Model.Messages.Validations.ContainsKey("PhoneNumber")
    }' />
    @Html.SosValidationMessageFor(SharedLocalizer, m => m.PhoneNumber)
</div>
<div class="mb-3 col-sm-6">
    @Html.SosLabelFor(Localizer, m => m.FaxNumber, ResourceConstants.LobbyistRegistration03FaxNumber, false)
    <partial name="_ContactNumberInput" model='new ContactNumberInputModel
    {
        Name = "Fax Number",
        CountryCodeName = "FaxNumberCountryCode",
        CountryCodeValue = Model?.FaxNumberCountryCode ?? "",
        ContactNumberName = "FaxNumber",
        ContactNumber = Model?.FaxNumber ?? "",
        // HasValidationError = Model.Messages.Validations.ContainsKey("FaxNumber")
    }' />
    @Html.SosValidationMessageFor(SharedLocalizer, m => m.FaxNumber)
</div>

<div class="mb-3 col-sm-6">
    <span class="form-label">
        @Localizer[ResourceConstants.LobbyistRegistration03LobbyistEmployerOrLobbyingFirm].Value
    </span>

    @if(Model?.IsDisableToSelectEmployerOrFirm == true) {
        @Html.TextFieldFor(Localizer, m => m.LobbyistEmployerOrLobbyingFirmName, ResourceConstants.LobbyingFirmName, disabled: true)
        @Html.HiddenFor(m => m.LobbyistEmployerOrLobbyingFirmId)
        @Html.HiddenFor(m => m.LobbyistEmployerOrLobbyingFirmName)
        @Html.HiddenFor(m => m.IsDisableToSelectEmployerOrFirm)
    } else {
        <partial name="_LobbyingEntitySearch" model="firmSearchViewModel" />
        @Html.SosValidationMessageFor(SharedLocalizer, m => m.LobbyistEmployerOrLobbyingFirmId)
    }
</div>

<hr />

@for (var i = 0; i < Model?.Addresses.Count; i++)
{
    @if (Model.Addresses[i].Purpose == "Business")
    {
        <h3>@Localizer[ResourceConstants.LobbyistRegistration03BusinessAddress].Value</h3>
        @Html.HiddenFor(m => Model.Addresses[i].Purpose)
        <div class="mb-3 col-sm-6">
            @Html.DropdownFor(Localizer, m => m.Addresses[i].Country, ResourceConstants.LobbyistRegistration03Country, CommonConstants.CountryDropdownOptions, ResourceConstants.SelectCountry, true)
        </div>

        <div class="col-sm-6">
            @Html.TextFieldFor(Localizer, m => m.Addresses[i].Street, ResourceConstants.LobbyistRegistration03Street, true)

            @Html.TextFieldFor(Localizer, m => m.Addresses[i].Street2, ResourceConstants.LobbyistRegistration03Street2, true)

            @Html.TextFieldFor(Localizer, m => m.Addresses[i].City, ResourceConstants.LobbyistRegistration03City, true)
        </div>
        <div class="mb-3 col-sm-6">
            @Html.DropdownFor(Localizer, m => m.Addresses[i].State, ResourceConstants.LobbyistRegistration03State, CommonConstants.StateDropdownOptions, ResourceConstants.SelectState, true)
        </div>

        <div class="col-sm-6">
            @Html.TextFieldFor(Localizer, m => m.Addresses[i].Zip, ResourceConstants.LobbyistRegistration03Zip, true)
        </div>
    }
}

<hr />

@for (var i = 0; i < Model?.Addresses.Count; i++)
{
    @if (Model.Addresses[i].Purpose == "Mailing")
    {
        <h3>@Localizer[ResourceConstants.LobbyistRegistration03MailingAddress].Value</h3>
        @Html.HiddenFor(m => Model.Addresses[i].Purpose)
        <div class="mb-3 col-sm-6">
            @Html.CheckBoxFor(m => m.IsSameAsBusinessAddress, new { @class = "form-check-input", @checked = "checked" })
            @Html.LabelFor(m => m.IsSameAsBusinessAddress, Localizer[ResourceConstants.LobbyistRegistration03SameAs].Value, new { @class = "form-check-label" })
        </div>

        <div id="mailing-address" class="d-block col-sm-6">
            <div class="mb-3">
                @Html.DropdownFor(Localizer, m => m.Addresses[i].Country, ResourceConstants.LobbyistRegistration03Country, CommonConstants.CountryDropdownOptions, ResourceConstants.SelectCountry, true)
            </div>

            @Html.TextFieldFor(Localizer, m => m.Addresses[i].Street, ResourceConstants.LobbyistRegistration03Street, true)

            @Html.TextFieldFor(Localizer, m => m.Addresses[i].Street2, ResourceConstants.LobbyistRegistration03Street2, true)

            @Html.TextFieldFor(Localizer, m => m.Addresses[i].City, ResourceConstants.LobbyistRegistration03City, true)

            <div class="mb-3">
                @Html.DropdownFor(Localizer, m => m.Addresses[i].State, ResourceConstants.LobbyistRegistration03State, CommonConstants.StateDropdownOptions, ResourceConstants.SelectState, true)
            </div>

            @Html.TextFieldFor(Localizer, m => m.Addresses[i].Zip, ResourceConstants.LobbyistRegistration03Zip, true)
        </div>
    }
}

<hr />

<div class="mb-3 col-sm-6">
    @if (Model != null && Model.IsRenewal.GetValueOrDefault())
    {
        @Html.DropdownFor(Localizer, m => m.LegislativeNextSessionId, ResourceConstants.LobbyistRegistration03LegislativeSession, legislativeSessionOptions, "YYYY - YYYY", required: true, disabled: true)
        @Html.HiddenFor(m => m.LegislativeSessionId)
        @Html.HiddenFor(m => m.LegislativeNextSessionId)
    }
    else
    {
        @Html.DropdownFor(Localizer, m => m.LegislativeSessionId, ResourceConstants.LobbyistRegistration03LegislativeSession, legislativeSessionOptions, "YYYY - YYYY", required: true)
    }
</div>

<div class="mb-3 col-sm-6">
    @Html.DatePickerFor(Localizer, m => m.QualificationDate, ResourceConstants.LobbyistRegistration03QualificationDate, isRequired: true, cssClass: "FormControlClass")
</div>

<div class="mb-3 col-lg-4">
    @Html.Label("PlacementAgent", Localizer[ResourceConstants.LobbyistRegistration03PlacementAgent].Value, new { @class = "form-label" })
    <div class="d-flex flex-column">
        <div>
            @Html.RadioButtonFor(m => m.PlacementAgent, true, new { @id = "isPlacementAgentYes" }) @Localizer["Common.Yes"]
        </div>
        <div>
            @Html.RadioButtonFor(m => m.PlacementAgent, false, new { @id = "isPlacementAgentNo" }) @Localizer["Common.No"]
        </div>
    </div>
    @Html.SosValidationMessageFor(SharedLocalizer, m => m.PlacementAgent)
</div>

<hr />

<h3>@Localizer[ResourceConstants.LobbyistRegistration03EthicsCourse].Value</h3>
<div class="form-check mb-2">
    @Html.RadioButtonFor(m => m.EthicsCourseCompleted, false, new { @class = "form-check-input", id = "statusNotTaken" })
    <label class="form-check-label" for="statusNotTaken">
        @Localizer[ResourceConstants.LobbyistRegistration03EthicsNotTaken].Value
    </label>
</div>

@* This is the hidden input to store IsNewCertification value *@
@Html.HiddenFor(m => m.IsNewCertification)

@* These radio inputs for IsNewCertification will not be changed and only for showing on UI *@
<div class="form-check ms-4 mb-2">
    @Html.RadioButtonFor(m => m.IsNewCertification, true, new { @class = "form-check-input", id = "typeNew", @disabled = "disabled" })
    <label class="form-check-label" for="typeNew">
        @Localizer[ResourceConstants.LobbyistRegistration03EthicsNew].Value
    </label>
</div>
<div class="form-check ms-4 mb-3">
    @Html.RadioButtonFor(m => m.IsNewCertification, false, new { @class = "form-check-input", id = "typeRenewal", @disabled = "disabled" })
    <label class="form-check-label" for="typeRenewal">
        @Localizer[ResourceConstants.LobbyistRegistration03EthicsRenewal].Value
    </label>
</div>

<div class="form-check">
    @Html.RadioButtonFor(m => m.EthicsCourseCompleted, true, new { @class = "form-check-input", id = "statusCompleted" })
    <label class="form-check-label" for="statusCompleted">
        @Localizer[ResourceConstants.LobbyistRegistration03EthicsCompleted].Value
    </label>
</div>

<div class="ms-3 mt-2">
    @Html.DatePickerFor(Localizer, m => m.EthicsCourseCompletedOn, "Date Completed", isRequired: false)
</div>

<div class="mb-3">
    @Html.SosValidationMessageFor(SharedLocalizer, m => m.EthicsCourseCompleted)
</div>

<hr />

<h3>@Localizer[ResourceConstants.LobbyistRegistration03AgenciesLobbied].Value</h3>
<p>@Localizer[ResourceConstants.LobbyistRegistration03ChooseOption].Value</p>

<div class="form-check mb-2">
    @Html.RadioButtonFor(m => m.LobbyOnlySpecifiedAgencies, false, new { @class = "form-check-input", id = "lobbyAll" })
    <label class="form-check-label" for="lobbyAll">
        @Localizer[ResourceConstants.LobbyistRegistration03AgenciesLobbied1].Value
    </label>
</div>

<div class="form-check mb-2">
    @Html.RadioButtonFor(m => m.LobbyOnlySpecifiedAgencies, true, new { @class = "form-check-input", id = "lobbySpecific" })
    <label class="form-check-label" for="lobbySpecific">
        @Localizer[ResourceConstants.LobbyistRegistration03AgenciesLobbied2].Value
    </label>
</div>

<div class="mb-3 col-lg-4 ms-4" id="agencyContainer">
    <p>
        @Localizer[ResourceConstants.LobbyistRegistration03StateAgenciesLobbied].Value
    </p>
    @Html.Raw(@Html.EJS().MultiSelect("SelectedAgencies")
            .Placeholder("Select agency")
            .DataSource(agencyOptions)
            .Fields(f => f.Value("Id").Text("Text"))
            .Value(Model?.SelectedAgencies)
            .AllowFiltering(true)
            .Mode(Syncfusion.EJ2.DropDowns.VisualMode.Box)
            .AllowCustomValue(false)
            .CssClass("multi-select rounded custom-border")
            .Render())

    <div class="mt-2">
        <a href="https://www.ca.gov/departments/list/" target="_blank">
            @Localizer[ResourceConstants.LobbyistRegistration03FullList].Value
        </a>
    </div>
</div>

<div id="specific-agency-controls" class="ms-4">
    @Html.Label("IsLobbyingStateLegislature", Localizer[ResourceConstants.LobbyistRegistration03WillYouLobby].Value, new { @class = "form-label" })

    <div class="form-check mb-1">
        @Html.RadioButtonFor(m => m.IsLobbyingStateLegislature, true, new { @class = "form-check-input", id = "lobbyYes" })
        <label class="form-check-label" for="lobbyYes">Yes</label>
    </div>

    <div class="form-check mb-3">
        @Html.RadioButtonFor(m => m.IsLobbyingStateLegislature, false, new { @class = "form-check-input", id = "lobbyNo" })
        <label class="form-check-label" for="lobbyNo">No</label>
    </div>
</div>

<div class="mb-2">
    @Html.SosValidationMessageFor(SharedLocalizer, m => m.LobbyOnlySpecifiedAgencies)
</div>
<div class="mb-2">
    @Html.SosValidationMessageFor(SharedLocalizer, m => m.SelectedAgencies)
</div>
<div class="mb-2">
    @Html.SosValidationMessageFor(SharedLocalizer, m => m.IsLobbyingStateLegislature)
</div>

<hr />

<h5>@Localizer[ResourceConstants.LobbyistRegistration03Upload].Value</h5>
<p class="text-muted small">
    @Localizer[ResourceConstants.LobbyistRegistration03UploadRules].Value
</p>
<div class="mb-2">
    @Html.SosValidationMessageFor(SharedLocalizer, m => m.UploadedFilename)
</div>
@Html.FileUploader(SharedLocalizer
    , handleMultipleFiles: false
    , allowedFileExtensions: ".jpg,.jpeg,.png"
    , titleResourceKey: string.Empty
    , relationshipType: RelationshipType.RegistrationFiling.ToString()
    , onUploadSuccess: "fileUploaded"
    , required: true
)



<partial name="_AccuMailValidationModal" model="accuMailValidateModalModel" />

<script>
    function fileUploaded(args) {
        setTimeout(() => {
            const guidFilename = args.file.name;
            console.log("GUID (after timeout):", guidFilename);  // Should be correct
            document.getElementById('uploadedFilename').value = guidFilename;
        }, 0);
    }
    document.addEventListener("DOMContentLoaded", function () {
        let mailingaddress = document.getElementById('mailing-address');
        let checkbox = document.getElementById('IsSameAsBusinessAddress');

        // Function to toggle the mailing address visibility
        function superToggle(element, class0, class1) {
            element.classList.toggle(class0);
            element.classList.toggle(class1);
        }

        // Check initial state and set visibility on page load
        if (checkbox.checked) {
            mailingaddress.classList.add('d-none');
            mailingaddress.classList.remove('d-block');
        } else {
            mailingaddress.classList.add('d-block');
            mailingaddress.classList.remove('d-none');
        }

        // Add event listener for toggling when checkbox is clicked
        checkbox.addEventListener('change', function () {
            superToggle(mailingaddress, 'd-block', 'd-none');
        });
    });

    document.addEventListener("DOMContentLoaded", function () {
        const statusRadios = document.getElementsByName("EthicsCourseCompleted");
        const newCert = document.getElementById("typeNew");
        const renewal = document.getElementById("typeRenewal");

        newCert.readonly = true;
        renewal.readonly = true;

        function updateTypeRadios() {
            const isCompleted = document.querySelector('input[name="EthicsCourseCompleted"]:checked')?.value === "True";
            const completedOnDatePicker = document.querySelector('input[name="EthicsCourseCompletedOn"]').ej2_instances[0];

            if (isCompleted == true) {
                newCert.checked = false;
                renewal.checked = false;
                completedOnDatePicker.enabled = true;
            } else {
                const isNewCertification = "@Model!.IsNewCertification" === "True";
                newCert.checked = isNewCertification;
                renewal.checked = !isNewCertification;
                completedOnDatePicker.enabled = false;
                completedOnDatePicker.value = "";
            }
        }

        statusRadios.forEach(r => r.addEventListener("change", updateTypeRadios));
        updateTypeRadios(); // Initialize on page load
    });

    document.addEventListener("DOMContentLoaded", function () {
        const allOption = document.getElementById("lobbyAll");
        const specificOption = document.getElementById("lobbySpecific");
        const legislatureYes = document.getElementById("lobbyYes");
        const legislatureNo = document.getElementById("lobbyNo");
        const agencyMultiSelect = document.getElementById("SelectedAgencies").ej2_instances[0];

        function updateAgencyControls() {
            const isLobbyOnlySpecifiedAgencies = document.querySelector('input[name="LobbyOnlySpecifiedAgencies"]:checked')?.value === "True";
            legislatureYes.disabled = !isLobbyOnlySpecifiedAgencies;
            legislatureNo.disabled = !isLobbyOnlySpecifiedAgencies;

            if (!isLobbyOnlySpecifiedAgencies) {
                legislatureYes.checked = false;
                legislatureNo.checked = false;
                agencyMultiSelect.value = "";
                agencyMultiSelect.enabled = false;
            } else {
                agencyMultiSelect.enabled = true;
            }
        }

        allOption.addEventListener("change", updateAgencyControls);
        specificOption.addEventListener("change", updateAgencyControls);

        updateAgencyControls(); // initialize
    });
</script>

<style>
    label[for="LobbyingFirmSearch"] {
        color: #898890;
    }
</style>

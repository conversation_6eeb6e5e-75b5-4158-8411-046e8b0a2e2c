// Ignore Spelling: Validator

using Newtonsoft.Json.Linq;
using SOS.CalAccess.Services.Common.DataValidation;


namespace SOS.CalAccess.Services.Business.Efile;

public class LobbyingNoticeOfWithdrawalValidator : IFormValidator
{
    private const string FormName = FormNameToIdMapping.LobbyingNoticeOfWithdrawal;

    public LobbyingNoticeOfWithdrawalValidator() { }

    public bool CanHandle(string formName)
        => string.Equals(formName, FormName, StringComparison.OrdinalIgnoreCase);

    public Task<int> ValidateAsync(JObject jsonData)
    {
        return Task.FromResult(0);
    }
}

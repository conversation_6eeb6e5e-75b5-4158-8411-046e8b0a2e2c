CREATE TABLE [dbo].[RegistrationFiling] (
    [Id]                                     BIGINT                                             IDENTITY (1, 1) NOT NULL,
    [ApprovedAt]                             DATETIME2 (7)                                      NULL,
    [TerminatedAt]                           DATETIME2 (7)                                      NULL,
    [FilerId]                                BIGINT                                             NULL,
    [Name]                                   NVARCHAR (MAX)                                     NOT NULL,
    [StatusId]                               BIGINT                                             NOT NULL,
    [OriginalId]                             BIGINT                                             NULL,
    [ParentId]                               BIGINT                                             NULL,
    [Version]                                INT                                                NULL,
    [CreatedBy]                              BIGINT                                             NOT NULL,
    [ModifiedBy]                             BIGINT                                             NOT NULL,
    [AuditableResourceTag]                   NVARCHAR (450)                                     NULL,
    [Discriminator]                          NVARCHAR (100)                                     NOT NULL,
    [PeriodEnd]                              DATETIME2 (7) GENERATED ALWAYS AS ROW END HIDDEN   NOT NULL,
    [PeriodStart]                            DATETIME2 (7) GENERATED ALWAYS AS ROW START HIDDEN NOT NULL,
    [Email]                                  NVARCHAR (MAX)                                     NULL,
    [County]                                 NVARCHAR (MAX)                                     NULL,
    [ActivityLevel]                          NVARCHAR (MAX)                                     NULL,
    [QualifiedCommittee]                     BIT                                                NULL,
    [DateQualified]                          DATETIME2 (7)                                      NULL,
    [VerificationExecutedAt]                 DATETIME2 (7)                                      NULL,
    [VerificationSignature]                  NVARCHAR (MAX)                                     NULL,
    [VerifiedById]                           BIGINT                                             NULL,
    [AddressListId]                          BIGINT                                             NULL,
    [PhoneNumberListId]                      BIGINT                                             NULL,
    [CommitteeSubType]                       NVARCHAR (MAX)                                     NULL,
    [JurisdictionCounty]                     NVARCHAR (MAX)                                     NULL,
    [JurisdictionActive]                     NVARCHAR (MAX)                                     NULL,
    [FinancialInstitutionName]               NVARCHAR (MAX)                                     NULL,
    [FinancialInstitutionPhone]              NVARCHAR (MAX)                                     NULL,
    [FinancialInstitutionAccountNumber]      NVARCHAR (MAX)                                     NULL,
    [PreviousCandidate]                      BIT                                                NULL,
    [CandidateId]                            BIGINT                                             NULL,
    [ElectionOfficeSought]                   NVARCHAR (MAX)                                     NULL,
    [ElectionDistrictNumber]                 NVARCHAR (MAX)                                     NULL,
    [ElectionId]                             BIGINT                                             NULL,
    [LegalDispute]                           NVARCHAR (MAX)                                     NULL,
    [ActivityDescription]                    NVARCHAR (MAX)                                     NULL,
    [IndustryOrGroupDescription]             NVARCHAR (MAX)                                     NULL,
    [SponsorId]                              BIGINT                                             NULL,
    [CampaignCommittee]                      BIT                                                NULL,
    [CommitteeId]                            BIGINT                                             NULL,
    [SelfRegister]                           BIT                                                NULL,
    [ElectionJurisdiction]                   NVARCHAR (MAX)                                     NULL,
    [ElectionCounty]                         NVARCHAR (MAX)                                     NULL,
    [ExpenditureLimitAccepted]               BIT                                                NULL,
    [ExpenditureCeilingAmount]               DECIMAL (18, 6)                                    NULL,
    [ExpenditureExceeded]                    BIT                                                NULL,
    [ContributedPersonalExcessFundsOn]       DATETIME2 (7)                                      NULL,
    [FirstName]                              NVARCHAR (MAX)                                     NULL,
    [MiddleName]                             NVARCHAR (MAX)                                     NULL,
    [LastName]                               NVARCHAR (MAX)                                     NULL,
    [IsSameAsCandidateAddress]               BIT                                                DEFAULT (CONVERT([bit],(0))) NOT NULL,
    [ResponsibleOfficerTitle]                NVARCHAR (MAX)                                     NULL,
    [WithdrawnAt]                            DATETIME2 (7)                                      NULL,
    [PlacementAgent]                         BIT                                                NULL,
    [EmployerName]                           NVARCHAR (MAX)                                     NULL,
    [StateLegislatureLobbying]               BIT                                                NULL,
    [EthicsCourseCompletionDate]             DATETIME2 (7)                                      NULL,
    [EmployerType]                           NVARCHAR (MAX)                                     NULL,
    [BusinessActivity]                       NVARCHAR (MAX)                                     NULL,
    [BusinessDescription]                    NVARCHAR (MAX)                                     NULL,
    [NatureAndPurpose]                       NVARCHAR (MAX)                                     NULL,
    [InterestType]                           NVARCHAR (MAX)                                     NULL,
    [IndustryDescription]                    NVARCHAR (MAX)                                     NULL,
    [IndustryPortion]                        NVARCHAR (MAX)                                     NULL,
    [NumberOfMembers]                        INT                                                NULL,
    [PoliticalPartyId]                       BIGINT                                             NULL,
    [SubmittedAt]                            DATETIME2 (7)                                      NULL,
    [CandidateAgency]                        NVARCHAR (MAX)                                     NULL,
    [ElectionRaceId]                         BIGINT                                             NULL,
    [LegislativeSessionId]                   BIGINT                                             NULL,
    [LlcDetailsId]                           BIGINT                                             NULL,
    [IsLobbyingCoalition]                    BIT                                                NULL,
    [DescriptionOfPrincipallyDerivedSupport] NVARCHAR (MAX)                                     NULL,
    [IndustryGroupClassificationId]          BIGINT                                             NULL,
    [IndustryGroupOtherText]                 NVARCHAR (MAX)                                     NULL,
    [NatureAndInterestEmployerName]          NVARCHAR (MAX)                                     NULL,
    [NatureAndInterestTypeId]                BIGINT                                             NULL,
    [CommitteeTypeId]                        BIGINT                                             NULL,
    [EthicsCourseCompleted]                  BIT                                                NULL,
    [EthicsCourseCompletedWithinPastYear]    BIT                                                NULL,
    [IsNewCertification]                     BIT                                                NULL,
    [LobbyOnlySpecifiedAgencies]             BIT                                                NULL,
    [EffectiveDateOfChanges]                 DATETIME2 (7)                                      NULL,
    [BusinessSubcategoryId]                  BIGINT                                             NULL,
    [BusinessSubcategoryOtherText]           NVARCHAR (MAX)                                     NULL,
    [IsFiftyMembersOrLess]                   BIT                                                NULL,
    [LobbyingInterestId]                     BIGINT                                             NULL,
    CONSTRAINT [PK_RegistrationFiling] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_RegistrationFiling_AddressList_AddressListId] FOREIGN KEY ([AddressListId]) REFERENCES [dbo].[AddressList] ([Id]),
    CONSTRAINT [FK_RegistrationFiling_BusinessSubcategory_BusinessSubcategoryId] FOREIGN KEY ([BusinessSubcategoryId]) REFERENCES [dbo].[BusinessSubcategory] ([Id]),
    CONSTRAINT [FK_RegistrationFiling_Candidate_CandidateId] FOREIGN KEY ([CandidateId]) REFERENCES [dbo].[Candidate] ([Id]),
    CONSTRAINT [FK_RegistrationFiling_Candidate_CandidateId1] FOREIGN KEY ([CandidateId]) REFERENCES [dbo].[Candidate] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_RegistrationFiling_CommitteeType_CommitteeTypeId] FOREIGN KEY ([CommitteeTypeId]) REFERENCES [dbo].[CommitteeType] ([Id]),
    CONSTRAINT [FK_RegistrationFiling_Election_ElectionId] FOREIGN KEY ([ElectionId]) REFERENCES [dbo].[Election] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_RegistrationFiling_ElectionRace_ElectionRaceId] FOREIGN KEY ([ElectionRaceId]) REFERENCES [dbo].[ElectionRace] ([Id]),
    CONSTRAINT [FK_RegistrationFiling_Filer_FilerId] FOREIGN KEY ([FilerId]) REFERENCES [dbo].[Filer] ([Id]),
    CONSTRAINT [FK_RegistrationFiling_LegislativeSession_LegislativeSessionId] FOREIGN KEY ([LegislativeSessionId]) REFERENCES [dbo].[LegislativeSession] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_RegistrationFiling_LegislativeSession_LegislativeSessionId2] FOREIGN KEY ([LegislativeSessionId]) REFERENCES [dbo].[LegislativeSession] ([Id]),
    CONSTRAINT [FK_RegistrationFiling_LlcDetail_LlcDetailsId] FOREIGN KEY ([LlcDetailsId]) REFERENCES [dbo].[LlcDetail] ([Id]),
    CONSTRAINT [FK_RegistrationFiling_LobbyingInterest_LobbyingInterestId] FOREIGN KEY ([LobbyingInterestId]) REFERENCES [dbo].[LobbyingInterest] ([Id]),
    CONSTRAINT [FK_RegistrationFiling_PhoneNumberList_PhoneNumberListId] FOREIGN KEY ([PhoneNumberListId]) REFERENCES [dbo].[PhoneNumberList] ([Id]),
    CONSTRAINT [FK_RegistrationFiling_PoliticalParty_PoliticalPartyId] FOREIGN KEY ([PoliticalPartyId]) REFERENCES [dbo].[PoliticalParty] ([Id]),
    CONSTRAINT [FK_RegistrationFiling_RegistrationContact_SponsorId] FOREIGN KEY ([SponsorId]) REFERENCES [dbo].[RegistrationContact] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_RegistrationFiling_RegistrationFiling_CommitteeId] FOREIGN KEY ([CommitteeId]) REFERENCES [dbo].[RegistrationFiling] ([Id]),
    CONSTRAINT [FK_RegistrationFiling_RegistrationFiling_OriginalId] FOREIGN KEY ([OriginalId]) REFERENCES [dbo].[RegistrationFiling] ([Id]),
    CONSTRAINT [FK_RegistrationFiling_RegistrationFiling_ParentId] FOREIGN KEY ([ParentId]) REFERENCES [dbo].[RegistrationFiling] ([Id]),
    CONSTRAINT [FK_RegistrationFiling_RegistrationStatus_StatusId] FOREIGN KEY ([StatusId]) REFERENCES [dbo].[RegistrationStatus] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_RegistrationFiling_User_VerifiedById] FOREIGN KEY ([VerifiedById]) REFERENCES [dbo].[User] ([Id]),
    PERIOD FOR SYSTEM_TIME ([PeriodStart], [PeriodEnd])
)
WITH (SYSTEM_VERSIONING = ON (HISTORY_TABLE=[dbo].[RegistrationFilingHistory], DATA_CONSISTENCY_CHECK=ON));








GO


GO
CREATE NONCLUSTERED INDEX [IX_RegistrationFiling_OriginalId]
    ON [dbo].[RegistrationFiling]([OriginalId] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_Registration_UniqueVersions]
    ON [dbo].[RegistrationFiling]([ParentId] ASC, [Version] ASC) WHERE ([StatusId]<>(6) AND [ParentId] IS NOT NULL AND [Version] IS NOT NULL);


GO
CREATE NONCLUSTERED INDEX [IX_RegistrationFiling_VerifiedById]
    ON [dbo].[RegistrationFiling]([VerifiedById] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RegistrationFiling_StatusId]
    ON [dbo].[RegistrationFiling]([StatusId] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RegistrationFiling_SponsorId]
    ON [dbo].[RegistrationFiling]([SponsorId] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RegistrationFiling_PhoneNumberListId]
    ON [dbo].[RegistrationFiling]([PhoneNumberListId] ASC);


GO



GO
CREATE NONCLUSTERED INDEX [IX_Registrations_FilerId]
    ON [dbo].[RegistrationFiling]([FilerId] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RegistrationFiling_ElectionId]
    ON [dbo].[RegistrationFiling]([ElectionId] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RegistrationFiling_CommitteeId]
    ON [dbo].[RegistrationFiling]([CommitteeId] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RegistrationFiling_CandidateId]
    ON [dbo].[RegistrationFiling]([CandidateId] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_RegistrationFiling_AuditableResourceTag]
    ON [dbo].[RegistrationFiling]([AuditableResourceTag] ASC) WHERE ([AuditableResourceTag] IS NOT NULL);


GO
CREATE NONCLUSTERED INDEX [IX_RegistrationFiling_AddressListId]
    ON [dbo].[RegistrationFiling]([AddressListId] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates how many members the lobbyist employer represents.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'NumberOfMembers';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Description of the specific portion or faction of the industry, trade, or profession segment the lobbyist employer represents.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'IndustryPortion';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Description of the industry, trade, or procession segment of the lobbyist employer.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'IndustryDescription';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Type of interest for the lobbyist employer.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'InterestType';


GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Description of the lobbyist employer business.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'BusinessDescription';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Business activity for a lobbyist employer.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'BusinessActivity';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Type of lobbyist employer.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'EmployerType';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Date of completion for a valid ethics course.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'EthicsCourseCompletionDate';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates if the lobbyist will lobby the state legislature.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'StateLegislatureLobbying';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Lobbying firm name or employer name for a lobbyist.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'EmployerName';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates if the lobbyist is a placement agent.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'PlacementAgent';


GO



GO



GO



GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Date the withdrawal is effective.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'WithdrawnAt';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Title of the responsible officer for a lobbying firm.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ResponsibleOfficerTitle';


GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates the date the candidate contributed personal funds in excess of the ceiling.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ContributedPersonalExcessFundsOn';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates the candidate has exceeded the expenditure ceiling in the primary and accepts the voluntary expenditure ceiling in the election.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ExpenditureExceeded';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'FPPC expenditure ceiling amount.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ExpenditureCeilingAmount';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates the candidate has accepted the expenditure limit if in the primary period. ', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ExpenditureLimitAccepted';


GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'County for the election.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ElectionCounty';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Election jurisdiction.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ElectionJurisdiction';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates if the candidate is completing the registration.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'SelfRegister';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Committee identifier.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'CommitteeId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to a committee.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'CommitteeId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates if the organization is a campaign committee.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'CampaignCommittee';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Sponsor of the organization.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'SponsorId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to a Registration Contact.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'SponsorId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Description of committee industry or group.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'IndustryOrGroupDescription';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Description of committee activity.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ActivityDescription';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Description of the legal dispute, if any.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'LegalDispute';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Election identifier.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ElectionId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to an election', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ElectionId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'District number for the election.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ElectionDistrictNumber';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Office sought in the election.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ElectionOfficeSought';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Candidate identifier.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'CandidateId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to a Candidate', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'CandidateId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates if this candidate has run for office before.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'PreviousCandidate';


GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Account number of the financial institution.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'FinancialInstitutionAccountNumber';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Phone number of the financial institution.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'FinancialInstitutionPhone';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Name of the financial institution.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'FinancialInstitutionName';


GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Jurisdiction where committee is active.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'JurisdictionActive';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'County of jurisdiction.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'JurisdictionCounty';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates the committee subtype for the registration.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'CommitteeSubType';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'The set of phone numbers for this entity.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'PhoneNumberListId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to a phone number list.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'PhoneNumberListId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'The set of addresses for this entity.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'AddressListId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to an address list.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'AddressListId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'User that performed the verification.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'VerifiedById';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Verification signature.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'VerificationSignature';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Date the verification was executed.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'VerificationExecutedAt';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Qualification date for the organization or individual.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'DateQualified';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates if the organization is a qualified committee.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'QualifiedCommittee';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Activity level for the organization or individual.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ActivityLevel';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'County for the registration.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'County';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Contact email address for the registration.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'Email';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Type (Class) of Registration stored in this record.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'Discriminator';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'System generated column used by Entity Framework in the table-per-hierarchy (TPH) pattern. TPH stores data for all types in a hierarchy to a single table. This column stores a string value that represents the type (Class) of entity in the record.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'Discriminator';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Unique resource tag.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'AuditableResourceTag';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'A string-serialized unique identifier used to link audit logs to arbitrary resource types.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'AuditableResourceTag';


GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'The identifier of the user who last modified this record.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ModifiedBy';


GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'The identifier of the user who created this record.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'CreatedBy';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Registration status identifier.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'StatusId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to a registration status.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'StatusId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Name of the filer.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'Name';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Requirement depends on filer registration type.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'Name';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Filer to which this registration is linked.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'FilerId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to a Filer.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'FilerId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Date the termination is effective.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'TerminatedAt';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Approval date.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ApprovedAt';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Registration identifier.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'Id';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'This table holds all registration filings in the system -  a registration is a filing that is used to indicate a user can submit disclosure filings in the system. Registrations are a type hierarchy of entities in the system where those types inherit from a common base (Registration). The table-per-hierarchy (TPH) pattern is used to store the data for all types in the hierarchy to a single table. In the TPH pattern, a discriminator column is used to identify which type is stored in each row. The Discriminator column stores a string value that represents the type (Class) of entity in that record.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Registration version used to indicate the version of the registration record as amendments are made.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'Version';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Incremental version number.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'Version';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Parent registration to which this registration is linked. If this registration is an amendment, the ParentId is populated with a reference to the registration ID that was amended by this amendment. This differs from the OriginalId in that it will always point to the previous registration.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ParentId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to a registration.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'ParentId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Original registration to which this registration is linked. If this registration is an amendment, the OriginalId is populated with a reference to the first registration ID that initialized the chain of amendments. This differs from ParentId in that it will always point to the original registration.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'OriginalId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to a registration.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'OriginalId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Candidate middle name.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'MiddleName';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Candidate last name.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'LastName';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates whether the current address is the same as the candidate’s primary address.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'IsSameAsCandidateAddress';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Candidate first name.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'FirstName';


GO



GO
CREATE NONCLUSTERED INDEX [IX_RegistrationFiling_ElectionRaceId]
    ON [dbo].[RegistrationFiling]([ElectionRaceId] ASC);


GO



GO
CREATE NONCLUSTERED INDEX [IX_RegistrationFiling_PoliticalPartyId]
    ON [dbo].[RegistrationFiling]([PoliticalPartyId] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RegistrationFiling_LegislativeSessionId]
    ON [dbo].[RegistrationFiling]([LegislativeSessionId] ASC);


GO


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_RegistrationFiling_LlcDetailsId]
    ON [dbo].[RegistrationFiling]([LlcDetailsId] ASC) WHERE ([LlcDetailsId] IS NOT NULL);


GO



GO



GO



GO



GO



GO


GO


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Nature And Purpose.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'NatureAndPurpose';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Foreign Key reference to the NatureAndInterestType Id for the registration.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'NatureAndInterestTypeId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Employer Name for individual type of nature and interest.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'NatureAndInterestEmployerName';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Industry Group Other Text.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'IndustryGroupOtherText';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Foreign Key reference to the IndustryGroupClassification Id for the registration.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'IndustryGroupClassificationId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Prinicipally derived support.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'DescriptionOfPrincipallyDerivedSupport';


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_RegistrationFiling_ParentId_StatusActive]
    ON [dbo].[RegistrationFiling]([ParentId] ASC) WHERE ([StatusId]<>(6) AND [ParentId] IS NOT NULL);

GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_Registrations_FilerId_Draft]
    ON [dbo].[RegistrationFiling]([FilerId] ASC) WHERE ([StatusId]=(1) AND [FilerId] IS NOT NULL);


GO
CREATE NONCLUSTERED INDEX [IX_RegistrationFiling_CommitteeTypeId]
    ON [dbo].[RegistrationFiling]([CommitteeTypeId] ASC);



GO




GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates the committee type for the registration.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'CommitteeTypeId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to a CommitteeType.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'CommitteeTypeId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates if the user will lobby only specified agencies.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'LobbyOnlySpecifiedAgencies';


GO



GO



GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates if the ethics certification course will be taken within a year.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'IsNewCertification';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates if the ethics course was completed within the past year.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'EthicsCourseCompletedWithinPastYear';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates if the ethics course was completed.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'EthicsCourseCompleted';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Gets or sets a value indicating whether registration is for a lobbying coalition.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'IsLobbyingCoalition';


GO


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Effective date of change.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'EffectiveDateOfChanges';


GO
CREATE NONCLUSTERED INDEX [IX_RegistrationFiling_BusinessSubcategoryId]
    ON [dbo].[RegistrationFiling]([BusinessSubcategoryId] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Gets or sets a value indicating threshold of members.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'IsFiftyMembersOrLess';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Business Subcategory Other Text.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'BusinessSubcategoryOtherText';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Foreign Key reference to the Business Subcategory Id for the registration.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'BusinessSubcategoryId';

GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Foreign Key reference to the LobbyingInterest Id for the registration.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFiling', @level2type = N'COLUMN', @level2name = N'LobbyingInterestId';

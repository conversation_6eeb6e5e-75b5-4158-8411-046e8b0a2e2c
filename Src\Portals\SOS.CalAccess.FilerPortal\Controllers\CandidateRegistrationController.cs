using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Localization;
using Refit;
using SOS.CalAccess.FilerPortal.Alerts;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Extensions;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.SharedModels;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Controllers;

#pragma warning disable S107 // Methods should not have too many parameters
/// <summary>
/// Controller for handling candidate registration related actions.
/// </summary>
public class CandidateRegistrationController(
    IAuthorizationSvc authorizationSvc,
    ICandidateIntentionRegistrationSvc candidateIntentionRegistrationSvc,
    ICandidateRegistrationCtlSvc candidateRegistrationCtlSvc,
    ICandidateSvc candidateSvc,
    IElectionSvc electionSvc,
    IReferenceDataSvc referenceDataSvc,
    IDateTimeSvc dateTimeSvc,
    IStringLocalizer<SharedResources> localizer) : Controller
#pragma warning restore S107 // Methods should not have too many parameters
{
    /// <summary>
    /// Common logic to call before loading a view.
    /// </summary>
    /// <param name="context"></param>
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        SetCommonViewData();
    }

    public async Task<IActionResult> Index()
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Create, User));
        return RedirectToAction(nameof(Page01));
    }

    public ActionResult RedirectToDashboard()
    {
        return RedirectToAction("Index", "Dashboard");
    }

    /// <summary>
    /// Handler for when user starts to edit a form.
    /// Redirects user to predetermined starting page.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<IActionResult> Edit([Required] long id)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: id));

        if (!ModelState.IsValid)
        {
            return RedirectToAction("Index", "Dashboard");
        }
        return RedirectToAction(nameof(Page04), new { Id = id });
    }

    #region Page01 - FR-CAND-Start
    /// <summary>
    /// Loads view for FR-CAND-Start
    /// </summary>
    /// <returns>Page01 View</returns>
    public async Task<IActionResult> Page01()
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Create, User));
        return View();
    }
    #endregion

    #region Page02 - FR-CAND-CandInformation-1
    /// <summary>
    /// Loads view for FR-CAND-CandInformation-1
    /// </summary>
    /// <returns>Page02 View</returns>
    public async Task<IActionResult> Page02()
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Create, User));
        return View();
    }
    #endregion

    #region Page03 - FR-CAND-CandInformation-2

    /// <summary>
    /// Loads view for FR-CAND-CandInformation-2
    /// </summary>
    /// <returns>Page03 View with model</returns>
    public async Task<IActionResult> Page03()
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Create, User));
        var viewModel = new CandidateIntentStep1();
        return View(viewModel);
    }

    /// <summary>
    /// Handler for all submissions to Page03
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public async Task<IActionResult> Page03(
        CandidateIntentStep1 model,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Create, User));

        if (model.Action == FormAction.Continue)
        {
            return await Page03Continue(model, cancellationToken);
        }
        if (model.Action == FormAction.Previous)
        {
            return RedirectToAction(nameof(Page02));
        }

        return View(model);
    }

    /// <summary>
    /// Handler for when Continue button is clicked on Page03
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    private async Task<IActionResult> Page03Continue(
        CandidateIntentStep1 model,
        CancellationToken cancellationToken = default)
    {
        var prefillData = await candidateRegistrationCtlSvc.Page03Submit(model, ModelState, cancellationToken, true);

        if (ModelState.IsValid)
        {
            if (prefillData is not null)
            {
                return View(nameof(Page04), prefillData);
            }
            else
            {
                return View(nameof(Page04), new CandidateIntentionStatementViewModel());
            }
        }

        return View(model);
    }
    #endregion

    #region Page04 - FR-CAND-CandInformation-4
    /// <summary>
    /// Loader for Page04
    /// </summary>
    /// <param name="id"></param>
    /// <returns>Page04 View with model</returns>
    [HttpGet]
    public async Task<IActionResult> Page04(
        [Required] long id,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: id));

        CandidateIntentionStatementResponseDto? registration = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(id);

        if (registration == null)
        {
            return NotFound();
        }

        var model = new CandidateIntentionStatementViewModel(registration);
        return View(model);
    }

    /// <summary>
    /// Handler for all submissions to Page04
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Page04(
        CandidateIntentionStatementViewModel model,
        [FromServices] IAccuMailValidatorService accuMailValidatorService,
        CancellationToken cancellationToken = default)
    {
        if (model.Id is not { })
        {
            await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Create, User));
        }
        else
        {
            await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: model.Id));
        }

        if (model.IsSameAsCandidateAddress)
        {
            model.Addresses.Find(x => x.Purpose == "Mailing")?.Clear();
        }

        if (await accuMailValidatorService.AccuMailValidationHandler(model, ModelState, model.Action == FormAction.Cancel))
        {
            return View(model);
        }

        if (model.Action == FormAction.SaveAndClose)
        {
            return await Page04Save(model, cancellationToken);
        }
        if (model.Action == FormAction.Continue)
        {
            return await Page04Continue(model, cancellationToken);
        }
        if (model.Action == FormAction.Previous)
        {
            return RedirectToAction(nameof(Page03), new { model.Id });
        }

        return View(model);
    }

    /// <summary>
    /// Handler for when user clicks Continue button on Page04
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    private async Task<IActionResult> Page04Continue(
        CandidateIntentionStatementViewModel model,
        CancellationToken cancellationToken = default)
    {
        var registrationId = await candidateRegistrationCtlSvc.Page04Submit(model, ModelState, cancellationToken, true);

        if (!ModelState.IsValid || model.AddressValidationResponse != null)
        {
            return View(nameof(Page04), model);
        }

        return RedirectToAction(nameof(Page05), new { id = registrationId });
    }

    /// <summary>
    /// Handler for when user clicks Save button on Page04
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    private async Task<IActionResult> Page04Save(
        CandidateIntentionStatementViewModel model,
        CancellationToken cancellationToken = default)
    {
        await candidateRegistrationCtlSvc.Page04Submit(model, ModelState, cancellationToken, false);

        if (ModelState.IsValid && model.AddressValidationResponse == null)
        {
            this.SetGlobalAlert(AlertType.Success, ResourceConstants.CisRegistrationCreatedSuccess);

            SetLocalizedToast(CommonResourceConstants.SavedMessage);

            return RedirectToDashboard();
        }

        return View(nameof(Page04), model);
    }
    #endregion

    #region Page05 - FR-CAND-Election-1
    /// <summary>
    /// Loader for Page05
    /// </summary>
    /// <returns>Page05 View</returns>
    [HttpGet]
    public async Task<IActionResult> Page05([FromRoute] long id)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Create, User));
        // Get the registrationId from TempData if not passed in
        _ = long.TryParse(TempData.Peek("Id")?.ToString(), CultureInfo.InvariantCulture, out var idTempData);

        var registrationId = id > 0 ? id : idTempData;

        return View(new CandidateIntentStep2 { Id = registrationId });
    }

    /// <summary>
    /// Handler for all submissions to Page05
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public async Task<IActionResult> Page05(
        CandidateIntentStep2 model,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: model.Id));

        _ = long.TryParse(TempData["Id"]?.ToString(), CultureInfo.InvariantCulture, out var idTempData);

        var id = model!.Id is null or 0 ? idTempData : model.Id;

        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.SaveAndClose:
                    return RedirectToDashboard();
                case FormAction.Continue:
                    return RedirectToAction(nameof(Page06), new { id });
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page04), new { id });
                case FormAction.Cancel:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
                case FormAction.Submit:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
                case null:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }
    #endregion

    #region Page06 - FR-CAND-Election-2
    /// <summary>
    /// Loader for Page06
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> Page06(
        [Required] long id,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: id));

        if (ModelState.IsValid)
        {
            var registration = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(id);

            if (registration is null)
            {
                return NotFound();
            }

            ElectionRace? electionRace = null;
            if (registration.ElectionRaceId.HasValue)
            {
                electionRace = await electionSvc.GetElectionRace(registration.ElectionRaceId.Value);
            }

            var candidateIntentStep2 = new CandidateIntentStep2()
            {
                Id = id,
                RegistrationId = id,
                SelectedElection = electionRace?.Election.Id,
                // TODO defaulting to "State" until support for other options are available.
                SelectedJurisdiction = registration.ElectionJurisdiction ?? "state",
                SelectedElectionYear = electionRace?.Election.ElectionDate.Year.ToString(),
                SelectedOffice = electionRace?.OfficeId,
                SelectedDistrict = electionRace?.DistrictId,
                SelectedPartyAffiliation = registration?.PoliticalPartyId //PartyAffiliation
            };

            return View(candidateIntentStep2);
        }

        return View();
    }

    /// <summary>
    /// Handler for all submissions to Page06
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> Page06(
        CandidateIntentStep2 model,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: model.Id));

        if (model.Id is not { } id)
        {
            return NotFound();
        }
        else
        {
            switch (model.Action)
            {
                case FormAction.SaveAndClose:
                    return await Page06Save(id, model);
                case FormAction.Continue:
                    return await Page06Continue(id, model);
                case FormAction.Previous:
                    return Page06Previous(id);
                case FormAction.Cancel:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
                case FormAction.Submit:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
                case null:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }

    private async Task<IActionResult> Page06Save(
        long id,
        CandidateIntentStep2 model)
    {
        await candidateRegistrationCtlSvc.Election02Submit(id, model, ModelState, false);

        if (ModelState.IsValid)
        {
            return RedirectToDashboard();
        }

        return View(model);
    }

    private async Task<IActionResult> Page06Continue(
        long id,
        CandidateIntentStep2 model)
    {
        await candidateRegistrationCtlSvc.Election02Submit(id, model, ModelState, true);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page07), new { model.Id });
        }

        return View(model);
    }

    private RedirectToActionResult Page06Previous(
        long id)
    {
        return RedirectToAction(nameof(Page05), new { Id = id });
    }
    #endregion

    #region Page07 - FR-CAND-Election-3
    [HttpGet]
    public async Task<IActionResult> Page07(long id)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: id));

        var expenditureAmount = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatementExpenditureExpenseAmount(id);
        // Redirect if page doesn't appply
        if (expenditureAmount == 0.00M)
        {
            string? referrer = Request.Headers.Referer.ToString();
            Uri uri = new(referrer);
            string referrerPath = uri.PathAndQuery;
            // Prevent loopback if expenditure page doesn't apply and comming from Verification page.
            if (referrerPath == $"/CandidateRegistration/Page08/{id}")
            {
                return RedirectToAction(nameof(Page06), new { id });
            }
            else
            {
                return RedirectToAction(nameof(Page08), new { id });
            }
        }
        var registration = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(id);
        var election = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatementElection(id);
        var viewModel = new CandidateIntentionStatementViewModel
        {
            RegistrationId = id,
            ExpenditureLimitAmount = (decimal)expenditureAmount,
            ElectionDate = election?.ElectionDate
        };

        if (registration!.ExpenditureCeilingAmount == 0.00M)
        {
            viewModel.ExpenditureLimitStatementAcceptance = "";
        }
        else if (registration.ExpenditureLimitAccepted)
        {
            viewModel.ExpenditureLimitStatementAcceptance = "true";
        }
        else
        {
            viewModel.ExpenditureLimitStatementAcceptance = "false";
        }
        return View(viewModel);
    }

    /// <summary>
    /// Expenditure01 POST handler
    /// </summary>
    /// <param name="model">verificationviewmodel.</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult> Expenditure01Post(
        [FromForm] CandidateIntentionStatementViewModel model,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: model.RegistrationId));

        if (!ModelState.IsValid)
        {
            return View(nameof(Page07), model);
        }
        // TODO: populate with real data
        CandidateIntentionStatementExpenditureLimitRequest payload = new()
        {
            ExpenditureLimitAccepted = model.ExpenditureLimitStatementAcceptance == "true",
        };

        await candidateIntentionRegistrationSvc.UpdateCandidateIntentionStatementExpenditureLimit(model.RegistrationId, payload);

        return RedirectToAction(nameof(Page08), "CandidateRegistration", new { id = model.RegistrationId });
    }

    /// <summary>
    /// Expenditure01 Save POST handler
    /// </summary>
    /// <param name="model">verificationviewmodel.</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult> Expenditure01SavePost(
        [FromForm] CandidateIntentionStatementViewModel model,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: model.RegistrationId));

        if (!ModelState.IsValid)
        {
            return View(nameof(Page07), model);
        }
        CandidateIntentionStatementExpenditureLimitRequest payload = new()
        {
            ExpenditureLimitAccepted = model.ExpenditureLimitStatementAcceptance == "true",
        };

        await candidateIntentionRegistrationSvc.UpdateCandidateIntentionStatementExpenditureLimit(model.RegistrationId, payload);

        return RedirectToDashboard();
    }
    #endregion

    #region Page08 - FR-CAND-Verification-1
    /// <summary>
    /// Verification page landing to submit the final form
    /// </summary>
    /// <param name="id">Registration ID</param>
    /// <param name="cancellationToken">cancellation token</param>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult> Page08(
        long id,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: id));

        if (!ModelState.IsValid)
        {
            return NotFound();
        }
        var response = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(id);
        if (response is not { } res)
        {
            return NotFound();
        }
        var isSubmission = await authorizationSvc.IsAuthorized(new(Permission.Registration_Candidate_Attest, User, registrationId: id));

        return View(new VerificationViewModel() { RegistrationId = res.Id, IsSubmission = isSubmission, CandidateName = $"{res.FirstName} {res.LastName}", ExecutedOn = dateTimeSvc.GetCurrentDateTime() });
    }

    /// <summary>
    /// Verifies and submits the candidateIntentStatement registration with attestation.
    /// </summary>
    /// <param name="model">verification viewModel.</param>
    /// <param name="cancellationToken">cancellation token</param>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult> Page08(
        VerificationViewModel model,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Amend, User, registrationId: model.Id));
        var isSubmission = await authorizationSvc.IsAuthorized(new(Permission.Registration_Candidate_Attest, User, registrationId: model.Id));

        if (model.Id is null && model.RegistrationId == 0)
        {
            return NotFound();
        }
        try
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }
            if (model.Action == FormAction.SaveAndClose)
            {
                SetLocalizedToast(CommonResourceConstants.SavedMessage);

                return RedirectToDashboard();
            }
            else if (model.Action == FormAction.Previous)
            {
                return RedirectToAction(nameof(Page07), new { model.Id });
            }
            else if (model.Action == FormAction.Submit)
            {
                RegistrationResponseDto response;
                if (isSubmission)
                {
                    response = await candidateIntentionRegistrationSvc.SubmitCandidateIntentionStatement(model.RegistrationId);
                }
                else
                {
                    response = await candidateIntentionRegistrationSvc.SendForAttestation(model.RegistrationId);
                }

                if (response.ValidationErrors.Count != 0)
                {
                    foreach (var error in response.ValidationErrors)
                    {
                        ModelState.AddModelError("", error.Message.Replace("{{Field Name}}", error.FieldName));
                    }
                    return View(model);
                }
                else
                {
                    return model.IsSubmission
                              ? RedirectToAction(nameof(Submission), new { id = model.RegistrationId })
                              : RedirectToAction(nameof(AttestationRequestSent), new { id = model.RegistrationId });
                }
            }

            return View(model);
        }
        catch (ApiException ex)
        {

            //if (ex.StatusCode == HttpStatusCode.NotFound) { } //handle 404, 422 etc for others as needed in the future
            this.AddGlobalAlertToViewModel(model, new GlobalAlert { Type = AlertType.Danger.ToString(), Message = ex.Message });

            return View(nameof(Page08), new VerificationViewModel() { RegistrationId = model.RegistrationId, Messages = model.Messages });
        }
    }
    #endregion

    /// <summary>
    /// Cancel the current action and return to dashboard
    /// </summary>
    /// <returns></returns>
    public async Task<IActionResult> Cancel()
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Create, User));

        return RedirectToDashboard();
    }

    /// <summary>
    /// Cancels the existing draft registration.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="registrationsApi">Registrations API wrapper.</param>
    /// <param name="model">CandidateIntentionStatementViewModel.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult> Cancel(
        [Required] long id,
        CandidateIntentionStatementViewModel model,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: id));

        if (id <= 0)
        {
            return NotFound();
        }
        try
        {
            await candidateIntentionRegistrationSvc.CancelCandidateIntentionStatement(id);
            // Neither of these seem to work
            // TempData["SuccessMessage"] = "Registration cancelled successfully";
            // this.AddGlobalAlertToViewModel(model, new GlobalAlert { Type = AlertType.Success.ToString().ToLower(), Message = "Registration Canceled successfully." });

            return RedirectToDashboard();
        }
        catch (ApiException ex)
        {

            //if (ex.StatusCode == HttpStatusCode.NotFound) { } //handle 404, 422 etc for others as needed in the future
            this.AddGlobalAlertToViewModel(model, new GlobalAlert { Type = AlertType.Danger.ToString(), Message = ex.Message });

            return RedirectToDashboard();
        }
    }

    /// <summary>
    /// Submission page landing after submitting the candidateIntentionStatement flow.
    /// </summary>
    /// <param name="executedAt"></param>
    /// <param name="id">Registration ID. Derived from route param</param>
    /// <param name="model">Model containing the form submission data from view.</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    [Route("[controller]/Submission/{id}")]
    public ActionResult Submission(
        [Required] int id,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        return View("Page09", new ConfirmationViewModel() { Id = id, IsSubmission = true, ExecutedOn = dateTimeSvc.GetCurrentDateTime() });
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [Route("[controller]/Submission/{id}")]
    public async Task<IActionResult> Submission(
    [Required] int id,
    [FromForm] ConfirmationViewModel model,
    CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return View("Page09", model);
        }

        var response = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(id);
        if (response is null)
        {
            return NotFound();
        }

        if (model.Action == FormAction.Continue)
        {
            if (model.AnticipatesSpendingOrReceivingOverXXXX == "yes")
            {
                return RedirectToAction("Page01", "LinkCommittee", new { id });
            }
            else
            {
                return RedirectToAction(nameof(Index), "Form470", new { filerId = response.FilerId });
            }
        }
        if (model.Action == FormAction.Close)
        {
            SetLocalizedToast(CommonResourceConstants.ToastSaved);
            return RedirectToDashboard();
        }

        return RedirectToAction("Submission", new { id });
    }


    /// <summary>
    /// Attestation Request sent page landing after non-candidate user request to attest on the candidateIntentionStatement flow.
    /// </summary>
    /// <param name="executedAt"></param>
    /// <param name="id">Registration ID. Derived from route param</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    [Route("[controller]/AttestationRequestSent/{id}")]
    public ActionResult AttestationRequestSent(
        [Required] int id,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        // TODO: Add service to fetch pending items

        var pendingItems = new List<PendingItemSharedViewModel>
        {
            new() { Item = "Candidate Attestation", Status = "Not yet started" },
        };
        return View("Page09", new ConfirmationViewModel() { Id = id, IsSubmission = false, ExecutedOn = dateTimeSvc.GetCurrentDateTime(), PendingItems = pendingItems });
    }

    /// <summary>
    /// The closing  process in the Candidate Intent Statement registration.
    /// </summary>
    /// <returns></returns>
    public ActionResult Close()
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        return RedirectToDashboard();

    }
    /// <summary>
    /// The first page in the Candidate Intent Statement registration process.
    /// This is the start of the process for a new registration
    /// </summary>
    /// <param name="search">Search text input. Partial of candidate name.</param>
    /// <param name="cancellationToken">Standard .NET cancellationToken for async operations</param>
    /// <returns></returns>
    [HttpGet]
    public async Task<JsonResult> SearchCandidateByName(
        [FromQuery] string search,
        CancellationToken cancellationToken
    )
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Candidate_SearchByName, User));

        var data = await candidateSvc.SearchCandidateByName(search);
        return new JsonResult(data, new JsonSerializerOptions { PropertyNamingPolicy = null });
    }

    public async Task<JsonResult> GetYears()
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.ReferenceData_Retrieve, User));

        var years = await electionSvc.GetElectionYears();

        return new JsonResult(years.Select(x => new { Id = x, Name = x }), new JsonSerializerOptions { PropertyNamingPolicy = null });
    }

    public async Task<JsonResult> GetElectionsForYear([FromQuery] int electionYear, CancellationToken cancellationToken)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.ReferenceData_Retrieve, User));

        if (ModelState.IsValid)
        {
            //populate elections for year
            var allElections = await electionSvc.GetAllElections();
            var yearElections = allElections.Where(x => x.ElectionDate.Year == electionYear && (x.ElectionType!.Id == 3002 || x.ElectionType.Id == 3004));
            var electionSelection = yearElections.Select(x => new { x.Id, x.Name });

            return new JsonResult(electionSelection, new JsonSerializerOptions { PropertyNamingPolicy = null });
        }

        return new JsonResult(Enumerable.Empty<string>(), new JsonSerializerOptions { PropertyNamingPolicy = null });
    }

    public async Task<JsonResult> GetOfficesForElection([FromQuery] long electionId, CancellationToken cancellationToken)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.ReferenceData_Retrieve, User));

        if (ModelState.IsValid)
        {
            var allOffices = await electionSvc.GetOfficesForElection(electionId);

            var officeDict = allOffices.Select(x => new { x.Id, x.Name });

            return new JsonResult(officeDict, new JsonSerializerOptions { PropertyNamingPolicy = null });
        }

        return new JsonResult(Enumerable.Empty<string>(), new JsonSerializerOptions { PropertyNamingPolicy = null });
    }

    public async Task<JsonResult> GetOfficeDetails([FromQuery] int officeId, CancellationToken cancellationToken)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.ReferenceData_Retrieve, User));

        if (ModelState.IsValid)
        {
            var office = await referenceDataSvc.GetOffice(officeId);

            return new JsonResult(office, new JsonSerializerOptions { PropertyNamingPolicy = null });
        }

        return new JsonResult(new { }, new JsonSerializerOptions { PropertyNamingPolicy = null });
    }


    public async Task<JsonResult> GetDistrictsForOffice([FromQuery] int officeId, [FromQuery] long electionId, CancellationToken cancellationToken)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.ReferenceData_Retrieve, User));

        if (ModelState.IsValid)
        {
            var allDistricts = await electionSvc.GetDistrictsForElectionAndOffice(electionId, officeId);
            var districtsInElectionYear = allDistricts.Select(x => new { x.Id, x.Name });

            return new JsonResult(districtsInElectionYear, new JsonSerializerOptions { PropertyNamingPolicy = null });
        }

        return new JsonResult(Enumerable.Empty<string>(), new JsonSerializerOptions { PropertyNamingPolicy = null });
    }

    public async Task<JsonResult> GetPartiesForOffice([FromQuery] long officeId, CancellationToken cancellationToken)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.ReferenceData_Retrieve, User));

        if (ModelState.IsValid)
        {
            var office = await referenceDataSvc.GetOffice(officeId);
            var parties = (await referenceDataSvc.GetAllPoliticalParties()).Select(x => new { x.Id, x.Name });

            if (office is not null && office.IsNonPartisanOffice)
            {
                // todo: risk that we are filtering by a specific ID that is generated by the system
                parties = parties.Where(x => x.Id == 1).ToList();
            }
            else
            {
                // Remove Non-Partisan from result if office selected is non-partisan
                parties = parties.Where(x => x.Id != 1).ToList();
            }

            return new JsonResult(parties, new JsonSerializerOptions { PropertyNamingPolicy = null });
        }

        return new JsonResult(Enumerable.Empty<string>(), new JsonSerializerOptions { PropertyNamingPolicy = null });

    }

    /// <summary>
    /// Sets UI data that is common to all pages in this form.
    /// </summary>
    private void SetCommonViewData()
    {
        ViewData[LayoutConstants.Title] = localizer[ResourceConstants.CisRegistrationTitle].Value;
        ViewData[LayoutConstants.Breadcrumbs] = new List<Breadcrumb>()
        {
            new(localizer[ResourceConstants.FilerPortalTitle].Value, "/FilerPortal"),
            new(localizer[ResourceConstants.CisRegistrationBreadcrumb], "/CandidateRegistration"),
        };
        ViewData["ProgressItem1Name"] = "1. " + localizer[ResourceConstants.Candidate].Value;
        ViewData["ProgressItem2Name"] = "2. " + localizer[ResourceConstants.Election].Value;
        ViewData["ProgressItem3Name"] = "3. " + localizer[ResourceConstants.Verification].Value;
    }

    public void SetLocalizedToast(string localizerKey)
    {
        var savedMessage = localizer[localizerKey]?.Value ?? "";
        TempData["ToastMessage"] = savedMessage;
        TempData["ToastType"] = "e-toast-success";
        TempData["ToastShowCloseButton"] = "true";
        TempData["ToastX"] = "Right";
        TempData["ToastY"] = "Bottom";
    }
}

using System.ComponentModel.DataAnnotations;
using SOS.CalAccess.FilerPortal.Models.Contacts;

namespace SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration;

public class LobbyistEmployerRegistrationStep02AddLobbyistViewModel : LobbyistEmployerRegistrationBase
{
    /// <summary>
    /// Gets or sets the targeted contact.
    /// </summary>
    [Display(Name = "Contact")]
    public long? ContactId { get; set; }
    /// <summary>
    /// Gets or sets the lobbying firm's registered filing ID (if registered firm is selected)
    /// </summary>
    public long? RegistrationFilingId { get; set; }
    /// <summary>
    /// Gets or sets the contact information.
    /// </summary>
    public GenericContactViewModel? Contact { get; set; }
    /// <summary>
    /// Gets or sets Lobbying Employer or Firm Id
    /// Used lobbyist registration process.
    /// </summary>
    public long? LobbyistId { get; set; }
}

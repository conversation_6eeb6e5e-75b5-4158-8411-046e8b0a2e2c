@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@model SOS.CalAccess.FilerPortal.Models.Dashboard.DashboardViewModel

@{
    ViewData["Title"] = "Dashboard";
    var latestRegistrations = Model.Registrations
    .GroupBy(r => r.OriginalId)
    .Select(g => g.OrderByDescending(r => r.Id).First())
    .ToHashSet();
}

@functions
{
    // region - Get Routing Path

    public static string GetRegistrationRoutingPath(string type)
    {
        switch (type)
        {
            case RegistrationConstants.RegistrationType.CandidateIntentionStatement:
                return RegistrationConstants.RoutingPath.CandidateIntentionStatement;
            case RegistrationConstants.RegistrationType.CandidateIntentionStatementAmendment:
                return RegistrationConstants.RoutingPath.CandidateIntentionStatementAmendment;
            case RegistrationConstants.RegistrationType.CandidateIntentionStatementWithdrawal:
                return RegistrationConstants.RoutingPath.CandidateIntentionStatementWithdrawal;
            case RegistrationConstants.RegistrationType.SlateMailerOrganization:
                return RegistrationConstants.RoutingPath.SlateMailerOrganization;
            case RegistrationConstants.RegistrationType.SlateMailerOrganizationAmendment:
                return RegistrationConstants.RoutingPath.SlateMailerOrganizationAmendment;
            case RegistrationConstants.RegistrationType.Lobbyist:
            case RegistrationConstants.RegistrationType.LobbyistRenewal:
            case RegistrationConstants.RegistrationType.LobbyistWithdrawal:
            case RegistrationConstants.RegistrationType.LobbyistTermination:
                return RegistrationConstants.RoutingPath.Lobbyist;
            case RegistrationConstants.RegistrationType.LobbyistEmployer:
                return RegistrationConstants.RoutingPath.LobbyistEmployer;
            default:
                return "";
        }
    }

    public static string GetAmendmentRegistrationRoutingPath(string type)
    {
        switch (type)
        {
            case RegistrationConstants.RegistrationType.CandidateIntentionStatement:
            case RegistrationConstants.RegistrationType.CandidateIntentionStatementAmendment:
                return RegistrationConstants.RoutingPath.CandidateIntentionStatementAmendment;
            case RegistrationConstants.RegistrationType.SlateMailerOrganization:
            case RegistrationConstants.RegistrationType.SlateMailerOrganizationAmendment:
                return RegistrationConstants.RoutingPath.SlateMailerOrganizationAmendment;
            case RegistrationConstants.RegistrationType.Lobbyist:
                return RegistrationConstants.RoutingPath.Lobbyist;
            default:
                return "";
        }
    }

    public static string GetPendingRegistrationRoutingPath(string type)
    {
        switch (type)
        {
            case RegistrationConstants.RegistrationType.CandidateIntentionStatement:
                return RegistrationConstants.RoutingPath.CandidateIntentionStatement;
            case RegistrationConstants.RegistrationType.CandidateIntentionStatementAmendment:
                return RegistrationConstants.RoutingPath.CandidateIntentionStatementAmendment;
            case RegistrationConstants.RegistrationType.Lobbyist:
                return RegistrationConstants.RoutingPath.Lobbyist;
            case RegistrationConstants.RegistrationType.LobbyistEmployer:
                return RegistrationConstants.RoutingPath.LobbyistEmployer;
            // SMO has a summary page
            case RegistrationConstants.RegistrationType.SlateMailerOrganization:
            case RegistrationConstants.RegistrationType.SlateMailerOrganizationAmendment:
                return RegistrationConstants.RoutingPath.SlateMailerOrganization;
            default:
                return "";
        }
    }

    public static string GetWithdrawalRegistrationRoutingPath(string type)
    {
        switch (type)
        {
            case RegistrationConstants.RegistrationType.CandidateIntentionStatement:
            case RegistrationConstants.RegistrationType.CandidateIntentionStatementAmendment:
                return RegistrationConstants.RoutingPath.CandidateIntentionStatementWithdrawal;
            case RegistrationConstants.RegistrationType.Lobbyist:
            case RegistrationConstants.RegistrationType.LobbyistRenewal:
                return RegistrationConstants.RoutingPath.Lobbyist;
            default:
                return "";
        }
    }

    public static string GetDisplayType(string type)
    {
        switch (type)
        {
            case RegistrationConstants.RegistrationType.Lobbyist:
                return "Lobbyist";
            case RegistrationConstants.RegistrationType.LobbyistWithdrawal:
                return "Lobbyist Withdrawal";
            case RegistrationConstants.RegistrationType.LobbyistTermination:
                return "Lobbyist Termination";
            case RegistrationConstants.RegistrationType.LobbyistEmployer:
                return "Lobbyist Employer";
            case RegistrationConstants.RegistrationType.LobbyistAmendment:
                return "Lobbyist Amendment";
            case RegistrationConstants.RegistrationType.LobbyistRenewal:
                return "Lobbyist Renew";
            default:
                return type;
        }
    }

    // region - Get Action

    public static string GetEditAction(string type)
    {
        switch (type)
        {
            case RegistrationConstants.RegistrationType.LobbyistWithdrawal:
                return RegistrationConstants.RoutingPath.LobbyistRegistrationWithdrawal;
            case RegistrationConstants.RegistrationType.LobbyistTermination:
                return RegistrationConstants.RoutingPath.LobbyistRegistrationTermination;
            default:
                return "Edit";
        }
    }

    public static string GetWithdrawalRegistrationAction(string type)
    {
        switch (type)
        {
            case RegistrationConstants.RegistrationType.Lobbyist:
            case RegistrationConstants.RegistrationType.LobbyistRenewal:
                return RegistrationConstants.RoutingPath.LobbyistRegistrationWithdrawal;
            default:
                return "Index";
        }
    }

    public static string GetPendingAction(string type)
    {
        switch (type)
        {
            case RegistrationConstants.RegistrationType.SlateMailerOrganization:
            case RegistrationConstants.RegistrationType.SlateMailerOrganizationAmendment:
                return "ViewOrViewAndComplete";
             case RegistrationConstants.RegistrationType.CandidateIntentionStatementWithdrawal:
                return "CompleteAttestation";
            default:
                return "Edit";
        }
    }

    // region - Is Allowed To

    public static bool IsAllowedToEdit(string type)
    {
        var allowedRegistrationTypes = new List<string>
        {
            RegistrationConstants.RegistrationType.CandidateIntentionStatement,
            RegistrationConstants.RegistrationType.CandidateIntentionStatementAmendment,
            RegistrationConstants.RegistrationType.CandidateIntentionStatementWithdrawal,
            RegistrationConstants.RegistrationType.SlateMailerOrganization,
            RegistrationConstants.RegistrationType.SlateMailerOrganizationAmendment,
            RegistrationConstants.RegistrationType.Lobbyist,
            RegistrationConstants.RegistrationType.LobbyistRenewal,
            RegistrationConstants.RegistrationType.LobbyistWithdrawal,
            RegistrationConstants.RegistrationType.LobbyistTermination,
            RegistrationConstants.RegistrationType.LobbyistEmployer,
        };

        return allowedRegistrationTypes.Contains(type);
    }

    public static bool IsAllowedToAmend(string type)
    {
        var allowedRegistrationTypes = new List<string>
        {
            RegistrationConstants.RegistrationType.CandidateIntentionStatement,
            RegistrationConstants.RegistrationType.CandidateIntentionStatementAmendment,
            RegistrationConstants.RegistrationType.SlateMailerOrganization,
            RegistrationConstants.RegistrationType.SlateMailerOrganizationAmendment,
        };

        return allowedRegistrationTypes.Contains(type);
    }

    public static bool IsAllowToRenewal(string type)
    {
        var allowedRegistrationTypes = new List<string>
        {
            RegistrationConstants.RegistrationType.Lobbyist,
        };

        return allowedRegistrationTypes.Contains(type);
    }

    public static bool IsAllowedToAmendTerminate(string type)
    {
        var allowedRegistrationTypes = new List<string>
        {
            RegistrationConstants.RegistrationType.SlateMailerOrganization,
            RegistrationConstants.RegistrationType.SlateMailerOrganizationAmendment
        };

        return allowedRegistrationTypes.Contains(type);
    }

    public static bool IsAllowedToTerminate(string type)
    {
        var allowedRegistrationTypes = new List<string>
        {
            RegistrationConstants.RegistrationType.CandidateIntentionStatement,
            RegistrationConstants.RegistrationType.CandidateIntentionStatementAmendment,
            RegistrationConstants.RegistrationType.Lobbyist,
        };

        return allowedRegistrationTypes.Contains(type);
    }

    public static bool IsAllowedToWithdrawal(string type, string status)
    {
        var allowStatuses = new List<string>
        {
            "Accepted",
            "Rejected",
            "Hold for PRD review"
        };
        var allowedRegistrationTypes = new List<string>
        {
            RegistrationConstants.RegistrationType.CandidateIntentionStatement,
            RegistrationConstants.RegistrationType.CandidateIntentionStatementAmendment,
            RegistrationConstants.RegistrationType.Lobbyist,
        };

        return allowedRegistrationTypes.Contains(type) && allowStatuses.Contains(status);
    }

    public static bool IsAllowedToAttest(string type, string status)
    {
        var allowStatuses = new List<string>
        {
            "Pending",
        };
        var allowedRegistrationTypes = new List<string>
        {
            RegistrationConstants.RegistrationType.CandidateIntentionStatementWithdrawal
        };

        return allowedRegistrationTypes.Contains(type) && allowStatuses.Contains(status);
    }
}

<div class="container mt-4">
    <h2>My Registrations</h2>

    @if (!Model.Registrations.Any())
    {
        <div class="alert alert-info">
            You don't have any registrations yet.
        </div>
    }
    else
    {
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-light">
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var registration in Model.Registrations)
                    {
                        var isLatest = latestRegistrations.Contains(registration);
                        var isDisable = false;
                        if (registration.OriginalId != null)
                        {
                            isDisable = Model.Registrations.Any(x => x.OriginalId == registration.OriginalId && x.StatusName == "Draft");
                        }

                        <tr>
                            <td>@registration.Id</td>
                            <td>@registration.Name</td>
                            <td>@GetDisplayType(registration.Type)</td>
                            <td>@registration.StatusName</td>
                            <td>
                                @if (@registration.FilerId is not null)
                                {
                                    <a asp-controller="Filer" asp-action="Index" asp-route-id="@registration.FilerId"
                                       class="btn btn-outline-primary btn-sm">
                                        View Filer
                                    </a>
                                }
                                @if (registration.StatusName == "Draft" && IsAllowedToEdit(@registration.Type))
                                {
                                    <a asp-controller="@GetRegistrationRoutingPath(@registration.Type)" asp-action="@GetEditAction(registration.Type)" asp-route-id="@registration.Id"
                                       class="btn btn-outline-primary btn-sm">
                                        Edit
                                    </a>
                                }
                                @if (registration.StatusName == "Pending")
                                {
                                    <a asp-controller="@GetRegistrationRoutingPath(@registration.Type)" asp-action="@GetPendingAction(@registration.Type)" 
                                       asp-route-id="@registration.Id" class="btn btn-outline-primary btn-sm">
                                        View or View and Complete
                                    </a>
                                }
                                @if (IsAllowedToAttest(@registration.Type, @registration.StatusName))
                                {
                                    <a asp-controller="@GetRegistrationRoutingPath(@registration.Type)" asp-action="@GetPendingAction(@registration.Type)"
                                       asp-route-id="@registration.Id" class="btn btn-outline-primary btn-sm">
                                        Complete Attestation
                                    </a>
                                }
                                @if (registration.Type == RegistrationConstants.RegistrationType.Lobbyist && (registration.StatusName == "Accepted" || registration.StatusName == "Rejected") && isLatest)
                                {
                                    <a asp-controller="@GetAmendmentRegistrationRoutingPath(registration.Type)" asp-action="Amend" asp-route-id="@registration.Id"
                                       class="btn btn-outline-primary btn-sm @(isDisable ? "disabled" : "")">
                                        Amend
                                    </a>
                                }
                                @if (@registration.StatusName == "Accepted" && IsAllowedToAmend(@registration.Type) && isLatest)
                                {
                                    <a asp-controller="@GetAmendmentRegistrationRoutingPath(@registration.Type)" asp-action="Index"
                                       asp-route-id="@registration.Id" class="btn btn-outline-primary btn-sm @(isDisable ? "disabled" : "")">
                                        @(IsAllowedToAmendTerminate(registration.Type) ? "Amend/Terminate" : "Amend")
                                    </a>
                                }
                                @if (IsAllowedToWithdrawal(@registration.Type, @registration.StatusName) && isLatest)
                                {
                                    <a href="#"
                                       class="btn btn-outline-primary btn-sm"
                                       data-bs-toggle="modal"
                                       data-bs-target="#withdrawConfirmModal"
                                       data-id="@registration.Id"
                                       data-action="@(Url.Action(GetWithdrawalRegistrationAction(registration.Type), GetWithdrawalRegistrationRoutingPath(registration.Type), new { id = registration.Id }))">
                                       Withdraw
                                    </a>
                                }
                                @if (@registration.StatusName == "Accepted" && IsAllowedToTerminate(@registration.Type) && isLatest)
                                {
                                    <a href="#"
                                       class="btn btn-outline-primary btn-sm"
                                       data-bs-toggle="modal"
                                       data-bs-target="#terminateConfirmModal"
                                       data-id="@registration.Id"
                                       data-action="@Url.Action("TerminationNotice", GetRegistrationRoutingPath(registration.Type), new { id = registration.Id })">
                                        Terminate
                                    </a>
                                }
                                @if (@registration.StatusName == "Accepted" && IsAllowToRenewal(@registration.Type) && isLatest)
                                {
                                    <a asp-controller="@GetRegistrationRoutingPath(@registration.Type)" asp-action="Page02"
                                       asp-route-id="@registration.Id" asp-route-isRenewal="true" class="btn btn-outline-primary btn-sm">
                                        Renew
                                    </a>
                                }
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
</div>

@{
    var terminateModal = new ConfirmDialogModal(
        Title: "Are you sure you want to terminate your registration?",
        Body: "UI Text Explanation under development and review",
        CloseButtonText: "Cancel",
        SubmitButtonText: "Continue",
        ActionUrl: "",
        Id: "terminateConfirmModal"
    );
    var withdrawModal = new ConfirmDialogModal(
        Title: "Are you sure you want to withdraw your registration?",
        Body: "UI Text Explanation under development and review",
        CloseButtonText: "Cancel",
        SubmitButtonText: "Continue",
        ActionUrl: "",
        Id: "withdrawConfirmModal"
    );
}

<partial name="_ConfirmDialog" model="terminateModal" />
<partial name="_ConfirmDialog" model="withdrawModal" />

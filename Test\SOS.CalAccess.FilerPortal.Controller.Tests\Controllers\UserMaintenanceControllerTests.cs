using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Localization;
using Namotion.Reflection;
using NSubstitute;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

[TestFixture]
public class UserMaintenanceControllerTests
{
    private UserMaintenanceController _controller;
    private IUserMaintenanceSvc _userMaintenanceSvc;
    private IAccuMailValidatorService _accuMailValidatorService;
    private IStringLocalizer<SharedResources> _localizer;

    /// <summary>
    /// Setuo dependencies
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _userMaintenanceSvc = Substitute.For<IUserMaintenanceSvc>();
        _accuMailValidatorService = Substitute.For<IAccuMailValidatorService>();
        _localizer = Substitute.For<IStringLocalizer<SharedResources>>();
        _controller = new UserMaintenanceController(_userMaintenanceSvc, _accuMailValidatorService, _localizer);
    }

    /// <summary>
    /// Tear down objects
    /// </summary>
    [TearDown]
    public void TearDown()
    {
        _controller.Dispose();
    }

    /// <summary>
    /// User details with address and phone number dto details
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task UserDetails_ShouldReturnView()
    {
        // Arrange
        long userId = 1;

        List<AddressViewModel> addressViewModel = new()
        {
            new(){
            Street = "Test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Type = "Test type"
            }
        };

        AddressDto addressDto = new()
        {
            Street = "Test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Type = "Test type"
        };

        List<PhoneNumberDto> phoneNumberDto = new()
        {
            new(){
            CountryCode = "Test country code",
            Extension = "Test extension",
            Id = 1,
            InternationalNumber = true,
            Number = "Test number",
            SelectedCountry = 1,
            SetAsPrimaryPhoneNumber = true,
            Type = "Test type",
            }
        };

        var userAccount = new AccountManagementModel
        {
            Id = userId,
            Email = "Test email",
            FirstName = "Test first name",
            LastName = "Test last name",
            UserName = "Test user name",
            Addresses = addressViewModel,
            PhoneNumberDto = phoneNumberDto
        };

        UserAccountDetailsDto userDto = new()
        {
            Email = "Test email",
            FirstName = "Test first name",
            LastName = "Test last name",
            UserName = "Test user name",
            AddressDto = addressDto,
            PhoneNumberDto = phoneNumberDto
        };

        var phones = userDto.PhoneNumberDto;
        if (phones == null)
        {
            phones = new List<PhoneNumberDto> { new() };
        }

        BasicUserDto basicUser = new(1, "Test email", "Test first name", "Test last anme");

        // Act
        var currentUser = _userMaintenanceSvc.GetCurrentUser().Returns(basicUser);
        var userDetails = _userMaintenanceSvc.GetUserAccountDetailsByUserId(userId).Returns(userDto);

        var result = await _controller.UserDetails() as ViewResult;
        var model = result!.Model as AccountManagementModel;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(model, Is.Not.Null);
            Assert.That(phones, Is.Not.Null);
            Assert.That(phones, Has.Count.EqualTo(1));
            Assert.That(model!.Suggestions, Has.Count.EqualTo(0));
            Assert.That(model!.FirstName, Is.EqualTo("Test first name"));
            Assert.That(model!.LastName, Is.EqualTo("Test last name"));
            Assert.That(model!.UserName, Is.EqualTo("Test user name"));
            Assert.That(model!.Email, Is.EqualTo("Test email"));
            Assert.That(model!.Addresses, Has.Count.EqualTo(1));
            Assert.That(model!.Addresses[0].Street, Is.EqualTo("Test street"));
            Assert.That(model!.Addresses[0].City, Is.EqualTo("Test city"));
            Assert.That(model!.Addresses[0].State, Is.EqualTo("Test state"));
            Assert.That(model!.Addresses[0].Zip, Is.EqualTo("Test zip"));
            Assert.That(model!.Addresses[0].Country, Is.EqualTo("Test country"));
            Assert.That(model!.PhoneNumberDto, Has.Count.EqualTo(1));
            Assert.That(model!.PhoneNumberDto![0].Number, Is.EqualTo("Test number"));
            Assert.That(model!.PhoneNumberDto![0].Extension, Is.EqualTo("Test extension"));
            Assert.That(model!.PhoneNumberDto![0].CountryCode, Is.EqualTo("Test country code"));
            Assert.That(model!.PhoneNumberDto![0].Type, Is.EqualTo("Test type"));
            Assert.That(model!.PhoneNumberDto![0].InternationalNumber, Is.EqualTo(true));
            Assert.That(model!.PhoneNumberDto![0].SetAsPrimaryPhoneNumber, Is.EqualTo(true));
            Assert.That(model!.PhoneNumberDto![0].SelectedCountry, Is.EqualTo(1));
        });
    }

    /// <summary>
    /// User details without phone number dto
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task UserDetails_WithoutPhoneNumberDto()
    {
        // Arrange
        long userId = 1;

        List<AddressViewModel> addressViewModel = new()
        {
            new(){
            Street = "Test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Type = "Test type"
            }
        };

        AddressDto addressDto = new()
        {
            Street = "Test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Type = "Test type"
        };

        List<PhoneNumberDto> phoneNumberDto = new()
        {
            new(){
            CountryCode = "Test country code",
            Extension = "Test extension",
            Id = 1,
            InternationalNumber = true,
            Number = "Test number",
            SelectedCountry = 1,
            SetAsPrimaryPhoneNumber = true,
            Type = "Test type",
            }
        };

        var userAccount = new AccountManagementModel
        {
            Id = userId,
            Email = "Test email",
            FirstName = "Test first name",
            LastName = "Test last name",
            UserName = "Test user name",
            Addresses = addressViewModel,
            //PhoneNumberDto = phoneNumberDto
        };

        UserAccountDetailsDto userDto = new()
        {
            Email = "Test email",
            FirstName = "Test first name",
            LastName = "Test last name",
            UserName = "Test user name",
            AddressDto = addressDto,
            //PhoneNumberDto = phoneNumberDto
        };

        var phones = userDto.PhoneNumberDto;
        if (phones == null)
        {
            phones = new List<PhoneNumberDto> { new() };
        }

        BasicUserDto basicUser = new(1, "Test email", "Test first name", "Test last anme");

        // Act
        var currentUser = _userMaintenanceSvc.GetCurrentUser().Returns(basicUser);
        var userDetails = _userMaintenanceSvc.GetUserAccountDetailsByUserId(userId).Returns(userDto);

        var result = await _controller.UserDetails() as ViewResult;
        var model = result!.Model as AccountManagementModel;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(model, Is.Not.Null);
            Assert.That(phones, Is.Not.Null);
            Assert.That(phones, Has.Count.EqualTo(1));
            Assert.That(model!.Suggestions, Has.Count.EqualTo(0));
            Assert.That(model!.FirstName, Is.EqualTo("Test first name"));
            Assert.That(model!.LastName, Is.EqualTo("Test last name"));
            Assert.That(model!.UserName, Is.EqualTo("Test user name"));
            Assert.That(model!.Email, Is.EqualTo("Test email"));
            Assert.That(model!.Addresses, Has.Count.EqualTo(1));
            Assert.That(model!.Addresses[0].Street, Is.EqualTo("Test street"));
            Assert.That(model!.Addresses[0].City, Is.EqualTo("Test city"));
            Assert.That(model!.Addresses[0].State, Is.EqualTo("Test state"));
            Assert.That(model!.Addresses[0].Zip, Is.EqualTo("Test zip"));
            Assert.That(model!.Addresses[0].Country, Is.EqualTo("Test country"));
            Assert.That(model!.PhoneNumberDto, Has.Count.EqualTo(1));
            Assert.That(model!.PhoneNumberDto![0].Number, Is.Null);
            Assert.That(model!.PhoneNumberDto![0].Extension, Is.Null);
            Assert.That(model!.PhoneNumberDto![0].CountryCode, Is.Null);
            Assert.That(model!.PhoneNumberDto![0].Type, Is.Null);
            Assert.That(model!.PhoneNumberDto![0].InternationalNumber, Is.Null);
            Assert.That(model!.PhoneNumberDto![0].SetAsPrimaryPhoneNumber, Is.True);
            Assert.That(model!.PhoneNumberDto![0].SelectedCountry, Is.Null);
        });
    }

    /// <summary>
    /// Update user with add phone
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task UpdateUser_ShouldUpdate_WithAction_AddPhone()
    {
        // Arrange
        long userId = 1;
        string action = "AddPhone";
        List<AddressViewModel> addressViewModel = new()
        {
            new(){
            Street = "test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Type = "Test type"
            }
        };

        AddressDto addressDto = new()
        {
            Street = "test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Type = "Test type"
        };

        List<PhoneNumberDto> phoneNumberDto = new()
        {
            new(){
            CountryCode = "Test country code",
            Extension = "Test extension",
            Id = 1,
            InternationalNumber = true,
            Number = "Test number",
            SelectedCountry = 1,
            SetAsPrimaryPhoneNumber = true,
            Type = "Test type",
            }
        };

        var userAccount = new AccountManagementModel
        {
            Id = userId,
            Email = "Test email",
            FirstName = "Test first name",
            LastName = "Test last name",
            UserName = "Test user name",
            Addresses = addressViewModel,
            PhoneNumberDto = phoneNumberDto
        };

        UserAccountDetailsDto userDto = new()
        {
            Email = "Test email",
            FirstName = "Test first name",
            LastName = "Test last name",
            UserName = "Test user name",
            AddressDto = addressDto,
            PhoneNumberDto = phoneNumberDto
        };

        _controller.ObjectValidator = new ObjectValidator();

        var httpContext = new DefaultHttpContext();
        var tempData = new TempDataDictionary(httpContext, Substitute.For<ITempDataProvider>());
        tempData["ToastMessage"] = "saved";
        _controller.TempData = tempData;
        var expected = "saved";

        // Act
        var userDetails = _userMaintenanceSvc.GetUserAccountDetailsByUserId(userId).Returns(userDto);
        userDetails.ValidateNullability(action == "AddPhone");
        await _accuMailValidatorService.AccuMailValidationHandler(userAccount, _controller.ModelState, userAccount.Action == UI.Common.Enums.FormAction.Cancel);
        var view = _controller.View(expected);
        var result = await _controller.UpdateUser(userAccount, action) as ActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ViewResult>());
        });
    }

    /// <summary>
    /// Update user
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task UpdateUser_ShouldUpdate_WithAction_UpdateUser()
    {
        // Arrange
        long userId = 1;
        string action = "UpdateUser";
        List<AddressViewModel> addressViewModel = new()
        {
            new(){
            Street = "test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Type = "Test type"
            }
        };

        AddressDto addressDto = new()
        {
            Street = "test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Type = "Test type"
        };

        List<PhoneNumberDto> phoneNumberDto = new()
        {
            new(){
            CountryCode = "Test country code",
            Extension = "Test extension",
            Id = 1,
            InternationalNumber = true,
            Number = "Test number",
            SelectedCountry = 1,
            SetAsPrimaryPhoneNumber = true,
            Type = "Test type",
            }
        };

        var userAccount = new AccountManagementModel
        {
            Id = userId,
            Email = "Test email",
            FirstName = "Test first name",
            LastName = "Test last name",
            UserName = "Test user name",
            Addresses = addressViewModel,
            PhoneNumberDto = phoneNumberDto
        };

        UserAccountDetailsDto userDto = new()
        {
            Email = "Test email",
            FirstName = "Test first name",
            LastName = "Test last name",
            UserName = "Test user name",
            AddressDto = addressDto,
            PhoneNumberDto = phoneNumberDto
        };

        _controller.ObjectValidator = new ObjectValidator();

        var httpContext = new DefaultHttpContext();
        var tempData = new TempDataDictionary(httpContext, Substitute.For<ITempDataProvider>());
        tempData["ToastMessage"] = "saved";
        _controller.TempData = tempData;
        var expected = "saved";

        // Act
        var userDetails = _userMaintenanceSvc.GetUserAccountDetailsByUserId(userId).Returns(userDto);
        userDetails.ValidateNullability(action == "UpdateUser");
        var accumail = await _accuMailValidatorService.AccuMailValidationHandler(userAccount, _controller.ModelState, userAccount.Action == UI.Common.Enums.FormAction.Cancel);
        var view = _controller.View(expected);
        if (accumail) { _controller.View("UserDetails"); }
        var result = await _controller.UpdateUser(userAccount, action) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(result!.ActionName, Is.EqualTo("UserDetails"));
            Assert.That(view.TempData.Keys, Has.Count.EqualTo(5));
            Assert.That(view.TempData.Values, Has.Count.EqualTo(5));
            Assert.That(accumail, Is.False);
        });
    }

    /// <summary>
    /// Accumail address validation
    /// </summary>
    private sealed class ObjectValidator : IObjectModelValidator
    {
        public void Validate(ActionContext actionContext, ValidationStateDictionary? validationState, string prefix, object? model)
        {
            var context = new ValidationContext(model!, serviceProvider: null, items: null);
            var results = new List<ValidationResult>();

            bool isValid = Validator.TryValidateObject(
                model!, context, results,
                validateAllProperties: true
            );

            if (!isValid)
            {
                results.ForEach((r) =>
                {
                    // Add validation errors to the ModelState
                    actionContext.ModelState.AddModelError("", r.ErrorMessage!);
                });
            }
        }
    }
}

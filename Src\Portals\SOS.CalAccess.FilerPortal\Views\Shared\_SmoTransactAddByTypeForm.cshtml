@using SOS.CalAccess.Models.FilerDisclosure.Contacts
@using SOS.CalAccess.UI.Common
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.FilerPortal.Models.Disclosure.SmoCampaignStatement
@using SOS.CalAccess.UI.Common.Enums
@using SOS.CalAccess.UI.Common.Localization;
@using SOS.CalAccess.UI.Common.Constants;
@inject IHtmlLocalizer<SharedResources> Localizer

@model SmoCampaignStatementTransactionEntryViewModel
@{
    // FR-CF-PaymentReceived-2
    // FD-CF-Expenditure-2 - PaymentMade02

    // Participant Type Options - Order matters
    var participantTypeOptions = new Dictionary<string, string>
    {
        { FilerContactType.Individual.Name, Localizer[ResourceConstants.SmoCampaignStatementTransactorsIndividual].Value },
    };

    if (Model.ScreenType == SmoCampaignStatementScreenTypes.PaymentReceived)
    {
        participantTypeOptions.Add(FilerContactType.Filer.Name, Localizer[ResourceConstants.SmoCampaignStatementTransactorsCommittee].Value);
        participantTypeOptions.Add(FilerContactType.Candidate.Name, Localizer[ResourceConstants.SmoCampaignStatementTransactorsCandidate].Value);
    }
    participantTypeOptions.Add(FilerContactType.Organization.Name, Localizer[ResourceConstants.SmoCampaignStatementTransactorsOther].Value);

    string participantResource = "";
    if (Model.ScreenType == SmoCampaignStatementScreenTypes.PaymentReceived)
    {
        Model.AddressTitleResourceKey = ResourceConstants.SmoCampaignStatementTransactorsAddressOfPayor;
        participantResource = ResourceConstants.ContactFormPayorType;
    }
    else
    {
        Model.AddressTitleResourceKey = ResourceConstants.ContactCommonFieldsAddressOfPayee;
        participantResource = ResourceConstants.ContactFormPayeeType;
    }
}

@Html.HiddenFor(m => m.Id)
@Html.HiddenFor(m => m.FilerId)
@Html.HiddenFor(m => m.ContactId)
@Html.HiddenFor(m => m.TransactionId)
@Html.HiddenFor(m => m.ScreenType)

<div class="mb-4 cursor-pointer">
    @Html.Radio(
        SharedLocalizer,
        nameof(Model.ParticipantType),
        Localizer[participantResource].Value,
        participantTypeOptions,
        Model.ParticipantType != null ? Model.ParticipantType : "",
        false,
        false
    )
    @Html.SosValidationMessageFor(SharedLocalizer, m => m.ParticipantType)
</div>

<partial name="_TransactorSectionIndividual" model="Model" />
<partial name="_TransactorSectionCommittee" model="Model" />
<partial name="_TransactorSectionCandidate" model="Model" />
<partial name="_TransactorSectionOther" model="Model" />

<script>
    document.addEventListener("DOMContentLoaded", function () {
        var hasContact  = @(Model.ContactId != null ? "true" : "false");
        var comValue    = "@FilerContactType.Filer.Name";  // your "COM"
        var selectedVal = "@Model.ParticipantType";

        if (hasContact && selectedVal === comValue) {
          // disable all non-COM radios
          document
            .querySelectorAll("input[type=radio][name='ParticipantType']")
            .forEach(function(r) {
              if (r.value !== comValue) {
                r.disabled = true;
                var lbl = document.querySelector("label[for='" + r.id + "']");
                if (lbl) lbl.classList.add("text-muted");
              }
            });

          // make the lookup box read-only
          var searchRoot = document.getElementById("committee-search-inner");
          if (searchRoot) {
            // disable the text input
            var searchInput = searchRoot.querySelector("input[name='CommitteeSearch']");
            if (searchInput) {
              searchInput.disabled = true;
              searchInput.classList.add("text-muted");
            }
            // disable all icons & prevent pointer-events on the container
            searchRoot.querySelectorAll(".search-icon, .clear-icon, .search-container").forEach(function(el){
              el.style.pointerEvents = "none";
            });
            // hide any results dropdown that might linger
            var resultContainer = searchRoot.querySelector(".result-container");
            if (resultContainer) {
              resultContainer.style.display = "none";
            }
          }
        }
        const radios = document.querySelectorAll("input[name='ParticipantType']");
        const sections = {
            individual: document.getElementById("individualSection"),
            filer: document.getElementById("committeeSection"),
            candidate: document.getElementById("candidateSection"),
            organization: document.getElementById("otherSection")
        };

        // Toggles visibility and enables/disables inputs in a given section
        // Disables inputs to prevent name collisions since all partials are rendered on page load
        function setSectionState(section, isEnabled) {
            if (!section) return;
            section.classList.toggle("d-none", !isEnabled);
            section.querySelectorAll("input, select, textarea").forEach(el => {
                el.disabled = !isEnabled;
            });
        }

        radios.forEach(radio => {
            radio.addEventListener("change", function () {
                const selected = this.value.toLowerCase();
                Object.entries(sections).forEach(([key, section]) => {
                    setSectionState(section, key === selected);
                });
            });

            // Trigger on load (if selected)
            if (radio.checked) {
                radio.dispatchEvent(new Event("change"));
            }
        });
    });
</script>

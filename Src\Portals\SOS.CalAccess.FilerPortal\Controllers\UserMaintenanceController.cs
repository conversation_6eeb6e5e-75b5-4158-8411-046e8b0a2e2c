using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Controllers;

[AllowAnonymous]
[Route("UserMaintenance")]
public class UserMaintenanceController(IUserMaintenanceSvc userMaintenanceSvc, IAccuMailValidatorService accuMailValidatorService, IStringLocalizer<SharedResources> localizer) : Controller
{
    /// <summary>
    /// Get User details and display on the UI
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Route("UserDetails")]
    public async Task<IActionResult> UserDetails()
    {
        UserAccountDetailsDto userAccountDetailsDto;
        AccountManagementModel accountManagementModel;

        var currentUser = await userMaintenanceSvc.GetCurrentUser();
        userAccountDetailsDto = await userMaintenanceSvc.GetUserAccountDetailsByUserId(currentUser.Id);

        var phones = userAccountDetailsDto.PhoneNumberDto;
        if (phones == null || phones.Count == 0)
        {
            phones = new List<PhoneNumberDto> { new() };
        }
        accountManagementModel = new()
        {
            Id = currentUser.Id,
            UserName = userAccountDetailsDto.UserName,
            Email = userAccountDetailsDto.Email,
            FirstName = userAccountDetailsDto.FirstName,
            LastName = userAccountDetailsDto.LastName,
            MiddleName = userAccountDetailsDto.MiddleName,
            FullName = userAccountDetailsDto.FirstName + " " + userAccountDetailsDto.MiddleName + " " + userAccountDetailsDto.LastName,
            Addresses = new List<AddressViewModel> { new() {
                        Street = userAccountDetailsDto.AddressDto!.Street,
                        Street2 = userAccountDetailsDto.AddressDto.Street2,
                        City = userAccountDetailsDto.AddressDto.City,
                        State = userAccountDetailsDto.AddressDto.State,
                        Zip = userAccountDetailsDto.AddressDto.Zip,
                        Type = userAccountDetailsDto.AddressDto.Type,
                        Country = userAccountDetailsDto.AddressDto.Country,
                        Purpose = userAccountDetailsDto.AddressDto.Purpose,
                     } },
            PhoneNumberDto = phones
        };

        return View(accountManagementModel);

    }

    /// <summary>
    /// Update User details with address and phone number
    /// </summary>
    /// <param name="accountManagementModel"></param>
    /// <param name="action"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("UpdateUser")]
    public async Task<IActionResult> UpdateUser(AccountManagementModel accountManagementModel, string action)
    {
        ModelState.Clear();
        if (TryValidateModel(accountManagementModel))
        {
            accountManagementModel.PhoneNumberDto ??= new List<PhoneNumberDto>();

            if (action == "AddPhone")
            {
                accountManagementModel.PhoneNumberDto.Add(new PhoneNumberDto()
                {
                    SetAsPrimaryPhoneNumber = false,
                    CountryCode = "+1",
                    CountryId = 1,
                });
                return View("UserDetails", accountManagementModel);
            }

            if (await accuMailValidatorService.AccuMailValidationHandler(accountManagementModel, ModelState, accountManagementModel.Action == FormAction.Cancel))
            {
                return View("UserDetails", accountManagementModel);
            }

            var userAccountDetailsDto = new UserAccountDetailsDto
            {
                Id = accountManagementModel.Id,
                FirstName = accountManagementModel.FirstName,
                LastName = accountManagementModel.LastName,
                Email = accountManagementModel.Email,
                UserName = accountManagementModel.UserName,
                AddressDto = new AddressDto
                {
                    Street = accountManagementModel.Addresses[0].Street,
                    Street2 = accountManagementModel.Addresses[0].Street2,
                    City = accountManagementModel.Addresses[0].City,
                    State = accountManagementModel.Addresses[0].State,
                    Country = accountManagementModel.Addresses[0].Country,
                    Zip = accountManagementModel.Addresses[0].Zip,
                    Type = accountManagementModel.Addresses[0].Type,
                    Purpose = accountManagementModel.Addresses[0].Purpose,
                },
                PhoneNumberDto = accountManagementModel.PhoneNumberDto,
            };
            await userMaintenanceSvc.UpdateUser(userAccountDetailsDto);

            SetLocalizedToast(CommonResourceConstants.SavedMessage);
        }

        return RedirectToAction("UserDetails");

    }

    /// <summary>
    /// Display toast message on save
    /// </summary>
    /// <param name="localizerKey"></param>
    public void SetLocalizedToast(string localizerKey)
    {
        var savedMessage = localizer[localizerKey]?.Value ?? "";
        TempData["ToastMessage"] = savedMessage;
        TempData["ToastType"] = "e-toast-success";
        TempData["ToastShowCloseButton"] = "true";
        TempData["ToastX"] = "Right";
        TempData["ToastY"] = "Bottom";
    }
}

using Microsoft.EntityFrameworkCore;
using NUnit.Framework.Internal;
using SOS.CalAccess.Data.EntityFramework.Repositories.UserAccountMaintenance;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.UserAccountMaintenance;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.Permissions;

/// <summary>
/// Permission repository tests
/// </summary>
[TestFixture]
public class UserRepositoryTests
{
    private const string User1 = "username1";
    private const string User2 = "username2";
    private const string InvalidUsername = "InvalidUser";

    private DatabaseContext _dbContext;
    private UserRepository _userRepository;
    private long _user1Id;
    private long _user2Id;
    private long _user3Id;

    /// <summary>
    /// Setup dependencies
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        var options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase(databaseName: "CARS")
            .Options;

        _dbContext = new DatabaseContext(options);
        if (_dbContext.Database.EnsureCreated())
        {
            SeedData();
        }
        _userRepository = new UserRepository(_dbContext);

    }

    /// <summary>
    /// Seed data
    /// </summary>
    private void SeedData()
    {
        var user1 = new User
        {
            FirstName = "Read",
            LastName = "Only",
            UserName = User1,
            EmailAddress = "",
            EntraOid = "TestOid"
        };

        var user2 = new User
        {
            FirstName = "John",
            LastName = "Doe",
            UserName = User2,
            EmailAddress = "",
            FilerUsers = new List<FilerUser>{new() {
                FilerId = 123,
            }},
            EntraOid = "TestOid2"
        };

        var address = new Address
        {
            Street = "Test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Purpose = "Office",
            Type = "Cell",
        };
        var phoneNumbers = new PhoneNumber
        {
            Number = "Test number",
            Extension = "Test extn",
            CountryCode = "Test code",
            Type = "Cell",
        };
        var addressList = new AddressList
        {
            Addresses = new List<Address> { address }
        };
        var phoneNumberList = new PhoneNumberList
        {
            PhoneNumbers = new List<PhoneNumber> { phoneNumbers }
        };
        var user3 = new User
        {
            FirstName = "Test first name",
            LastName = "Test last name",
            EmailAddress = "Test email address",
            UserName = "Test user name",
            EntraOid = "Test Oid",
            AddressList = addressList,
            PhoneNumberList = phoneNumberList,
        };
        _dbContext.Users.AddRange(user1, user2, user3);

        _ = _dbContext.SaveChanges();

        _user1Id = user1.Id;
        _user2Id = user2.Id;
        _user3Id = user3.Id;
    }

    /// <summary>
    /// Tear down objects
    /// </summary>
    [TearDown]
    public void TearDown()
    {
        _ = _dbContext.Database.EnsureDeleted();
        _dbContext.Dispose();
    }

    [Test]
    public async Task FindUseridByUsername_Valid()
    {
        // Act
        var result1 = await _userRepository.FindUserIdByUsername(User1);
        var result2 = await _userRepository.FindUserIdByUsername(User2);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result1, Is.EqualTo(_user1Id));
            Assert.That(result2, Is.EqualTo(_user2Id));
        });
    }

    [Test]
    public async Task FindUseridByUsername_Invalid()
    {
        // Act
        var result = await _userRepository.FindUserIdByUsername(InvalidUsername);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task GetFilerUserByFilerId_Found_ShouldReturnResult()
    {
        // Arrange
        var usernames = new List<string> { User1 };

        // Act
        var result = await _userRepository.FindUsersByUserNames(usernames);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<List<User>>());
            Assert.That(result.Any(x => usernames.Contains(x.UserName)));
        });
    }

    [Test]
    public async Task GetUserByEntraOId_ReturnsCorrectUser_WhenExists()
    {
        var result = await _userRepository.GetUserByEntraOId("TestOid");

        Assert.That(result, Is.Not.Null);
        Assert.That(result!.FirstName, Is.EqualTo("Read"));
    }

    [Test]
    public async Task GetUserByEntraOId_ReturnsNull_WhenUserDoesNotExist()
    {
        var result = await _userRepository.GetUserByEntraOId("Test1");

        Assert.That(result, Is.Null);
    }

    /// <summary>
    /// Get user account details by valid user id
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task GetUserAccountDetailsByUserId_Valid()
    {
        // Arrange

        // Act
        var result = await _userRepository.GetUserAccountDetailsByUserId(_user3Id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<User>());
            Assert.That(result.FirstName, Is.EqualTo("Test first name"));
            Assert.That(result.LastName, Is.EqualTo("Test last name"));
            Assert.That(result.UserName, Is.EqualTo("Test user name"));
            Assert.That(result.EmailAddress, Is.EqualTo("Test email address"));
            Assert.That(result.AddressList!.Addresses[0].Street, Is.EqualTo("Test street"));
            Assert.That(result.AddressList.Addresses[0].City, Is.EqualTo("Test city"));
            Assert.That(result.AddressList.Addresses[0].State, Is.EqualTo("Test state"));
            Assert.That(result.AddressList.Addresses[0].Zip, Is.EqualTo("Test zip"));
            Assert.That(result.AddressList.Addresses[0].Country, Is.EqualTo("Test country"));
            Assert.That(result.PhoneNumberList!.PhoneNumbers[0].Number, Is.EqualTo("Test number"));
            Assert.That(result.PhoneNumberList.PhoneNumbers[0].Extension, Is.EqualTo("Test extn"));
            Assert.That(result.PhoneNumberList.PhoneNumbers[0].Type, Is.EqualTo("Cell"));
            Assert.That(result.PhoneNumberList.PhoneNumbers[0].CountryCode, Is.EqualTo("Test code"));
            Assert.That(result.PhoneNumberList.PhoneNumbers[0].InternationalNumber, Is.False);
            Assert.That(result.PhoneNumberList.PhoneNumbers[0].IsPrimaryPhoneNumber, Is.False);
        });
    }

    /// <summary>
    /// Get user account details by invalid user id
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task GetUserAccountDetailsByUserId_InValid()
    {
        // Arrange
        long userId = 11111;
        var expected = new User
        {
            Id = 1,
            FirstName = "Test",
            LastName = "Test",
            EmailAddress = "Test",
            UserName = "Test",
            EntraOid = "TestOid"
        };

        // Act
        var result = await _userRepository.GetUserAccountDetailsByUserId(userId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Null);
            Assert.That(result, Is.Not.EqualTo(expected));
        });
    }

    /// <summary>
    /// Save changes to repository
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task SaveChangesAsync_Valid()
    {
        // Arrange
        var expected = new User
        {
            FirstName = "Test",
            LastName = "Test",
            EmailAddress = "Test",
            UserName = "Test",
            EntraOid = "TestOid"
        };

        // Act
        _dbContext.Users.AddRange(expected);
        await _userRepository.SaveChangesAsync();

        // Assert
        Assert.That(_dbContext.Users.Count, Is.Not.EqualTo(0));
    }
}

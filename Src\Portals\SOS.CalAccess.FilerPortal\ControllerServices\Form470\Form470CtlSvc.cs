using System.Globalization;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations.Form470;
using SOS.CalAccess.FilerPortal.Models.Registrations.Form470Attestation;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using Authorization = SOS.CalAccess.Models.Authorization;
using Form470AmendResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.Form470AmendResponseDto;
using Form470AttestRequest = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.Form470AttestRequest;
using Form470Committee = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.Form470Committee;
using ValidatedForm470ResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.ValidatedForm470ResponseDto;

namespace SOS.CalAccess.FilerPortal.ControllerServices.Form470;

public class Form470CtlSvc(IFilingsApi filingsApi,
    IForm470Svc form470Svc,
    IForm470AmendSvc form470AmendSvc,
    IDateTimeSvc dateTimeSvc,
    IStringLocalizer<SharedResources> localizer,
    IFilersApi filersApi) : IForm470CtlSvc
{
    #region Core
    public async Task Cancel(long id)
    {
        await filingsApi.CancelFiling(id);
    }

    public async Task<bool> UserIsCandidate(long filingId)
    {
        var form470 = await form470Svc.GetForm470Overview(filingId);

        if (form470.Form470Filing is not { } form)
        {
            return false;
        }

        return await filersApi.CurrentUserHasFilerRole(form.FilerId, Authorization.FilerRole.CandidateRegistration_Candidate.Id);
    }
    #endregion

    #region Page01
    public async Task<Form470Page01ViewModel?> Page01GetExisting(long id)
    {
        var res = await form470Svc.GetForm470Overview(id);
        if (res.CandidateIntentionStatement470 is { } cisData && res.Form470Filing is { } data)
        {
            var filingPeriodOptions = await GetCalendarYearOptions();
            var model = new Form470Page01ViewModel { FilingPeriodOptions = filingPeriodOptions };
            model.Apply(cisData);
            model.Id = id;
            model.FilingPeriodId = data.FilingPeriodId;
            return model;
        }
        return null;
    }
    public async Task<Form470Page01ViewModel?> Page01GetViewModelFromFilerId(long filerId)
    {
        var cisDataResp = await form470Svc.GetCandidateIntentionStatementWithElectionByFilerId(filerId);
        if (cisDataResp is { } cisData && cisData.FilerId is not null)
        {
            var filingPeriodOptions = await GetCalendarYearOptions();
            var model = new Form470Page01ViewModel { FilingPeriodOptions = filingPeriodOptions };
            model.Apply(cisData);
            return model;
        }

        return null;
    }

    public async Task<Form470Page01ViewModel> Page01ViewModelSetReadOnlyData(Form470Page01ViewModel model)
    {
        if (model.FilerId is { } filerId)
        {
            var cisDataResp = await form470Svc.GetCandidateIntentionStatementWithElectionByFilerId(filerId);
            if (cisDataResp is { } cisData)
            {
                model.FilingPeriodOptions = await GetCalendarYearOptions();
                model.Apply(cisData);
                return model;
            }
        }

        return model;
    }

    public async Task<long?> Page01Submit(Form470Page01ViewModel model, ModelStateDictionary modelState, bool isSubmission = false)
    {
        // Validate required fields
        if (model.FilingPeriodId is not { } filingPeriodId)
        {
            modelState.AddModelError("FilingPeriodId", "Calendar Year Covered is required.");
            return null;
        }

        if (model.Id is { } id)
        {
            await form470Svc.UpdateOfficeHolderCandidateShortFormFiling(new()
            {
                FilingId = id,
                FilingPeriodId = filingPeriodId,
            });
            return id;
        }
        var filingId = await form470Svc.CreateOfficeHolderCandidateShortFormFiling(model.FilerId, model.FilingPeriodId);
        return filingId;
    }

    private async Task<List<SelectListItem>> GetCalendarYearOptions()
    {
        var filingPeriods = await filingsApi.GetAllFilingPeriods();
        var filingPeriodOptions = filingPeriods.Select(x =>
        {
            var year = x.StartDate.ToString("yyyy", CultureInfo.InvariantCulture);
            var startDate = x.StartDate.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture);
            var endDate = x.EndDate.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture);
            return new SelectListItem
            {
                Text = $"{year} ({startDate} - {endDate})",
                Value = x.Id.ToString(CultureInfo.InvariantCulture)
            };
        }).ToList();

        return filingPeriodOptions;
    }
    #endregion

    #region Page02
    public async Task<Form470Page02ViewModel> Page02GetViewModel(long id)
    {
        var committees = await form470Svc.GetFilingRelatedFiler(id);

        var model = new Form470Page02ViewModel
        {
            Id = id,
            CommitteesGridModel = new SmallDataGridModel()
        };

        if (committees.Count == 0)
        {
            return model;
        }
        committees.ForEach(r =>
        {
            r.CommitteeAddressDisplay = new AddressViewModel(r.CommitteeAddress).ToPrivatizedAddressString(localizer, UI.Common.Enums.AddressDisplayMode.Vertical);
        });

        string deleteMessage = $"{localizer[ResourceConstants.Form470Page02DeleteMessage].Value}";

        model.CommitteesGridModel = new SmallDataGridModel
        {
            PrimaryKey = nameof(Form470Committee.FilerRelatedFilerId),
            GridId = "CommitteeGrid",
            GridType = nameof(Form470Committee),
            DataSource = committees,
            AllowPaging = true,
            AllowTextWrap = false,
            AllowAdding = false,
            AllowDeleting = true,
            DeleteConfirmationMessage = deleteMessage,
            EnableExport = false,
            Columns = new List<DataGridColumn>
            {
                new() { Field = "CommitteeName", HeaderText = localizer[ResourceConstants.Form470Page02TableCommitteeName].Value },
                new() { Field = "Id", HeaderText = localizer[ResourceConstants.Form470Page02TableId].Value },
                new() { Field = "CommitteeAddressDisplay", HeaderText = localizer[ResourceConstants.Form470Page02TableAddress].Value },
                new() { Field = "TreasureName", HeaderText = localizer[ResourceConstants.Form470Page02TreasurerName].Value }
            },
            ActionItems = new List<GridActionItem>
            {
                new() { Label = CommonResourceConstants.Delete, Action = "deleteRow", ControllerName = $"Form470", ActionName = "Page02DeleteCommittee" }
            },

        };
        return model;
    }
    #endregion

    #region Page03
    public async Task<CommitteeSearchResult[]> Page03FindCommitteesByIdOrName(string search)
    {
        var data = await form470Svc.FindPrimarilyFormedCommitteeByIdOrName(search);

        return data.Select(x =>
        {
            var data = new CommitteeSearchResult
            {
                CommitteeName = x.CommitteeName,
                Id = x.Id,
                FilerId = x.FilerId,
            };

            if (x.Treasurer is not null)
            {
                data.TreasurerName = $"{x.Treasurer.FirstName} {x.Treasurer.LastName}";
            }

            data.Address = new AddressViewModel(x.Address[0]).ToPrivatizedAddressString(localizer, UI.Common.Enums.AddressDisplayMode.Horizontal);

            return data;
        }).ToArray();
    }

    public async Task Page03Save(long filingId, long committeeId, ModelStateDictionary modelState)
    {
        if (committeeId == 0)
        {
            modelState.AddModelError("FilerId", "Committee name or ID is required.");
            return;
        }

        await form470Svc.CreateFilingRelatedFiler(filingId, committeeId);
    }
    #endregion

    #region Page04
    public async Task<Form470Page04ViewModel?> Page04GetViewModel(long id)
    {
        Form470Page04ViewModel? model = null;

        var form470 = await form470Svc.GetForm470Overview(id);
        var cis = form470?.CandidateIntentionStatement470;

        if (cis is { } data)
        {
            model = new()
            {
                RegistrationId = data.RegistrationId,
                Name = data.Name,
                ExecutedOn = dateTimeSvc.GetCurrentDateTime(),
            };
        }

        return model;
    }
    public async Task<ValidatedForm470ResponseDto?> Page04Submit(
        Form470Page04ViewModel model,
        ModelStateDictionary modelState,
        bool isSubmission = false)
    {
        if (model.Id == null)
        {
            modelState.AddModelError(nameof(model.Id), "Form ID is required.");
            return null;
        }

        var id = model.Id.Value;

        var request = new Form470AttestRequest
        {
            CheckRequiredFieldsFlag = isSubmission
        };

        if (model.IsCandidate)
        {
            request.IsAgreedTerm = model.IsAgreedTerm;

            return await form470Svc.AttestOfficeholderAndCandidateCampaignStatement(id, request);
        }
        else
        {
            return await form470Svc.AttestOfficeholderAndNonCandidateCampaignStatement(id, request);
        }
    }
    #endregion

    #region Amendment
    public async Task<long> InitializeForm470Amendment(long filerId)
    {
        Form470AmendResponseDto response = await form470AmendSvc.Initialize470Amendment(filerId) ?? throw new ArgumentException("Error");
        if (!response.Valid)
        {
            throw new ArgumentException(response.ErrorMessage);
        }
        return (long)response!.Id!;
    }
    #endregion
}

using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.ControllerServices.Form470;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations.Form470;
using SOS.CalAccess.FilerPortal.Models.Registrations.Form470Attestation;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Services;
using PendingItem = SOS.CalAccess.FilerPortal.Models.Registrations.Form470Attestation.PendingItem;

namespace SOS.CalAccess.FilerPortal.Controllers;

public class Form470Controller(
    IAuthorizationSvc authorizationSvc,
    IStringLocalizer<SharedResources> localizer,
    IForm470CtlSvc form470CtlSvc,
    IForm470Svc form470Svc,
    IDateTimeSvc dateTimeSvc,
    IToastService toastService
) : Controller
{
    #region Core
    /// <summary>
    /// Common logic to call before loading a view.
    /// </summary>
    /// <param name="context"></param>
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        SetCommonViewData();
    }

    public async Task<IActionResult> Index([FromQuery] long? filerId)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Create, User));
        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page01), new { filerId });
        }
        return NotFound();
    }

    public async Task<IActionResult> Cancel(
        [Required] long id,
        CancellationToken cancellationToken)
    {
        if (ModelState.IsValid)
        {
            await form470CtlSvc.Cancel(id);
            toastService.Success(localizer[CommonResourceConstants.ToastCancelled]);
            return RedirectToDashboard();
        }
        return NotFound();
    }

    public IActionResult Close()
    {
        return RedirectToDashboard();
    }

    public async Task<IActionResult> New()
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User));

        var filerId = await form470Svc.GetFilerIdOfLatestAccepted501();

        if (filerId != 0)
        {
            return RedirectToAction(nameof(Page01), new { filerId });
        }
        return NotFound();
    }

    /// <summary>
    /// Redirects user to the starting page when editing this form.
    /// </summary>
    /// <param name="id">Filing ID</param>
    /// <returns></returns>
    public async Task<IActionResult> Edit([Required] long id)
    {
        // TD: replace with correct permission check
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User));

        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        return RedirectToAction(nameof(Page01), new { id });
    }
    #endregion

    #region Page01 FD-CF-470-Overview
    /// <summary>
    /// Get Page01 View
    /// Loads data depending on what ID is passed.
    /// If FilingId (id) is passed, it will look up an existing filing record to get the view data
    /// Else if FilerId (filerId) is passed, it will get the view data only (no existing record)
    /// </summary>
    /// <param name="id">Filing ID</param>
    /// <param name="filerId">Filer ID of Registration Filing</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> Page01(
        long? id,
        [FromQuery] long? filerId,
        CancellationToken cancellationToken = default)
    {
        // TD: replace with correct permission check
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Create, User));

        if (ModelState.IsValid)
        {
            Form470Page01ViewModel? viewModel = null;
            if (id is { } filingId)
            {
                viewModel = await form470CtlSvc.Page01GetExisting(filingId);
            }
            else if (filerId is { } fid)
            {
                viewModel = await form470CtlSvc.Page01GetViewModelFromFilerId(fid);
            }
            if (viewModel is null)
            {
                return NotFound();
            }
            return View(viewModel);
        }
        // TD: Return not found
        return View(new Form470Page01ViewModel());
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Page01(
        Form470Page01ViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.SaveAndClose:
                    return await Page01Save(model);
                case FormAction.Continue:
                    return await Page01Continue(model);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        await form470CtlSvc.Page01ViewModelSetReadOnlyData(model);
        return View(model);
    }

    private async Task<IActionResult> Page01Save(Form470Page01ViewModel model)
    {
        await form470CtlSvc.Page01Submit(model, ModelState, false);

        if (ModelState.IsValid)
        {
            toastService.Success(localizer[CommonResourceConstants.ToastSaved]);
            return RedirectToDashboard();
        }

        await form470CtlSvc.Page01ViewModelSetReadOnlyData(model);
        return View(nameof(Page01), model);
    }

    private async Task<IActionResult> Page01Continue(Form470Page01ViewModel model)
    {
        var filingId = await form470CtlSvc.Page01Submit(model, ModelState, false);

        if (ModelState.IsValid && filingId is not null)
        {
            return RedirectToAction(nameof(Page02), new { Id = filingId });
        }

        await form470CtlSvc.Page01ViewModelSetReadOnlyData(model);
        return View(nameof(Page01), model);
    }

    #endregion

    #region Page 02 - FD-CF-470-Committees-2
    [HttpDelete("/Form470/Page02DeleteCommittee/{filingRelatedFilerId}")]
    public async Task<IActionResult> Page02DeleteCommittee(
    [Required] long filingRelatedFilerId)
    {
        if (ModelState.IsValid)
        {
            await form470Svc.CancelFilingRelatedFiler(filingRelatedFilerId);
            return Ok();
        }
        return NotFound();
    }
    [HttpGet]
    public async Task<IActionResult> Page02([Required] long id)
    {
        if (ModelState.IsValid)
        {
            var model = await form470CtlSvc.Page02GetViewModel(id);
            if (model is not null)
            {
                return View(model);
            }
        }
        return View(new Form470Page02ViewModel());
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    public IActionResult Page02(
        Form470Page02ViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            if (model.Action == FormAction.Previous)
            {
                return RedirectToAction(nameof(Page01), new { model.Id });
            }
            if (model.Action == FormAction.Continue)
            {
                return Page02Continue(model);
            }
            if (model.Action == FormAction.SaveAndClose)
            {
                return Page02SaveAndClose();
            }
        }
        return View(model);
    }
    private RedirectToActionResult Page02SaveAndClose()
    {
        // Note: No need to save. Info is already saved when committee
        // is added.
        toastService.Success(localizer[CommonResourceConstants.ToastSaved]);
        return RedirectToDashboard();
    }
    private RedirectToActionResult Page02Continue(Form470Page02ViewModel model)
    {
        // Redirect to Page04
        return RedirectToAction(nameof(Page04), new { model.Id });
    }
    #endregion

    #region Page 03 - FD-CF-470-Committees-1
    [HttpGet]
    public IActionResult Page03([Required] long id)
    {
        if (ModelState.IsValid)
        {
            var model = new Form470Page03ViewModel
            {
                Id = id
            };
            if (ModelState is not null)
            {
                return View(model);
            }
        }
        return View();
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Page03(
    Form470Page03ViewModel model,
    CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            if (model.Action == FormAction.Previous)
            {
                return RedirectToAction(nameof(Page02), new { model.Id });
            }
            if (model.Action == FormAction.Continue)
            {
                return await Page03Continue(model);
            }
            if (model.Action == FormAction.SaveAndClose)
            {
                return await Page03Save(model);
            }
        }
        return View(model);
    }
    private async Task<IActionResult> Page03Continue(Form470Page03ViewModel model)
    {
        await form470CtlSvc.Page03Save(model.Id ?? 0, model.FilerId ?? 0, ModelState);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page02), new { model.Id });
        }
        // If there are validation errors, return to the view with the model
        return View(nameof(Page03), model);
    }

    private async Task<IActionResult> Page03Save(Form470Page03ViewModel model)
    {
        await form470CtlSvc.Page03Save(model.Id ?? 0, model.FilerId ?? 0, ModelState);

        if (ModelState.IsValid)
        {
            toastService.Success(localizer[CommonResourceConstants.ToastSaved]);
            return RedirectToDashboard();
        }
        // If there are validation errors, return to the view with the model
        return View(nameof(Page03), model);
    }
    #endregion

    #region FD-CF-470-Verification
    [HttpGet]
    public async Task<IActionResult> Page04(long id)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var viewModel = await form470CtlSvc.Page04GetViewModel(id);

        if (viewModel == null)
        {
            return NotFound();
        }

        viewModel.IsCandidate = await form470CtlSvc.UserIsCandidate(id);

        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Page04(Form470Page04ViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        if (model.Id is not { } id)
        {
            return NotFound();
        }

        switch (model.Action)
        {
            case FormAction.Previous:
                return RedirectToAction(nameof(Page02), new { id });
            case FormAction.SaveAndClose:
                return await Page04Save(model);
            case FormAction.Submit:
                return await Page04Continue(id, model);
            default:
                ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission].Value);
                return View(model);
        }
    }
    private async Task<IActionResult> Page04Continue(long id, Form470Page04ViewModel model)
    {
        await form470CtlSvc.Page04Submit(model, ModelState, true);

        var isCandidate = await form470CtlSvc.UserIsCandidate(id);
        if (ModelState.IsValid)
        {
            return isCandidate
                ? RedirectToAction(nameof(Page05), new { id })
                : RedirectToAction(nameof(Page06), new { id });
        }

        return View(nameof(Page04), model);
    }
    private async Task<IActionResult> Page04Save(Form470Page04ViewModel model)
    {
        await form470CtlSvc.Page04Submit(model, ModelState, false);

        if (ModelState.IsValid)
        {
            toastService.Success(localizer[CommonResourceConstants.ToastSaved]);
            return RedirectToDashboard();
        }

        return View(nameof(Page04), model);
    }
    #endregion

    #region FD-CF-470-Confirmation
    [HttpGet]
    public async Task<IActionResult> Page05(long id)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var form470 = await form470Svc.GetForm470Overview(id);
        var submittedDate = form470?.Form470Filing?.SubmittedDate;

        var model = new Form470Page05ViewModel
        {
            SubmittedDate = submittedDate ?? dateTimeSvc.GetCurrentDateTime()
        };

        return View(model);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public IActionResult Page05(Form470Page05ViewModel model)
    {
        if (ModelState.IsValid)
        {
            toastService.Success(localizer[CommonResourceConstants.ToastSaved]);
            return Close();
        }
        return View();
    }
    [HttpGet]
    public async Task<IActionResult> Page06(long id)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var form470 = await form470Svc.GetForm470Overview(id);
        var submittedDate = form470?.Form470Filing?.SubmittedDate;

        var pendingItems = new List<PendingItem>
        {
            new() { Item = "Candidate Attestation", Status = "Not yet started" },
        };

        Form470Page06ViewModel model = new()
        {
            PendingItems = pendingItems,
            SubmittedDate = submittedDate ?? dateTimeSvc.GetCurrentDateTime()
        };

        return View(model);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public IActionResult Page06(Form470Page06ViewModel model)
    {
        if (ModelState.IsValid)
        {
            toastService.Success(localizer[CommonResourceConstants.ToastSaved]);
            return Close();
        }
        return View();
    }
    #endregion

    #region Amendment
    [HttpGet]
    [Route("[controller]/Amend/{filerId:long}")]
    public async Task<IActionResult> Amend([Required] long filerId)
    {
        if (ModelState.IsValid)
        {
            try
            {
                long newId = await form470CtlSvc.InitializeForm470Amendment(filerId);
                return RedirectToAction(nameof(Page01), new { id = newId });
            }
            catch (ArgumentException ae)
            {
                toastService.Error(ae.Message);
                return RedirectToDashboard();   // error
            }
        }
        return RedirectToDashboard();
    }
    #endregion

    #region AJAX Endpoints
    /// <summary>
    /// Search for committee by name or id.
    /// </summary>
    /// <param name="search">Search text input.</param>
    /// <param name="cancellationToken">Standard dotnet cancellationToken for async operations</param>
    /// <returns></returns>
    [HttpGet]
    public async Task<JsonResult> SearchCommitteeByNameOrId(
        [FromQuery] string search,
        CancellationToken cancellationToken
    )
    {
        var data = await form470CtlSvc.Page03FindCommitteesByIdOrName(search);
        return new JsonResult(data, new JsonSerializerOptions { PropertyNamingPolicy = null });
    }
    #endregion

    #region private
    private void SetCommonViewData()
    {
        ViewData[LayoutConstants.Title] = localizer[ResourceConstants.Form470AttestationTitle].Value;
    }

    private RedirectToActionResult RedirectToDashboard()
    {
        return RedirectToAction("Index", "DisclosureTemporaryDashboard");
    }
    #endregion
}

using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Services.Business.FilerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.ControllerServices;


public class CandidateRegistrationCtlSvc(
    ICandidateIntentionRegistrationSvc candidateIntentionRegistrationSvc,
    ICandidateSvc candidateSvc,
    IUserMaintenanceSvc userMaintenanceSvc,
    IDecisionsValidationMapService decisionsValidationMapService,
    IStringLocalizer<SharedResources> localizer) : BaseCandidateRegistrationCtlSvc, ICandidateRegistrationCtlSvc
{
    public async Task<CandidateIntentionStatementViewModel?> Page03Submit(CandidateIntentStep1 model, ModelStateDictionary modelState, CancellationToken cancellationToken, bool isSubmission = true)
    {
        if (model.SelfRegister is null)
        {
            modelState.AddModelError("SelfRegister", localizer[CommonResourceConstants.NoSelectionError]);
            return null;
        }

        if (model.CandidateIsMe())
        {
            // Return my information for prefill
            var response = await userMaintenanceSvc.GetCurrentUser();
            var myInfo = new CandidateIntentionStatementViewModel
            {
                Email = response.Email,
                FirstName = response.FirstName,
                LastName = response.LastName,
                SelfRegister = true,
                PreviousCandidate = false,
            };
            return myInfo;
        }
        else
        {
            if (model.PreviousCandidate is null)
            {
                modelState.AddModelError("PreviousCandidate", localizer[CommonResourceConstants.NoSelectionError]);
                return null;
            }

            if (model.RegisteredBefore())
            {
                // Require candidate search
                if (model.CandidateId is not null)
                {
                    var response = await candidateSvc.GetCandidateById((long)model.CandidateId) ?? throw new KeyNotFoundException($"Unable to find Candidate {model.CandidateId}");

                    var candidateInfo = new CandidateIntentionStatementViewModel
                    {
                        CandidateId = response.Id,
                        FirstName = response.FirstName,
                        LastName = response.LastName,
                        MiddleName = response?.MiddleInitial,
                        SelfRegister = false,
                        PreviousCandidate = false,
                        IsSameAsCandidateAddress = false,
                    };
                    return candidateInfo;

                }
                else
                {
                    modelState.AddModelError("CandidateId", string.Format(localizer[ResourceConstants.FieldIsRequired].Value, "This field"));
                }
            }

            // If not registered before, there is no data to prefill with
            return null;
        }
    }

    /// <summary>
    /// Handles submission of Page04 form data to WebApi
    /// If the model passed contains an ID, this will call the UPDATE endpoint, otherwise it will call the CREATE endpoint.
    /// </summary>
    /// <param name="model">View model used for Page04 form</param>
    /// <param name="modelState">ModelState property from View calling this method</param>
    /// <param name="cancellationToken"></param>
    /// <param name="isSubmission">Flag indicating whether endpoint should check for all required fields or not</param>
    /// <returns></returns>
    public async Task<long?> Page04Submit(CandidateIntentionStatementViewModel model, ModelStateDictionary modelState, CancellationToken cancellationToken, bool isSubmission = true)
    {
        CandidateIntentionStatementRequest request = MapCandidateIntentionStatementViewModelToRequest(model);
        request.CheckRequiredFieldsFlag = isSubmission;

        RegistrationResponseDto response;
        if (model.RegistrationId == 0)
        {
            response = await candidateIntentionRegistrationSvc.CreateCandidateIntentionStatement(request);
        }
        else
        {
            response = await candidateIntentionRegistrationSvc.UpdateCandidateIntentionStatement(model.RegistrationId, request);
        }

        //Populate AddressValidationResponse.
        if (response.AddressValidationResult?.AddressResults?.Count > 0)
        {
            model.AddressValidationResponse = new();
            PopulateAddressValidation(response.AddressValidationResult.AddressResults, model.AddressValidationResponse);
        }


        if (!response.Valid)
        {
            decisionsValidationMapService.ApplyErrorsToModelState(GetCisFieldValidationMap(model), response.ValidationErrors, modelState);
        }

        return response.Id;
    }

    /// <summary>
    /// Calls WebApi to save selections made in the Election02 step.
    /// </summary>
    /// <param name="registrationId">ID of the registration filing record. Parent ID of this form.</param>
    /// <param name="model">Election02 ViewModel source for building the request body</param>
    /// <param name="modelState">Reference to current Action's ModelState so that workflow errors in the response can be set to it.</param>
    /// <param name="isSubmission">Flag indicating whether the user is calling this endpoint to save or submit</param>
    /// <returns></returns>
    public async Task Election02Submit(long registrationId, CandidateIntentStep2 model, ModelStateDictionary modelState, bool isSubmission = true)
    {
        var electionreq = new UpdateRegistrationElectionRequest
        {
            CountyId = model.SelectedDistrict ?? 0, //countynumber 
            DistrictId = model.SelectedDistrict ?? 0, //districtnumber
            ElectionYear = model.SelectedElectionYear ?? "", //electionyear
            ElectionId = model.SelectedElection ?? 0, //electiontypeid            
            Jurisdiction = model.SelectedJurisdiction ?? "", //jurisdiction
            OfficeId = model.SelectedOffice ?? 0, //officeid
            PartyId = model.SelectedPartyAffiliation ?? 0, // partyid
            CheckRequiredFieldsFlag = isSubmission
        };
        var response = await candidateIntentionRegistrationSvc.LinkElectionToCandidateIntentionStatement(registrationId, electionreq);

        if (!response.Valid)
        {
            Election02ApplyErrorsToModelState(response.ValidationErrors, modelState);
        }
    }

    /// <summary>
    /// Maps workflow errors to ModelState so that they can be displayed next to the appropriate field.
    /// </summary>
    /// <param name="errors">List of validation errors received from Decisions.</param>
    /// <param name="modelState">Reference to current Action's ModelState so that workflow errors in the response can be set to it.</param>
    private void Election02ApplyErrorsToModelState(List<WorkFlowError> errors, ModelStateDictionary modelState)
    {
        foreach (var error in errors)
        {
            switch (error.FieldName)
            {
                case "FR_CR_Filing_CandidateIntention_ElectionInformation_Data.Jurisdiction":
                case "StateOffice":
                    modelState.AddModelError("SelectedJurisdiction", ReplaceFieldName(error.Message, localizer[ResourceConstants.Jurisdiction].Value));
                    break;
                case "FR_CR_Filing_CandidateIntention_ElectionInformation_Data.ElectionYear":
                case "ElectionYear":
                    modelState.AddModelError("SelectedElectionYear", ReplaceFieldName(error.Message, localizer[ResourceConstants.CisElectionElectionYear].Value));
                    break;
                case "FR_CR_Filing_CandidateIntention_ElectionInformation_Data.Election":
                case "ElectionType":
                case "ElectionDate":
                    modelState.AddModelError("SelectedElection", ReplaceFieldName(error.Message, localizer[ResourceConstants.Election].Value));
                    break;
                case "FR_CR_Filing_CandidateIntention_ElectionInformation_Data.Office":
                case "Office":
                    modelState.AddModelError("SelectedOffice", ReplaceFieldName(error.Message, localizer[ResourceConstants.CisElectionOfficeSought].Value));
                    break;
                case "FR_CR_Filing_CandidateIntention_ElectionInformation_Data.District":
                case "DistrictNumber":
                case "DistrictName":
                    modelState.AddModelError("SelectedDistrict", ReplaceFieldName(error.Message, localizer[ResourceConstants.CisElectionDistrictNumber].Value));
                    break;
                case "FR_CR_Filing_CandidateIntention_ElectionInformation_Data.Party":
                case "PoliticalParty":
                    modelState.AddModelError("SelectedPartyAffiliation", ReplaceFieldName(error.Message, localizer[ResourceConstants.Party].Value));
                    break;
                case "AgencyName":
                    modelState.AddModelError("SelectedAgency", ReplaceFieldName(error.Message, localizer[ResourceConstants.CisElectionAgencyName].Value));
                    break;
                default:
                    modelState.AddModelError("", error.Message);
                    break;
            }
        }
    }
}

using Newtonsoft.Json.Linq;

namespace SOS.CalAccess.Services.Business.Efile.Tests;

[TestFixture]
public class LobbyingNoticeOfTerminationValidatorTests
{
    private LobbyingNoticeOfTerminationValidator _validator = null!;

    [SetUp]
    public void SetUp()
    {
        _validator = new LobbyingNoticeOfTerminationValidator();
    }

    [Test]
    public void CanHandle_CorrectFormName_ReturnsTrue()
    {
        Assert.That(
            _validator.CanHandle("Lobbying-NoticeOfTermination"), Is.True);
    }

    [Test]
    public void CanHandle_WrongFormName_ReturnsFalse()
    {
        Assert.That(
            _validator.CanHandle("SomeOtherForm"), Is.False);
    }

    [Test]
    public async Task ValidateAsync_AnyPayload_ReturnsZero()
    {
        // Arrange
        var jsonData = JObject.Parse("{}");

        // Act
        int? result = await _validator.ValidateAsync(jsonData);

        // Assert
        Assert.That(result, Is.EqualTo(0), "ValidateAsync should always return 0 for this validator.");
    }
}

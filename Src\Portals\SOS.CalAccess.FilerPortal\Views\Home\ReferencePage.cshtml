@using Microsoft.AspNetCore.Mvc.Localization
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models
@using SOS.CalAccess.UI.Common.Helpers
@using SOS.CalAccess.Services.Business;
@using SOS.CalAccess.UI.Common
@using SOS.CalAccess.UI.Common.Models
@using SOS.CalAccess.FilerPortal.Models
@using SOS.CalAccess.Models.Common
@model ReferenceViewModel
@inject IHtmlLocalizer<SharedResources> SharedLocalizer
@inject IReferenceDataSvc refDataSvc

@{
    ViewData["Title"] = "Developer Reference Page";

    var cancelModal = new CancelConfirmModal(
        Title: "Confirm Cancellation",
        Body: "Are you sure you want to cancel? You will be rediredted to Disclosure page.",
        CloseButtonText: "No, Stay Here",
        SubmitButtonText: "Yes, Cancel",
        ActionUrl: Url.Action("Index", "Disclosure")!
    );

    var progressBar = new ProgressBar(
    new List<ProgressItem>
    {
        new ProgressItem("1.General information", true, false, true, "/Home/Index"),
        new ProgressItem("2.Financial institution", true, false, true, "/Home/Index"),
        new ProgressItem("3.Committee details", false, true, false, "/Home/Index"),
        new ProgressItem("4.Officers", false, false, false, "/Home/Index"),
        new ProgressItem("5.Registration fee", false, false, false, "/Home/Index"),
        new ProgressItem("6.Submit", false, false, false,"/Home/Index")
    });
}

<div class="container mt-5">
    <h2>Sample Form</h2>

    @Html.Button(SharedLocalizer, "Back", "nav-previous", "button", "btn btn-primary mb-3")

    @Html.LinkButton(SharedLocalizer, "Next", "Home", "ReferencePage", routeValues: null)

    <partial name="_LayoutProgressbar" for="@progressBar" />

    <form method="post" action="#" class="mb-3 border p-2">
        <!-- ExpenditureExpenseAmount Field -->
        <div class="me-2 mb-2">
            @Html.CurrencyInputFor(SharedLocalizer, model => model.ExpenditureExpenseAmount, "FilerPortal.Disclosure.CoalitionPayments.Amount", isAlignRight: true, displayCurrencySymbol: true) @* widthInEmUnits:null *@
        </div>

        <!-- Title Field -->
        @Html.TextField(SharedLocalizer, "title", "Notification.Title", true, "Test Notification", placeholder: "Enter notification title", readOnly: true)

        <!-- Filer Name Field -->
        @Html.TextFieldFor(SharedLocalizer, m => m.FilerName, "Filer Name", readOnly: true)

        <!-- Message Field -->
        @Html.TextArea(SharedLocalizer, "message", "Message", true, "Message Description", "Enter the notification message", readOnly: true)

        <!-- Type Dropdown (info, success, warning, error) -->
        @Html.DropdownFor(SharedLocalizer, m => m.NotificationType, "NotificationKey", Model.NotificationTypeList!, placeHolder: "--Select a Value--", readOnly: true)


        <!-- Radio Buttons: Urgency -->
        @Html.Radio(SharedLocalizer, "urgency", "Urgency", new Dictionary<string, string>
        {
            { "high", "High" },
            { "medium", "Medium" },
            { "low", "Low" }
        }, "medium", required: true)


        @Html.RadioFor(SharedLocalizer, m => m.NotificationPreference, "Notification Preference", options: new List<SelectListItem>
        {
            new SelectListItem { Text = "Email", Value = "Email", Selected = true },
            new SelectListItem { Text = "Phone", Value = "Phone" },
            new SelectListItem { Text = "SMS", Value = "SMS" }
        }, required: true)

        @Html.TextAreaFor(SharedLocalizer, m => m.DescriptiveMessage!, "Description", readOnly: true)

        <!-- Checkboxes: Channels (Email, SMS, In-app) -->
        @Html.Checkbox(SharedLocalizer, "channels", "Notification.Channels", new Dictionary<string, string>
        {
            { "email", "Email" },
            { "sms", "SMS" },
        }, required: false)

        @Html.CheckBoxFor(SharedLocalizer, m => m.ReceiveSms, "ReceiveSMS", readOnly: true)

        @* @Html.EJS().DatePickerFor(model => model.RegistrationDate) *@
        @Html.DatePickerFor(SharedLocalizer, model => model.RegistrationDate, labelResourceKey: ResourceConstants.RegistrationDate, isReadOnly: true)
        @Html.DatePickerFor(SharedLocalizer, model => model.FilingDate, ResourceConstants.FilingDate)

        <div >
            @Html.PhoneNumberFor(refDataSvc, SharedLocalizer, model => model.PhoneNumber1!, ResourceConstants.CisRegistrationFormPhoneNumber)
        </div>
        <div >
            @Html.PhoneNumberFor(refDataSvc, SharedLocalizer, model => model.PhoneNumber2!, ResourceConstants.CisRegistrationFormPhoneNumber, required: true)
        </div>
        <div >
            @Html.PhoneNumberFor(refDataSvc, SharedLocalizer, model => model.PhoneNumber3!, ResourceConstants.CisRegistrationFormFaxNumber, readOnly: true)
        </div>
        <div >
            @Html.PhoneNumberFor(refDataSvc, SharedLocalizer, model => model.PhoneNumber4!  )
        </div>
        <button type="submit" class="btn btn-primary">Submit</button> @* to test validation errors *@
    </form>

    <div class="mb-3">
        @Html.Button(SharedLocalizer, "Continue", "nav-next", "submit", "btn btn-primary mb-3")
        @Html.Button(SharedLocalizer, "Save and Exit", "save-exit", "button", "btn btn-outline-primary mb-3")

        @Html.Button(SharedLocalizer, "Cancel", "cancel", "button", "btn btn-warning d-block mb-3")

        <button type="button" class="btn btn-outline-primary btn-sm ms-auto" data-bs-toggle="modal" data-bs-target="#cancelConfirmModal">
            OpenModal
        </button>
    </div>

    <partial name="_CancelConfirmModal" model="cancelModal" />

    <div class="mt-5 mb-5">
    @Html.FileUploader(SharedLocalizer, relationshipType: RelationshipType.RegistrationFiling.ToString(), relationshipId: 500136, titleResourceKey: "Common.UploadFile.Title", instructionsResourceKey: "Common.UploadFile.Instructions", onUploadSuccess : "uploadFile", required: true)
    </div>

    <div>
        <a href="https://devprdportal.azurewebsites.us/notificationtemplate">Implementation of Small Grid</a><br />
        <a href="https://devfilerportal.azurewebsites.us/Messages">Implementation of Large Grid</a>
    </div>

</div>

<script>
    function uploadFile(args){
        console.log(args);
    }
</script>

using SOS.CalAccess.FilerPortal.Models.Registrations.CisRegistrationWithdrawal;
using SOS.CalAccess.Services.Business;

namespace SOS.CalAccess.FilerPortal.ControllerServices.CisRegistrationWithdrawalCtlSvc;

public interface ICisRegistrationWithdrawalCtlSvc
{
    /// <summary>
    /// Cancels CIS Withdrawal
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public Task<MethodResult> CancelCisWithdrawal(long id);

    /// <summary>
    /// Initializes the CIS withdrawal process and returns the registration ID.
    /// </summary>
    /// <param name="id">The ID of an accepted CIS registration.</param>
    /// <returns>A method result containing the registration ID if a CIS Withdrawal.</returns>
    Task<MethodResult<long>> InitializeCisWithdrawal(long id);

    /// <summary>
    /// Gets Page01 viewModel.
    /// </summary>
    /// <param name="id">The ID of the registration withdrawal.</param>
    /// <returns>A method result containing the Page01 view model.</returns>
    Task<MethodResult<CisRegistrationWithdrawalPage01ViewModel>> Page01GetViewModel(long id);

    /// <summary>
    /// Gets Page02 viewModel
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public Task<MethodResult<CisRegistrationWithdrawalPage02ViewModel>> Page02GetViewModel(long id);

    /// <summary>
    /// Submits the page 02 form
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public Task<MethodResult> Page02Submit(long id);

    /// <summary>
    /// Gets Page03 viewModel
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public Task<MethodResult<CisRegistrationWithdrawalPage03ViewModel>> Page03GetViewModel(long id);

}

using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Model for response body of SearchLobbyistByIdOrName API.
/// </summary>
public class LobbyistSearchResultDto
{
    /// <summary>
    /// Lobbyist Id
    /// </summary>
    [Required]
    [JsonPropertyName("Id")]
    public required long Id { get; set; }

    /// <summary>
    /// Gets or sets the Lobbyist name.
    /// </summary>
    [Required]
    [JsonPropertyName("Name")]
    public required string Name { get; set; }

    /// <summary>
    /// Gets or sets the Lobbyist email.
    /// </summary>
    [Required]
    [JsonPropertyName("Email")]
    public string? Email { get; set; }

    /// <summary>
    /// Disabled if status is accepted.
    /// </summary>
    [Required]
    [JsonPropertyName("IsDisabled")]
    public required bool IsDisabled { get; set; }
}

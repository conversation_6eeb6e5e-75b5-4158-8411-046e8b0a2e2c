using NSubstitute;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration;

namespace SOS.CalAccess.Services.Business.Contracts.Tests.FilerRegistration.Registrations;

/// <summary>
/// Unit tests for the <see cref="CandidateSvc"/> class.
/// </summary>
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(CandidateSvc))]
public sealed class CandidateSvcTest
{
    private ICandidateRepository _candidateRepository;
    private IRegistrationRepository _registrationRepository;
    private IDateTimeSvc _dateTimeSvcMock;

    private CandidateSvc _service;

    private static readonly Election _election2026 = new() { Name = "2026", ElectionDate = new(2026, 11, 5, 0, 0, 0, kind: 0) };
    private static readonly ElectionRace _gov2026 = new() { Election = _election2026, Office = new() { Name = "Govenor" } };
    private static readonly ElectionRace _attGen2026 = new() { Election = _election2026, Office = new() { Name = "Attorney General" } };
    private static readonly ElectionRace _dist52026 = new() { Election = _election2026, Office = new() { Name = "Assemby District 5" } };

    private static readonly Election _election2024 = new() { Name = "2024", ElectionDate = new(2024, 11, 10, 0, 0, 0, kind: 0) };
    private static readonly ElectionRace _gov2024 = new() { Election = _election2024, Office = new() { Name = "Govenor" } };
    private static readonly ElectionRace _attGen2024 = new() { Election = _election2024, Office = new() { Name = "Attorney General" } };
    private static readonly ElectionRace _dist52024 = new() { Election = _election2024, Office = new() { Name = "Assemby District 5" } };

    private static readonly Election _election2022 = new() { Name = "2022", ElectionDate = new(2022, 11, 9, 0, 0, 0, kind: 0) };
    private static readonly ElectionRace _gov2022 = new() { Election = _election2022, Office = new() { Name = "Govenor" } };
    private static readonly ElectionRace _attGen2022 = new() { Election = _election2022, Office = new() { Name = "Attorney General" } };
    private static readonly ElectionRace _dist52022 = new() { Election = _election2022, Office = new() { Name = "Assemby District 5" } };

    /// <summary>
    /// Sets up the unit tests for this fixture.
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _candidateRepository = Substitute.For<ICandidateRepository>();
        _registrationRepository = Substitute.For<IRegistrationRepository>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();

        _service = new CandidateSvc(
            _candidateRepository,
            _registrationRepository,
            _dateTimeSvcMock
        );
    }

    [Test]
    public async Task GetCandidateById_NotFound()
    {

        // Arrange
        var id = 82L;
        _candidateRepository.FindCandidateById(id).Returns((Candidate?)null);

        // Act
        var result = await _service.GetCandidateById(id);

        //Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task GetCandidateById_NullableRegistrations_ReturnCandidateSafely()
    {

        // Arrange
        long id = 1;
        Candidate candidate = new()
        {
            Id = id,
        };
        _candidateRepository.FindCandidateById(id).Returns(candidate);

        // Act
        var result = await _service.GetCandidateById(id);

        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(candidate.Id));
    }

    [Test]
    public async Task GetCandidateById_Found()
    {

        // Arrange
        var id = 82L;
        Candidate candidate = GetCandidate(id);
        _candidateRepository.FindCandidateById(id).Returns(candidate);

        // Act
        var result = await _service.GetCandidateById(id);

        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.CandidateAddress.Street, Is.EqualTo("9876 Main St"));
            Assert.That(result.CandidateAddress.Street2, Is.EqualTo("suite 100"));
            Assert.That(result.CandidateAddress.City, Is.EqualTo("Plainsville"));
            Assert.That(result.CandidateAddress.State, Is.EqualTo("CA"));
            Assert.That(result.CandidateAddress.Zip, Is.EqualTo("12345"));
            Assert.That(result.CandidateAddress.Country, Is.EqualTo("United States"));
            Assert.That(result.CandidateAddress.Type, Is.EqualTo("Business"));
            Assert.That(result.MailingAddress.Street, Is.EqualTo("1234 Main St"));
            Assert.That(result.MailingAddress.Street2, Is.EqualTo("suite ABC"));
            Assert.That(result.MailingAddress.City, Is.EqualTo("Anytown"));
            Assert.That(result.MailingAddress.State, Is.EqualTo("CA"));
            Assert.That(result.MailingAddress.Zip, Is.EqualTo("12345"));
            Assert.That(result.MailingAddress.Country, Is.EqualTo("United States"));
            Assert.That(result.MailingAddress.Type, Is.EqualTo("Home"));
            Assert.That(result.Phone.CountryCode, Is.EqualTo("+1"));
            Assert.That(result.Phone.Extension, Is.EqualTo("555"));
            Assert.That(result.Phone.Number, Is.EqualTo("1231234"));
            Assert.That(result.Fax.CountryCode, Is.EqualTo("+2"));
            Assert.That(result.Fax.Extension, Is.EqualTo("555"));
            Assert.That(result.Fax.Number, Is.EqualTo("9879876"));
        });
    }

    [Test]
    public async Task SearchCandidateByName_EmptyList_ReturnEmptyList()
    {
        // Arrange
        _registrationRepository.FindCandidateRegistrationsWithElectionByIdOrName(Arg.Any<string>()).Returns(new List<CandidateIntentionStatement>());

        // Act
        var result = await _service.SearchCandidateByName(Arg.Any<string>());

        //Assert
        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task SearchCandidateByName_MultipleResults()
    {
        var hits = new List<CandidateIntentionStatement>() {
            new() {
                Name = "James Smith",
                StatusId = 1,
                CandidateId = 678,  //result 5 Smith  after Doe
                LastName = "Smith",
                ElectionRace = _dist52026,
            },
            new() {
                Name = "John Doe",
                StatusId = 1,
                CandidateId = 123, //result 2
                LastName = "Doe",
                ElectionRace = _attGen2024,
            },
            new() {
                Name = "John Doe",
                StatusId = 1,
                CandidateId = 123, //result 2
                LastName = "Doe",
                ElectionRace = _gov2026,
            },
            new() {
                Name = "John Doe",
                StatusId = 1,
                CandidateId = 345,  //result 3
                LastName = "Doe",
                ElectionRace = _gov2024,
            },
            new() {
                Name = "Joan Doe",
                StatusId = 1,
                CandidateId = 890,  //result 1 (Joan before John since 1,2,3 all last name Doe)
                LastName = "Doe",
                ElectionRace = _attGen2022,
            },
            new() {
                Name = "John P Doe",
                StatusId = 1,
                CandidateId = 123, //result 2
                LastName = "Doe",
                ElectionRace = _dist52022,
            },
            new() {
                Name = "Laura Jones",
                StatusId = 1,
                CandidateId = 999, //result 4
                LastName = "Jones",
                ElectionRace = null,  //don't think this is actually valid data scenario but testing to ensure handled gracefully
            },
            new() {
                Name = "Jim Smith",
                StatusId = 1,
                CandidateId = 678, //result 5
                LastName = "Smith",
                ElectionRace = _dist52024,
            }
        };

        _registrationRepository.FindCandidateRegistrationsWithElectionByIdOrName("Jo").Returns(hits);

        //Act
        var result = (await _service.SearchCandidateByName("Jo")).ToList();


        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Has.Count.EqualTo(5));
        Assert.Multiple(() =>
        {
            Assert.That(result[0].Name, Is.EqualTo("Joan Doe"));
            Assert.That(result[0].LastElection, Is.EqualTo("2022"));
            Assert.That(result[0].LastElectionDate, Is.EqualTo(_election2022.ElectionDate));
            Assert.That(result[0].Registrations, Has.Count.EqualTo(1));
            Assert.That(result[1].Name, Is.EqualTo("John Doe"));
            Assert.That(result[1].LastElection, Is.EqualTo("2026"));
            Assert.That(result[1].LastElectionDate, Is.EqualTo(_election2026.ElectionDate));
            Assert.That(result[1].Registrations, Has.Count.EqualTo(2));
            Assert.That(result[2].Name, Is.EqualTo("John Doe"));
            Assert.That(result[2].LastElection, Is.EqualTo("2024"));
            Assert.That(result[2].LastElectionDate, Is.EqualTo(_election2024.ElectionDate));
            Assert.That(result[2].Registrations, Has.Count.EqualTo(1));
            Assert.That(result[3].Name, Is.EqualTo("Laura Jones"));
            Assert.That(result[3].LastElection, Is.EqualTo(string.Empty));
            Assert.That(result[3].LastElectionDate, Is.EqualTo(DateTime.MinValue));
            Assert.That(result[3].Registrations, Has.Count.EqualTo(0));
            Assert.That(result[4].Name, Is.EqualTo("James Smith"));
            Assert.That(result[4].LastElection, Is.EqualTo("2026"));
            Assert.That(result[4].LastElectionDate, Is.EqualTo(_election2026.ElectionDate));
            Assert.That(result[4].Registrations, Has.Count.EqualTo(2));
        });

    }

    #region Private methods
    private static Candidate GetCandidate(long id)
    {
        return new Candidate()
        {
            Id = id,
            Registrations = new List<CandidateIntentionStatement>
            {
                new ()
                {
                    Name = "Test",
                    StatusId = 1,
                    AddressList = new ()
                    {
                        Addresses = new List<Address>()
                        {
                           new()
                           {
                            Street = "9876 Main St",
                            Street2 = "suite 100",
                            City = "Plainsville",
                            State = "CA",
                            Zip = "12345",
                            Type = "Business",
                            Country = "United States",
                            Purpose = "Candidate",
                           },
                           new()
                           {
                            Street = "1234 Main St",
                            Street2 = "suite ABC",
                            City = "Anytown",
                            State = "CA",
                            Zip = "12345",
                            Type = "Home",
                            Country = "United States",
                            Purpose = "Mailing",
                           },
                        }
                    },
                    PhoneNumberList = new ()
                    {
                        PhoneNumbers = new List<PhoneNumber>
                        {
                            new()
                            {
                                Number = "1231234",
                                Type = "Phone",
                                Extension = "555",
                                CountryCode = "+1"
                            },
                            new()
                            {
                                Number = "9879876",
                                Type = "Fax",
                                Extension = "555",
                                CountryCode = "+2"
                            },
                        }
                    }
                }
            }
        };
    }
    #endregion Private methods
}

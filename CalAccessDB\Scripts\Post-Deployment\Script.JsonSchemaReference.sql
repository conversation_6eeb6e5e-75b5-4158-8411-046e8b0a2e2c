/*
Populate the JsonSchemaReference table with valid JSON and respective form name
*/

SET IDENTITY_INSERT JsonSchemaReference ON;
GO

MERGE INTO JsonSchemaReference
USING (VALUES
    (1, '{
  "title": "Form 501 Schema",
  "description": "Candidate Intention Statement",
  "type": "object",
  "additionalProperties": false,
  "properties": {
    "amendment": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "isAmendment": {
          "type": "boolean"
        },
        "supercededFilingId": {
          "type": "string"
        },
        "descriptionOfAmendment": {
          "type": "string"
        }
      }
    },
    "candidateInformation": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "firstName": {
          "type": "string"
        },
        "middleName": {
          "type": "string"
        },
        "lastName": {
          "type": "string"
        },
        "candidateAddress": {
          "type": "object",
          "additionalProperties": false,
          "properties": {
            "street": {
              "type": "string"
            },
            "street2": {
              "type": "string"
            },
            "city": {
              "type": "string"
            },
            "state": {
              "type": "string"
            },
            "zipCode": {
              "type": "string"
            },
            "county": {
              "type": "string"
            },
            "country": {
              "type": "string"
            },
            "type": {
              "type": "string"
            }
          },
          "required": [
            "street",
            "city",
            "state",
            "zipCode",
            "country",
            "type"
          ]
        },
        "mailingAddress": {
          "type": "object",
          "additionalProperties": false,
          "properties": {
            "street": {
              "type": "string"
            },
            "street2": {
              "type": "string"
            },
            "city": {
              "type": "string"
            },
            "state": {
              "type": "string"
            },
            "zipCode": {
              "type": "string"
            },
            "county": {
              "type": "string"
            },
            "country": {
              "type": "string"
            },
            "type": {
              "type": "string"
            }
          },
          "required": [
            "street",
            "city",
            "state",
            "zipCode",
            "country",
            "type"
          ]
        },
        "phone": {
          "type": "string",
          "pattern": "^[0-9]+$",
          "maxLength": 40
        },
        "fax": {
          "type": "string",
          "pattern": "^[0-9]+$",
          "maxLength": 40
        },
        "email": {
          "type": "string",
          "format": "email"
        },
        "electionJurisdiction": {
          "type": "string"
        },
        "isNonPartisanOffice": {
          "type": "boolean"
        },
        "partyAffiliation": {
          "type": "string"
        },
        "electionRaceId": {
          "type": "integer",
          "format": "int64"
        }
      },
      "required": [
        "firstName",
        "lastName",
        "candidateAddress",
        "phone",
        "electionRaceId"
      ],
      "anyOf": [
        { "required": [ "isNonPartisanOffice" ] },
        { "required": [ "partyAffiliation" ] }
      ]
    },
    "stateCandidateExpenditureLimit": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "expenditureLimitAccepted": {
          "type": "boolean"
        },
        "expenditureExceeded": {
          "type": "boolean"
        },
        "expenditureLimitAmendment": {
          "type": "boolean"
        },
        "expenditureLimitAmendmentElectionDate": {
          "type": "string",
          "format": "date"
        },
        "contributedPersonalExcessFundsOn": {
          "type": "string",
          "format": "date"
        }
      },
      "required": [
        "expenditureLimitAccepted"
      ]
    },
    "attestation": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "firstName": {
          "type": "string"
        },
        "middleName": {
          "type": "string"
        },
        "lastName": {
          "type": "string"
        },
        "executedAt": {
          "type": "string",
          "format": "date"
        },
        "executedAtCity": {
          "type": "string"
        },
        "executedAtState": {
          "type": "string"
        },
        "role": {
          "type": "string"
        },
        "signature": {
          "type": "string"
        }
      },
      "required": [
        "executedAt",
        "signature"
      ]
    }
  },
  "required": [
    "candidateInformation",
    "stateCandidateExpenditureLimit",
    "attestation"
  ]
}', 'Campaign-Statement-CandidateIntention'),
    (2, '{
  "title": "Form 401 Schema",
  "description": "Campaign Disclosure Slate Mailer",
  "type": "object",
  "additionalProperties": false,
  "properties": {
    "statementPeriod": {
      "type": "object",
      "properties": {
        "startDate": {
          "type": "string",
          "format": "date",
          "description": "Start date of the reporting period."
        },
        "endDate": {
          "type": "string",
          "format": "date",
          "description": "End date of the reporting period."
        }
      },
      "additionalProperties": false
    },
    "amendment": {
      "type": "object",
      "properties": {
        "isAmendment": {
          "type": "boolean",
          "description": "Indicates if the submission is an amendment to a previously filed form."
        },
        "supercededFilingId": {
          "type": "number",
          "format": "int64",
          "description": "The Secretary of State issued filing ID number for the filing being amended"
        }
      },
      "description": "Amendment information, if applicable.",
      "required": [
        "isAmendment",
        "supercededFilingId"
      ],
      "additionalProperties": false
    },
    "contactDetails": {
      "type": "object",
      "properties": {
        "address": {
          "type": "object",
          "description": "Detailed business or residential address",
          "properties": {
            "id": {
              "type": "string",
              "format": "uuid",
              "description": "Unique Id for the address."
            },
            "street": {
              "type": "string",
              "description": "Street address."
            },
            "street2": {
              "type": "string",
              "description": "Street address."
            },
            "city": {
              "type": "string",
              "description": "City."
            },
            "state": {
              "type": "string",
              "description": "State."
            },
            "zipCode": {
              "type": "string",
              "description": "ZIP code."
            },
            "county": {
              "type": "string",
              "description": "County."
            },
            "country": {
              "type": "string",
              "description": "Country."
            },
            "type": {
              "type": "string",
              "description": "Type of Address."
            }
          },
          "required": [
            "street",
            "city",
            "state",
            "zipCode",
            "country",
            "type"
          ],
          "example": {
            "street": "123 Main Street",
            "street2": "Suite 456",
            "city": "Sacramento",
            "state": "CA",
            "zipCode": "95814",
            "country": "United States",
            "type": "Residential"
          },
          "additionalProperties": false
        },
        "phoneNumber": {
          "type": "string",
          "description": "Contact phone number as digits only.",
          "pattern": "^\\d+$",
          "maxLength": 40
        }
      },
      "additionalProperties": false
    },
    "recipientCommitteeIdNumber": {
      "type": "number",
      "description": "Recipient committee ID number if applicable."
    },
    "recipientCommittee": {
      "anyOf": [
        {
          "type": "object",
          "properties": {
            "periodFrom": {
              "type": "string",
              "format": "date",
              "description": "Statement covers period from."
            },
            "periodThrough": {
              "type": "string",
              "format": "date",
              "description": "Statement covers period through."
            },
            "dateOfElection": {
              "type": "string",
              "format": "date",
              "description": "Date of the election, if applicable."
            },
            "typeOfRecipientCommittee": {
              "type": "string",
              "description": "Type of recipent committee."
            },
            "expendituresMade": {
              "type": "object",
              "properties": {
                "over100": {
                  "type": "number",
                  "format": "double",
                  "description": "Expenditures of $100 or more made this period."
                },
                "under100": {
                  "type": "number",
                  "format": "double",
                  "description": "Expenditures under $100 made this period."
                },
                "subtotal": {
                  "type": "number",
                  "format": "double",
                  "description": "Subtotal of expenditures made this period."
                },
                "nonmonetaryAdjustment": {
                  "type": "number",
                  "format": "double",
                  "description": "Nonmonetary adjustment."
                },
                "previousStatementTotalExpenditures": {
                  "type": "number",
                  "format": "double",
                  "description": "Total expenditures made from previous statement (If this is the first statement for the calendar year, enter zero.)"
                },
                "totalToDate": {
                  "type": "number",
                  "format": "double",
                  "description": "Total expenditures made to date."
                }
              },
              "required": [
                "over100",
                "under100",
                "subtotal",
                "totalToDate"
              ]
            },
            "contributionsReceived": {
              "type": "object",
              "properties": {
                "monetary": {
                  "type": "number",
                  "format": "double",
                  "description": "Monetary contributions received this period."
                },
                "nonMonetary": {
                  "type": "number",
                  "format": "double",
                  "description": "Non-monetary contributions received this period."
                },
                "previousStatementTotalContributions": {
                  "type": "number",
                  "format": "double",
                  "description": "Total contributions received from previous statement. (If this is the first statement for the calendar year, enter zero.)"
                },
                "totalToDate": {
                  "type": "number",
                  "format": "double",
                  "description": "Total contributions received to date."
                }
              },
              "required": [
                "monetary",
                "nonMonetary",
                "totalToDate"
              ]
            },
            "cashStatement": {
              "type": "object",
              "properties": {
                "beginningBalance": {
                  "type": "number",
                  "format": "double",
                  "description": "Beginning cash balance for the period."
                },
                "cashReceipts": {
                  "type": "number",
                  "format": "double",
                  "description": "Total cash receipts this period."
                },
                "miscellaneousIncreases": {
                  "type": "number",
                  "format": "double",
                  "description": "Miscellaneous increases to cash this period."
                },
                "cashExpenditures": {
                  "type": "number",
                  "format": "double",
                  "description": "Total cash expenditures this period."
                },
                "endingBalance": {
                  "type": "number",
                  "format": "double",
                  "description": "Ending cash balance for the period."
                }
              },
              "required": [
                "beginningBalance",
                "cashReceipts",
                "cashExpenditures",
                "endingBalance"
              ]
            },
            "paymentsMade": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "date": {
                    "type": "string",
                    "format": "date",
                    "description": "Date of payment."
                  },
                  "payee": {
                    "type": "string",
                    "description": "Name of the payee."
                  },
                  "address": {
                    "allOf": [
                      {
                        "type": "object",
                        "description": "Detailed business or residential address",
                        "properties": {
                          "id": {
                            "type": "string",
                            "format": "uuid",
                            "description": "Unique Id for the address."
                          },
                          "street": {
                            "type": "string",
                            "description": "Street address."
                          },
                          "street2": {
                            "type": "string",
                            "description": "Street address."
                          },
                          "city": {
                            "type": "string",
                            "description": "City."
                          },
                          "state": {
                            "type": "string",
                            "description": "State."
                          },
                          "zipCode": {
                            "type": "string",
                            "description": "ZIP code."
                          },
                          "county": {
                            "type": "string",
                            "description": "County."
                          },
                          "country": {
                            "type": "string",
                            "description": "Country."
                          },
                          "type": {
                            "type": "string",
                            "description": "Type of Address."
                          }
                        },
                        "required": [
                          "street",
                          "city",
                          "state",
                          "zipCode",
                          "country",
                          "type"
                        ],
                        "example": {
                          "street": "123 Main Street",
                          "street2": "Suite 456",
                          "city": "Sacramento",
                          "state": "CA",
                          "zipCode": "95814",
                          "country": "United States",
                          "type": "Residential"
                        }
                      }
                    ],
                    "description": "Address of the payee."
                  },
                  "committeeId": {
                    "type": "integer",
                    "format": "int64"
                  },
                  "description": {
                    "type": "string",
                    "description": "Description of the payment."
                  },
                  "amount": {
                    "type": "number",
                    "format": "double",
                    "description": "Amount of the payment."
                  },
                  "calendarYearCumulativeToDate": {
                    "type": "number",
                    "format": "double",
                    "description": "Cumulative amount paid to date for calendar year."
                  },
                  "otherCumulativeToDate": {
                    "format": "string",
                    "description": "Cumulative amount paid to date for other. May contain a alphanumeric code to indicate the limitation cycle and year of election.",
                    "example": "$4,200 P-16"
                  },
                  "candidateName": {
                    "type": "string",
                    "description": "Name of candidate."
                  },
                  "candidateOffice": {
                    "type": "string",
                    "description": "Name of candidate office."
                  },
                  "ballotMeasureName": {
                    "type": "string",
                    "description": "Name of ballot measure."
                  },
                  "ballotMeasureId": {
                    "type": "string",
                    "description": "Ballot measure number or letter and jurisdiction."
                  },
                  "supportOppose": {
                    "type": "string",
                    "enum": [
                      "Support",
                      "Oppose"
                    ],
                    "description": "Indicates if the payment supports or opposes a candidate or measure."
                  },
                  "type": {
                    "type": "string",
                    "enum": [
                      "Contribution",
                      "Independent Expenditure"
                    ],
                    "description": "Indicates the payment type."
                  }
                },
                "required": [
                  "date",
                  "payee",
                  "description",
                  "amount"
                ]
              },
              "description": "List of payments made during the period."
            },
            "filingType": {
              "type": "object",
              "properties": {
                "amendment": {
                  "type": "boolean"
                },
                "isPreElectionStatement": {
                  "type": "boolean"
                },
                "isQuarterlyStatement": {
                  "type": "boolean"
                },
                "isSemiAnnualStatement": {
                  "type": "boolean"
                },
                "isSpecialOddYearReport": {
                  "type": "boolean"
                },
                "termination": {
                  "type": "boolean"
                },
                "amendmentNumber": {
                  "type": "string"
                },
                "initialFilingId": {
                  "type": "string"
                },
                "explanation": {
                  "type": "string"
                }
              }
            },
            "attestation": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "firstName": {
                    "type": "string",
                    "description": "First Name of verification officer."
                  },
                  "lastName": {
                    "type": "string",
                    "description": "Last Name of verification officer."
                  },
                  "executedAt": {
                    "type": "string",
                    "format": "date",
                    "description": "Date the statement was executed."
                  },
                  "executedAtCity": {
                    "type": "string",
                    "description": "Executed at City."
                  },
                  "executedAtState": {
                    "type": "string",
                    "description": "Executed at state."
                  },
                  "role": {
                    "type": "string"
                  },
                  "signature": {
                    "type": "string",
                    "description": "Name of the treasurer or filer."
                  }
                },
                "required": [
                  "executedAt",
                  "signature"
                ]
              },
              "description": "Up to 4 separate attestations"
            }
          }
        },
        {
          "type": "object",
          "properties": {
            "reportingPeriod": {
              "allOf": [
                {
                  "type": "object",
                  "required": [
                    "periodFrom",
                    "periodTo"
                  ],
                  "properties": {
                    "periodFrom": {
                      "type": "string",
                      "format": "date",
                      "description": "The start date of the reporting period."
                    },
                    "periodTo": {
                      "type": "string",
                      "format": "date",
                      "description": "The end date of the reporting period."
                    }
                  }
                }
              ],
              "description": "Cover Page - If this is the first report of the calendar year, the periodFrom date should be January 1. Otherwise, this date should be the day after the closing date of the most recently filed campaign statement. The periodTo date should be as identified on the campaign statement filing schedule."
            },
            "dateOfElection": {
              "type": "string",
              "format": "date",
              "description": "Cover Page - When filing a preelection statement in connection with an election, provide the date of the election"
            },
            "typeOfStatement": {
              "type": "string",
              "description": "Cover Page - The type of statement being filed. The filing schedule will identify the type of statement (e.g., preelection, semi-annual)."
            },
            "amendment": {
              "type": "object",
              "properties": {
                "isAmendment": {
                  "type": "boolean",
                  "description": "Indicates if the submission is an amendment to a previously filed form."
                },
                "supercededFilingId": {
                  "type": "string",
                  "description": "The Secretary of State issued filing ID number for the filing being amended"
                },
                "descriptionOfAmendment": {
                  "type": "string",
                  "description": "Explanation of changes"
                }
              },
              "description": "Amendment information, if applicable.",
              "required": [
                "isAmendment",
                "supercededFilingId",
                "descriptionOfAmendment"
              ]
            },
            "treasurer": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "id": {
                    "type": "string",
                    "format": "uuid",
                    "description": "A unique id (in the context of the filer) for the treasurer."
                  },
                  "firstName": {
                    "type": "string",
                    "description": "First name of the treasurer"
                  },
                  "lastName": {
                    "type": "string",
                    "description": "Last name of the treasurer"
                  },
                  "mailingAddress": {
                    "type": "object",
                    "description": "Detailed business or residential address",
                    "properties": {
                      "id": {
                        "type": "string",
                        "format": "uuid",
                        "description": "Unique Id for the address."
                      },
                      "street": {
                        "type": "string",
                        "description": "Street address."
                      },
                      "street2": {
                        "type": "string",
                        "description": "Street address."
                      },
                      "city": {
                        "type": "string",
                        "description": "City."
                      },
                      "state": {
                        "type": "string",
                        "description": "State."
                      },
                      "zipCode": {
                        "type": "string",
                        "description": "ZIP code."
                      },
                      "county": {
                        "type": "string",
                        "description": "County."
                      },
                      "country": {
                        "type": "string",
                        "description": "Country."
                      },
                      "type": {
                        "type": "string",
                        "description": "Type of Address."
                      }
                    },
                    "required": [
                      "street",
                      "city",
                      "state",
                      "zipCode",
                      "country",
                      "type"
                    ],
                    "example": {
                      "street": "123 Main Street",
                      "street2": "Suite 456",
                      "city": "Sacramento",
                      "state": "CA",
                      "zipCode": "95814",
                      "country": "United States",
                      "type": "Residential"
                    }
                  },
                  "phone": {
                    "type": "string",
                    "description": "Daytime phone number of the officer as digits only.",
                    "pattern": "^\\d+$",
                    "maxLength": 40
                  },
                  "fax": {
                    "type": "string",
                    "description": "Optional fax number as digits only.",
                    "pattern": "^\\d+$",
                    "maxLength": 40
                  },
                  "emailAddress": {
                    "type": "string",
                    "format": "email",
                    "description": "Email address of treasurer"
                  }
                }
              }
            },
            "relatedCommittees": {
              "type": "array",
              "items": {
                "allOf": [
                  {
                    "type": "object",
                    "properties": {
                      "id": {
                        "type": "integer",
                        "description": "Unique id of the committee as assigned by Secretary of State"
                      },
                      "name": {
                        "type": "string",
                        "description": "Name of committee as provided on the committee''s most recently filed Statement of Organization, Form 410."
                      },
                      "committeeAddress": {
                        "type": "object",
                        "description": "Detailed business or residential address",
                        "properties": {
                          "id": {
                            "type": "string",
                            "format": "uuid",
                            "description": "Unique Id for the address."
                          },
                          "street": {
                            "type": "string",
                            "description": "Street address."
                          },
                          "street2": {
                            "type": "string",
                            "description": "Street address."
                          },
                          "city": {
                            "type": "string",
                            "description": "City."
                          },
                          "state": {
                            "type": "string",
                            "description": "State."
                          },
                          "zipCode": {
                            "type": "string",
                            "description": "ZIP code."
                          },
                          "county": {
                            "type": "string",
                            "description": "County."
                          },
                          "country": {
                            "type": "string",
                            "description": "Country."
                          },
                          "type": {
                            "type": "string",
                            "description": "Type of Address."
                          }
                        },
                        "required": [
                          "street",
                          "city",
                          "state",
                          "zipCode",
                          "country",
                          "type"
                        ],
                        "example": {
                          "street": "123 Main Street",
                          "street2": "Suite 456",
                          "city": "Sacramento",
                          "state": "CA",
                          "zipCode": "95814",
                          "country": "United States",
                          "type": "Residential"
                        }
                      }
                    }
                  },
                  {
                    "type": "object",
                    "properties": {
                      "committeeType": {
                        "type": "string",
                        "description": "Type of committee as provided on the committee''s most recently filed Statement of Organization, Form 410."
                      },
                      "controlledCommittee": {
                        "type": "boolean",
                        "description": "Indicates if the committee is a controlled committee"
                      },
                      "firstName": {
                        "type": "string",
                        "description": "Name of Candidate as provided on the committee''s most recently filed Statement of Organization, Form 410."
                      },
                      "lastName": {
                        "type": "string",
                        "description": "Name of Candidate as provided on the committee''s most recently filed Statement of Organization, Form 410."
                      },
                      "candidateAddress": {
                        "type": "object",
                        "description": "Detailed business or residential address",
                        "properties": {
                          "id": {
                            "type": "string",
                            "format": "uuid",
                            "description": "Unique Id for the address."
                          },
                          "street": {
                            "type": "string",
                            "description": "Street address."
                          },
                          "street2": {
                            "type": "string",
                            "description": "Street address."
                          },
                          "city": {
                            "type": "string",
                            "description": "City."
                          },
                          "state": {
                            "type": "string",
                            "description": "State."
                          },
                          "zipCode": {
                            "type": "string",
                            "description": "ZIP code."
                          },
                          "county": {
                            "type": "string",
                            "description": "County."
                          },
                          "country": {
                            "type": "string",
                            "description": "Country."
                          },
                          "type": {
                            "type": "string",
                            "description": "Type of Address."
                          }
                        },
                        "required": [
                          "street",
                          "city",
                          "state",
                          "zipCode",
                          "country",
                          "type"
                        ],
                        "example": {
                          "street": "123 Main Street",
                          "street2": "Suite 456",
                          "city": "Sacramento",
                          "state": "CA",
                          "zipCode": "95814",
                          "country": "United States",
                          "type": "Residential"
                        }
                      },
                      "electionOfficeSought": {
                        "type": "object",
                        "properties": {
                          "office": {
                            "type": "string",
                            "description": "Office held or sought by the candidate."
                          },
                          "officeDescription": {
                            "type": "string",
                            "description": "Title of the office by the candidate."
                          },
                          "jurisdiction": {
                            "type": "string",
                            "description": "Jurisdiction of the office."
                          },
                          "jurisdictionDescription": {
                            "type": "string",
                            "description": "Jurisdiction of the candidate or measure."
                          },
                          "electionDistrictNumber": {
                            "type": "string",
                            "description": "District number (if applicable)."
                          }
                        }
                      },
                      "electionDistrictNumber": {
                        "type": "string",
                        "description": "District number (if applicable)."
                      },
                      "committeeAddress": {
                        "type": "object",
                        "description": "Detailed business or residential address",
                        "properties": {
                          "id": {
                            "type": "string",
                            "format": "uuid",
                            "description": "Unique Id for the address."
                          },
                          "street": {
                            "type": "string",
                            "description": "Street address."
                          },
                          "street2": {
                            "type": "string",
                            "description": "Street address."
                          },
                          "city": {
                            "type": "string",
                            "description": "City."
                          },
                          "state": {
                            "type": "string",
                            "description": "State."
                          },
                          "zipCode": {
                            "type": "string",
                            "description": "ZIP code."
                          },
                          "county": {
                            "type": "string",
                            "description": "County."
                          },
                          "country": {
                            "type": "string",
                            "description": "Country."
                          },
                          "type": {
                            "type": "string",
                            "description": "Type of Address."
                          }
                        },
                        "required": [
                          "street",
                          "city",
                          "state",
                          "zipCode",
                          "country",
                          "type"
                        ],
                        "example": {
                          "street": "123 Main Street",
                          "street2": "Suite 456",
                          "city": "Sacramento",
                          "state": "CA",
                          "zipCode": "95814",
                          "country": "United States",
                          "type": "Residential"
                        }
                      },
                      "emailAddress": {
                        "type": "string",
                        "format": "email"
                      }
                    }
                  }
                ]
              },
              "description": "Cover Page Part 2 Section 5- Ballot measure committees do not complete this section."
            },
            "reportingPeriodSummary": {
              "allOf": [
                {
                  "type": "object",
                  "properties": {
                    "totalMonetaryContributions": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Monetary Contributions Received"
                    },
                    "totalLoansReceived": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Loans Received"
                    },
                    "totalCashContributions": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Cash Contributions Received (Sum of total monetary contributions and total loans received)"
                    },
                    "totalNonMonetaryContributions": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Non-Monetary Contributions Received"
                    },
                    "totalContributions": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of All Contributions (Sum of Total cash contributions and total non monetary contributions)"
                    },
                    "totalPaymentsMade": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Payments Made"
                    },
                    "totalLoansMade": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Loans Made"
                    },
                    "totalCashPayments": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Cash Payments (Sum of total payments made and total loans made)"
                    },
                    "totalAccruedExpenses": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Accrued Expenses (Unpaid Bills)"
                    },
                    "totalNonMonetaryAdjustments": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Non-monetary adjustments"
                    },
                    "totalExpenditures": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of expenditures made (Sum of total cash payments, total accrued expenses, and total non-monetary adjustments)"
                    }
                  }
                }
              ],
              "description": "Summary Page - Column A - Reflects the committee''s activity during the current reporting period as reported on Schedules A through I. If there is no activity to report on a particular schedule, enter a zero. There should be no blank values"
            },
            "yearToDateSummary": {
              "allOf": [
                {
                  "type": "object",
                  "properties": {
                    "totalMonetaryContributions": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Monetary Contributions Received"
                    },
                    "totalLoansReceived": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Loans Received"
                    },
                    "totalCashContributions": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Cash Contributions Received (Sum of total monetary contributions and total loans received)"
                    },
                    "totalNonMonetaryContributions": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Non-Monetary Contributions Received"
                    },
                    "totalContributions": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of All Contributions (Sum of Total cash contributions and total non monetary contributions)"
                    },
                    "totalPaymentsMade": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Payments Made"
                    },
                    "totalLoansMade": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Loans Made"
                    },
                    "totalCashPayments": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Cash Payments (Sum of total payments made and total loans made)"
                    },
                    "totalAccruedExpenses": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Accrued Expenses (Unpaid Bills)"
                    },
                    "totalNonMonetaryAdjustments": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of Non-monetary adjustments"
                    },
                    "totalExpenditures": {
                      "type": "number",
                      "format": "double",
                      "description": "Total Value of expenditures made (Sum of total cash payments, total accrued expenses, and total non-monetary adjustments)"
                    }
                  }
                }
              ],
              "description": "Summary Page - Column B - Reflects the cumulative total since January 1 of the current calendar year. However, if the committee began raising funds in connection with the qualification of a measure that extends into two calendar years, the contributions and expenditures must be cumulated beginning January 1 of the year the committee began raising funds. Add the totals from Column B of the committee''s last campaign statement (if any) to the corresponding amounts in Column A. If this is the first report being filed for a calendar year, only carry forward the amounts for loans and accrued expenses reported on Lines 2, 7, and 9 of Column B (if any) from the committee''s last statement. (Note: The amounts reported on Lines 2, 7, and 9 of Column B should be the same as the total outstanding amounts disclosed in column (d) of Schedules B, F, and H, respectively, of the current report.) When loans (Schedules B and H) and accrued expenses (Schedule F) are paid, the figures to be carried from the schedules to Lines 2, 7, and 9 of Column A may be negative numbers. In this case, be sure to show them as negative figures on the Summary Page (e.g., with a minus sign (-) or in parentheses), and subtract them when totaling Columns A and B."
            },
            "cashStatement": {
              "allOf": [
                {
                  "type": "object",
                  "properties": {
                    "beginningCashBalance": {
                      "type": "number",
                      "format": "double",
                      "description": "Beginning cash balance. Should include the total amount of funds in campaign checking and savings accounts, plus any investments that can be readily converted to cash, such as certificates of deposit, money market accounts, stocks and bonds, etc."
                    },
                    "cashReceipts": {
                      "type": "number",
                      "format": "double",
                      "description": "Total cash contributions from reporting period summary"
                    },
                    "miscellaneousIncreasesToCash": {
                      "type": "number",
                      "format": "double",
                      "description": "Total miscellaneous increases to cash during reporting period"
                    },
                    "cashPayments": {
                      "type": "number",
                      "format": "double",
                      "description": "Total cash payments from reporting period summary"
                    },
                    "endingCashBalance": {
                      "type": "number",
                      "format": "double",
                      "description": "Ending cash balance. Should include the total amount of funds in campaign checking and savings accounts, plus any investments that can be readily converted to cash, such as certificates of deposit, money market accounts, stocks and bonds, etc."
                    }
                  }
                }
              ],
              "description": "Summary Page - Current Cash Statement - The Current Cash Statement section should accurately reflect the committee''s cash position at the end of the reporting period. If deposits or expenditures have been made that have not cleared the account, the committee''s bank balance may not match the ending cash balance. Beginning and ending cash balances should include the total amount of funds in the committee''s campaign checking and savings accounts, plus any investments that can be readily converted to cash, such as certificates of deposit, money market accounts, stocks and bonds, etc."
            },
            "unitemizedAmounts": {
              "allOf": [
                {
                  "type": "object",
                  "properties": {
                    "totalUnitemizedMonetaryContributions": {
                      "type": "number",
                      "format": "double",
                      "description": "Total value of all unitemized cash contributions (less than $100) received during reporting period"
                    },
                    "totalUnitemizedNonMonetaryContributions": {
                      "type": "number",
                      "format": "double",
                      "description": "Total value of all unitemized non-monetary contributions (less than $100) received during reporting period"
                    },
                    "totalUnitemizedMiscellaneousIncreases": {
                      "type": "number",
                      "format": "double",
                      "description": "Total value of all unitemized miscellaneous increases to cash (less than $100) received during reporting period"
                    },
                    "totalUnitemizedLoansReceived": {
                      "type": "number",
                      "format": "double",
                      "description": "Total value of all unitemized loans (less than $100) received during reporting period"
                    },
                    "totalUnitemizedLoansPaid": {
                      "type": "number",
                      "format": "double",
                      "description": "Total value of all unitemized loans paid during reporting period"
                    },
                    "totalUnitemizedLoansMade": {
                      "type": "number",
                      "format": "double",
                      "description": "Total value of all unitemized loans (less than $100) made during reporting period"
                    },
                    "totalUnitemizedLoanPaymentsReceived": {
                      "type": "number",
                      "format": "double",
                      "description": "Total value of all unitemized loan payments received during reporting period"
                    },
                    "totalUnitemizedContributionsAndIndependentExpendituresSupportingCommittees": {
                      "type": "number",
                      "format": "double",
                      "description": "Total value of all unitemized contributions and independent expenditures (less than $100) made during reporting period"
                    },
                    "totalUnitemizedExpenditures": {
                      "type": "number",
                      "format": "double",
                      "description": "Total value of all unitemized expenditures (less than $100) made during reporting period"
                    },
                    "totalUnitemizedAccruedExpensesIncurred": {
                      "type": "number",
                      "format": "double",
                      "description": "Total value of all unitemized accrued expenses (less than $100) during reporting period"
                    },
                    "totalUnitemizedAccruedExpensesPaid": {
                      "type": "number",
                      "format": "double",
                      "description": "Total value of all unitemized accrued expenses paid during reporting period"
                    }
                  }
                }
              ],
              "description": "Unitemized amounts included in summary sections for schedule A, B, C, D, E, F, G, H, I"
            },
            "totalLoanGuaranteesReceived": {
              "type": "number",
              "format": "double",
              "description": "Summary Page/Schedule B Part 2 - The total of all loan guarantees, endorsements, or security received during the period."
            },
            "totalCashEquivalents": {
              "type": "number",
              "format": "double",
              "description": "Summary Page - This figure includes investments that cannot be readily converted to cash as well as the balance due on all outstanding loans the committee has made to others. Do not include any amount that is invested in interest bearing accounts, certificates of deposit, money market accounts, or any other investments that can be readily converted to cash. This amount should be part of the ending cash figure reported on Line 16."
            },
            "totalOutstandingDebts": {
              "type": "number",
              "format": "double",
              "description": "Summary Page - Report the total of all money owed by the committee. Using Column B, add Line 2 (loans received) and Line 9 (accrued expenses)"
            },
            "candidateForPrimaryAndGeneralElectionSummary": {
              "allOf": [
                {
                  "type": "object",
                  "description": "Only for committees that are either Controlled by a candidate who is being voted on in both the state primary and general elections (does not apply to controlled ballot measure committees) or Primarily formed to support or oppose candidates being voted on in both the state primary and general elections. Complete this summary on the preelection and semi-annual statements for the general election, covering periods during the last six months of the year (July 1 - December 31).",
                  "properties": {
                    "totalContributionsFirstHalf": {
                      "type": "number",
                      "format": "double",
                      "description": "Total contributions received January 1st thru June 30th"
                    },
                    "totalExpendituresFirstHalf": {
                      "type": "number",
                      "format": "double",
                      "description": "Total expenditures January 1st thru June 30th"
                    },
                    "totalContributionsSecondHalfToDate": {
                      "type": "number",
                      "format": "double",
                      "description": "Total contributions received July 1st to Date"
                    },
                    "totalExpendituresSecondHalfToDate": {
                      "type": "number",
                      "format": "double",
                      "description": "Total expenditures July 1st to Date"
                    }
                  }
                }
              ],
              "description": "Summary Page - Calendar year summary for candidates running in both the state primary and general elections. Ballot measure committees do not complete this section"
            },
            "expenditureLimitSummaries": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "electionDate": {
                    "type": "string",
                    "format": "date",
                    "description": "Date of the election the candidate has accepted the voluntary expenditure ceiling for."
                  },
                  "totalExpenditures": {
                    "type": "number",
                    "format": "double",
                    "description": "Total amount of expenditures made through the end of the reporting period that are subject to the expenditure ceiling for the election. Report totals for the primary and general elections separately"
                  }
                }
              },
              "description": "Summary Page - Expenditure Limit summary for state candidates. Ballot measure committees do not complete this section"
            },
            "contacts": {
              "type": "array",
              "items": {
                "oneOf": [
                  {
                    "allOf": [
                      {
                        "type": "object",
                        "properties": {
                          "id": {
                            "type": "string",
                            "format": "uuid",
                            "description": "Unique Id (in the context of the filer) for the contact ."
                          },
                          "contactType": {
                            "type": "string",
                            "description": "Indicate whether the contact is an individual, a committee, or \"other\" (such as a business entity), or a political party. Use commercialLendingInstitution to identify a contact that is providing a loan in the normal course of business. Commercial lending institutions will appear as other on the form."
                          },
                          "addresses": {
                            "type": "array",
                            "items": {
                              "type": "object",
                              "description": "Detailed business or residential address",
                              "properties": {
                                "id": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "Unique Id for the address."
                                },
                                "street": {
                                  "type": "string",
                                  "description": "Street address."
                                },
                                "street2": {
                                  "type": "string",
                                  "description": "Street address."
                                },
                                "city": {
                                  "type": "string",
                                  "description": "City."
                                },
                                "state": {
                                  "type": "string",
                                  "description": "State."
                                },
                                "zipCode": {
                                  "type": "string",
                                  "description": "ZIP code."
                                },
                                "county": {
                                  "type": "string",
                                  "description": "County."
                                },
                                "country": {
                                  "type": "string",
                                  "description": "Country."
                                },
                                "type": {
                                  "type": "string",
                                  "description": "Type of Address."
                                }
                              },
                              "required": [
                                "street",
                                "city",
                                "state",
                                "zipCode",
                                "country",
                                "type"
                              ],
                              "example": {
                                "street": "123 Main Street",
                                "street2": "Suite 456",
                                "city": "Sacramento",
                                "state": "CA",
                                "zipCode": "95814",
                                "country": "United States",
                                "type": "Residential"
                              }
                            },
                            "description": "List of addresses associated to the contact"
                          },
                          "cumulativeContributionsYearToDate": {
                            "type": "number",
                            "format": "double",
                            "description": "Schedule A - Cumulative to Date Calendar Year - Generally will be sum of all contributions including nonmonetary contributions, loans, or loan guarantees from the same source from January 1 through December 31 unless the committee began raising funds in connection with the qualification of a measure that extends into two calendar years. In that case, the period over which contributions are cumulated begins January 1 of the year the committee began raising funds."
                          },
                          "cumulativeContributionsPerElection": {
                            "type": "array",
                            "items": {
                              "type": "object",
                              "properties": {
                                "electionType": {
                                  "type": "string"
                                },
                                "electionYear": {
                                  "type": "integer"
                                }
                              }
                            },
                            "description": "Schedule A - Per Election to Date - State candidates must complete for each itemized contribution, disclosing the type of election, the year of the election, and the amount received from the contributor for that election. This column does not apply to ballot measure committees, unless otherwise instructed by a local ordinance."
                          },
                          "monetaryContributions": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "type": "object",
                                      "properties": {
                                        "transactionId": {
                                          "type": "string",
                                          "format": "uuid",
                                          "description": "Unique id for the transaction in the context of the filer"
                                        },
                                        "transactionDate": {
                                          "type": "string",
                                          "format": "date",
                                          "description": "Date the transaction occurred"
                                        },
                                        "amount": {
                                          "type": "number",
                                          "format": "double",
                                          "description": "Amount of the transaction"
                                        }
                                      }
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "addressId": {
                                          "type": "string",
                                          "format": "uuid",
                                          "description": "The id of the contact address that is associated with this transaction"
                                        }
                                      }
                                    }
                                  ]
                                },
                                {
                                  "type": "object",
                                  "properties": {
                                    "intermediaries": {
                                      "type": "array",
                                      "items": {
                                        "type": "string",
                                        "format": "uuid",
                                        "description": "Unique Id of the contact that served as an intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary."
                                      }
                                    }
                                  }
                                }
                              ]
                            },
                            "description": "Schedule A - List of monetary contributions of $100 or more made by the contact, except for loans (reported on Schedule B)."
                          },
                          "nonmonetaryContributions": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "allOf": [
                                        {
                                          "type": "object",
                                          "properties": {
                                            "transactionId": {
                                              "type": "string",
                                              "format": "uuid",
                                              "description": "Unique id for the transaction in the context of the filer"
                                            },
                                            "transactionDate": {
                                              "type": "string",
                                              "format": "date",
                                              "description": "Date the transaction occurred"
                                            },
                                            "amount": {
                                              "type": "number",
                                              "format": "double",
                                              "description": "Amount of the transaction"
                                            }
                                          }
                                        },
                                        {
                                          "type": "object",
                                          "properties": {
                                            "addressId": {
                                              "type": "string",
                                              "format": "uuid",
                                              "description": "The id of the contact address that is associated with this transaction"
                                            }
                                          }
                                        }
                                      ]
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "description": {
                                          "type": "string",
                                          "description": "A description the transaction"
                                        }
                                      }
                                    }
                                  ]
                                },
                                {
                                  "type": "object",
                                  "properties": {
                                    "intermediaries": {
                                      "type": "array",
                                      "items": {
                                        "type": "string",
                                        "format": "uuid",
                                        "description": "Unique Id of the contact that served as an intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary."
                                      }
                                    }
                                  }
                                }
                              ]
                            },
                            "description": "Schedule C - List of nonmonetary contributions of $100 or more made by the contact. Nonmonetary contributions (also referred to as in-kind contributions) are goods or services provided to the committee for which it does not pay fair market value. The fair market value is the amount the committee would pay for the goods or services on the open market (whatever it would cost any member of the general public to obtain the same goods or services)."
                          },
                          "loansIssued": {
                            "type": "array",
                            "items": {
                              "type": "object",
                              "properties": {
                                "transactionId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "Unique id for the transaction in the context of the filer"
                                },
                                "addressId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "The id of the contact address that is associated with this transaction"
                                },
                                "dateIncurred": {
                                  "type": "string",
                                  "format": "date",
                                  "description": "The date the loan was incurred"
                                },
                                "dateDue": {
                                  "type": "string",
                                  "format": "date",
                                  "description": "The date the loan is due"
                                },
                                "originalAmount": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The initial amount of the loan"
                                },
                                "interestRate": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The annual interest rate of the loan"
                                },
                                "beginningPeriodBalance": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The outstanding balance of the loan at the beginning of the period. If the loan was received this period, this value should be blank"
                                },
                                "amountInPeriod": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The amount received from the lender during this reporting period. If the loan was received in a previous reporting period, leave blank."
                                },
                                "amountPaid": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The amount of principal repaid by the committee during this reporting period. Interest paid is reported separately from payments made on the loan principal."
                                },
                                "interestPaid": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The amount of interest paid by the committee during this reporting period. Interest paid is reported separately from payments made on the loan principal. Interest payments are also transferred to the Schedule E Summary"
                                },
                                "amountForgiven": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "Report any reduction of the loan during this reporting period that was not paid by the committee. When the lender forgives a loan or a third party makes a payment on a loan, also report the lender or third party on Schedule A"
                                },
                                "endingPeriodBalance": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The outstanding balance of the loan at the close of this reporting period"
                                },
                                "intermediaries": {
                                  "type": "array",
                                  "items": {
                                    "type": "string",
                                    "format": "uuid",
                                    "description": "Id of the contact that served as the intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary."
                                  }
                                }
                              }
                            },
                            "description": "Schedule B - Part 1 - list of loans of $100 or more the contact has issued or are outstanding."
                          },
                          "loansGuaranteed": {
                            "type": "array",
                            "items": {
                              "type": "object",
                              "properties": {
                                "transactionId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "Unique id for the transaction in the context of the filer"
                                },
                                "transactionDate": {
                                  "type": "string",
                                  "format": "date",
                                  "description": "The date the loan was incurred"
                                },
                                "addressId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "The id of the contact address that is associated with this transaction"
                                },
                                "loanId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "Unique transactionId for the loan that was guaranteed"
                                },
                                "amountGuaranteedThisPeriod": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The amount guaranteed this period, if applicable. For lines of credit, enter the full amount established or secured by the guarantor during the period."
                                },
                                "balanceOutstanding": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The outstanding balance for which the guarantor is liable at the close of this reporting period"
                                },
                                "intermediaries": {
                                  "type": "array",
                                  "items": {
                                    "type": "string",
                                    "format": "uuid",
                                    "description": "Id of the contact that served as the intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary."
                                  }
                                }
                              }
                            },
                            "description": "Schedule B - Part 2 - list of loans of $100 or more contact has guaranteed, co-signed, endorsed, or provided security for."
                          },
                          "payments": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "allOf": [
                                        {
                                          "allOf": [
                                            {
                                              "type": "object",
                                              "properties": {
                                                "transactionId": {
                                                  "type": "string",
                                                  "format": "uuid",
                                                  "description": "Unique id for the transaction in the context of the filer"
                                                },
                                                "transactionDate": {
                                                  "type": "string",
                                                  "format": "date",
                                                  "description": "Date the transaction occurred"
                                                },
                                                "amount": {
                                                  "type": "number",
                                                  "format": "double",
                                                  "description": "Amount of the transaction"
                                                }
                                              }
                                            },
                                            {
                                              "type": "object",
                                              "properties": {
                                                "addressId": {
                                                  "type": "string",
                                                  "format": "uuid",
                                                  "description": "The id of the contact address that is associated with this transaction"
                                                }
                                              }
                                            }
                                          ]
                                        },
                                        {
                                          "type": "object",
                                          "properties": {
                                            "description": {
                                              "type": "string",
                                              "description": "A description the transaction"
                                            }
                                          }
                                        }
                                      ]
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "expenseType": {
                                          "type": "string"
                                        }
                                      }
                                    }
                                  ]
                                },
                                {
                                  "type": "object",
                                  "properties": {
                                    "subvendorPayment": {
                                      "type": "boolean",
                                      "description": "True if this payment was made by an agent or independent contractor on behalf of the committee. The transaction id must be included in the subvendor payments property of the paying party"
                                    },
                                    "paymentFor": {
                                      "type": "string",
                                      "format": "uuid",
                                      "description": "Provide the transaction id of the accrued expense if the payment is associated to a previously accrued expense"
                                    }
                                  }
                                }
                              ]
                            },
                            "description": "Schedule E - list of payments to the contact of $100 or more or payments totaling $100 or more made during the period for a single product or service"
                          },
                          "accruedExpenses": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "allOf": [
                                        {
                                          "allOf": [
                                            {
                                              "type": "object",
                                              "properties": {
                                                "transactionId": {
                                                  "type": "string",
                                                  "format": "uuid",
                                                  "description": "Unique id for the transaction in the context of the filer"
                                                },
                                                "transactionDate": {
                                                  "type": "string",
                                                  "format": "date",
                                                  "description": "Date the transaction occurred"
                                                },
                                                "amount": {
                                                  "type": "number",
                                                  "format": "double",
                                                  "description": "Amount of the transaction"
                                                }
                                              }
                                            },
                                            {
                                              "type": "object",
                                              "properties": {
                                                "addressId": {
                                                  "type": "string",
                                                  "format": "uuid",
                                                  "description": "The id of the contact address that is associated with this transaction"
                                                }
                                              }
                                            }
                                          ]
                                        },
                                        {
                                          "type": "object",
                                          "properties": {
                                            "description": {
                                              "type": "string",
                                              "description": "A description the transaction"
                                            }
                                          }
                                        }
                                      ]
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "expenseType": {
                                          "type": "string"
                                        }
                                      }
                                    }
                                  ]
                                },
                                {
                                  "type": "object",
                                  "properties": {
                                    "beginningPeriodBalance": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Amount still owed on an expense incurred during a prior period at the beginning of the current reporting period"
                                    },
                                    "amountIncurred": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Amount incurred during the current period that is outstanding at the end of the period"
                                    },
                                    "estimatedAmount": {
                                      "type": "boolean",
                                      "description": "Flag to indicate that the amount is an estimate. If the exact amount of a debt or obligation, provide an estimate. Once the exact amount is known, amend the estimated amount or note the correct amount on the next campaign statement"
                                    },
                                    "amountAdjusted": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Difference between the estimated amount reported on previous campaign statement and actual amount incurred"
                                    },
                                    "amountPaid": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Amount paid this period. Also report on Schedule E."
                                    },
                                    "endingPeriodBalance": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Amount still owed on an expense at the end of the current reporting period"
                                    }
                                  }
                                }
                              ]
                            },
                            "description": "Schedule F"
                          },
                          "subVendorPayments": {
                            "type": "array",
                            "description": "List of transaction ids that appear in schedule G that were paid by this contact",
                            "items": {
                              "type": "string",
                              "format": "uuid"
                            }
                          },
                          "miscellaneousIncreasesToCash": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "allOf": [
                                        {
                                          "type": "object",
                                          "properties": {
                                            "transactionId": {
                                              "type": "string",
                                              "format": "uuid",
                                              "description": "Unique id for the transaction in the context of the filer"
                                            },
                                            "transactionDate": {
                                              "type": "string",
                                              "format": "date",
                                              "description": "Date the transaction occurred"
                                            },
                                            "amount": {
                                              "type": "number",
                                              "format": "double",
                                              "description": "Amount of the transaction"
                                            }
                                          }
                                        },
                                        {
                                          "type": "object",
                                          "properties": {
                                            "addressId": {
                                              "type": "string",
                                              "format": "uuid",
                                              "description": "The id of the contact address that is associated with this transaction"
                                            }
                                          }
                                        }
                                      ]
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "description": {
                                          "type": "string",
                                          "description": "A description the transaction"
                                        }
                                      }
                                    }
                                  ]
                                }
                              ]
                            },
                            "description": "Schedule I"
                          }
                        }
                      },
                      {
                        "type": "object",
                        "properties": {
                          "id": {
                            "type": "integer",
                            "description": "Unique id of the committee as assigned by Secretary of State"
                          },
                          "name": {
                            "type": "string",
                            "description": "Name of committee as provided on the committee''s most recently filed Statement of Organization, Form 410."
                          },
                          "committeeAddress": {
                            "type": "object",
                            "description": "Detailed business or residential address",
                            "properties": {
                              "id": {
                                "type": "string",
                                "format": "uuid",
                                "description": "Unique Id for the address."
                              },
                              "street": {
                                "type": "string",
                                "description": "Street address."
                              },
                              "street2": {
                                "type": "string",
                                "description": "Street address."
                              },
                              "city": {
                                "type": "string",
                                "description": "City."
                              },
                              "state": {
                                "type": "string",
                                "description": "State."
                              },
                              "zipCode": {
                                "type": "string",
                                "description": "ZIP code."
                              },
                              "county": {
                                "type": "string",
                                "description": "County."
                              },
                              "country": {
                                "type": "string",
                                "description": "Country."
                              },
                              "type": {
                                "type": "string",
                                "description": "Type of Address."
                              }
                            },
                            "required": [
                              "street",
                              "city",
                              "state",
                              "zipCode",
                              "country",
                              "type"
                            ],
                            "example": {
                              "street": "123 Main Street",
                              "street2": "Suite 456",
                              "city": "Sacramento",
                              "state": "CA",
                              "zipCode": "95814",
                              "country": "United States",
                              "type": "Residential"
                            }
                          }
                        }
                      },
                      {
                        "type": "object",
                        "properties": {
                          "firstName": {
                            "type": "string",
                            "description": "For independent expenditure or major donor committees, the first name of the LLC''s responsible officer as defined in Regulation 18402.2. For recipient committees, the first name of the principal officer as defined in Section 82047.6. For an LLC that has not qualified as a committee, the first name of the individual primarily responsible for approving the contribution. If more than one individual shares in the primary responsibility of approving a contribution, at least one such individual must be identified"
                          },
                          "lastName": {
                            "type": "string",
                            "description": "For independent expenditure or major donor committees, the last name of the LLC''s responsible officer as defined in Regulation 18402.2. For recipient committees, the last name of the principal officer as defined in Section 82047.6. For an LLC that has not qualified as a committee, the last name of the individual primarily responsible for approving the contribution. If more than one individual shares in the primary responsibility of approving a contribution, at least one such individual must be identified"
                          }
                        }
                      }
                    ]
                  },
                  {
                    "allOf": [
                      {
                        "type": "object",
                        "properties": {
                          "id": {
                            "type": "string",
                            "format": "uuid",
                            "description": "Unique Id (in the context of the filer) for the contact ."
                          },
                          "contactType": {
                            "type": "string",
                            "description": "Indicate whether the contact is an individual, a committee, or \"other\" (such as a business entity), or a political party. Use commercialLendingInstitution to identify a contact that is providing a loan in the normal course of business. Commercial lending institutions will appear as other on the form."
                          },
                          "addresses": {
                            "type": "array",
                            "items": {
                              "type": "object",
                              "description": "Detailed business or residential address",
                              "properties": {
                                "id": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "Unique Id for the address."
                                },
                                "street": {
                                  "type": "string",
                                  "description": "Street address."
                                },
                                "street2": {
                                  "type": "string",
                                  "description": "Street address."
                                },
                                "city": {
                                  "type": "string",
                                  "description": "City."
                                },
                                "state": {
                                  "type": "string",
                                  "description": "State."
                                },
                                "zipCode": {
                                  "type": "string",
                                  "description": "ZIP code."
                                },
                                "county": {
                                  "type": "string",
                                  "description": "County."
                                },
                                "country": {
                                  "type": "string",
                                  "description": "Country."
                                },
                                "type": {
                                  "type": "string",
                                  "description": "Type of Address."
                                }
                              },
                              "required": [
                                "street",
                                "city",
                                "state",
                                "zipCode",
                                "country",
                                "type"
                              ],
                              "example": {
                                "street": "123 Main Street",
                                "street2": "Suite 456",
                                "city": "Sacramento",
                                "state": "CA",
                                "zipCode": "95814",
                                "country": "United States",
                                "type": "Residential"
                              }
                            },
                            "description": "List of addresses associated to the contact"
                          },
                          "cumulativeContributionsYearToDate": {
                            "type": "number",
                            "format": "double",
                            "description": "Schedule A - Cumulative to Date Calendar Year - Generally will be sum of all contributions including nonmonetary contributions, loans, or loan guarantees from the same source from January 1 through December 31 unless the committee began raising funds in connection with the qualification of a measure that extends into two calendar years. In that case, the period over which contributions are cumulated begins January 1 of the year the committee began raising funds."
                          },
                          "cumulativeContributionsPerElection": {
                            "type": "array",
                            "items": {
                              "type": "object",
                              "properties": {
                                "electionType": {
                                  "type": "string"
                                },
                                "electionYear": {
                                  "type": "integer"
                                }
                              }
                            },
                            "description": "Schedule A - Per Election to Date - State candidates must complete for each itemized contribution, disclosing the type of election, the year of the election, and the amount received from the contributor for that election. This column does not apply to ballot measure committees, unless otherwise instructed by a local ordinance."
                          },
                          "monetaryContributions": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "type": "object",
                                      "properties": {
                                        "transactionId": {
                                          "type": "string",
                                          "format": "uuid",
                                          "description": "Unique id for the transaction in the context of the filer"
                                        },
                                        "transactionDate": {
                                          "type": "string",
                                          "format": "date",
                                          "description": "Date the transaction occurred"
                                        },
                                        "amount": {
                                          "type": "number",
                                          "format": "double",
                                          "description": "Amount of the transaction"
                                        }
                                      }
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "addressId": {
                                          "type": "string",
                                          "format": "uuid",
                                          "description": "The id of the contact address that is associated with this transaction"
                                        }
                                      }
                                    }
                                  ]
                                },
                                {
                                  "type": "object",
                                  "properties": {
                                    "intermediaries": {
                                      "type": "array",
                                      "items": {
                                        "type": "string",
                                        "format": "uuid",
                                        "description": "Unique Id of the contact that served as an intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary."
                                      }
                                    }
                                  }
                                }
                              ]
                            },
                            "description": "Schedule A - List of monetary contributions of $100 or more made by the contact, except for loans (reported on Schedule B)."
                          },
                          "nonmonetaryContributions": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "allOf": [
                                        {
                                          "type": "object",
                                          "properties": {
                                            "transactionId": {
                                              "type": "string",
                                              "format": "uuid",
                                              "description": "Unique id for the transaction in the context of the filer"
                                            },
                                            "transactionDate": {
                                              "type": "string",
                                              "format": "date",
                                              "description": "Date the transaction occurred"
                                            },
                                            "amount": {
                                              "type": "number",
                                              "format": "double",
                                              "description": "Amount of the transaction"
                                            }
                                          }
                                        },
                                        {
                                          "type": "object",
                                          "properties": {
                                            "addressId": {
                                              "type": "string",
                                              "format": "uuid",
                                              "description": "The id of the contact address that is associated with this transaction"
                                            }
                                          }
                                        }
                                      ]
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "description": {
                                          "type": "string",
                                          "description": "A description the transaction"
                                        }
                                      }
                                    }
                                  ]
                                },
                                {
                                  "type": "object",
                                  "properties": {
                                    "intermediaries": {
                                      "type": "array",
                                      "items": {
                                        "type": "string",
                                        "format": "uuid",
                                        "description": "Unique Id of the contact that served as an intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary."
                                      }
                                    }
                                  }
                                }
                              ]
                            },
                            "description": "Schedule C - List of nonmonetary contributions of $100 or more made by the contact. Nonmonetary contributions (also referred to as in-kind contributions) are goods or services provided to the committee for which it does not pay fair market value. The fair market value is the amount the committee would pay for the goods or services on the open market (whatever it would cost any member of the general public to obtain the same goods or services)."
                          },
                          "loansIssued": {
                            "type": "array",
                            "items": {
                              "type": "object",
                              "properties": {
                                "transactionId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "Unique id for the transaction in the context of the filer"
                                },
                                "addressId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "The id of the contact address that is associated with this transaction"
                                },
                                "dateIncurred": {
                                  "type": "string",
                                  "format": "date",
                                  "description": "The date the loan was incurred"
                                },
                                "dateDue": {
                                  "type": "string",
                                  "format": "date",
                                  "description": "The date the loan is due"
                                },
                                "originalAmount": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The initial amount of the loan"
                                },
                                "interestRate": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The annual interest rate of the loan"
                                },
                                "beginningPeriodBalance": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The outstanding balance of the loan at the beginning of the period. If the loan was received this period, this value should be blank"
                                },
                                "amountInPeriod": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The amount received from the lender during this reporting period. If the loan was received in a previous reporting period, leave blank."
                                },
                                "amountPaid": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The amount of principal repaid by the committee during this reporting period. Interest paid is reported separately from payments made on the loan principal."
                                },
                                "interestPaid": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The amount of interest paid by the committee during this reporting period. Interest paid is reported separately from payments made on the loan principal. Interest payments are also transferred to the Schedule E Summary"
                                },
                                "amountForgiven": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "Report any reduction of the loan during this reporting period that was not paid by the committee. When the lender forgives a loan or a third party makes a payment on a loan, also report the lender or third party on Schedule A"
                                },
                                "endingPeriodBalance": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The outstanding balance of the loan at the close of this reporting period"
                                },
                                "intermediaries": {
                                  "type": "array",
                                  "items": {
                                    "type": "string",
                                    "format": "uuid",
                                    "description": "Id of the contact that served as the intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary."
                                  }
                                }
                              }
                            },
                            "description": "Schedule B - Part 1 - list of loans of $100 or more the contact has issued or are outstanding."
                          },
                          "loansGuaranteed": {
                            "type": "array",
                            "items": {
                              "type": "object",
                              "properties": {
                                "transactionId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "Unique id for the transaction in the context of the filer"
                                },
                                "transactionDate": {
                                  "type": "string",
                                  "format": "date",
                                  "description": "The date the loan was incurred"
                                },
                                "addressId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "The id of the contact address that is associated with this transaction"
                                },
                                "loanId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "Unique transactionId for the loan that was guaranteed"
                                },
                                "amountGuaranteedThisPeriod": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The amount guaranteed this period, if applicable. For lines of credit, enter the full amount established or secured by the guarantor during the period."
                                },
                                "balanceOutstanding": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The outstanding balance for which the guarantor is liable at the close of this reporting period"
                                },
                                "intermediaries": {
                                  "type": "array",
                                  "items": {
                                    "type": "string",
                                    "format": "uuid",
                                    "description": "Id of the contact that served as the intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary."
                                  }
                                }
                              }
                            },
                            "description": "Schedule B - Part 2 - list of loans of $100 or more contact has guaranteed, co-signed, endorsed, or provided security for."
                          },
                          "payments": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "allOf": [
                                        {
                                          "allOf": [
                                            {
                                              "type": "object",
                                              "properties": {
                                                "transactionId": {
                                                  "type": "string",
                                                  "format": "uuid",
                                                  "description": "Unique id for the transaction in the context of the filer"
                                                },
                                                "transactionDate": {
                                                  "type": "string",
                                                  "format": "date",
                                                  "description": "Date the transaction occurred"
                                                },
                                                "amount": {
                                                  "type": "number",
                                                  "format": "double",
                                                  "description": "Amount of the transaction"
                                                }
                                              }
                                            },
                                            {
                                              "type": "object",
                                              "properties": {
                                                "addressId": {
                                                  "type": "string",
                                                  "format": "uuid",
                                                  "description": "The id of the contact address that is associated with this transaction"
                                                }
                                              }
                                            }
                                          ]
                                        },
                                        {
                                          "type": "object",
                                          "properties": {
                                            "description": {
                                              "type": "string",
                                              "description": "A description the transaction"
                                            }
                                          }
                                        }
                                      ]
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "expenseType": {
                                          "type": "string"
                                        }
                                      }
                                    }
                                  ]
                                },
                                {
                                  "type": "object",
                                  "properties": {
                                    "subvendorPayment": {
                                      "type": "boolean",
                                      "description": "True if this payment was made by an agent or independent contractor on behalf of the committee. The transaction id must be included in the subvendor payments property of the paying party"
                                    },
                                    "paymentFor": {
                                      "type": "string",
                                      "format": "uuid",
                                      "description": "Provide the transaction id of the accrued expense if the payment is associated to a previously accrued expense"
                                    }
                                  }
                                }
                              ]
                            },
                            "description": "Schedule E - list of payments to the contact of $100 or more or payments totaling $100 or more made during the period for a single product or service"
                          },
                          "accruedExpenses": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "allOf": [
                                        {
                                          "allOf": [
                                            {
                                              "type": "object",
                                              "properties": {
                                                "transactionId": {
                                                  "type": "string",
                                                  "format": "uuid",
                                                  "description": "Unique id for the transaction in the context of the filer"
                                                },
                                                "transactionDate": {
                                                  "type": "string",
                                                  "format": "date",
                                                  "description": "Date the transaction occurred"
                                                },
                                                "amount": {
                                                  "type": "number",
                                                  "format": "double",
                                                  "description": "Amount of the transaction"
                                                }
                                              }
                                            },
                                            {
                                              "type": "object",
                                              "properties": {
                                                "addressId": {
                                                  "type": "string",
                                                  "format": "uuid",
                                                  "description": "The id of the contact address that is associated with this transaction"
                                                }
                                              }
                                            }
                                          ]
                                        },
                                        {
                                          "type": "object",
                                          "properties": {
                                            "description": {
                                              "type": "string",
                                              "description": "A description the transaction"
                                            }
                                          }
                                        }
                                      ]
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "expenseType": {
                                          "type": "string"
                                        }
                                      }
                                    }
                                  ]
                                },
                                {
                                  "type": "object",
                                  "properties": {
                                    "beginningPeriodBalance": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Amount still owed on an expense incurred during a prior period at the beginning of the current reporting period"
                                    },
                                    "amountIncurred": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Amount incurred during the current period that is outstanding at the end of the period"
                                    },
                                    "estimatedAmount": {
                                      "type": "boolean",
                                      "description": "Flag to indicate that the amount is an estimate. If the exact amount of a debt or obligation, provide an estimate. Once the exact amount is known, amend the estimated amount or note the correct amount on the next campaign statement"
                                    },
                                    "amountAdjusted": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Difference between the estimated amount reported on previous campaign statement and actual amount incurred"
                                    },
                                    "amountPaid": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Amount paid this period. Also report on Schedule E."
                                    },
                                    "endingPeriodBalance": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Amount still owed on an expense at the end of the current reporting period"
                                    }
                                  }
                                }
                              ]
                            },
                            "description": "Schedule F"
                          },
                          "subVendorPayments": {
                            "type": "array",
                            "description": "List of transaction ids that appear in schedule G that were paid by this contact",
                            "items": {
                              "type": "string",
                              "format": "uuid"
                            }
                          },
                          "miscellaneousIncreasesToCash": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "allOf": [
                                        {
                                          "type": "object",
                                          "properties": {
                                            "transactionId": {
                                              "type": "string",
                                              "format": "uuid",
                                              "description": "Unique id for the transaction in the context of the filer"
                                            },
                                            "transactionDate": {
                                              "type": "string",
                                              "format": "date",
                                              "description": "Date the transaction occurred"
                                            },
                                            "amount": {
                                              "type": "number",
                                              "format": "double",
                                              "description": "Amount of the transaction"
                                            }
                                          }
                                        },
                                        {
                                          "type": "object",
                                          "properties": {
                                            "addressId": {
                                              "type": "string",
                                              "format": "uuid",
                                              "description": "The id of the contact address that is associated with this transaction"
                                            }
                                          }
                                        }
                                      ]
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "description": {
                                          "type": "string",
                                          "description": "A description the transaction"
                                        }
                                      }
                                    }
                                  ]
                                }
                              ]
                            },
                            "description": "Schedule I"
                          }
                        }
                      },
                      {
                        "type": "object",
                        "properties": {
                          "firstName": {
                            "type": "string",
                            "description": "The persons first name."
                          },
                          "lastName": {
                            "type": "string",
                            "description": "The persons last name."
                          },
                          "occupationEmployerInformationRequested": {
                            "type": "boolean",
                            "description": "If occupation and employer information has not yet been obtained for an individual contributor, set true and amend Schedule A when the information has been received."
                          },
                          "occupation": {
                            "type": "string",
                            "description": "If the contact is an individual contributor, provide the individual''s occupation."
                          },
                          "employer": {
                            "type": "string",
                            "description": "If the contact is an individual contributor, provide the individual''s employer. If the contributor is self-employed, provide the name of the business."
                          }
                        }
                      }
                    ]
                  },
                  {
                    "allOf": [
                      {
                        "type": "object",
                        "properties": {
                          "id": {
                            "type": "string",
                            "format": "uuid",
                            "description": "Unique Id (in the context of the filer) for the contact ."
                          },
                          "contactType": {
                            "type": "string",
                            "description": "Indicate whether the contact is an individual, a committee, or \"other\" (such as a business entity), or a political party. Use commercialLendingInstitution to identify a contact that is providing a loan in the normal course of business. Commercial lending institutions will appear as other on the form."
                          },
                          "addresses": {
                            "type": "array",
                            "items": {
                              "type": "object",
                              "description": "Detailed business or residential address",
                              "properties": {
                                "id": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "Unique Id for the address."
                                },
                                "street": {
                                  "type": "string",
                                  "description": "Street address."
                                },
                                "street2": {
                                  "type": "string",
                                  "description": "Street address."
                                },
                                "city": {
                                  "type": "string",
                                  "description": "City."
                                },
                                "state": {
                                  "type": "string",
                                  "description": "State."
                                },
                                "zipCode": {
                                  "type": "string",
                                  "description": "ZIP code."
                                },
                                "county": {
                                  "type": "string",
                                  "description": "County."
                                },
                                "country": {
                                  "type": "string",
                                  "description": "Country."
                                },
                                "type": {
                                  "type": "string",
                                  "description": "Type of Address."
                                }
                              },
                              "required": [
                                "street",
                                "city",
                                "state",
                                "zipCode",
                                "country",
                                "type"
                              ],
                              "example": {
                                "street": "123 Main Street",
                                "street2": "Suite 456",
                                "city": "Sacramento",
                                "state": "CA",
                                "zipCode": "95814",
                                "country": "United States",
                                "type": "Residential"
                              }
                            },
                            "description": "List of addresses associated to the contact"
                          },
                          "cumulativeContributionsYearToDate": {
                            "type": "number",
                            "format": "double",
                            "description": "Schedule A - Cumulative to Date Calendar Year - Generally will be sum of all contributions including nonmonetary contributions, loans, or loan guarantees from the same source from January 1 through December 31 unless the committee began raising funds in connection with the qualification of a measure that extends into two calendar years. In that case, the period over which contributions are cumulated begins January 1 of the year the committee began raising funds."
                          },
                          "cumulativeContributionsPerElection": {
                            "type": "array",
                            "items": {
                              "type": "object",
                              "properties": {
                                "electionType": {
                                  "type": "string"
                                },
                                "electionYear": {
                                  "type": "integer"
                                }
                              }
                            },
                            "description": "Schedule A - Per Election to Date - State candidates must complete for each itemized contribution, disclosing the type of election, the year of the election, and the amount received from the contributor for that election. This column does not apply to ballot measure committees, unless otherwise instructed by a local ordinance."
                          },
                          "monetaryContributions": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "type": "object",
                                      "properties": {
                                        "transactionId": {
                                          "type": "string",
                                          "format": "uuid",
                                          "description": "Unique id for the transaction in the context of the filer"
                                        },
                                        "transactionDate": {
                                          "type": "string",
                                          "format": "date",
                                          "description": "Date the transaction occurred"
                                        },
                                        "amount": {
                                          "type": "number",
                                          "format": "double",
                                          "description": "Amount of the transaction"
                                        }
                                      }
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "addressId": {
                                          "type": "string",
                                          "format": "uuid",
                                          "description": "The id of the contact address that is associated with this transaction"
                                        }
                                      }
                                    }
                                  ]
                                },
                                {
                                  "type": "object",
                                  "properties": {
                                    "intermediaries": {
                                      "type": "array",
                                      "items": {
                                        "type": "string",
                                        "format": "uuid",
                                        "description": "Unique Id of the contact that served as an intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary."
                                      }
                                    }
                                  }
                                }
                              ]
                            },
                            "description": "Schedule A - List of monetary contributions of $100 or more made by the contact, except for loans (reported on Schedule B)."
                          },
                          "nonmonetaryContributions": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "allOf": [
                                        {
                                          "type": "object",
                                          "properties": {
                                            "transactionId": {
                                              "type": "string",
                                              "format": "uuid",
                                              "description": "Unique id for the transaction in the context of the filer"
                                            },
                                            "transactionDate": {
                                              "type": "string",
                                              "format": "date",
                                              "description": "Date the transaction occurred"
                                            },
                                            "amount": {
                                              "type": "number",
                                              "format": "double",
                                              "description": "Amount of the transaction"
                                            }
                                          }
                                        },
                                        {
                                          "type": "object",
                                          "properties": {
                                            "addressId": {
                                              "type": "string",
                                              "format": "uuid",
                                              "description": "The id of the contact address that is associated with this transaction"
                                            }
                                          }
                                        }
                                      ]
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "description": {
                                          "type": "string",
                                          "description": "A description the transaction"
                                        }
                                      }
                                    }
                                  ]
                                },
                                {
                                  "type": "object",
                                  "properties": {
                                    "intermediaries": {
                                      "type": "array",
                                      "items": {
                                        "type": "string",
                                        "format": "uuid",
                                        "description": "Unique Id of the contact that served as an intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary."
                                      }
                                    }
                                  }
                                }
                              ]
                            },
                            "description": "Schedule C - List of nonmonetary contributions of $100 or more made by the contact. Nonmonetary contributions (also referred to as in-kind contributions) are goods or services provided to the committee for which it does not pay fair market value. The fair market value is the amount the committee would pay for the goods or services on the open market (whatever it would cost any member of the general public to obtain the same goods or services)."
                          },
                          "loansIssued": {
                            "type": "array",
                            "items": {
                              "type": "object",
                              "properties": {
                                "transactionId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "Unique id for the transaction in the context of the filer"
                                },
                                "addressId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "The id of the contact address that is associated with this transaction"
                                },
                                "dateIncurred": {
                                  "type": "string",
                                  "format": "date",
                                  "description": "The date the loan was incurred"
                                },
                                "dateDue": {
                                  "type": "string",
                                  "format": "date",
                                  "description": "The date the loan is due"
                                },
                                "originalAmount": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The initial amount of the loan"
                                },
                                "interestRate": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The annual interest rate of the loan"
                                },
                                "beginningPeriodBalance": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The outstanding balance of the loan at the beginning of the period. If the loan was received this period, this value should be blank"
                                },
                                "amountInPeriod": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The amount received from the lender during this reporting period. If the loan was received in a previous reporting period, leave blank."
                                },
                                "amountPaid": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The amount of principal repaid by the committee during this reporting period. Interest paid is reported separately from payments made on the loan principal."
                                },
                                "interestPaid": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The amount of interest paid by the committee during this reporting period. Interest paid is reported separately from payments made on the loan principal. Interest payments are also transferred to the Schedule E Summary"
                                },
                                "amountForgiven": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "Report any reduction of the loan during this reporting period that was not paid by the committee. When the lender forgives a loan or a third party makes a payment on a loan, also report the lender or third party on Schedule A"
                                },
                                "endingPeriodBalance": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The outstanding balance of the loan at the close of this reporting period"
                                },
                                "intermediaries": {
                                  "type": "array",
                                  "items": {
                                    "type": "string",
                                    "format": "uuid",
                                    "description": "Id of the contact that served as the intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary."
                                  }
                                }
                              }
                            },
                            "description": "Schedule B - Part 1 - list of loans of $100 or more the contact has issued or are outstanding."
                          },
                          "loansGuaranteed": {
                            "type": "array",
                            "items": {
                              "type": "object",
                              "properties": {
                                "transactionId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "Unique id for the transaction in the context of the filer"
                                },
                                "transactionDate": {
                                  "type": "string",
                                  "format": "date",
                                  "description": "The date the loan was incurred"
                                },
                                "addressId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "The id of the contact address that is associated with this transaction"
                                },
                                "loanId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "Unique transactionId for the loan that was guaranteed"
                                },
                                "amountGuaranteedThisPeriod": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The amount guaranteed this period, if applicable. For lines of credit, enter the full amount established or secured by the guarantor during the period."
                                },
                                "balanceOutstanding": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "The outstanding balance for which the guarantor is liable at the close of this reporting period"
                                },
                                "intermediaries": {
                                  "type": "array",
                                  "items": {
                                    "type": "string",
                                    "format": "uuid",
                                    "description": "Id of the contact that served as the intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary."
                                  }
                                }
                              }
                            },
                            "description": "Schedule B - Part 2 - list of loans of $100 or more contact has guaranteed, co-signed, endorsed, or provided security for."
                          },
                          "payments": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "allOf": [
                                        {
                                          "allOf": [
                                            {
                                              "type": "object",
                                              "properties": {
                                                "transactionId": {
                                                  "type": "string",
                                                  "format": "uuid",
                                                  "description": "Unique id for the transaction in the context of the filer"
                                                },
                                                "transactionDate": {
                                                  "type": "string",
                                                  "format": "date",
                                                  "description": "Date the transaction occurred"
                                                },
                                                "amount": {
                                                  "type": "number",
                                                  "format": "double",
                                                  "description": "Amount of the transaction"
                                                }
                                              }
                                            },
                                            {
                                              "type": "object",
                                              "properties": {
                                                "addressId": {
                                                  "type": "string",
                                                  "format": "uuid",
                                                  "description": "The id of the contact address that is associated with this transaction"
                                                }
                                              }
                                            }
                                          ]
                                        },
                                        {
                                          "type": "object",
                                          "properties": {
                                            "description": {
                                              "type": "string",
                                              "description": "A description the transaction"
                                            }
                                          }
                                        }
                                      ]
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "expenseType": {
                                          "type": "string"
                                        }
                                      }
                                    }
                                  ]
                                },
                                {
                                  "type": "object",
                                  "properties": {
                                    "subvendorPayment": {
                                      "type": "boolean",
                                      "description": "True if this payment was made by an agent or independent contractor on behalf of the committee. The transaction id must be included in the subvendor payments property of the paying party"
                                    },
                                    "paymentFor": {
                                      "type": "string",
                                      "format": "uuid",
                                      "description": "Provide the transaction id of the accrued expense if the payment is associated to a previously accrued expense"
                                    }
                                  }
                                }
                              ]
                            },
                            "description": "Schedule E - list of payments to the contact of $100 or more or payments totaling $100 or more made during the period for a single product or service"
                          },
                          "accruedExpenses": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "allOf": [
                                        {
                                          "allOf": [
                                            {
                                              "type": "object",
                                              "properties": {
                                                "transactionId": {
                                                  "type": "string",
                                                  "format": "uuid",
                                                  "description": "Unique id for the transaction in the context of the filer"
                                                },
                                                "transactionDate": {
                                                  "type": "string",
                                                  "format": "date",
                                                  "description": "Date the transaction occurred"
                                                },
                                                "amount": {
                                                  "type": "number",
                                                  "format": "double",
                                                  "description": "Amount of the transaction"
                                                }
                                              }
                                            },
                                            {
                                              "type": "object",
                                              "properties": {
                                                "addressId": {
                                                  "type": "string",
                                                  "format": "uuid",
                                                  "description": "The id of the contact address that is associated with this transaction"
                                                }
                                              }
                                            }
                                          ]
                                        },
                                        {
                                          "type": "object",
                                          "properties": {
                                            "description": {
                                              "type": "string",
                                              "description": "A description the transaction"
                                            }
                                          }
                                        }
                                      ]
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "expenseType": {
                                          "type": "string"
                                        }
                                      }
                                    }
                                  ]
                                },
                                {
                                  "type": "object",
                                  "properties": {
                                    "beginningPeriodBalance": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Amount still owed on an expense incurred during a prior period at the beginning of the current reporting period"
                                    },
                                    "amountIncurred": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Amount incurred during the current period that is outstanding at the end of the period"
                                    },
                                    "estimatedAmount": {
                                      "type": "boolean",
                                      "description": "Flag to indicate that the amount is an estimate. If the exact amount of a debt or obligation, provide an estimate. Once the exact amount is known, amend the estimated amount or note the correct amount on the next campaign statement"
                                    },
                                    "amountAdjusted": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Difference between the estimated amount reported on previous campaign statement and actual amount incurred"
                                    },
                                    "amountPaid": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Amount paid this period. Also report on Schedule E."
                                    },
                                    "endingPeriodBalance": {
                                      "type": "number",
                                      "format": "double",
                                      "description": "Amount still owed on an expense at the end of the current reporting period"
                                    }
                                  }
                                }
                              ]
                            },
                            "description": "Schedule F"
                          },
                          "subVendorPayments": {
                            "type": "array",
                            "description": "List of transaction ids that appear in schedule G that were paid by this contact",
                            "items": {
                              "type": "string",
                              "format": "uuid"
                            }
                          },
                          "miscellaneousIncreasesToCash": {
                            "type": "array",
                            "items": {
                              "allOf": [
                                {
                                  "allOf": [
                                    {
                                      "allOf": [
                                        {
                                          "type": "object",
                                          "properties": {
                                            "transactionId": {
                                              "type": "string",
                                              "format": "uuid",
                                              "description": "Unique id for the transaction in the context of the filer"
                                            },
                                            "transactionDate": {
                                              "type": "string",
                                              "format": "date",
                                              "description": "Date the transaction occurred"
                                            },
                                            "amount": {
                                              "type": "number",
                                              "format": "double",
                                              "description": "Amount of the transaction"
                                            }
                                          }
                                        },
                                        {
                                          "type": "object",
                                          "properties": {
                                            "addressId": {
                                              "type": "string",
                                              "format": "uuid",
                                              "description": "The id of the contact address that is associated with this transaction"
                                            }
                                          }
                                        }
                                      ]
                                    },
                                    {
                                      "type": "object",
                                      "properties": {
                                        "description": {
                                          "type": "string",
                                          "description": "A description the transaction"
                                        }
                                      }
                                    }
                                  ]
                                }
                              ]
                            },
                            "description": "Schedule I"
                          }
                        }
                      },
                      {
                        "type": "object",
                        "properties": {
                          "firstName": {
                            "type": "string",
                            "description": "For an LLC that has not qualified as a committee, the first name of the individual primarily responsible for approving the contribution. If more than one individual shares in the primary responsibility of approving a contribution, at least one such individual must be identified"
                          },
                          "lastName": {
                            "type": "string",
                            "description": "For an LLC that has not qualified as a committee, the last name of the individual primarily responsible for approving the contribution. If more than one individual shares in the primary responsibility of approving a contribution, at least one such individual must be identified"
                          },
                          "organizationName": {
                            "type": "string",
                            "description": "The name of the entity if the contact type is other or commercial lending institution."
                          }
                        }
                      }
                    ]
                  }
                ]
              },
              "description": "A list of all entities that have provided itemized contributions, received itemized expenditures, received or provided itemized loans, served as an intermediary for an itemized contribution, or were a source of itemized miscellaneous increases to cash."
            },
            "supportedCommittees": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "committee": {
                    "allOf": [
                      {
                        "allOf": [
                          {
                            "type": "object",
                            "properties": {
                              "id": {
                                "type": "integer",
                                "description": "Unique id of the committee as assigned by Secretary of State"
                              },
                              "name": {
                                "type": "string",
                                "description": "Name of committee as provided on the committee''s most recently filed Statement of Organization, Form 410."
                              },
                              "committeeAddress": {
                                "type": "object",
                                "description": "Detailed business or residential address",
                                "properties": {
                                  "id": {
                                    "type": "string",
                                    "format": "uuid",
                                    "description": "Unique Id for the address."
                                  },
                                  "street": {
                                    "type": "string",
                                    "description": "Street address."
                                  },
                                  "street2": {
                                    "type": "string",
                                    "description": "Street address."
                                  },
                                  "city": {
                                    "type": "string",
                                    "description": "City."
                                  },
                                  "state": {
                                    "type": "string",
                                    "description": "State."
                                  },
                                  "zipCode": {
                                    "type": "string",
                                    "description": "ZIP code."
                                  },
                                  "county": {
                                    "type": "string",
                                    "description": "County."
                                  },
                                  "country": {
                                    "type": "string",
                                    "description": "Country."
                                  },
                                  "type": {
                                    "type": "string",
                                    "description": "Type of Address."
                                  }
                                },
                                "required": [
                                  "street",
                                  "city",
                                  "state",
                                  "zipCode",
                                  "country",
                                  "type"
                                ],
                                "example": {
                                  "street": "123 Main Street",
                                  "street2": "Suite 456",
                                  "city": "Sacramento",
                                  "state": "CA",
                                  "zipCode": "95814",
                                  "country": "United States",
                                  "type": "Residential"
                                }
                              }
                            }
                          },
                          {
                            "type": "object",
                            "properties": {
                              "committeeType": {
                                "type": "string",
                                "description": "Type of committee as provided on the committee''s most recently filed Statement of Organization, Form 410."
                              },
                              "controlledCommittee": {
                                "type": "boolean",
                                "description": "Indicates if the committee is a controlled committee"
                              },
                              "firstName": {
                                "type": "string",
                                "description": "Name of Candidate as provided on the committee''s most recently filed Statement of Organization, Form 410."
                              },
                              "lastName": {
                                "type": "string",
                                "description": "Name of Candidate as provided on the committee''s most recently filed Statement of Organization, Form 410."
                              },
                              "candidateAddress": {
                                "type": "object",
                                "description": "Detailed business or residential address",
                                "properties": {
                                  "id": {
                                    "type": "string",
                                    "format": "uuid",
                                    "description": "Unique Id for the address."
                                  },
                                  "street": {
                                    "type": "string",
                                    "description": "Street address."
                                  },
                                  "street2": {
                                    "type": "string",
                                    "description": "Street address."
                                  },
                                  "city": {
                                    "type": "string",
                                    "description": "City."
                                  },
                                  "state": {
                                    "type": "string",
                                    "description": "State."
                                  },
                                  "zipCode": {
                                    "type": "string",
                                    "description": "ZIP code."
                                  },
                                  "county": {
                                    "type": "string",
                                    "description": "County."
                                  },
                                  "country": {
                                    "type": "string",
                                    "description": "Country."
                                  },
                                  "type": {
                                    "type": "string",
                                    "description": "Type of Address."
                                  }
                                },
                                "required": [
                                  "street",
                                  "city",
                                  "state",
                                  "zipCode",
                                  "country",
                                  "type"
                                ],
                                "example": {
                                  "street": "123 Main Street",
                                  "street2": "Suite 456",
                                  "city": "Sacramento",
                                  "state": "CA",
                                  "zipCode": "95814",
                                  "country": "United States",
                                  "type": "Residential"
                                }
                              },
                              "electionOfficeSought": {
                                "type": "object",
                                "properties": {
                                  "office": {
                                    "type": "string",
                                    "description": "Office held or sought by the candidate."
                                  },
                                  "officeDescription": {
                                    "type": "string",
                                    "description": "Title of the office by the candidate."
                                  },
                                  "jurisdiction": {
                                    "type": "string",
                                    "description": "Jurisdiction of the office."
                                  },
                                  "jurisdictionDescription": {
                                    "type": "string",
                                    "description": "Jurisdiction of the candidate or measure."
                                  },
                                  "electionDistrictNumber": {
                                    "type": "string",
                                    "description": "District number (if applicable)."
                                  }
                                }
                              },
                              "electionDistrictNumber": {
                                "type": "string",
                                "description": "District number (if applicable)."
                              },
                              "committeeAddress": {
                                "type": "object",
                                "description": "Detailed business or residential address",
                                "properties": {
                                  "id": {
                                    "type": "string",
                                    "format": "uuid",
                                    "description": "Unique Id for the address."
                                  },
                                  "street": {
                                    "type": "string",
                                    "description": "Street address."
                                  },
                                  "street2": {
                                    "type": "string",
                                    "description": "Street address."
                                  },
                                  "city": {
                                    "type": "string",
                                    "description": "City."
                                  },
                                  "state": {
                                    "type": "string",
                                    "description": "State."
                                  },
                                  "zipCode": {
                                    "type": "string",
                                    "description": "ZIP code."
                                  },
                                  "county": {
                                    "type": "string",
                                    "description": "County."
                                  },
                                  "country": {
                                    "type": "string",
                                    "description": "Country."
                                  },
                                  "type": {
                                    "type": "string",
                                    "description": "Type of Address."
                                  }
                                },
                                "required": [
                                  "street",
                                  "city",
                                  "state",
                                  "zipCode",
                                  "country",
                                  "type"
                                ],
                                "example": {
                                  "street": "123 Main Street",
                                  "street2": "Suite 456",
                                  "city": "Sacramento",
                                  "state": "CA",
                                  "zipCode": "95814",
                                  "country": "United States",
                                  "type": "Residential"
                                }
                              },
                              "emailAddress": {
                                "type": "string",
                                "format": "email"
                              }
                            }
                          }
                        ]
                      }
                    ],
                    "description": "For each contribution or independent expenditure of $100 or more to support or oppose a candidate, ballot measure, or general purpose committee (e.g., political party, PAC), For candidates, disclose the Name of candidate, the office sought or held, and the district, if any. For Ballot Measures, disclose the Number or letter and jurisdiction of ballot measure. If a number or letter has not been assigned, include the measure''s title. For General Purpose Committees disclose the Name of the committee."
                  },
                  "cumulativeContributionsYearToDate": {
                    "type": "number",
                    "format": "double",
                    "description": "Schedule D - Report the cumulative amount contributed to or expended to support or oppose each itemized committee since January 1 of the current calendar year."
                  },
                  "cumulativeContributionsPerElection": {
                    "type": "array",
                    "items": {
                      "type": "object",
                      "properties": {
                        "electionType": {
                          "type": "string"
                        },
                        "electionYear": {
                          "type": "integer"
                        }
                      }
                    },
                    "description": "Schedule D - This section is for reporting payments to state candidates. This section is generally not applicable for ballot measure committees. Note: A ballot measure committee controlled by a state candidate or officeholder may not make a contribution to a controlled committee of a candidate for elective office. The committee may contribute leftover funds when the committee is preparing to terminate to a segregated account of a political party committee so long as the funds are not used for candidate contributions or for communications that expressly advocate for or against a candidate. (See Regulation 18521.5.)"
                  },
                  "contributions": {
                    "type": "array",
                    "items": {
                      "description": "Report the date the contribution or independent expenditure was made. A monetary contribution is made on the date it is mailed, delivered, or otherwise transmitted to the committee or an agent of the committee. A nonmonetary contribution is made on the earlier of, the date funds were expended for the goods or services, the date the committee or agent of the committee obtained possession or control of the goods or services, or the date the committee otherwise received the benefit of the expenditure. An independent expenditure is made on the earlier of the date the payment is made or the date the committee making the payment receives consideration in exchange for the expenditure(s) (e.g., when the advertisement appears). For purposes of reporting independent expenditures on Schedule D, the date an independent expenditure is made is the date the communication is mailed, broadcast, or otherwise disseminated to the public. If payments are made in one reporting period for a communication that is disseminated to the public during a subsequent reporting period, report the payments on Schedule E for the period in which the payments they were made and complete Schedule D for the period in which the communication is disseminated. A payment for a communication that is never disseminated to the public is not an independent expenditure and need not be reported on Schedule D. However, the payment must still be reported on Schedule E. Because payments must be described when they are reported on Schedules E or F, a description is not required for payments reported on Schedules E or F that are nonmonetary contributions or independent expenditures. However, if no payment was made, describe the goods or services and disclose the fair market value of the contribution. For example, if goods on hand are contributed to a candidate or another committee (e.g., office supplies), a description must be included.",
                      "allOf": [
                        {
                          "allOf": [
                            {
                              "type": "object",
                              "properties": {
                                "transactionId": {
                                  "type": "string",
                                  "format": "uuid",
                                  "description": "Unique id for the transaction in the context of the filer"
                                },
                                "transactionDate": {
                                  "type": "string",
                                  "format": "date",
                                  "description": "Date the transaction occurred"
                                },
                                "amount": {
                                  "type": "number",
                                  "format": "double",
                                  "description": "Amount of the transaction"
                                }
                              }
                            },
                            {
                              "type": "object",
                              "properties": {
                                "description": {
                                  "type": "string",
                                  "description": "A description the transaction"
                                }
                              }
                            }
                          ]
                        },
                        {
                          "type": "object",
                          "properties": {
                            "supportedCommitteeContributionType": {
                              "type": "string",
                              "description": "Indicate whether the payment was a monetary contribution, nonmonetary contribution, or independent expenditure."
                            }
                          }
                        }
                      ]
                    },
                    "description": "List of contributions or independent expenditures made this period relative to the committee."
                  }
                }
              },
              "description": "Schedule D - List of other candidates, measures and committees that were the supported or opposed by contributions or independent expenditures made by the filing committee."
            },
            "scheduleASummaries": {
              "allOf": [
                {
                  "type": "object",
                  "properties": {
                    "totalItemizedMonetaryContributions": {
                      "type": "number",
                      "format": "double",
                      "description": "Amount received this period \u2013 itemized monetary contributions(Include all Schedule A subtotals.)."
                    },
                    "totalUnitemizedMonetaryContributionsLessThan100": {
                      "type": "number",
                      "format": "double",
                      "description": "Amount received this period \u2013 unitemized monetary contributions of less than $100."
                    },
                    "totalMonetaryContributions": {
                      "type": "number",
                      "format": "double",
                      "description": "Total monetary contributions received this period"
                    }
                  }
                }
              ],
              "description": "Summary sections for schedule A"
            },
            "scheduleBSummaries": {
              "allOf": [
                {
                  "type": "object",
                  "properties": {
                    "loansReceived": {
                      "type": "number",
                      "format": "double",
                      "description": "Amount received plus unitemized loans of less than $100."
                    },
                    "loansPaidOrForgiven": {
                      "type": "number",
                      "format": "double",
                      "description": "Amount paid or forgiven plus loans under 100 paid or forgiven and include loans paid by third party and itemized on Schedule A."
                    },
                    "scheduleBNetChange": {
                      "type": "number",
                      "format": "double",
                      "description": "Net change this period."
                    }
                  }
                }
              ],
              "description": "Summary sections for schedule B"
            },
            "scheduleCSummaries": {
              "allOf": [
                {
                  "type": "object",
                  "properties": {
                    "itemizedNonMonetaryContributions": {
                      "type": "number",
                      "format": "double",
                      "description": "Amount received this period \u2013 itemized nonmonetary contributions(Include all Schedule A subtotals.)."
                    },
                    "unitemizedMonetaryContributionsLessThan100": {
                      "type": "number",
                      "format": "double",
                      "description": "Amount received this period \u2013 unitemized monetary contributions of less than $100."
                    },
                    "totalNonMonetaryContributions": {
                      "type": "number",
                      "format": "double",
                      "description": "Total monetary contributions received this period."
                    }
                  }
                }
              ],
              "description": "Summary sections for schedule C"
            },
            "scheduleDSummaries": {
              "allOf": [
                {
                  "type": "object",
                  "properties": {
                    "itemizedContributionsAndIndependentExpenditures": {
                      "type": "number",
                      "format": "double",
                      "description": "Itemized contributions and independent expenditures made this period."
                    },
                    "unitemizedContributionsAndIndependentExpenditures": {
                      "type": "number",
                      "format": "double",
                      "description": "UnItemized contributions and independent expenditures made this period under 100."
                    },
                    "TotalContributionsAndIndependentExpenditures": {
                      "type": "number",
                      "format": "double",
                      "description": "Total contributions and independent expenditures made this period."
                    }
                  }
                }
              ],
              "description": "Summary sections for schedule D"
            },
            "scheduleESummaries": {
              "allOf": [
                {
                  "type": "object",
                  "properties": {
                    "itemizedPayment": {
                      "type": "number",
                      "format": "double",
                      "description": "Itemized payments made this period. (Include all Schedule E subtotals.)"
                    },
                    "unitemizedPayment": {
                      "type": "number",
                      "format": "double",
                      "description": "Unitemized payments made this period of under $100"
                    },
                    "totalInterestPaid": {
                      "type": "number",
                      "format": "double",
                      "description": "Total interest paid this period on loans."
                    },
                    "totalPaymentsPaid": {
                      "type": "number",
                      "format": "double",
                      "description": "Total payments made this period."
                    }
                  }
                }
              ],
              "description": "Summary sections for schedule E"
            },
            "scheduleFSummaries": {
              "allOf": [
                {
                  "type": "object",
                  "properties": {
                    "totalAccruedExpensesIncurred": {
                      "type": "number",
                      "format": "double",
                      "description": "Total accrued expenses incurred this period"
                    },
                    "totalAccruedExpensesPaid": {
                      "type": "number",
                      "format": "double",
                      "description": "Total accrued expenses paid this period"
                    },
                    "scheduleFNetChange": {
                      "type": "number",
                      "format": "double",
                      "description": "Net change this period"
                    }
                  }
                }
              ],
              "description": "Summary sections for schedule F"
            },
            "scheduleHSummaries": {
              "allOf": [
                {
                  "type": "object",
                  "properties": {
                    "loansMade": {
                      "type": "number",
                      "format": "double",
                      "description": "Loans made this period plus unitemized loans of less than $100."
                    },
                    "paymentReceived": {
                      "type": "number",
                      "format": "double",
                      "description": "Payments received on loans plus unitemized payments of less than $100."
                    },
                    "scheduleHNetChange": {
                      "type": "number",
                      "format": "double",
                      "description": "Net change this period."
                    }
                  }
                }
              ],
              "description": "Summary sections for schedule H"
            },
            "scheduleISummaries": {
              "allOf": [
                {
                  "type": "object",
                  "properties": {
                    "id": {
                      "type": "string",
                      "format": "uuid"
                    },
                    "address": {
                      "type": "object",
                      "description": "Detailed business or residential address",
                      "properties": {
                        "id": {
                          "type": "string",
                          "format": "uuid",
                          "description": "Unique Id for the address."
                        },
                        "street": {
                          "type": "string",
                          "description": "Street address."
                        },
                        "street2": {
                          "type": "string",
                          "description": "Street address."
                        },
                        "city": {
                          "type": "string",
                          "description": "City."
                        },
                        "state": {
                          "type": "string",
                          "description": "State."
                        },
                        "zipCode": {
                          "type": "string",
                          "description": "ZIP code."
                        },
                        "county": {
                          "type": "string",
                          "description": "County."
                        },
                        "country": {
                          "type": "string",
                          "description": "Country."
                        },
                        "type": {
                          "type": "string",
                          "description": "Type of Address."
                        }
                      },
                      "required": [
                        "street",
                        "city",
                        "state",
                        "zipCode",
                        "country",
                        "type"
                      ],
                      "example": {
                        "street": "123 Main Street",
                        "street2": "Suite 456",
                        "city": "Sacramento",
                        "state": "CA",
                        "zipCode": "95814",
                        "country": "United States",
                        "type": "Residential"
                      }
                    },
                    "description": {
                      "type": "string"
                    },
                    "amount": {
                      "type": "number",
                      "format": "double"
                    },
                    "itemizedIncreaseToCash": {
                      "type": "number",
                      "format": "double",
                      "description": "Itemized increases to cash this period."
                    },
                    "unitemizedIncreaseToCash": {
                      "type": "number",
                      "format": "double",
                      "description": "Unitemized increases to cash of under $100 this period."
                    },
                    "totalInterestReceived": {
                      "type": "number",
                      "format": "double",
                      "description": "Total of all interest received this period on loans made to others."
                    },
                    "totalMiscellaneousIncreaseToCash": {
                      "type": "number",
                      "format": "double",
                      "description": "Total miscellaneous increases to cash this period"
                    }
                  }
                }
              ],
              "description": "Summary sections for schedule I"
            },
            "attestation": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "firstName": {
                    "type": "string",
                    "description": "First Name of verification officer."
                  },
                  "lastName": {
                    "type": "string",
                    "description": "Last Name of verification officer."
                  },
                  "executedAt": {
                    "type": "string",
                    "format": "date",
                    "description": "Date the statement was executed."
                  },
                  "executedAtCity": {
                    "type": "string",
                    "description": "Executed at City."
                  },
                  "executedAtState": {
                    "type": "string",
                    "description": "Executed at state."
                  },
                  "role": {
                    "type": "string"
                  },
                  "signature": {
                    "type": "string",
                    "description": "Name of the treasurer or filer."
                  }
                },
                "required": [
                  "executedAt",
                  "signature"
                ]
              },
              "description": "Cover Page - The committee treasurer or the assistant treasurer listed on the committee''s Statement of Organization, Form 410, must complete the verification. The statement is signed under penalty of perjury that the information is true and correct. If the Form 460 is not signed, it is not considered filed. If three or fewer officeholders/candidates or state ballot measure proponents control the committee, the officeholders/candidates must also complete the verification. If there are more than three officeholders/ candidates controlling the committee, one may complete the verification on behalf of the others. If the committee is sponsored and the sponsor is reporting contributions received through the sponsor or made by the sponsor, the responsible officer must also complete the verification."
            }
          }
        },
        {
          "type": "object",
          "properties": {
            "filer": {
              "type": "object",
              "description": "Either ''filerId'' or ''natureAndInterestsOfFiler'' is required, but not both.",
              "oneOf": [
                {
                  "type": "object",
                  "properties": {
                    "filerId": {
                      "type": "string"
                    }
                  },
                  "required": [
                    "filerId"
                  ]
                },
                {
                  "type": "object",
                  "properties": {
                    "individualFiler": {
                      "type": "object",
                      "description": "Details if the filer is an individual.",
                      "properties": {
                        "employerName": {
                          "type": "string",
                          "description": "Name of the employer."
                        },
                        "employerAddress": {
                          "type": "object",
                          "description": "Detailed business or residential address",
                          "properties": {
                            "id": {
                              "type": "string",
                              "format": "uuid",
                              "description": "Unique Id for the address."
                            },
                            "street": {
                              "type": "string",
                              "description": "Street address."
                            },
                            "street2": {
                              "type": "string",
                              "description": "Street address."
                            },
                            "city": {
                              "type": "string",
                              "description": "City."
                            },
                            "state": {
                              "type": "string",
                              "description": "State."
                            },
                            "zipCode": {
                              "type": "string",
                              "description": "ZIP code."
                            },
                            "county": {
                              "type": "string",
                              "description": "County."
                            },
                            "country": {
                              "type": "string",
                              "description": "Country."
                            },
                            "type": {
                              "type": "string",
                              "description": "Type of Address."
                            }
                          },
                          "required": [
                            "street",
                            "city",
                            "state",
                            "zipCode",
                            "country",
                            "type"
                          ],
                          "example": {
                            "street": "123 Main Street",
                            "street2": "Suite 456",
                            "city": "Sacramento",
                            "state": "CA",
                            "zipCode": "95814",
                            "country": "United States",
                            "type": "Residential"
                          }
                        },
                        "businessInterests": {
                          "type": "string",
                          "description": "Nature of the filer''s business interests."
                        }
                      }
                    },
                    "businessEntityFiler": {
                      "type": "object",
                      "description": "Details if the filer is a business entity.",
                      "properties": {
                        "businessActivity": {
                          "type": "string",
                          "description": "Description of the business activity in which the entity is engaged."
                        }
                      }
                    },
                    "associationFiler": {
                      "type": "object",
                      "description": "Details if the filer is an association.",
                      "properties": {
                        "interests": {
                          "type": "string",
                          "description": "A specific description of the association''s interests."
                        }
                      }
                    },
                    "otherEntityFiler": {
                      "type": "object",
                      "description": "Details if the filer is another type of entity.",
                      "properties": {
                        "commonEconomicInterest": {
                          "type": "string",
                          "description": "Description of the common economic interests of the group or entity."
                        }
                      }
                    }
                  }
                }
              ]
            },
            "summary": {
              "type": "object",
              "properties": {
                "totalExpenditures100orMore": {
                  "type": "number",
                  "description": "Total expenditures and contributions (including loans) of $100 or more made during this period."
                },
                "totalUnitemizedExpendituresUnder100": {
                  "type": "number",
                  "description": "Total unitemized expenditures and contributions under $100 made during this period."
                },
                "totalExpendituresThisPeriod": {
                  "type": "number",
                  "description": "Total expenditures and contributions made during this period (sum of itemized and unitemized)."
                },
                "totalExpendituresPriorStatements": {
                  "type": "number",
                  "description": "Total expenditures and contributions made in prior statements."
                },
                "totalExpendituresSinceJanuary1": {
                  "type": "number",
                  "description": "Total expenditures and contributions made since January 1 of the current calendar year."
                }
              }
            },
            "attestation": {
              "type": "object",
              "properties": {
                "firstName": {
                  "type": "string",
                  "description": "First Name of verification officer."
                },
                "lastName": {
                  "type": "string",
                  "description": "Last Name of verification officer."
                },
                "executedAt": {
                  "type": "string",
                  "format": "date",
                  "description": "Date the statement was executed."
                },
                "executedAtCity": {
                  "type": "string",
                  "description": "Executed at City."
                },
                "executedAtState": {
                  "type": "string",
                  "description": "Executed at state."
                },
                "role": {
                  "type": "string"
                },
                "signature": {
                  "type": "string",
                  "description": "Name of the treasurer or filer."
                }
              },
              "required": [
                "executedAt",
                "signature"
              ]
            },
            "contributions": {
              "type": "array",
              "description": "Itemized contributions made during the reporting period.",
              "items": {
                "type": "object",
                "properties": {
                  "transactionDate": {
                    "type": "string",
                    "format": "date",
                    "description": "Date of the contribution or payment."
                  },
                  "name": {
                    "type": "string",
                    "maxLength": 32,
                    "description": "Type of payment made."
                  },
                  "payee": {
                    "type": "object",
                    "properties": {
                      "idNumber": {
                        "type": "string",
                        "description": "Unique identifier number for the payee or recipient."
                      },
                      "firstName": {
                        "type": "string",
                        "description": "First name of the payee or recipient."
                      },
                      "lastName": {
                        "type": "string",
                        "description": "Last name of the payee or recipient."
                      },
                      "address": {
                        "type": "object",
                        "description": "Detailed business or residential address",
                        "properties": {
                          "id": {
                            "type": "string",
                            "format": "uuid",
                            "description": "Unique Id for the address."
                          },
                          "street": {
                            "type": "string",
                            "description": "Street address."
                          },
                          "street2": {
                            "type": "string",
                            "description": "Street address."
                          },
                          "city": {
                            "type": "string",
                            "description": "City."
                          },
                          "state": {
                            "type": "string",
                            "description": "State."
                          },
                          "zipCode": {
                            "type": "string",
                            "description": "ZIP code."
                          },
                          "county": {
                            "type": "string",
                            "description": "County."
                          },
                          "country": {
                            "type": "string",
                            "description": "Country."
                          },
                          "type": {
                            "type": "string",
                            "description": "Type of Address."
                          }
                        },
                        "required": [
                          "street",
                          "city",
                          "state",
                          "zipCode",
                          "country",
                          "type"
                        ],
                        "example": {
                          "street": "123 Main Street",
                          "street2": "Suite 456",
                          "city": "Sacramento",
                          "state": "CA",
                          "zipCode": "95814",
                          "country": "United States",
                          "type": "Residential"
                        }
                      }
                    }
                  },
                  "candidate_office_measure_jurisdiction_committee": {
                    "type": "object",
                    "properties": {
                      "Name": {
                        "type": "string",
                        "description": "Name of the candidate, office, measure, or committee."
                      },
                      "electionOfficeSought": {
                        "type": "object",
                        "properties": {
                          "office": {
                            "type": "string",
                            "description": "Office held or sought by the candidate."
                          },
                          "officeDescription": {
                            "type": "string",
                            "description": "Title of the office by the candidate."
                          },
                          "jurisdiction": {
                            "type": "string",
                            "description": "Jurisdiction of the office."
                          },
                          "jurisdictionDescription": {
                            "type": "string",
                            "description": "Jurisdiction of the candidate or measure."
                          },
                          "electionDistrictNumber": {
                            "type": "string",
                            "description": "District number (if applicable)."
                          }
                        }
                      },
                      "committee": {
                        "type": "string",
                        "description": "Committee name."
                      },
                      "code": {
                        "type": "string",
                        "description": "Ballot measure code."
                      },
                      "position": {
                        "type": "string",
                        "enum": [
                          "Support",
                          "Oppose"
                        ],
                        "description": "Whether the payment supports or opposes the candidate or measure."
                      }
                    }
                  },
                  "cumulativeAmount": {
                    "type": "number",
                    "description": "Cumulative amount contributed to this candidate, measure, or committee during the calendar year."
                  },
                  "amountThisPeriod": {
                    "type": "number",
                    "description": "Amount contributed during this period."
                  },
                  "paymentDescription": {
                    "type": "string",
                    "description": "Detailed description of the payment."
                  },
                  "subTotalCumulative": {
                    "type": "number",
                    "description": "Cumulative subtotal contributed during this period."
                  },
                  "subTotalThisPeriod": {
                    "type": "number",
                    "description": "Subtotal amount contributed during this period."
                  }
                }
              }
            },
            "statementPeriod": {
              "type": "object",
              "description": "The period covered by the statement being submitted.",
              "properties": {
                "startDate": {
                  "type": "string",
                  "format": "date",
                  "description": "Start date of the statement period."
                },
                "endDate": {
                  "type": "string",
                  "format": "date",
                  "description": "End date of the statement period."
                },
                "submittedDate": {
                  "type": "string",
                  "format": "date",
                  "description": "Form submitted date."
                },
                "electionDate": {
                  "type": "string",
                  "format": "date",
                  "description": "Date of election."
                }
              }
            },
            "amendment": {
              "type": "object",
              "properties": {
                "isAmendment": {
                  "type": "boolean",
                  "description": "Indicates if the submission is an amendment to a previously filed form."
                },
                "supercededFilingId": {
                  "type": "string",
                  "description": "The Secretary of State issued filing ID number for the filing being amended"
                },
                "descriptionOfAmendment": {
                  "type": "string",
                  "description": "Explanation of changes"
                }
              },
              "description": "Amendment information, if applicable.",
              "required": [
                "isAmendment",
                "supercededFilingId",
                "descriptionOfAmendment"
              ]
            }
          },
          "required": [
            "filer"
          ]
        }
      ],
      "description": "fill the most recent committee campaign statement (Form 450, 460 or 461)."
    },
    "summaryOfPayments": {
      "type": "object",
      "properties": {
        "totalPaymentsReceived": {
          "type": "object",
          "properties": {
            "currentPeriod": {
              "type": "number",
              "format": "float",
              "description": "Total payments received during the reporting period."
            },
            "cumulativeToDate": {
              "type": "number",
              "format": "float",
              "description": "Cumulative payments received since January 1."
            }
          },
          "additionalProperties": false
        },
        "totalPaymentsMade": {
          "type": "object",
          "properties": {
            "currentPeriod": {
              "type": "number",
              "format": "float",
              "description": "Total payments made during the reporting period."
            },
            "cumulativeToDate": {
              "type": "number",
              "format": "float",
              "description": "Cumulative payments made since January 1."
            }
          },
          "additionalProperties": false
        }
      },
      "additionalProperties": false
    },
    "scheduleA": {
      "type": "object",
      "properties": {
        "summary": {
          "type": "object",
          "properties": {
            "itemizedPayments": {
              "type": "number",
              "format": "float",
              "description": "Total itemized payments received (\u2265 $100)."
            },
            "nonItemizedPayments": {
              "type": "number",
              "format": "float",
              "description": "Total non-itemized payments received (< $100)."
            },
            "totalPayments": {
              "type": "number",
              "format": "float",
              "description": "Total payments received (sum of itemized and non-itemized payments)."
            }
          },
          "additionalProperties": false
        },
        "details": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "id": {
                "type": "string",
                "description": "Unique identifier for the payment record."
              },
              "dateReceived": {
                "type": "string",
                "format": "date",
                "description": "Date the payment was received."
              },
              "amountReceivedThisPeriod": {
                "type": "number",
                "format": "float",
                "description": "Payment amount received during this period."
              },
              "cumulativeAmountReceived": {
                "type": "number",
                "format": "float",
                "description": "Cumulative amount received since January 1."
              },
              "payerDetails": {
                "type": "object",
                "properties": {
                  "id": {
                    "type": "string",
                    "description": "Unique identifier for the payer."
                  },
                  "firstName": {
                    "type": "string",
                    "description": "First name of the payer."
                  },
                  "lastName": {
                    "type": "string",
                    "description": "Last name of the payer."
                  },
                  "address": {
                    "type": "object",
                    "description": "Detailed business or residential address",
                    "properties": {
                      "id": {
                        "type": "string",
                        "format": "uuid",
                        "description": "Unique Id for the address."
                      },
                      "street": {
                        "type": "string",
                        "description": "Street address."
                      },
                      "street2": {
                        "type": "string",
                        "description": "Street address."
                      },
                      "city": {
                        "type": "string",
                        "description": "City."
                      },
                      "state": {
                        "type": "string",
                        "description": "State."
                      },
                      "zipCode": {
                        "type": "string",
                        "description": "ZIP code."
                      },
                      "county": {
                        "type": "string",
                        "description": "County."
                      },
                      "country": {
                        "type": "string",
                        "description": "Country."
                      },
                      "type": {
                        "type": "string",
                        "description": "Type of Address."
                      }
                    },
                    "required": [
                      "street",
                      "city",
                      "state",
                      "zipCode",
                      "country",
                      "type"
                    ],
                    "example": {
                      "street": "123 Main Street",
                      "street2": "Suite 456",
                      "city": "Sacramento",
                      "state": "CA",
                      "zipCode": "95814",
                      "country": "United States",
                      "type": "Residential"
                    },
                    "additionalProperties": false
                  },
                  "employerOrBusiness": {
                    "type": "string",
                    "description": "Employer or business of the payer (if applicable)."
                  },
                  "candidateOrMeasureDetails": {
                    "type": "object",
                    "properties": {
                      "firstName": {
                        "type": "string",
                        "description": "First name of the candidate (if applicable)."
                      },
                      "lastName": {
                        "type": "string",
                        "description": "Last name of the candidate (if applicable)."
                      },
                      "name": {
                        "type": "string",
                        "description": "Name of the ballot measure (if applicable)."
                      },
                      "electionOfficeSought": {
                        "type": "object",
                        "properties": {
                          "office": {
                            "type": "string",
                            "description": "Office held or sought by the candidate."
                          },
                          "officeDescription": {
                            "type": "string",
                            "description": "Title of the office by the candidate."
                          },
                          "jurisdiction": {
                            "type": "string",
                            "description": "Jurisdiction of the office."
                          },
                          "jurisdictionDescription": {
                            "type": "string",
                            "description": "Jurisdiction of the candidate or measure."
                          },
                          "electionDistrictNumber": {
                            "type": "string",
                            "description": "District number (if applicable)."
                          }
                        },
                        "additionalProperties": false
                      },
                      "ballotMeasureJurisdiction": {
                        "type": "string",
                        "description": "Jurisdiction of the measure"
                      },
                      "code": {
                        "type": "string",
                        "description": "Ballot number of letter."
                      },
                      "position": {
                        "type": "string",
                        "enum": [
                          "Support",
                          "Oppose"
                        ],
                        "description": "Indicates whether the payment supports or opposes the candidate or measure."
                      }
                    },
                    "additionalProperties": false
                  }
                },
                "additionalProperties": false
              }
            },
            "additionalProperties": false
          }
        }
      },
      "additionalProperties": false
    },
    "scheduleB": {
      "type": "object",
      "properties": {
        "summary": {
          "type": "object",
          "properties": {
            "itemizedPayments": {
              "type": "number",
              "format": "float",
              "description": "Total payments made to itemized sources (\u2265 $100)."
            },
            "nonItemizedPayments": {
              "type": "number",
              "format": "float",
              "description": "Total non-itemized payments made (< $100)."
            },
            "totalPayments": {
              "type": "number",
              "format": "float",
              "description": "Total payments made (sum of itemized and non-itemized payments)."
            }
          },
          "additionalProperties": false
        },
        "details": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "id": {
                "type": "string",
                "description": "Unique identifier for the payment record."
              },
              "payeeDetails": {
                "type": "object",
                "properties": {
                  "id": {
                    "type": "string",
                    "description": "Unique identifier for the payee."
                  },
                  "firstName": {
                    "type": "string",
                    "description": "First name of the payee."
                  },
                  "lastName": {
                    "type": "string",
                    "description": "Last name of the payee."
                  },
                  "address": {
                    "type": "object",
                    "description": "Detailed business or residential address",
                    "properties": {
                      "id": {
                        "type": "string",
                        "format": "uuid",
                        "description": "Unique Id for the address."
                      },
                      "street": {
                        "type": "string",
                        "description": "Street address."
                      },
                      "street2": {
                        "type": "string",
                        "description": "Street address."
                      },
                      "city": {
                        "type": "string",
                        "description": "City."
                      },
                      "state": {
                        "type": "string",
                        "description": "State."
                      },
                      "zipCode": {
                        "type": "string",
                        "description": "ZIP code."
                      },
                      "county": {
                        "type": "string",
                        "description": "County."
                      },
                      "country": {
                        "type": "string",
                        "description": "Country."
                      },
                      "type": {
                        "type": "string",
                        "description": "Type of Address."
                      }
                    },
                    "required": [
                      "street",
                      "city",
                      "state",
                      "zipCode",
                      "country",
                      "type"
                    ],
                    "example": {
                      "street": "123 Main Street",
                      "street2": "Suite 456",
                      "city": "Sacramento",
                      "state": "CA",
                      "zipCode": "95814",
                      "country": "United States",
                      "type": "Residential"
                    },
                    "additionalProperties": false
                  }
                },
                "additionalProperties": false
              },
              "descriptionOfPayment": {
                "type": "string",
                "description": "Description of the product or service received."
              },
              "amount": {
                "type": "number",
                "format": "float",
                "description": "Amount paid during the current period."
              }
            },
            "additionalProperties": false
          }
        }
      },
      "additionalProperties": false
    },
    "scheduleB1": {
      "type": "object",
      "properties": {
        "agentDetails": {
          "type": "object",
          "properties": {
            "id": {
              "type": "string",
              "description": "Unique identifier for the agent or contractor."
            },
            "firstName": {
              "type": "string",
              "description": "First name of the agent or contractor."
            },
            "lastName": {
              "type": "string",
              "description": "Last name of the agent or contractor."
            },
            "address": {
              "type": "object",
              "description": "Detailed business or residential address",
              "properties": {
                "id": {
                  "type": "string",
                  "format": "uuid",
                  "description": "Unique Id for the address."
                },
                "street": {
                  "type": "string",
                  "description": "Street address."
                },
                "street2": {
                  "type": "string",
                  "description": "Street address."
                },
                "city": {
                  "type": "string",
                  "description": "City."
                },
                "state": {
                  "type": "string",
                  "description": "State."
                },
                "zipCode": {
                  "type": "string",
                  "description": "ZIP code."
                },
                "county": {
                  "type": "string",
                  "description": "County."
                },
                "country": {
                  "type": "string",
                  "description": "Country."
                },
                "type": {
                  "type": "string",
                  "description": "Type of Address."
                }
              },
              "required": [
                "street",
                "city",
                "state",
                "zipCode",
                "country",
                "type"
              ],
              "example": {
                "street": "123 Main Street",
                "street2": "Suite 456",
                "city": "Sacramento",
                "state": "CA",
                "zipCode": "95814",
                "country": "United States",
                "type": "Residential"
              },
              "additionalProperties": false
            }
          },
          "additionalProperties": false
        },
        "payments": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "id": {
                "type": "string",
                "description": "Unique identifier for the payment record."
              },
              "payeeFirstName": {
                "type": "string",
                "description": "First name of the ultimate payee."
              },
              "payeeLastName": {
                "type": "string",
                "description": "Last name of the ultimate payee."
              },
              "descriptionOfPayment": {
                "type": "string",
                "description": "Description of the payment made."
              },
              "amount": {
                "type": "number",
                "format": "float",
                "description": "Amount paid by the agent or contractor."
              }
            },
            "additionalProperties": false
          }
        }
      },
      "additionalProperties": false
    },
    "scheduleC": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid",
            "description": "Unique identifier for the payment record."
          },
          "firstName": {
            "type": "string",
            "description": "First name of the individual receiving payments ($1,000 or more)."
          },
          "lastName": {
            "type": "string",
            "description": "Last name of the individual receiving payments ($1,000 or more)."
          },
          "amountThisPeriod": {
            "type": "number",
            "format": "float",
            "description": "Payment received by the individual during this period."
          },
          "cumulativeAmount": {
            "type": "number",
            "format": "float",
            "description": "Cumulative payments received by the individual since January 1."
          }
        },
        "additionalProperties": false
      }
    },
    "scheduleD": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid",
            "description": "Unique identifier for the candidate or measure."
          },
          "candidateOrMeasures": {
            "type": "object",
            "properties": {
              "firstName": {
                "type": "string",
                "description": "First name of the candidate (if applicable)."
              },
              "lastName": {
                "type": "string",
                "description": "Last name of the candidate (if applicable)."
              },
              "name": {
                "type": "string",
                "description": "Name of the ballot measure (if applicable)."
              },
              "electionOfficeSought": {
                "type": "object",
                "properties": {
                  "office": {
                    "type": "string",
                    "description": "Office held or sought by the candidate."
                  },
                  "officeDescription": {
                    "type": "string",
                    "description": "Title of the office by the candidate."
                  },
                  "jurisdiction": {
                    "type": "string",
                    "description": "Jurisdiction of the office."
                  },
                  "jurisdictionDescription": {
                    "type": "string",
                    "description": "Jurisdiction of the candidate or measure."
                  },
                  "electionDistrictNumber": {
                    "type": "string",
                    "description": "District number (if applicable)."
                  }
                },
                "additionalProperties": false
              },
              "ballotMeasureJurisdiction": {
                "type": "string",
                "description": "Jurisdiction of the measure"
              },
              "code": {
                "type": "string",
                "description": "Ballot number of letter."
              },
              "position": {
                "type": "string",
                "enum": [
                  "Support",
                  "Oppose"
                ],
                "description": "Indicates whether the payment supports or opposes the candidate or measure."
              }
            },
            "additionalProperties": false
          }
        },
        "additionalProperties": false
      }
    },
    "filingType": {
      "type": "object",
      "properties": {
        "amendment": {
          "type": "boolean"
        },
        "isPreElectionStatement": {
          "type": "boolean"
        },
        "isQuarterlyStatement": {
          "type": "boolean"
        },
        "isSemiAnnualStatement": {
          "type": "boolean"
        },
        "isSpecialOddYearReport": {
          "type": "boolean"
        },
        "termination": {
          "type": "boolean"
        },
        "amendmentNumber": {
          "type": "string"
        },
        "initialFilingId": {
          "type": "string"
        },
        "explanation": {
          "type": "string"
        }
      },
      "additionalProperties": false
    },
    "attestation": {
      "type": "object",
      "properties": {
        "firstName": {
          "type": "string",
          "description": "First Name of verification officer."
        },
        "lastName": {
          "type": "string",
          "description": "Last Name of verification officer."
        },
        "executedAt": {
          "type": "string",
          "format": "date",
          "description": "Date the statement was executed."
        },
        "executedAtCity": {
          "type": "string",
          "description": "Executed at City."
        },
        "executedAtState": {
          "type": "string",
          "description": "Executed at state."
        },
        "role": {
          "type": "string"
        },
        "signature": {
          "type": "string",
          "description": "Name of the treasurer or filer."
        }
      },
      "required": [
        "executedAt",
        "signature"
      ],
      "additionalProperties": false
    }
  }
}', 'Campaign-Disclosure-SlateMailer'),
    (3, '{
  "title": "Form 470 Schema - Short",
  "description": "Campaign Statement Short",
  "type": "object",
  "additionalProperties": false,
  "properties": {
    "statementFilingPeriodId": {
      "type": "number",
      "format": "int64",
      "description": "Filing Period identification number for the statement."
    },
    "committeeDetails": {
      "type": "array",
      "items": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "committeeId": {
            "type": "number",
            "format": "int64",
            "description": "Committee identification number."
          }
        }
      }
    },
    "attestation": {
      "type": "object",
      "additionalProperties": false,
      "required": [
        "executedAt",
        "signature"
      ],
      "properties": {
        "firstName": {
          "type": "string",
          "description": "First Name of verification officer."
        },
        "lastName": {
          "type": "string",
          "description": "Last Name of verification officer."
        },
        "executedAt": {
          "type": "string",
          "format": "date",
          "description": "Date the statement was executed."
        },
        "executedAtCity": {
          "type": "string",
          "description": "Executed at City."
        },
        "executedAtState": {
          "type": "string",
          "description": "Executed at state."
        },
        "role": {
          "type": "string"
        },
        "signature": {
          "type": "string",
          "description": "Name of the treasurer or filer."
        }
      }
    },
    "amendment": {
      "type": "object",
      "additionalProperties": false,
      "description": "Amendment information, if applicable.",
      "required": [
        "isAmendment",
        "supercededFilingId"
      ],
      "properties": {
        "isAmendment": {
          "type": "boolean",
          "description": "Indicates if the submission is an amendment to a previously filed form."
        },
        "supercededFilingId": {
          "type": "string",
          "description": "The Secretary of State issued filing ID number for the filing being amended"
        }
      }
    }
  },
  "required": [
    "statementFilingPeriodId",
    "attestation"
  ]
}', 'Campaign-Disclosure-Candidate-CampaignStatement-Short'),
    (4, '{
  "title": "Form 470 Schema - Supplement",
  "description": "Campaign Statement Supplement",
  "type": "object",
  "additionalProperties": false,
  "properties": {
    "dateThresholdReached": {
      "type": "string",
      "format": "date",
      "description": "Date contributions totaling $2,000 or more were received or expenditures of $2,000 or more were made."
    },
    "committeeDetails": {
      "type": "array",
      "items": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "committeeId": {
            "type": "number",
            "format": "int64",
            "description": "Committee identification number."
          }
        }
      }
    },
    "attestation": {
      "type": "object",
      "additionalProperties": false,
      "required": [
        "executedAt",
        "signature"
      ],
      "properties": {
        "firstName": {
          "type": "string",
          "description": "First Name of verification officer."
        },
        "lastName": {
          "type": "string",
          "description": "Last Name of verification officer."
        },
        "executedAt": {
          "type": "string",
          "format": "date",
          "description": "Date the statement was executed."
        },
        "executedAtCity": {
          "type": "string",
          "description": "Executed at City."
        },
        "executedAtState": {
          "type": "string",
          "description": "Executed at state."
        },
        "role": {
          "type": "string"
        },
        "signature": {
          "type": "string",
          "description": "Name of the treasurer or filer."
        }
      }
    },
    "amendment": {
      "type": "object",
      "additionalProperties": false,
      "description": "Amendment information, if applicable.",
      "required": [
        "isAmendment",
        "supercededFilingId"
      ],
      "properties": {
        "isAmendment": {
          "type": "boolean",
          "description": "Indicates if the submission is an amendment to a previously filed form."
        },
        "supercededFilingId": {
          "type": "string",
          "description": "The Secretary of State issued filing ID number for the filing being amended"
        }
      }
    }
  },
  "required": [
    "dateThresholdReached",
    "attestation"
  ]
}', 'Campaign-Disclosure-Candidate-CampaignStatement-Supplement'),
    (5, '{
  "title": "Form 402 Schema",
  "description": "Statement Of Termination",
  "type": "object",
  "additionalProperties": false,
  "properties": {
    "slateMailerOrganization": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "name": { "type": "string" },
        "terminationDate": {
          "type": "string",
          "format": "date"
        },
        "committeeId": {
          "type": "integer",
          "format": "int64"
        },
        "email": {
          "type": "string",
          "format": "email"
        },
        "telephoneNumber": {
          "type": "object",
          "additionalProperties": false,
          "properties": {
            "countryCode": { "type": "string" },
            "number": { "type": "string" }
          },
          "required": [ "countryCode", "number" ]
        },
        "faxNumber": {
          "type": "object",
          "additionalProperties": false,
          "properties": {
            "countryCode": { "type": "string" },
            "number": { "type": "string" }
          },
          "required": [ "countryCode", "number" ]
        },
        "address": {
          "type": "object",
          "additionalProperties": false,
          "properties": {
            "street": { "type": "string" },
            "street2": { "type": "string" },
            "city": { "type": "string" },
            "state": { "type": "string" },
            "zipCode": { "type": "string" },
            "county": { "type": "string" },
            "country": { "type": "string" },
            "type": { "type": "string" }
          },
          "required": [
            "street",
            "city",
            "state",
            "zipCode",
            "country",
            "county",
            "type"
          ]
        }
      },
      "required": [
        "name",
        "terminationDate",
        "committeeId",
        "address",
        "telephoneNumber",
        "email"
      ]
    },
    "officers": {
      "type": "array",
      "items": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "firstName": { "type": "string" },
          "lastName": { "type": "string" },
          "role": {
            "type": "string",
            "enum": [ "Treasurer", "Principal", "Officer" ]
          },
          "email": {
            "type": "string",
            "format": "email"
          },
          "telephoneNumber": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
              "countryCode": { "type": "string" },
              "number": { "type": "string" }
            },
            "required": [ "countryCode", "number" ]
          },
          "address": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
              "street": { "type": "string" },
              "street2": { "type": "string" },
              "city": { "type": "string" },
              "state": { "type": "string" },
              "zipCode": { "type": "string" },
              "county": { "type": "string" },
              "country": { "type": "string" },
              "type": { "type": "string" }
            },
            "required": [
              "street",
              "city",
              "state",
              "zipCode",
              "country",
              "type"
            ]
          }
        },
        "required": [
          "firstName",
          "lastName",
          "role",
          "email",
          "telephoneNumber",
          "address"
        ]
      }
    },
    "amendment": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "isAmendment": { "type": "boolean" },
        "supercededFilingId": { "type": "string" }
      },
      "required": [ "isAmendment", "supercededFilingId" ]
    },
    "attestation": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "firstName": { "type": "string" },
        "middleName": { "type": "string" },
        "lastName": { "type": "string" },
        "executedAt": {
          "type": "string",
          "format": "date"
        },
        "executedAtCity": { "type": "string" },
        "executedAtState": { "type": "string" },
        "role": { "type": "string" },
        "signature": { "type": "string" }
      },
      "required": [ "executedAt", "signature" ]
    }
  },
  "required": [
    "slateMailerOrganization",
    "attestation",
    "amendment",
    "officers"
  ]
}
', 'Campaign-Termination-SlateMailer'),
    (6, '{
  "title": "Form 425 Schema",
  "description": "Semi-Annual Statement of No Activity",
  "type": "object",
  "additionalProperties": false,
  "properties": {
    "submittedDate": {
      "type": "string",
      "format": "date",
      "description": "Date of stamp."
    },
    "treasurerInformation": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "treasurer": {
          "type": "object",
          "additionalProperties": false,
          "description": "Details of the Treasurer.",
          "properties": {
            "firstName": {
              "type": "string",
              "description": "First name of the Treasurer."
            },
            "lastName": {
              "type": "string",
              "description": "Last name of the Treasurer."
            },
            "address": {
              "type": "object",
              "additionalProperties": false,
              "description": "Detailed business or residential address",
              "required": [
                "street",
                "city",
                "state",
                "zipCode"
              ],
              "properties": {
                "id": {
                  "type": "string",
                  "format": "uuid",
                  "description": "Unique Id for the address."
                },
                "street": {
                  "type": "string",
                  "description": "Street address."
                },
                "street2": {
                  "type": "string",
                  "description": "Street address."
                },
                "city": {
                  "type": "string",
                  "description": "City."
                },
                "state": {
                  "type": "string",
                  "description": "State."
                },
                "zipCode": {
                  "type": "string",
                  "description": "ZIP code."
                },
                "county": {
                  "type": "string",
                  "description": "County."
                }
              }
            },
            "phone": {
              "type": "string",
              "description": "Phone number of the Treasurer as digits only.",
              "pattern": "^\\d+$",
              "maxLength": 40
            },
            "email": {
              "type": "string",
              "format": "email",
              "description": "Fax number or email address of the Treasurer as digits only."
            },
            "fax": {
              "type": "string",
              "description": "Fax number of the committee as digits only (optional).",
              "pattern": "^\\d+$",
              "maxLength": 40
            }
          }
        },
        "assistantTreasurer": {
          "type": "object",
          "additionalProperties": false,
          "description": "Details of the Assistant Treasurer, if applicable.",
          "properties": {
            "firstName": {
              "type": "string",
              "description": "First name of the Assistant Treasurer (if any)."
            },
            "lastName": {
              "type": "string",
              "description": "Last name of the Assistant Treasurer (if any)."
            },
            "address": {
              "type": "object",
              "additionalProperties": false,
              "description": "Detailed business or residential address",
              "required": [
                "street",
                "city",
                "state",
                "zipCode"
              ],
              "properties": {
                "id": {
                  "type": "string",
                  "format": "uuid",
                  "description": "Unique Id for the address."
                },
                "street": {
                  "type": "string",
                  "description": "Street address."
                },
                "street2": {
                  "type": "string",
                  "description": "Street address."
                },
                "city": {
                  "type": "string",
                  "description": "City."
                },
                "state": {
                  "type": "string",
                  "description": "State."
                },
                "zipCode": {
                  "type": "string",
                  "description": "ZIP code."
                },
                "county": {
                  "type": "string",
                  "description": "County."
                }
              }
            },
            "phoneNumber": {
              "type": "string",
              "description": "Phone number of the Assistant Treasurer as digits only.",
              "pattern": "^\\d+$",
              "maxLength": 40
            },
            "email": {
              "type": "string",
              "description": "Fax number or email address of the Treasurer as digits only."
            },
            "fax": {
              "type": "string",
              "description": "Fax number of the committee as digits only (optional).",
              "pattern": "^\\d+$",
              "maxLength": 40
            }
          }
        }
      }
    },
    "noActivityPeriod": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "startDate": {
          "type": "string",
          "format": "date",
          "description": "start date period"
        },
        "endDate": {
          "type": "string",
          "format": "date",
          "description": "end date period"
        }
      }
    },
    "attestation": {
      "type": "object",
      "additionalProperties": false,
      "required": [
        "executedAt",
        "signature"
      ],
      "properties": {
        "firstName": {
          "type": "string",
          "description": "First Name of verification officer."
        },
        "lastName": {
          "type": "string",
          "description": "Last Name of verification officer."
        },
        "executedAt": {
          "type": "string",
          "format": "date",
          "description": "Date the statement was executed."
        },
        "executedAtCity": {
          "type": "string",
          "description": "Executed at City."
        },
        "executedAtState": {
          "type": "string",
          "description": "Executed at state."
        },
        "role": {
          "type": "string"
        },
        "signature": {
          "type": "string",
          "description": "Name of the treasurer or filer."
        }
      }
    }
  }
}', 'Campaign-Disclosure-NoActivity'),
    (7, '{
  "title": "Form 410 Schema",
  "description": "Statement of Organization Recipient Committee",
  "type": "object",
  "additionalProperties": false,
  "properties": {
    "statementType": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "amendment": {
          "type": "object",
          "additionalProperties": false,
          "description": "Amendment information, if applicable.",
          "required": [
            "isAmendment",
            "supercededFilingId"
          ],
          "properties": {
            "isAmendment": {
              "type": "boolean",
              "description": "Indicates if the submission is an amendment to a previously filed form."
            },
            "supercededFilingId": {
              "type": "string",
              "description": "The Secretary of State issued filing ID number for the filing being amended"
            }
          }
        },
        "notYetQualified": {
          "type": "boolean",
          "description": "Indicates if the committee is not yet qualified."
        },
        "dateQualified": {
          "type": "string",
          "format": "date",
          "description": "Date when the committee met the qualification threshold (if applicable)."
        },
        "terminatedAt": {
          "type": "string",
          "format": "date",
          "description": "Date when the committee was terminated (if applicable)."
        }
      }
    },
    "committeeInformation": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "id": {
          "type": "string",
          "description": "State-assigned Committee ID."
        },
        "name": {
          "type": "string",
          "description": "Full name of the committee."
        },
        "committeeType": {
          "type": "string",
          "description": "The committee type (Controlled Committee, Sponsored Committee, etc.)"
        },
        "streetAddress": {
          "type": "object",
          "additionalProperties": false,
          "description": "Detailed business or residential address",
          "required": [
            "street",
            "city",
            "state",
            "zipCode"
          ],
          "properties": {
            "id": {
              "type": "string",
              "format": "uuid",
              "description": "Unique Id for the address."
            },
            "street": {
              "type": "string",
              "description": "Street address."
            },
            "street2": {
              "type": "string",
              "description": "Street address."
            },
            "city": {
              "type": "string",
              "description": "City."
            },
            "state": {
              "type": "string",
              "description": "State."
            },
            "zipCode": {
              "type": "string",
              "description": "ZIP code."
            },
            "county": {
              "type": "string",
              "description": "County."
            }
          }
        },
        "mailingAddress": {
          "type": "object",
          "additionalProperties": false,
          "description": "Detailed business or residential address",
          "required": [
            "street",
            "city",
            "state",
            "zipCode"
          ],
          "properties": {
            "id": {
              "type": "string",
              "format": "uuid",
              "description": "Unique Id for the address."
            },
            "street": {
              "type": "string",
              "description": "Street address."
            },
            "street2": {
              "type": "string",
              "description": "Street address."
            },
            "city": {
              "type": "string",
              "description": "City."
            },
            "state": {
              "type": "string",
              "description": "State."
            },
            "zipCode": {
              "type": "string",
              "description": "ZIP code."
            },
            "county": {
              "type": "string",
              "description": "County."
            }
          }
        },
        "county": {
          "type": "string",
          "description": "County where the committee is domiciled."
        },
        "jurisdictionActive": {
          "type": "string",
          "description": "Jurisdiction where the committee is active (e.g., state, city, or county)."
        },
        "email": {
          "type": "string",
          "format": "email",
          "description": "Email address of the committee."
        },
        "committeeFax": {
          "type": "string",
          "description": "Fax number of the committee as digits only (optional).",
          "pattern": "^\\d+$",
          "maxLength": 40
        },
        "phoneNumber": {
          "type": "string",
          "description": "Phone number of the committee as digits only.",
          "pattern": "^\\d+$",
          "maxLength": 40
        }
      }
    },
    "treasurerAndOfficers": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "treasurer": {
          "type": "object",
          "additionalProperties": false,
          "properties": {
            "firstName": {
              "type": "string",
              "description": "First name of the officer."
            },
            "lastName": {
              "type": "string",
              "description": "Last name of the officer."
            },
            "email": {
              "type": "string",
              "format": "email",
              "description": "Email address of the officer."
            },
            "phoneNumber": {
              "type": "string",
              "description": "Contact phone number of the officer."
            },
            "address": {
              "type": "object",
              "additionalProperties": false,
              "description": "Detailed business or residential address",
              "required": [
                "street",
                "city",
                "state",
                "zipCode"
              ],
              "properties": {
                "id": {
                  "type": "string",
                  "format": "uuid",
                  "description": "Unique Id for the address."
                },
                "street": {
                  "type": "string",
                  "description": "Street address."
                },
                "street2": {
                  "type": "string",
                  "description": "Street address."
                },
                "city": {
                  "type": "string",
                  "description": "City."
                },
                "state": {
                  "type": "string",
                  "description": "State."
                },
                "zipCode": {
                  "type": "string",
                  "description": "ZIP code."
                },
                "county": {
                  "type": "string",
                  "description": "County."
                }
              }
            }
          }
        },
        "assistantTreasurer": {
          "type": "object",
          "additionalProperties": false,
          "properties": {
            "firstName": {
              "type": "string",
              "description": "First name of the officer."
            },
            "lastName": {
              "type": "string",
              "description": "Last name of the officer."
            },
            "email": {
              "type": "string",
              "format": "email",
              "description": "Email address of the officer."
            },
            "phoneNumber": {
              "type": "string",
              "description": "Contact phone number of the officer."
            },
            "address": {
              "type": "object",
              "additionalProperties": false,
              "description": "Detailed business or residential address",
              "required": [
                "street",
                "city",
                "state",
                "zipCode"
              ],
              "properties": {
                "id": {
                  "type": "string",
                  "format": "uuid",
                  "description": "Unique Id for the address."
                },
                "street": {
                  "type": "string",
                  "description": "Street address."
                },
                "street2": {
                  "type": "string",
                  "description": "Street address."
                },
                "city": {
                  "type": "string",
                  "description": "City."
                },
                "state": {
                  "type": "string",
                  "description": "State."
                },
                "zipCode": {
                  "type": "string",
                  "description": "ZIP code."
                },
                "county": {
                  "type": "string",
                  "description": "County."
                }
              }
            }
          }
        },
        "principalOfficers": {
          "type": "array",
          "items": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
              "firstName": {
                "type": "string",
                "description": "First name of the officer."
              },
              "lastName": {
                "type": "string",
                "description": "Last name of the officer."
              },
              "email": {
                "type": "string",
                "format": "email",
                "description": "Email address of the officer."
              },
              "phoneNumber": {
                "type": "string",
                "description": "Contact phone number of the officer."
              },
              "address": {
                "type": "object",
                "additionalProperties": false,
                "description": "Detailed business or residential address",
                "required": [
                  "street",
                  "city",
                  "state",
                  "zipCode"
                ],
                "properties": {
                  "id": {
                    "type": "string",
                    "format": "uuid",
                    "description": "Unique Id for the address."
                  },
                  "street": {
                    "type": "string",
                    "description": "Street address."
                  },
                  "street2": {
                    "type": "string",
                    "description": "Street address."
                  },
                  "city": {
                    "type": "string",
                    "description": "City."
                  },
                  "state": {
                    "type": "string",
                    "description": "State."
                  },
                  "zipCode": {
                    "type": "string",
                    "description": "ZIP code."
                  },
                  "county": {
                    "type": "string",
                    "description": "County."
                  }
                }
              }
            }
          }
        }
      }
    },
    "typeOfCommittee": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "controlledCommittee": {
          "type": "array",
          "description": "List of a committees controlled by a candidate or officeholder.",
          "items": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
              "name": {
                "type": "string",
                "description": "The name of the jointly controlled committee"
              },
              "id": {
                "type": "string",
                "format": "uuid",
                "description": "The identification number of the jointly controlled committee"
              },
              "candidate": {
                "type": "object",
                "additionalProperties": false,
                "properties": {
                  "firstName": {
                    "type": "string",
                    "description": "First name of the candidate."
                  },
                  "lastName": {
                    "type": "string",
                    "description": "Last name of the candidate."
                  }
                }
              },
              "electionOfficeSought": {
                "type": "object",
                "additionalProperties": false,
                "properties": {
                  "office": {
                    "type": "string",
                    "description": "Office held or sought by the candidate."
                  },
                  "officeDescription": {
                    "type": "string",
                    "description": "Title of the office by the candidate."
                  },
                  "jurisdiction": {
                    "type": "string",
                    "description": "Jurisdiction of the office."
                  },
                  "jurisdictionDescription": {
                    "type": "string",
                    "description": "Jurisdiction of the candidate or measure."
                  },
                  "electionDistrictNumber": {
                    "type": "string",
                    "description": "District number (if applicable)."
                  }
                }
              },
              "city": {
                "type": "string",
                "description": "City (if applicable)."
              },
              "county": {
                "type": "string",
                "description": "County (if applicable)."
              },
              "year": {
                "type": "integer",
                "description": "Year of the election."
              },
              "partyAffiliation": {
                "type": "object",
                "additionalProperties": false,
                "properties": {
                  "partyPartisanship": {
                    "type": "string",
                    "enum": [
                      "Partisan",
                      "Non-Partisan"
                    ],
                    "description": "Indicates if the candidate is running as partisan or non-partisan."
                  },
                  "partyAffiliation": {
                    "type": "string",
                    "description": "Political parties qualified by the California Secretary of State."
                  }
                }
              },
              "officeholderOrStateMeasureProponent": {
                "type": "boolean",
                "description": "Indicates if the committee is for an officeholder or state measure proponent."
              }
            }
          }
        },
        "primarilyFormedCommittee": {
          "type": "object",
          "additionalProperties": false,
          "description": "List of primarily formed committees.",
          "properties": {
            "candidates": {
              "type": "array",
              "items": {
                "type": "object",
                "additionalProperties": false,
                "required": [
                  "firstName",
                  "lastName",
                  "position"
                ],
                "properties": {
                  "firstName": {
                    "type": "string",
                    "description": "Name of the candidate supported or opposed."
                  },
                  "lastName": {
                    "type": "string",
                    "description": "Last Name of the candidate supported or opposed."
                  },
                  "electionCity": {
                    "type": "string",
                    "description": "City (if applicable)."
                  },
                  "electionCounty": {
                    "type": "string",
                    "description": "County (if applicable)."
                  },
                  "officeSoughtOrHeld": {
                    "type": "string",
                    "description": "Office sought or held by the candidate."
                  },
                  "districtNumber": {
                    "type": "string",
                    "description": "District number of the office (if applicable)."
                  },
                  "jurisdiction": {
                    "type": "string",
                    "description": "District number of the office (if applicable)."
                  },
                  "position": {
                    "type": "string",
                    "enum": [
                      "Support",
                      "Oppose"
                    ],
                    "description": "candidate supported/opposed."
                  }
                }
              }
            },
            "ballotMeasures": {
              "type": "array",
              "items": {
                "type": "object",
                "additionalProperties": false,
                "required": [
                  "name",
                  "position"
                ],
                "properties": {
                  "name": {
                    "type": "string",
                    "description": "Name of the measure supported or opposed."
                  },
                  "jurisdiction": {
                    "type": "string",
                    "description": "District number of the measure (if applicable)."
                  },
                  "position": {
                    "type": "string",
                    "enum": [
                      "Support",
                      "Oppose"
                    ],
                    "description": "Ballot measure supported/opposed."
                  },
                  "number": {
                    "type": "string",
                    "description": "Ballot/Measure number."
                  }
                }
              }
            }
          }
        },
        "generalPurposeCommittee": {
          "type": "object",
          "additionalProperties": false,
          "properties": {
            "jurisdictionType": {
              "type": "string",
              "enum": [
                "City",
                "County",
                "State"
              ]
            },
            "activityDescription": {
              "type": "string",
              "description": "Description of the general-purpose committee activity."
            }
          }
        },
        "sponsoredCommittee": {
          "type": "array",
          "description": "List of sponsored committees.",
          "items": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
              "firstName": {
                "type": "string",
                "description": "First name of the sponsor."
              },
              "lastName": {
                "type": "string",
                "description": "Last name of the sponsor."
              },
              "industryOrGroupDescription": {
                "type": "string",
                "description": "Industry or field of the sponsor."
              },
              "sponsorAddress": {
                "type": "object",
                "additionalProperties": false,
                "description": "Detailed business or residential address",
                "required": [
                  "street",
                  "city",
                  "state",
                  "zipCode"
                ],
                "properties": {
                  "id": {
                    "type": "string",
                    "format": "uuid",
                    "description": "Unique Id for the address."
                  },
                  "street": {
                    "type": "string",
                    "description": "Street address."
                  },
                  "street2": {
                    "type": "string",
                    "description": "Street address."
                  },
                  "city": {
                    "type": "string",
                    "description": "City."
                  },
                  "state": {
                    "type": "string",
                    "description": "State."
                  },
                  "zipCode": {
                    "type": "string",
                    "description": "ZIP code."
                  },
                  "county": {
                    "type": "string",
                    "description": "County."
                  }
                }
              },
              "phoneNumber": {
                "type": "string",
                "description": "Phone number of the committee as digits only.",
                "pattern": "^\\d+$",
                "maxLength": 40
              }
            }
          }
        },
        "smallContributorCommittee": {
          "type": "object",
          "additionalProperties": false,
          "properties": {
            "dateQualified": {
              "type": "string",
              "format": "date",
              "description": "Date the small contributor committee qualified."
            }
          }
        }
      }
    },
    "financialInstitutionDetails": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "financialInstitutionName": {
          "type": "string",
          "description": "Name of the financial institution."
        },
        "address": {
          "type": "object",
          "additionalProperties": false,
          "description": "Detailed business or residential address",
          "required": [
            "street",
            "city",
            "state",
            "zipCode"
          ],
          "properties": {
            "id": {
              "type": "string",
              "format": "uuid",
              "description": "Unique Id for the address."
            },
            "street": {
              "type": "string",
              "description": "Street address."
            },
            "street2": {
              "type": "string",
              "description": "Street address."
            },
            "city": {
              "type": "string",
              "description": "City."
            },
            "state": {
              "type": "string",
              "description": "State."
            },
            "zipCode": {
              "type": "string",
              "description": "ZIP code."
            },
            "county": {
              "type": "string",
              "description": "County."
            }
          }
        },
        "financialInstitutionPhone": {
          "type": "string",
          "description": "Phone number of the committee as digits only.",
          "pattern": "^\\d+$",
          "maxLength": 40
        },
        "accountNumber": {
          "type": "string",
          "description": "Account number associated with the committee (if applicable)."
        },
        "bankRecordAuthorizedPeople": {
          "type": "array",
          "items": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
              "firstName": {
                "type": "string",
                "description": "First name of the authorized person."
              },
              "lastName": {
                "type": "string",
                "description": "Last name of the authorized person."
              }
            }
          }
        }
      }
    },
    "filingType": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "amendment": {
          "type": "boolean"
        },
        "isPreElectionStatement": {
          "type": "boolean"
        },
        "isQuarterlyStatement": {
          "type": "boolean"
        },
        "isSemiAnnualStatement": {
          "type": "boolean"
        },
        "isSpecialOddYearReport": {
          "type": "boolean"
        },
        "termination": {
          "type": "boolean"
        },
        "amendmentNumber": {
          "type": "string"
        },
        "initialFilingId": {
          "type": "string"
        },
        "explanation": {
          "type": "string"
        }
      }
    },
    "attestation": {
      "type": "array",
      "items": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "executedAt",
          "signature"
        ],
        "properties": {
          "firstName": {
            "type": "string",
            "description": "First Name of verification officer."
          },
          "lastName": {
            "type": "string",
            "description": "Last Name of verification officer."
          },
          "executedAt": {
            "type": "string",
            "format": "date",
            "description": "Date the statement was executed."
          },
          "executedAtCity": {
            "type": "string",
            "description": "Executed at City."
          },
          "executedAtState": {
            "type": "string",
            "description": "Executed at state."
          },
          "role": {
            "type": "string"
          },
          "signature": {
            "type": "string",
            "description": "Name of the treasurer or filer."
          }
        }
      }
    }
  }
}', 'Campaign-Registration-RecipientCommittee'),
    (8, '{
  "title": "Form 606 Schema",
  "description": "Notice of Termination",
  "type": "object",
  "additionalProperties": false,
  "properties": {
    "terminationDetails": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "terminatedAt": {
          "type": "string",
          "format": "date"
        },
        "legislativeSession": {
          "type": "string"
        }
      },
      "required": [
        "terminatedAt"
      ]
    },
    "attestation": {
      "type": "array",
      "items": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "firstName": {
            "type": "string"
          },
          "lastName": {
            "type": "string"
          },
          "executedAt": {
            "type": "string",
            "format": "date"
          },
          "executedAtCity": {
            "type": "string",
            "description": "Executed at City."
          },
          "executedAtState": {
            "type": "string"
          },
          "role": {
            "type": "string"
          },
          "signature": {
            "type": "string"
          }
        },
        "required": [
          "executedAt",
          "signature"
        ]
      }
    }
  },
  "required": [
    "terminationDetails",
    "attestation"
  ]
}', 'Lobbying-NoticeOfTermination'),
    (9, '{
  "title": "Form 400 Schema",
  "description": "Campaign Registration Slate Mailer",
  "type": "object",
  "properties": {
    "slateMailerOrganization": {
      "description": "Basic information about the Slate Mailer Organization, including its level of activity and addresses.",
      "type": "object",
      "properties": {
        "submittedDate": {
          "type": "string",
          "format": "date",
          "description": "Date submitted"
        },
        "name": {
          "type": "string",
          "description": "Full name of the Slate Mailer Organization."
        },
        "dateQualified": {
          "type": "string",
          "format": "date",
          "description": "Date the organization qualified as a Slate Mailer Organization."
        },
        "activityLevel": {
          "type": "string",
          "enum": [
            "City",
            "County",
            "State"
          ],
          "description": "Indicates the organization''s primary level of activity (city, county, or state)."
        },
        "address": {
          "allOf": [
            {
              "type": "object",
              "description": "Detailed business or residential address",
              "properties": {
                "id": {
                  "type": "string",
                  "format": "uuid",
                  "description": "Unique Id for the address."
                },
                "street": {
                  "type": "string",
                  "description": "Street address."
                },
                "street2": {
                  "type": "string",
                  "description": "Street address."
                },
                "city": {
                  "type": "string",
                  "description": "City."
                },
                "state": {
                  "type": "string",
                  "description": "State."
                },
                "zipCode": {
                  "type": "string",
                  "description": "ZIP code."
                },
                "county": {
                  "type": "string",
                  "description": "County."
                },
                "country": {
                  "type": "string",
                  "description": "Country."
                },
                "type": {
                  "type": "string",
                  "description": "Type of Address."
                }
              },
              "required": [
                "street",
                "city",
                "state",
                "zipCode",
                "country",
                "type"
              ],
              "example": {
                "street": "123 Main Street",
                "street2": "Suite 456",
                "city": "Sacramento",
                "state": "CA",
                "zipCode": "95814",
                "country": "United States",
                "type": "Residential"
              }
            },
            {
              "type": "object",
              "required": [
                "county"
              ],
              "example": {
                "street": "123 Main Street",
                "street2": "Suite 456",
                "city": "Sacramento",
                "county": "Sacramento",
                "state": "CA",
                "zipCode": "95814",
                "country": "United States",
                "type": "Residential"
              }
            }
          ]
        },
        "mailingAddress": {
          "allOf": [
            {
              "type": "object",
              "description": "Detailed business or residential address",
              "properties": {
                "id": {
                  "type": "string",
                  "format": "uuid",
                  "description": "Unique Id for the address."
                },
                "street": {
                  "type": "string",
                  "description": "Street address."
                },
                "street2": {
                  "type": "string",
                  "description": "Street address."
                },
                "city": {
                  "type": "string",
                  "description": "City."
                },
                "state": {
                  "type": "string",
                  "description": "State."
                },
                "zipCode": {
                  "type": "string",
                  "description": "ZIP code."
                },
                "county": {
                  "type": "string",
                  "description": "County."
                },
                "country": {
                  "type": "string",
                  "description": "Country."
                },
                "type": {
                  "type": "string",
                  "description": "Type of Address."
                }
              },
              "required": [
                "street",
                "city",
                "state",
                "zipCode",
                "country",
                "type"
              ],
              "example": {
                "street": "123 Main Street",
                "street2": "Suite 456",
                "city": "Sacramento",
                "state": "CA",
                "zipCode": "95814",
                "country": "United States",
                "type": "Residential"
              }
            },
            {
              "type": "object",
              "required": [
                "county"
              ],
              "example": {
                "street": "123 Main Street",
                "street2": "Suite 456",
                "city": "Sacramento",
                "county": "Sacramento",
                "state": "CA",
                "zipCode": "95814",
                "country": "United States",
                "type": "Residential"
              }
            }
          ]
        },
        "phone": {
          "type": "string",
          "description": "Phone number of the organization as digits only.",
          "pattern": "^\\d+$",
          "maxLength": 40
        },
        "fax": {
          "type": "string",
          "description": "Optional fax number for the organization as digits only.",
          "pattern": "^\\d+$",
          "maxLength": 40
        },
        "email": {
          "type": "string",
          "format": "email",
          "description": "email address for the organization."
        }
      },
      "required": [
        "name",
        "activityLevel",
        "dateQualified",
        "address",
        "phone",
        "email"
      ],
      "additionalProperties": false
    },
    "officers": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "firstName": {
            "type": "string",
            "description": "First name of the officer."
          },
          "lastName": {
            "type": "string",
            "description": "Last name of the officer."
          },
          "role": {
            "type": "string",
            "enum": [
              "Treasurer",
              "Principal",
              "Officer"
            ],
            "description": "Position of the officer (Treasurer or Principal Officer)."
          },
          "address": {
            "type": "object",
            "description": "Detailed business or residential address",
            "properties": {
              "id": {
                "type": "string",
                "format": "uuid",
                "description": "Unique Id for the address."
              },
              "street": {
                "type": "string",
                "description": "Street address."
              },
              "street2": {
                "type": "string",
                "description": "Street address."
              },
              "city": {
                "type": "string",
                "description": "City."
              },
              "state": {
                "type": "string",
                "description": "State."
              },
              "zipCode": {
                "type": "string",
                "description": "ZIP code."
              },
              "county": {
                "type": "string",
                "description": "County."
              },
              "country": {
                "type": "string",
                "description": "Country."
              },
              "type": {
                "type": "string",
                "description": "Type of Address."
              }
            },
            "required": [
              "street",
              "city",
              "state",
              "zipCode",
              "country",
              "type"
            ],
            "example": {
              "street": "123 Main Street",
              "street2": "Suite 456",
              "city": "Sacramento",
              "state": "CA",
              "zipCode": "95814",
              "country": "United States",
              "type": "Residential"
            },
            "additionalProperties": false
          },
          "phone": {
            "type": "string",
            "description": "Daytime phone number of the officer as digits only.",
            "pattern": "^\\d+$",
            "maxLength": 40
          },
          "email": {
            "type": "string",
            "format": "email",
            "description": "email address of the officer."
          }
        },
        "required": [
          "firstName",
          "lastName",
          "role",
          "address",
          "phone",
          "email"
        ],
        "additionalProperties": false
      },
      "description": "List of Treasurers and Principal Officers, including their roles, contact information, and addresses."
    },
    "individualsAuthorizingSlateMailers": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "firstName": {
            "type": "string",
            "description": "First name of the individual authorizing slate mailers."
          },
          "lastName": {
            "type": "string",
            "description": "Last name of the individual authorizing slate mailers."
          },
          "address": {
            "type": "object",
            "description": "Detailed business or residential address",
            "properties": {
              "id": {
                "type": "string",
                "format": "uuid",
                "description": "Unique Id for the address."
              },
              "street": {
                "type": "string",
                "description": "Street address."
              },
              "street2": {
                "type": "string",
                "description": "Street address."
              },
              "city": {
                "type": "string",
                "description": "City."
              },
              "state": {
                "type": "string",
                "description": "State."
              },
              "zipCode": {
                "type": "string",
                "description": "ZIP code."
              },
              "county": {
                "type": "string",
                "description": "County."
              },
              "country": {
                "type": "string",
                "description": "Country."
              },
              "type": {
                "type": "string",
                "description": "Type of Address."
              }
            },
            "required": [
              "street",
              "city",
              "state",
              "zipCode",
              "country",
              "type"
            ],
            "example": {
              "street": "123 Main Street",
              "street2": "Suite 456",
              "city": "Sacramento",
              "state": "CA",
              "zipCode": "95814",
              "country": "United States",
              "type": "Residential"
            },
            "additionalProperties": false
          },
          "phone": {
            "type": "string",
            "description": "Business telephone number of the individual as digits only.",
            "pattern": "^\\d+$",
            "maxLength": 40
          },
          "email": {
            "type": "string",
            "format": "email",
            "description": "email address of the individual."
          }
        },
        "required": [
          "firstName",
          "lastName",
          "address",
          "phone",
          "email"
        ],
        "additionalProperties": false
      },
      "description": "List of individuals with final decision-making authority over the slate mailer''s contents."
    },
    "committeeInfo": {
      "type": "object",
      "properties": {
        "isCommittee": {
          "type": "boolean",
          "description": "Indicates if the organization is a committee under Government Code Section 82013."
        },
        "committeeDetails": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "description": "Name of the recipient committee (if applicable)."
            },
            "idNumber": {
              "type": "integer",
              "format": "int64",
              "description": "Identification number of the recipient committee."
            }
          },
          "description": "Details of the committee, if applicable.",
          "additionalProperties": false
        }
      },
      "description": "Indicates whether the organization qualifies as a committee and provides committee details if applicable.",
      "additionalProperties": false
    },
    "amendment": {
      "type": "object",
      "properties": {
        "isAmendment": {
          "type": "boolean",
          "description": "Indicates if the submission is an amendment to a previously filed form."
        },
        "supercededFilingId": {
          "type": "number",
          "format": "int64",
          "description": "The Secretary of State issued filing ID number for the filing being amended"
        }
      },
      "description": "Amendment information, if applicable.",
      "required": [
        "isAmendment",
        "supercededFilingId"
      ],
      "additionalProperties": false
    },
    "attestation": {
      "type": "object",
      "properties": {
        "firstName": {
          "type": "string",
          "description": "First Name of verification officer."
        },
        "lastName": {
          "type": "string",
          "description": "Last Name of verification officer."
        },
        "executedAt": {
          "type": "string",
          "format": "date",
          "description": "Date the statement was executed."
        },
        "executedAtCity": {
          "type": "string",
          "description": "Executed at City."
        },
        "executedAtState": {
          "type": "string",
          "description": "Executed at state."
        },
        "role": {
          "type": "string"
        },
        "signature": {
          "type": "string",
          "description": "Name of the treasurer or filer."
        }
      },
      "required": [
        "executedAt",
        "signature"
      ],
      "additionalProperties": false
    }
  },
  "required": [
    "slateMailerOrganization",
    "attestation",
    "amendment",
    "officers",
    "individualsAuthorizingSlateMailers",
    "committeeInfo"
  ],
  "additionalProperties": false
}', 'Campaign-Registration-SlateMailer'),
    (10, '', 'Campaign-Disclosure-RecipientCommittee-Short'),
    (11, '', 'Campaign-Disclosure-RecipientCommittee'),
    (12, '', 'Campaign-Disclosure-IndependentExpenditureCommittee'),
    (13, '', 'Campaign-Disclosure-MajorDonor'),
    (14, '', 'Campaign-Disclosure-IndependentExpenditureReport'),
    (15, '', 'Campaign-Disclosure-ContributionReport'),
    (16, '{
  "type": "object",
  "title": "Form 498 Schema",
  "description": "SlateMailer LatePayments",
  "additionalProperties": false,
  "required": [
    "submittedDate",
    "amendment",
    "latePaymentReceived"
  ],
  "properties": {
    "submittedDate": {
      "type": "string",
      "format": "date"
    },
    "amendment": {
      "type": "object",
      "additionalProperties": false,
      "required": [ "isAmendment" ],
      "properties": {
        "isAmendment": { "type": "boolean" },
        "supercededFilingId": { "type": "string" },
        "descriptionOfAmendment": { "type": "string" }
       }
    },
    "latePaymentReceived": {
      "type": "array",
      "items": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "transactionDate",
          "address",
          "telephoneNumber",
          "amount",
          "candidateOrMeasureDetails"
        ],
        "anyOf": [
          { "required": [ "firstName", "lastName" ] },
          { "required": [ "orgName" ] }
        ],
        "properties": {
          "firstName": { "type": "string" },
          "middleName": { "type": "string" },
          "lastName": { "type": "string" },
          "orgName": { "type": "string" },
          "idNumber": { "type": "string" },
          "transactionDate": {
            "type": "string",
            "format": "date"
          },
          "address": {
            "type": "object",
            "additionalProperties": false,
            "required": [
              "street",
              "city",
              "state",
              "zipCode",
              "country",
              "county",
              "type"
            ],
            "properties": {
              "street": { "type": "string" },
              "street2": { "type": "string" },
              "city": { "type": "string" },
              "state": { "type": "string" },
              "zipCode": { "type": "string" },
              "county": { "type": "string" },
              "country": { "type": "string" },
              "type": { "type": "string" }
            }
          },
          "mailingAddress": {
            "type": "object",
            "additionalProperties": false,
            "required": [
              "street",
              "city",
              "state",
              "zipCode",
              "country",
              "county",
              "type"
            ],
            "properties": {
              "street": { "type": "string" },
              "street2": { "type": "string" },
              "city": { "type": "string" },
              "state": { "type": "string" },
              "zipCode": { "type": "string" },
              "county": { "type": "string" },
              "country": { "type": "string" },
              "type": { "type": "string" }
            }
          },
          "telephoneNumber": {
            "type": "object",
            "additionalProperties": false,
            "required": [
              "countryCode",
              "number"
            ],
            "properties": {
              "countryCode": {
                "type": "string",
                "description": "Country code of the telephone number."
              },
              "number": {
                "type": "string",
                "description": "Telephone number."
              },
              "extension": {
                "type": "string",
                "description": "Extension of the telephone number."
              }
            }
          },
          "faxNumber": {
            "type": "object",
            "additionalProperties": false,
            "required": [
              "countryCode",
              "number"
            ],
            "properties": {
              "countryCode": {
                "type": "string",
                "description": "Country code of the telephone number."
              },
              "number": {
                "type": "string",
                "description": "Telephone number."
              },
              "extension": {
                "type": "string",
                "description": "Extension of the telephone number."
              }
            }
          },
          "email": {
            "type": "string",
            "format": "email"
          },
          "amount": { "type": "number" },
          "occupation": { "type": "string" },
          "employer": { "type": "string" },
          "candidateOrMeasureDetails": {
            "type": "array",
            "items": {
              "type": "object",
              "additionalProperties": false,
              "required": [
                "officeSought",
                "jurisdiction",
                "position",
                "amountAttributed"
              ],
              "anyOf": [
                { "required": [ "firstName", "lastName" ] },
                { "required": [ "name" ] }
              ],
              "properties": {
                "firstName": { "type": "string" },
                "middleName": { "type": "string" },
                "lastName": { "type": "string" },
                "name": { "type": "string" },
                "officeSought": { "type": "string" },
                "jurisdiction": { "type": "string" },
                "position": { "type": "string" },
                "amountAttributed": { "type": "number" }
              }
            }
          }
        }
      }
    }
  }
}', 'Campaign-Disclosure-SlateMailer-LatePayments'),
    (17, '', 'Campaign-PaidSpokespersonReport'),
    (18, '', 'Lobbying-Registration-LobbyingFirm'),
    (19, '', 'Lobbying-ActivityAuthorization-LobbyingFirm'),
    (20, '{
  "title": "Form 603 Schema",
  "description": "Lobbyist Employer or Lobbying Coalition Registration Statement",
  "type": "object",
  "properties": {
    "filerDetails": {
      "type": "object",
      "properties": {
        "id": {
          "type": "string",
          "description": "Unique ID for the filer details."
        },
        "name": {
          "type": "object",
          "required": [
            "firstName",
            "lastName"
          ],
          "properties": {
            "firstName": {
              "type": "string"
            },
            "middleName": {
              "type": "string"
            },
            "lastName": {
              "type": "string"
            }
          },
          "additionalProperties": false
        },
        "businessAddress": {
          "type": "object",
          "description": "Detailed business or residential address",
          "properties": {
            "id": {
              "type": "string",
              "format": "uuid",
              "description": "Unique Id for the address."
            },
            "street": {
              "type": "string",
              "description": "Street address."
            },
            "street2": {
              "type": "string",
              "description": "Street address."
            },
            "city": {
              "type": "string",
              "description": "City."
            },
            "state": {
              "type": "string",
              "description": "State."
            },
            "zipCode": {
              "type": "string",
              "description": "ZIP code."
            },
            "county": {
              "type": "string",
              "description": "County."
            },
            "country": {
              "type": "string",
              "description": "Country."
            },
            "type": {
              "type": "string",
              "description": "Type of Address."
            }
          },
          "required": [
            "street",
            "city",
            "state",
            "zipCode",
            "country",
            "type"
          ],
          "example": {
            "street": "123 Main Street",
            "street2": "Suite 456",
            "city": "Sacramento",
            "state": "CA",
            "zipCode": "95814",
            "country": "United States",
            "type": "Residential"
          },
          "additionalProperties": false
        },
        "mailingAddress": {
          "type": "object",
          "description": "Detailed business or residential address",
          "properties": {
            "id": {
              "type": "string",
              "format": "uuid",
              "description": "Unique Id for the address."
            },
            "street": {
              "type": "string",
              "description": "Street address."
            },
            "street2": {
              "type": "string",
              "description": "Street address."
            },
            "city": {
              "type": "string",
              "description": "City."
            },
            "state": {
              "type": "string",
              "description": "State."
            },
            "zipCode": {
              "type": "string",
              "description": "ZIP code."
            },
            "county": {
              "type": "string",
              "description": "County."
            },
            "country": {
              "type": "string",
              "description": "Country."
            },
            "type": {
              "type": "string",
              "description": "Type of Address."
            }
          },
          "required": [
            "street",
            "city",
            "state",
            "zipCode",
            "country",
            "type"
          ],
          "example": {
            "street": "123 Main Street",
            "street2": "Suite 456",
            "city": "Sacramento",
            "state": "CA",
            "zipCode": "95814",
            "country": "United States",
            "type": "Residential"
          },
          "additionalProperties": false
        },
        "phone": {
          "type": "object",
          "additionalProperties": false,
          "required": [
            "countryCode",
            "number"
          ],
          "properties": {
            "countryCode": {
              "type": "string",
              "description": "Country code of the telephone number."
            },
            "number": {
              "type": "string",
              "description": "Telephone number."
            },
            "extension": {
              "type": "string",
              "description": "Extension of the telephone number."
            }
          }
        },
        "fax": {
          "type": "object",
          "additionalProperties": false,
          "required": [
            "countryCode",
            "number"
          ],
          "properties": {
            "countryCode": {
              "type": "string",
              "description": "Country code of the telephone number."
            },
            "number": {
              "type": "string",
              "description": "Telephone number."
            },
            "extension": {
              "type": "string",
              "description": "Extension of the telephone number."
            }
          }
        },
        "email": {
          "type": "string",
          "format": "email",
          "description": "Filer email address."
        }
      },
      "required": [
        "name",
        "businessAddress",
        "phone"
      ],
      "additionalProperties": false
    },
    "dateQualified": {
      "type": "string",
      "format": "date",
      "description": "Date the filer qualified as a lobbyist employer or lobbying coalition."
    },
    "isLobbyingCoalition": {
      "type": "boolean",
      "description": "Describe the Registration type either as LobbyistEmployer or LobbyingCoalition."
    },
    "lobbyists": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "legislativeSessionId": {
            "type": "number",
            "format": "int64",
            "description": "The Legislative Session Id."
          },
          "lobbyistDetails": {
            "type": "object",
            "properties": {
              "firstName": {
                "type": "string",
                "description": "First name of the lobbyist."
              },
              "lastName": {
                "type": "string",
                "description": "Last name of the lobbyist."
              },
              "middleInitial": {
                "type": "string",
                "description": "Middle initial of the lobbyist (optional)."
              },
              "dateQualified": {
                "type": "string",
                "format": "date",
                "description": "Date the lobbyist qualified as a lobbyist."
              },
              "isLobbyistPlacementAgent": {
                "type": "boolean",
                "description": "Indicates if the lobbyist is a placement agent."
              },
              "telephoneNumber": {
                "description": "Contact telephone number of the lobbyist as digits only.",
                "type": "object",
                "properties": {
                  "countryCode": {
                    "type": "string",
                    "description": "Country Code.",
                    "default": "+1"
                  },
                  "number": {
                    "type": "string",
                    "description": "Phone Number with area code, digits only.",
                    "maxLength": 20
                  },
                  "extension": {
                    "type": "string",
                    "description": "Phone Number Extension.",
                    "maxLength": 20
                  }
                },
                "required": [
                  "countryCode",
                  "number"
                ],
                "additionalProperties": false
              },
              "faxNumber": {
                "description": "Contact telephone number of the lobbyist as digits only.",
                "type": "object",
                "properties": {
                  "countryCode": {
                    "type": "string",
                    "description": "Country Code.",
                    "default": "+1"
                  },
                  "number": {
                    "type": "string",
                    "description": "Phone Number with area code, digits only.",
                    "maxLength": 20
                  },
                  "extension": {
                    "type": "string",
                    "description": "Phone Number Extension.",
                    "maxLength": 20
                  }
                },
                "required": [
                  "countryCode",
                  "number"
                ],
                "additionalProperties": false
              },
              "email": {
                "type": "string",
                "format": "email",
                "description": "Email address of the lobbyist."
              },
              "lobbyistEmployerOrLobbyingFirmId": {
                "type": "string",
                "description": "Name of the employer or lobbying firm."
              }
            },
            "required": [
              "firstName",
              "lastName",
              "dateQualified",
              "isLobbyistPlacementAgent",
              "telephoneNumber",
              "email"
            ],
            "additionalProperties": false
          },
          "businessAddress": {
            "type": "object",
            "description": "Detailed business or residential address",
            "properties": {
              "id": {
                "type": "string",
                "format": "uuid",
                "description": "Unique Id for the address."
              },
              "street": {
                "type": "string",
                "description": "Street address."
              },
              "street2": {
                "type": "string",
                "description": "Street address."
              },
              "city": {
                "type": "string",
                "description": "City."
              },
              "state": {
                "type": "string",
                "description": "State."
              },
              "zipCode": {
                "type": "string",
                "description": "ZIP code."
              },
              "county": {
                "type": "string",
                "description": "County."
              },
              "country": {
                "type": "string",
                "description": "Country."
              },
              "type": {
                "type": "string",
                "description": "Type of Address."
              }
            },
            "required": [
              "street",
              "city",
              "state",
              "zipCode",
              "country",
              "type"
            ],
            "example": {
              "street": "123 Main Street",
              "street2": "Suite 456",
              "city": "Sacramento",
              "state": "CA",
              "zipCode": "95814",
              "country": "United States",
              "type": "Residential"
            },
            "additionalProperties": false
          },
          "mailingAddress": {
            "type": "object",
            "description": "Detailed business or residential address",
            "properties": {
              "id": {
                "type": "string",
                "format": "uuid",
                "description": "Unique Id for the address."
              },
              "street": {
                "type": "string",
                "description": "Street address."
              },
              "street2": {
                "type": "string",
                "description": "Street address."
              },
              "city": {
                "type": "string",
                "description": "City."
              },
              "state": {
                "type": "string",
                "description": "State."
              },
              "zipCode": {
                "type": "string",
                "description": "ZIP code."
              },
              "county": {
                "type": "string",
                "description": "County."
              },
              "country": {
                "type": "string",
                "description": "Country."
              },
              "type": {
                "type": "string",
                "description": "Type of Address."
              }
            },
            "required": [
              "street",
              "city",
              "state",
              "zipCode",
              "country",
              "type"
            ],
            "example": {
              "street": "123 Main Street",
              "street2": "Suite 456",
              "city": "Sacramento",
              "state": "CA",
              "zipCode": "95814",
              "country": "United States",
              "type": "Residential"
            },
            "additionalProperties": false
          },
          "ethicsOrientation": {
            "type": "object",
            "properties": {
              "isCourseTaken": {
                "type": "boolean",
                "description": "Indicates if the course was not taken within the past 12 months."
              },
              "courseCompletedDate": {
                "type": "string",
                "format": "date",
                "description": "Date of ethics orientation course completion (if applicable)."
              }
            },
            "additionalProperties": false
          },
          "agenciesLobbied": {
            "type": "object",
            "properties": {
              "willLobbyFromRegistration": {
                "type": "boolean",
                "description": "Indicates if the lobbyist will lobby the agencies listed in the Lobbyist Employer or Lobbying Firm Registration Statement (Form 601/603)."
              },
              "stateLegislature": {
                "type": "boolean",
                "description": "Indicates if the lobbyist will lobby the State Legislature."
              },
              "stateAgencies": {
                "type": "array",
                "items": {
                  "type": "number",
                  "format": "int64"
                },
                "description": "List of specific state agencies the lobbyist will lobby."
              }
            },
            "additionalProperties": false
          },
          "amendment": {
            "type": "object",
            "properties": {
              "isAmendment": {
                "type": "boolean",
                "description": "Indicates if the submission is an amendment to a previously filed form."
              },
              "supercededFilingId": {
                "type": "number",
                "format": "int64",
                "description": "The Secretary of State issued filing ID number for the filing being amended"
              }
            },
            "description": "Amendment information, if applicable.",
            "required": [
              "isAmendment",
              "supercededFilingId"
            ],
            "additionalProperties": false
          },
          "filingType": {
            "type": "object",
            "properties": {
              "amendment": {
                "type": "boolean"
              },
              "isPreElectionStatement": {
                "type": "boolean"
              },
              "isQuarterlyStatement": {
                "type": "boolean"
              },
              "isSemiAnnualStatement": {
                "type": "boolean"
              },
              "isSpecialOddYearReport": {
                "type": "boolean"
              },
              "termination": {
                "type": "boolean"
              },
              "amendmentNumber": {
                "type": "string"
              },
              "initialFilingId": {
                "type": "string"
              },
              "explanation": {
                "type": "string"
              }
            },
            "additionalProperties": false
          },
          "attestation": {
            "type": "object",
            "properties": {
              "firstName": {
                "type": "string",
                "description": "First Name of verification officer."
              },
              "lastName": {
                "type": "string",
                "description": "Last Name of verification officer."
              },
              "executedAt": {
                "type": "string",
                "format": "date",
                "description": "Date the statement was executed."
              },
              "executedAtCity": {
                "type": "string",
                "description": "Executed at City."
              },
              "executedAtState": {
                "type": "string",
                "description": "Executed at state."
              },
              "role": {
                "type": "string"
              },
              "signature": {
                "type": "string",
                "description": "Name of the treasurer or filer."
              }
            },
            "required": [
              "executedAt",
              "signature"
            ],
            "additionalProperties": false
          }
        },
        "required": [
          "legislativeSessionId",
          "lobbyistDetails",
          "businessAddress",
          "mailingAddress",
          "ethicsOrientation",
          "agenciesLobbied"
        ],
        "additionalProperties": false
      },
      "description": "List of in-house employee lobbyists (604) and lobbying firms."
    },
    "lobbyingFirms": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "name": {
            "type": "string",
            "description": "The name of the lobbying firm."
          }
        },
        "required": [
          "name"
        ],
        "additionalProperties": false
      },
      "description": "List of lobbying firms."
    },
    "stateAgenciesInfluenced": {
      "type": "array",
      "items": {
        "type": "string"
      },
      "description": "List of state agencies whose actions will be influenced."
    },
    "lobbyingInterest": {
      "type": "string",
      "description": "Lobbying interest name."
    },
    "natureAndInterests": {
      "type": "array",
      "items": {
        "oneOf": [
          {
            "type": "object",
            "title": "Individual",
            "properties": {
              "individual": {
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "natureAndInterestEmployerName": {
                      "type": "string"
                    },
                    "employerAddress": {
                      "type": "object",
                      "description": "Detailed business or residential address",
                      "properties": {
                        "id": {
                          "type": "string",
                          "format": "uuid",
                          "description": "Unique Id for the address."
                        },
                        "street": {
                          "type": "string",
                          "description": "Street address."
                        },
                        "street2": {
                          "type": "string",
                          "description": "Street address."
                        },
                        "city": {
                          "type": "string",
                          "description": "City."
                        },
                        "state": {
                          "type": "string",
                          "description": "State."
                        },
                        "zipCode": {
                          "type": "string",
                          "description": "ZIP code."
                        },
                        "county": {
                          "type": "string",
                          "description": "County."
                        },
                        "country": {
                          "type": "string",
                          "description": "Country."
                        },
                        "type": {
                          "type": "string",
                          "description": "Type of Address."
                        }
                      },
                      "required": [
                        "street",
                        "city",
                        "state",
                        "zipCode",
                        "country",
                        "type"
                      ],
                      "example": {
                        "street": "123 Main Street",
                        "street2": "Suite 456",
                        "city": "Sacramento",
                        "state": "CA",
                        "zipCode": "95814",
                        "country": "United States",
                        "type": "Residential"
                      }
                    },
                    "businessActivity": {
                      "type": "string",
                      "description": "Description of business activity in which you or your employer are engaged."
                    }
                  }
                }
              },
              "industryGroupClassification": {
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "industryGroupClassifications": {
                      "type": "string",
                      "description": "Industry group sub-categories classification and Industry group classification."
                    },
                    "industryGroupOtherText": {
                      "type": "string",
                      "description": "The description of the industry group when other is selected."
                    }
                  }
                }
              }
            }
          },
          {
            "type": "object",
            "title": "BusinessEntity",
            "properties": {
              "businessEntity": {
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "businessDescription": {
                      "type": "string",
                      "description": "Description of business activity in which engaged."
                    }
                  }
                }
              },
              "industryGroupClassification": {
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "industryGroupClassifications": {
                      "type": "string",
                      "description": "Industry group sub-categories classification and Industry group classification."
                    },
                    "industryGroupOtherText": {
                      "type": "string",
                      "description": "The description of the industry group when other is selected."
                    }
                  }
                }
              }
            }
          },
          {
            "type": "object",
            "title": "IndustryTradeOrProfessionalAssociation",
            "properties": {
              "industryTradeOrProfessionalAssociation": {
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "industryDescription": {
                      "type": "string",
                      "description": "Description of industry, trade or profession represented."
                    },
                    "industryPortion": {
                      "type": "string",
                      "description": "Specific description of any portion or faction of the industry, trade, or profession which the association exclusively or primarily represents."
                    },
                    "numberOfMembers": {
                      "type": "string"
                    }
                  }
                }
              },
              "industryGroupClassification": {
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "industryGroupClassifications": {
                      "type": "string",
                      "description": "Industry group sub-categories classification and Industry group classification."
                    },
                    "industryGroupOtherText": {
                      "type": "string",
                      "description": "The description of the industry group when other is selected."
                    }
                  }
                }
              }
            }
          },
          {
            "type": "object",
            "title": "Other",
            "properties": {
              "other": {
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "statementOfNatureAndPurposes": {
                      "type": "string",
                      "description": "Statement of nature and purposes."
                    },
                    "commonInterest": {
                      "type": "string",
                      "description": "Description of any trade, profession, or other group with a common economic interest which is principally represented or from which membership or financial support is principally derived."
                    }
                  }
                }
              },
              "industryGroupClassification": {
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "industryGroupClassifications": {
                      "type": "string",
                      "description": "Industry group sub-categories classification and Industry group classification."
                    },
                    "industryGroupOtherText": {
                      "type": "string",
                      "description": "The description of the industry group when other is selected."
                    }
                  }
                }
              }
            }
          }
        ]
      }
    },
    "filingType": {
      "type": "object",
      "properties": {
        "amendment": {
          "type": "boolean"
        },
        "isPreElectionStatement": {
          "type": "boolean"
        },
        "isQuarterlyStatement": {
          "type": "boolean"
        },
        "isSemiAnnualStatement": {
          "type": "boolean"
        },
        "isSpecialOddYearReport": {
          "type": "boolean"
        },
        "termination": {
          "type": "boolean"
        },
        "amendmentNumber": {
          "type": "string"
        },
        "initialFilingId": {
          "type": "string"
        },
        "explanation": {
          "type": "string"
        }
      },
      "additionalProperties": false
    },
    "attestation": {
      "type": "object",
      "properties": {
        "firstName": {
          "type": "string",
          "description": "First Name of verification officer."
        },
        "lastName": {
          "type": "string",
          "description": "Last Name of verification officer."
        },
        "executedAt": {
          "type": "string",
          "format": "date",
          "description": "Date the statement was executed."
        },
        "executedAtCity": {
          "type": "string",
          "description": "Executed at City."
        },
        "executedAtState": {
          "type": "string",
          "description": "Executed at state."
        },
        "role": {
          "type": "string"
        },
        "signature": {
          "type": "string",
          "description": "Name of the treasurer or filer."
        }
      },
      "required": [
        "executedAt",
        "signature"
      ],
      "additionalProperties": false
    }
  },
  "required": [
    "filerDetails",
    "lobbyists",
    "natureAndInterests",
    "stateAgenciesInfluenced",
    "lobbyingInterest",
    "attestation",
    "isLobbyingCoalition"
  ]
}
', 'Lobbying-Registration-LobbyingCoalition'),
    (21, '', 'Lobbying-Registration-LobbyistEmployer'),
    (22, '{
  "title": "Form 604 Schema",
  "description": "Lobbyist Certification Statement",
  "type": "object",
  "additionalProperties": false,
  "required": [
    "legislativeSessionId",
    "lobbyistDetails",
    "businessAddress",
    "mailingAddress",
    "ethicsOrientation",
    "agenciesLobbied"
  ],
  "properties": {
    "legislativeSessionId": {
      "type": "number",
      "format": "int64",
      "description": "The Legislative Session Id."
    },
    "lobbyistDetails": {
      "type": "object",
      "additionalProperties": false,
      "required": [
        "firstName",
        "lastName",
        "email",
        "telephoneNumber",
        "dateQualified",
        "isLobbyistPlacementAgent"
      ],
      "properties": {
        "firstName": {
          "type": "string",
          "description": "First name of the lobbyist."
        },
        "lastName": {
          "type": "string",
          "description": "Last name of the lobbyist."
        },
        "middleInitial": {
          "type": "string",
          "description": "Middle initial of the lobbyist (optional)."
        },
        "dateQualified": {
          "type": "string",
          "format": "date",
          "description": "Date the lobbyist qualified as a lobbyist."
        },
        "isLobbyistPlacementAgent": {
          "type": "boolean",
          "description": "Indicates if the lobbyist is a placement agent"
        },
        "telephoneNumber": {
          "type": "object",
          "additionalProperties": false,
          "required": [
            "countryCode",
            "number"
          ],
          "properties": {
            "countryCode": {
              "type": "string",
              "description": "Country code of the telephone number."
            },
            "number": {
              "type": "string",
              "description": "Telephone number."
            },
            "extension": {
              "type": "string",
              "description": "Extension of the telephone number."
            }
          }
        },
        "faxNumber": {
          "type": "object",
          "additionalProperties": false,
          "required": [
            "countryCode",
            "number"
          ],
          "properties": {
            "countryCode": {
              "type": "string",
              "description": "Country code of the telephone number."
            },
            "number": {
              "type": "string",
              "description": "Telephone number."
            },
            "extension": {
              "type": "string",
              "description": "Extension of the telephone number."
            }
          }
        },
        "email": {
          "type": "string",
          "format": "email",
          "description": "Email address of the lobbyist."
        },
        "lobbyistEmployerOrLobbyingFirmId": {
          "type": "string",
          "description": "Name of the employer or lobbying firm."
        }
      }
    },
    "businessAddress": {
      "type": "object",
      "additionalProperties": false,
      "description": "Detailed business or residential address",
      "required": [
        "street",
        "city",
        "state",
        "zipCode",
        "country",
        "type"
      ],
      "properties": {
        "id": {
          "type": "string",
          "format": "uuid",
          "description": "Unique Id for the address."
        },
        "street": {
          "type": "string",
          "description": "Street address."
        },
        "street2": {
          "type": "string",
          "description": "Street address."
        },
        "city": {
          "type": "string",
          "description": "City."
        },
        "state": {
          "type": "string",
          "description": "State."
        },
        "zipCode": {
          "type": "string",
          "description": "ZIP code."
        },
        "county": {
          "type": "string",
          "description": "County."
        },
        "country": {
          "type": "string",
          "description": "Country."
        },
        "type": {
          "type": "string",
          "description": "Type of Address."
        }
      }
    },
    "mailingAddress": {
      "type": "object",
      "additionalProperties": false,
      "description": "Detailed business or residential address",
      "required": [
        "street",
        "city",
        "state",
        "zipCode",
        "country",
        "type"
      ],
      "properties": {
        "id": {
          "type": "string",
          "format": "uuid",
          "description": "Unique Id for the address."
        },
        "street": {
          "type": "string",
          "description": "Street address."
        },
        "street2": {
          "type": "string",
          "description": "Street address."
        },
        "city": {
          "type": "string",
          "description": "City."
        },
        "state": {
          "type": "string",
          "description": "State."
        },
        "zipCode": {
          "type": "string",
          "description": "ZIP code."
        },
        "county": {
          "type": "string",
          "description": "County."
        },
        "country": {
          "type": "string",
          "description": "Country."
        },
        "type": {
          "type": "string",
          "description": "Type of Address."
        }
      }
    },
    "ethicsOrientation": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "isCourseTaken": {
          "type": "boolean",
          "description": "Indicates if the course was not taken within the past 12 months."
        },
        "courseCompletedDate": {
          "type": "string",
          "format": "date",
          "description": "Date of ethics orientation course completion (if applicable)."
        }
      }
    },
    "agenciesLobbied": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "willLobbyFromRegistration": {
          "type": "boolean",
          "description": "Indicates if the lobbyist will lobby the agencies listed in the Lobbyist Employer or Lobbying Firm Registration Statement (Form 601/603)."
        },
        "stateLegislature": {
          "type": "boolean",
          "description": "Indicates if the lobbyist will lobby the State Legislature."
        },
        "stateAgencies": {
          "type": "array",
          "description": "List of specific state agencies the lobbyist will lobby.",
          "items": {
            "type": "number",
            "format": "int64"
          }
        }
      }
    },
    "amendment": {
      "type": "object",
      "additionalProperties": false,
      "description": "Amendment information, if applicable.",
      "required": [
        "isAmendment",
        "supercededFilingId"
      ],
      "properties": {
        "isAmendment": {
          "type": "boolean",
          "description": "Indicates if the submission is an amendment to a previously filed form."
        },
        "supercededFilingId": {
          "type": "string",
          "description": "The Secretary of State issued filing ID number for the filing being amended"
        }
      }
    },
    "attestation": {
      "type": "object",
      "additionalProperties": false,
      "required": [
        "executedAt",
        "signature"
      ],
      "properties": {
        "firstName": {
          "type": "string",
          "description": "First Name of verification officer."
        },
        "lastName": {
          "type": "string",
          "description": "Last Name of verification officer."
        },
        "executedAt": {
          "type": "string",
          "format": "date",
          "description": "Date the statement was executed."
        },
        "executedAtCity": {
          "type": "string",
          "description": "Executed at City."
        },
        "executedAtState": {
          "type": "string",
          "description": "Executed at state."
        },
        "role": {
          "type": "string"
        },
        "signature": {
          "type": "string",
          "description": "Name of the treasurer or filer."
        }
      }
    }
  }
}', 'Lobbying-Certification-Lobbyist'),
    (23, '', 'Lobbying-Registration-Amendment'),
    (24, '{
  "title": "NoticeOfWithdrawal",
  "type": "object",
  "additionalProperties": false,
  "properties": {
    "withdrawalDetails": {
      "type": "object",
      "properties": {
        "effectiveDate": {
          "type": "string",
          "format": "date"
        },
        "legislativeSession": {
          "type": "string"
        }
      },
      "required": [
        "effectiveDate"
      ]
    },
    "attestation": {
      "type": "object",
      "properties": {
        "firstName": {
          "type": "string"
        },
        "lastName": {
          "type": "string"
        },
        "executedAt": {
          "type": "string",
          "format": "date"
        },
        "executedAtCity": {
          "type": "string"
        },
        "executedAtState": {
          "type": "string"
        },
        "role": {
          "type": "string"
        },
        "signature": {
          "type": "string"
        }
      },
      "required": [
        "executedAt",
        "signature"
      ]
    }
  },
  "required": [
    "withdrawalDetails",
    "attestation"
  ]
}', 'Lobbying-NoticeOfWithdrawal'),
    (25, '{
  "title": "Form 615 Schema",
  "description": "Lobbyist Report",
  "type": "object",
  "required": [
    "reportingPeriod",
    "attestation",
    "nothingToReportActivityExpense",
    "activityExpenses",
    "nothingToReportCampaignContributions",
    "campaignContributions"
  ],
  "allOf": [
    {
      "if": {
        "properties": {
          "nothingToReportActivityExpense": { "const": false }
        }
      },
      "then": {
        "required": [ "activityExpenses" ]
      }
    },
    {
      "if": {
        "properties": {
          "nothingToReportCampaignContributions": { "const": false }
        }
      },
      "then": {
        "required": [ "campaignContributions" ]
      }
    }
  ],
  "additionalProperties": false,
  "properties": {
    "amendment": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "isAmendment": {
          "type": "boolean"
        },
        "supercededFilingId": {
          "type": "string"
        },
        "descriptionOfAmendment": {
          "type": "string"
        }
      }
    },
    "reportingPeriod": {
      "type": "object",
      "additionalProperties": false,
      "required": [ "startDate", "endDate" ],
      "properties": {
        "startDate": {
          "type": "string",
          "format": "date"
        },
        "endDate": {
          "type": "string",
          "format": "date"
        }
      }
    },
    "attestation": {
      "type": "object",
      "required": [ "executedAt", "executedAtCity", "executedAtState", "signature" ],
      "additionalProperties": false,
      "properties": {
        "firstName": { "type": "string" },
        "middleName": { "type": "string" },
        "lastName": { "type": "string" },
        "executedAt": {
          "type": "string",
          "format": "date"
        },
        "executedAtCity": { "type": "string" },
        "executedAtState": { "type": "string" },
        "role": { "type": "string" },
        "signature": { "type": "string" }
      }
    },
    "nothingToReportActivityExpense": { "type": "boolean" },
    "contacts": {
      "type": "array",
      "items": {
        "type": "object",
        "additionalProperties": false,
        "required": [ "contactTypeId", "externalId", "contactInfo" ],
        "properties": {
          "contactTypeId": { "type": "number" },
          "externalId": { "type": "string" },
          "contactInfo": {
            "oneOf": [
              {
                "type": "object",
                "properties": {
                  "personName": {
                    "type": "object",
                    "properties": {
                      "firstName": { "type": "string" },
                      "lastName": { "type": "string" }
                    },
                    "required": [ "firstName", "lastName" ],
                    "additionalProperties": false
                  },
                  "phone": { "type": "string" },
                  "email": {
                    "type": "string",
                    "format": "email"
                  },
                  "address": {
                    "type": "object",
                    "properties": {
                      "street": { "type": "string" },
                      "street2": { "type": "string" },
                      "city": { "type": "string" },
                      "state": { "type": "string" },
                      "zipCode": { "type": "string" },
                      "county": { "type": "string" },
                      "country": { "type": "string" },
                      "type": { "type": "string" }
                    },
                    "required": [ "street", "city", "state", "zipCode" ],
                    "additionalProperties": false
                  }
                },
                "required": [ "personName", "address" ],
                "additionalProperties": false
              },
              {
                "type": "object",
                "properties": {
                  "organizationName": { "type": "string" },
                  "phone": { "type": "string" },
                  "email": {
                    "type": "string",
                    "format": "email"
                  },
                  "address": {
                    "type": "object",
                    "properties": {
                      "street": { "type": "string" },
                      "street2": { "type": "string" },
                      "city": { "type": "string" },
                      "state": { "type": "string" },
                      "zipCode": { "type": "string" },
                      "county": { "type": "string" },
                      "country": { "type": "string" },
                      "type": { "type": "string" }
                    },
                    "required": [ "street", "city", "state", "zipCode" ],
                    "additionalProperties": false
                  }
                },
                "required": [ "organizationName", "address" ],
                "additionalProperties": false
              }
            ]
          }
        }
      }
    },
    "activityExpenses": {
      "type": "array",
      "items": {
        "type": "object",
        "required": [
          "date",
          "filerContactId",
          "isActivityChargedToCreditCard",
          "cardName",
          "reportablePersons",
          "activityExpenseTypeId",
          "total"
        ],
        "allOf": [
          {
            "if": {
              "properties": {
                "isActivityChargedToCreditCard": { "const": true }
              }
            },
            "then": {
              "required": [ "cardName" ]
            }
          }
        ],
        "additionalProperties": false,
        "properties": {
          "date": {
            "type": "string",
            "format": "date"
          },
          "filerContactId": { "type": "string" },
          "isActivityChargedToCreditCard": { "type": "boolean" },
          "cardName": { "type": "string" },
          "reportablePersons": {
            "type": "array",
            "items": {
              "type": "object",
              "additionalProperties": false,
              "required": [
                "reportablePersonName",
                "officialPositionId",
                "agencyId",
                "eachItemAmount"
              ],
              "properties": {
                "reportablePersonName": {
                  "type": "object",
                  "properties": {
                    "firstName": { "type": "string" },
                    "lastName": { "type": "string" }
                  },
                  "required": [ "firstName", "lastName" ]
                },
                "officialPositionId": { "type": "number" },
                "otherOfficialPosition": { "type": "string" },
                "agencyId": { "type": "number" },
                "otherAgency": { "type": "string" },
                "eachItemAmount": { "type": "number" }
              }
            }
          },
          "activityExpenseTypeId": { "type": "number" },
          "otherExpenses": { "type": "string" },
          "additionalInformation": { "type": "string" },
          "total": { "type": "number" }
        }
      }
    },
    "nothingToReportCampaignContributions": { "type": "boolean" },
    "campaignContributions": {
      "type": "array",
      "items": {
        "type": "object",
        "required": [
          "date",
          "amount"
        ],
        "anyOf": [
          {
            "required": [ "nameOfRecipient" ]
          },
          {
            "required": [ "recipientCommitteeID" ]
          }
        ],
        "additionalProperties": false,
        "properties": {
          "date": {
            "type": "string",
            "format": "date"
          },
          "separateAccountName": { "type": "string" },
          "nonFilerContributorName": { "type": "string" },
          "nameOfRecipient": { "type": "string" },
          "recipientCommitteeID": { "type": "string" },
          "amount": { "type": "number" }
        }
      }
    }
  }
}', 'Lobbying-Disclosure-Lobbyist'),
    (26, '', 'Lobbying-Disclosure-LobbyingFirm'),
    (27, '{
  "title": "Form 630",
  "type": "object",
  "additionalProperties": false,
  "properties": {
    "periodCovered": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "startDate": {
          "type": "string",
          "format": "date"
        },
        "endDate": {
          "type": "string",
          "format": "date"
        }
      },
      "required": [ "startDate", "endDate" ]
    },
    "payments": {
      "type": "array",
      "items": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "coalitionName": {
            "type": "string"
          },
          "coalitionAddress": {
            "$ref": "#/definitions/Address"
          },
          "amountReceivedThisPeriod": {
            "type": "number"
          },
          "cumulativeAmountPaid": {
            "type": "number"
          }
        },
        "required": [
          "coalitionName",
          "coalitionAddress",
          "amountReceivedThisPeriod"
        ]
      }
    }
  },
  "required": [ "periodCovered", "payments" ],
  "definitions": {
    "Address": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "street": { "type": "string" },
        "street2": { "type": "string" },
        "city": { "type": "string" },
        "state": { "type": "string" },
        "zipCode": { "type": "string" },
        "county": { "type": "string" }
      },
      "required": [ "street", "city", "state", "zipCode" ]
    }
  }
}', 'Lobbying-Activity-PaymentsMade'),
    (28, '{
  "title": "Form 635-C Schema",
  "description": "Lobbying Coalition Payment Reporting",
  "type": "object",
  "additionalProperties": false,
  "properties": {
    "periodFrom": {
      "type": "string",
      "format": "date",
      "description": "Start date of the reporting period in `YYYY-MM-DD` format."
    },
    "periodTo": {
      "type": "string",
      "format": "date",
      "description": "End date of the reporting period in `YYYY-MM-DD` format."
    },
    "payments": {
      "type": "array",
      "description": "List of payments made.",
      "items": {
        "type": "object",
        "required": [
          "name",
          "address",
          "amountReceived"
        ],
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "object",
            "description": "Complete name components of an individual",
            "required": [
              "firstName",
              "lastName"
            ],
            "additionalProperties": false,
            "properties": {
              "firstName": {
                "type": "string",
                "description": "First name of the individual",
                "minLength": 1
              },
              "middleName": {
                "type": [
                  "string",
                  "null"
                ],
                "description": "Middle name(s) of the individual"
              },
              "lastName": {
                "type": "string",
                "description": "Last name of the individual",
                "minLength": 1
              },
              "suffix": {
                "type": [
                  "string",
                  "null"
                ],
                "description": "Name suffix (e.g., Jr., Sr., III)"
              }
            }
          },
          "address": {
            "type": "object",
            "description": "Detailed business or residential address",
            "required": [
              "street",
              "city",
              "state",
              "zipCode"
            ],
            "additionalProperties": false,
            "properties": {
              "id": {
                "type": "string",
                "format": "uuid",
                "description": "Unique Id for the address."
              },
              "street": {
                "type": "string",
                "description": "Street address."
              },
              "street2": {
                "type": "string",
                "description": "Street address."
              },
              "city": {
                "type": "string",
                "description": "City."
              },
              "state": {
                "type": "string",
                "description": "State."
              },
              "zipCode": {
                "type": "string",
                "description": "ZIP code."
              },
              "county": {
                "type": "string",
                "description": "County."
              }
            }
          },
          "amountReceived": {
            "type": "number",
            "format": "float",
            "description": "Amount received from the member during the reporting period."
          }
        }
      }
    }
  }
}', 'Lobbying-Disclosure-PaymentsReceived'),
    (29, '', 'Lobbying-Report-LobbyistEmployer'),
    (30, '', 'Lobbying-Report-LobbyingCoalition'),
    (31, '', 'Lobbying-Disclosure-OtherPaymentsToInfluence'),
    (32, '', 'Lobbying-Report-5000DollarFiler'),
    (33, '', 'Lobbying-Disclosure-Amendment'),
    (34, '', 'Campaign-Disclosure-IssueAdvocacy'),
    (35, '{
  "title": "Form 409 Schema",
  "description": "Limited Liability Company  Statement of Members",
  "type": "object",
  "additionalProperties": false,
  "properties": {
    "amendment": {
      "type": "object",
      "additionalProperties": false,
      "description": "Amendment information, if applicable.",
      "required": [
        "isAmendment",
        "supercededFilingId"
      ],
      "properties": {
        "isAmendment": {
          "type": "boolean",
          "description": "Indicates if the submission is an amendment to a previously filed form."
        },
        "supercededFilingId": {
          "type": "string",
          "description": "The Secretary of State issued filing ID number for the filing being amended"
        }
      }
    },
    "llcDateQualificationThresholdMet": {
      "type": "string",
      "format": "date",
      "description": "The date this LLC met the qualification threshold to become an LLC"
    },
    "llcName": {
      "type": "string",
      "description": "Legal name of the Limited Liability Company (LLC)"
    },
    "llcStreetAddress": {
      "type": "object",
      "additionalProperties": false,
      "description": "Detailed business or residential address",
      "required": [
        "street",
        "city",
        "state",
        "zipCode"
      ],
      "properties": {
        "id": {
          "type": "string",
          "format": "uuid",
          "description": "Unique Id for the address."
        },
        "street": {
          "type": "string",
          "description": "Street address."
        },
        "street2": {
          "type": "string",
          "description": "Street address."
        },
        "city": {
          "type": "string",
          "description": "City."
        },
        "state": {
          "type": "string",
          "description": "State."
        },
        "zipCode": {
          "type": "string",
          "description": "ZIP code."
        },
        "county": {
          "type": "string",
          "description": "County."
        }
      }
    },
    "llcMailingAddress": {
      "type": "object",
      "additionalProperties": false,
      "description": "Detailed business or residential address",
      "required": [
        "street",
        "city",
        "state",
        "zipCode"
      ],
      "properties": {
        "id": {
          "type": "string",
          "format": "uuid",
          "description": "Unique Id for the address."
        },
        "street": {
          "type": "string",
          "description": "Street address."
        },
        "street2": {
          "type": "string",
          "description": "Street address."
        },
        "city": {
          "type": "string",
          "description": "City."
        },
        "state": {
          "type": "string",
          "description": "State."
        },
        "zipCode": {
          "type": "string",
          "description": "ZIP code."
        },
        "county": {
          "type": "string",
          "description": "County."
        }
      }
    },
    "llcResponsibleOfficer": {
      "type": "object",
      "additionalProperties": false,
      "required": [
        "name"
      ],
      "properties": {
        "name": {
          "type": "string",
          "description": "Name of the responsible or principal officer."
        },
        "phoneNumber": {
          "type": "string",
          "description": "Phone number of the responsible officer as digits only.",
          "pattern": "^\\d+$",
          "maxLength": 40
        },
        "email": {
          "type": "string",
          "format": "email",
          "description": "Email address of the responsible officer"
        }
      }
    },
    "members": {
      "type": "array",
      "items": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "fullName"
        ],
        "anyOf": [
          {
            "required": [
              "tenPercentOrGreater"
            ],
            "properties": {
              "tenPercentOrGreater": {
                "enum": [
                  true
                ]
              }
            }
          },
          {
            "required": [
              "capitalContributionOver10K"
            ],
            "properties": {
              "capitalContributionOver10K": {
                "enum": [
                  true
                ]
              }
            }
          }
        ],
        "properties": {
          "fullName": {
            "type": "string",
            "description": "Full legal name of the member"
          },
          "tenPercentOrGreater": {
            "type": "boolean",
            "description": "At least one of this or capitalContributionsOver10K must be true for the member to qualify.\n"
          },
          "capitalContributionOver10K": {
            "type": "boolean",
            "description": "At least one of this or tenPercentOrGreater must be true for the member to qualify.\n"
          },
          "datesOfCapitalContributions": {
            "type": "array",
            "description": "For those members qualifying because of meeting the $10,000 capital contribution threshold, enter the dates of each capital contribution made since qualification and in the preceding 12 months.\n",
            "items": {
              "type": "string",
              "format": "date"
            }
          },
          "cumulativeCapitalContribution": {
            "type": "number",
            "format": "float",
            "description": "Cumulative capital contributions of the member"
          },
          "percentageOwnership": {
            "type": "number",
            "format": "float",
            "description": "Percentage ownership interest in the LLC (0 - 100)",
            "minimum": 0,
            "maximum": 100,
            "multipleOf": 0.1
          }
        }
      }
    },
    "memberLLCs": {
      "type": "array",
      "items": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "name"
        ],
        "anyOf": [
          {
            "required": [
              "tenPercentOrGreater"
            ],
            "properties": {
              "tenPercentOrGreater": {
                "enum": [
                  true
                ]
              }
            }
          },
          {
            "required": [
              "capitalContributionOver10K"
            ],
            "properties": {
              "capitalContributionOver10K": {
                "enum": [
                  true
                ]
              }
            }
          }
        ],
        "properties": {
          "name": {
            "type": "string",
            "description": "Name of the member LLC"
          },
          "tenPercentOrGreater": {
            "type": "boolean",
            "description": "At least one of this or capitalContributionsOver10K must be true for the member to qualify.\n"
          },
          "capitalContributionOver10K": {
            "type": "boolean",
            "description": "At least one of this or tenPercentOrGreater must be true for the member to qualify.\n"
          },
          "datesOfCapitalContributions": {
            "type": "array",
            "description": "For those members qualifying because of meeting the $10,000 capital contribution threshold, enter the dates of each capital contribution made since qualification and in the preceding 12 months.\n",
            "items": {
              "type": "string",
              "format": "date"
            }
          },
          "cumulativeCapitalContribution": {
            "type": "number",
            "format": "float",
            "description": "Cumulative capital contributions of the member"
          },
          "percentageOwnership": {
            "type": "number",
            "format": "float",
            "description": "Percentage ownership interest in the LLC (0 - 100)",
            "minimum": 0,
            "maximum": 100,
            "multipleOf": 0.1
          },
          "members": {
            "type": "array",
            "items": {
              "type": "string",
              "description": "Full name of the member of the member LLC"
            }
          }
        }
      }
    },
    "filingType": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "amendment": {
          "type": "boolean"
        },
        "isPreElectionStatement": {
          "type": "boolean"
        },
        "isQuarterlyStatement": {
          "type": "boolean"
        },
        "isSemiAnnualStatement": {
          "type": "boolean"
        },
        "isSpecialOddYearReport": {
          "type": "boolean"
        },
        "termination": {
          "type": "boolean"
        },
        "amendmentNumber": {
          "type": "string"
        },
        "initialFilingId": {
          "type": "string"
        },
        "explanation": {
          "type": "string"
        }
      }
    },
    "attestation": {
      "type": "object",
      "additionalProperties": false,
      "required": [
        "executedAt",
        "signature"
      ],
      "properties": {
        "firstName": {
          "type": "string",
          "description": "First Name of verification officer."
        },
        "lastName": {
          "type": "string",
          "description": "Last Name of verification officer."
        },
        "executedAt": {
          "type": "string",
          "format": "date",
          "description": "Date the statement was executed."
        },
        "executedAtCity": {
          "type": "string",
          "description": "Executed at City."
        },
        "executedAtState": {
          "type": "string",
          "description": "Executed at state."
        },
        "role": {
          "type": "string"
        },
        "signature": {
          "type": "string",
          "description": "Name of the treasurer or filer."
        }
      }
    }
  },
  "required": [
    "llcName",
    "llcStreetAddress",
    "llcResponsibleOfficer",
    "attestation"
  ]
}', 'Campaign-MemberStatement-LLC')) AS source (Id, JsonSchema, AssociatedFPPCForm)
ON JsonSchemaReference.Id = source.Id
WHEN MATCHED THEN
 UPDATE SET
    JsonSchema = source.JsonSchema,
    AssociatedFPPCForm = source.AssociatedFPPCForm
WHEN NOT MATCHED THEN
 INSERT (Id, JsonSchema, AssociatedFPPCForm)
 VALUES (source.Id, source.JsonSchema, source.AssociatedFPPCForm);

SET IDENTITY_INSERT JsonSchemaReference OFF
GO


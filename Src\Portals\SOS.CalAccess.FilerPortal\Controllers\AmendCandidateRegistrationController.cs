using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Localization;
using Refit;
using SOS.CalAccess.FilerPortal.Alerts;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Extensions;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.Registrations.AmendCandidateRegistration;
using SOS.CalAccess.FilerPortal.Models.SharedModels;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Controllers;

#pragma warning disable S107 // Methods should not have too many parameters
/// <summary>
/// Controller for handling candidate registration related actions.
/// </summary>
public class AmendCandidateRegistrationController(
    IAuthorizationSvc authorizationSvc,
    ICandidateIntentionRegistrationSvc candidateIntentionRegistrationSvc,
    IAmendCandidateRegistrationCtlSvc amendCandidateRegistrationCtlSvc,
    ICandidateSvc candidateSvc,
    IElectionSvc electionSvc,
    IReferenceDataSvc referenceDataSvc,
    IDateTimeSvc dateTimeSvc,
    IStringLocalizer<SharedResources> localizer) : Controller
#pragma warning restore S107 // Methods should not have too many parameters
{
    /// <summary>
    /// Common logic to call before loading a view.
    /// </summary>
    /// <param name="context"></param>
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        SetCommonViewData();
    }

    /// <summary>
    /// Sets UI data that is common to all pages in this form.
    /// </summary>
    private void SetCommonViewData()
    {
        ViewData[LayoutConstants.Title] = localizer[ResourceConstants.CisRegistrationTitle].Value;
        ViewData[LayoutConstants.Subtitle] = localizer[ResourceConstants.AmendRegistration].Value;
        ViewData[LayoutConstants.Breadcrumbs] = new List<Breadcrumb>()
        {
            new(localizer[ResourceConstants.FilerPortalTitle].Value, "/FilerPortal"),
            new(localizer[ResourceConstants.CisRegistrationBreadcrumb], "/CandidateRegistration"),
        };
        ViewData["ProgressItem1Name"] = "1. " + localizer[ResourceConstants.Candidate].Value;
        ViewData["ProgressItem2Name"] = "2. " + localizer[ResourceConstants.Election].Value;
        ViewData["ProgressItem3Name"] = "3. " + localizer[ResourceConstants.Verification].Value;
    }

    public async Task<IActionResult> Index(
       [Required] long id)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Amend, User, registrationId: id));

        if (!ModelState.IsValid)
        {
            return RedirectToAction("Index", "Dashboard");
        }

        return RedirectToAction(nameof(Page01), new { id });
    }

    public async Task<IActionResult> Edit([Required] long id)
    {
        return await Index(id);
    }

    public ActionResult RedirectToDashboard()
    {
        return RedirectToAction("Index", "Dashboard");
    }

    /// <summary>
    /// The closing  process in the Candidate Intennt Statement registration.
    /// </summary>
    /// <returns></returns>
    public ActionResult Close()
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        return RedirectToDashboard();
    }

    public void SetLocalizedToast(string localizerKey)
    {
        var savedMessage = localizer[localizerKey]?.Value ?? "";
        TempData["ToastMessage"] = savedMessage;
        TempData["ToastType"] = "e-toast-success";
        TempData["ToastShowCloseButton"] = "true";
        TempData["ToastX"] = "Right";
        TempData["ToastY"] = "Bottom";
    }

    private static bool IsCorrectStatus(long statusId)
    {
        const long draftId = 1;
        return statusId == draftId;
    }

    /// <summary>
    /// Cancel the current action and return to dashboard
    /// </summary>
    /// <returns></returns>
    public async Task<IActionResult> Cancel()
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Create, User));

        return RedirectToDashboard();
    }
    /// <summary>
    /// Cancels the existing draft registration.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="model">CandidateIntentionStatementViewModel.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns></returns>
    [SuppressMessage(
    "csharpsquid", "S6967:ModelState.IsValid should be checked in controller actions",
    Justification = "User should be able to cancel even if form is invalid.")]
    [HttpPost]
    public async Task<ActionResult> Cancel(
        [Required] long id,
        CandidateIntentionStatementViewModel model,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Amend, User, registrationId: id));

        if (id <= 0)
        {
            return NotFound();
        }

        try
        {
            await candidateIntentionRegistrationSvc.CancelCandidateIntentionStatement(id);
            return RedirectToDashboard();
        }
        catch (ApiException ex)
        {
            this.AddGlobalAlertToViewModel(model, new GlobalAlert { Type = AlertType.Danger.ToString(), Message = ex.Message });
            return RedirectToDashboard();
        }
    }

    #region Page01 - FR-CAND-A-CandInformation
    /// <summary>
    /// Loads view for FR-CAND-A-CandInformation
    /// </summary>
    /// <returns>Page01 View</returns>
    [HttpGet]
    public async Task<IActionResult> Page01(
        [Required] long id,
        CancellationToken cancellation = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Amend, User, registrationId: id));

        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        CandidateIntentionStatementResponseDto? registration = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(id);

        if (registration == null)
        {
            return NotFound();
        }
        var model = new AmendCandidateRegistrationStep01ViewModel(registration);
        return View(model);
    }

    /// <summary>
    /// Handler for all submissions to Page01
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Page01(
        AmendCandidateRegistrationStep01ViewModel model,
        [FromServices] IAccuMailValidatorService accuMailValidatorService,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Amend, User, registrationId: model.Id));
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        if (model.IsSameAsCandidateAddress)
        {
            model.Addresses.Find(x => x.Purpose == "Mailing")?.Clear();
        }

        if (await accuMailValidatorService.AccuMailValidationHandler(model, ModelState, model.Action == FormAction.Cancel))
        {
            return View(model);
        }

        if (model.Action == FormAction.SaveAndClose)
        {
            return await Page01Save(model, cancellationToken);
        }
        if (model.Action == FormAction.Continue)
        {
            return await Page01Continue(model, cancellationToken);
        }

        return View(model);
    }

    /// <summary>
    /// Handler for when user clicks Continue button on Page01
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    private async Task<IActionResult> Page01Continue(
        AmendCandidateRegistrationStep01ViewModel model,
        CancellationToken cancellationToken = default)
    {
        var registrationId = await amendCandidateRegistrationCtlSvc.Page01Submit(model, ModelState, cancellationToken, true);

        if (!ModelState.IsValid || model.AddressValidationResponse != null)
        {
            return View(nameof(Page01), model);
        }

        return RedirectToAction(nameof(Page02), new { id = registrationId });
    }
    /// <summary>
    /// Handler for when user clicks Save button on Page01
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    private async Task<IActionResult> Page01Save(
        AmendCandidateRegistrationStep01ViewModel model,
        CancellationToken cancellationToken = default)
    {
        await amendCandidateRegistrationCtlSvc.Page01Submit(model, ModelState, cancellationToken, false);

        if (ModelState.IsValid && model.AddressValidationResponse == null)
        {
            this.SetGlobalAlert(AlertType.Success, ResourceConstants.CisRegistrationCreatedSuccess);

            SetLocalizedToast(CommonResourceConstants.SavedMessage);

            return RedirectToDashboard();
        }

        return View(nameof(Page01), model);
    }

    [HttpPost]
    public async Task<JsonResult> Page01AddressValidation(
        [FromBody] AmendCandidateRegistrationStep01ViewModel model,
        CancellationToken cancellationToken = default)
    {
        /* Scenarios to cover:
         * Save as entered
         * Suggested
         * Continue editing
         * Suggested & Continue editing
         * Suggested & Suggested
         * Suggested & Save as entered
         * ContinueEditing & ContinueEditing
         */
        var baseUri = $"{Request.Scheme}://{Request.Host}/AmendCandidateRegistration";

        // Stay on Page0Z if the user choose ContinueEditing for both addresses.
        if (ModelState.IsValid
            && model is { CandidateAddressConfirmation.IsContinueEditing: true, MailingAddressConfirmation.IsContinueEditing: true })
        {
            return new JsonResult(new { Url = string.Empty });
        }

        // Handle Saving based on Selection Addresses
        var registrationId = await amendCandidateRegistrationCtlSvc.Page01Submit(model, ModelState, cancellationToken, true);

        //After saving, if either of the option is Continue editing, Stay on Page04.
        if (model.CandidateAddressConfirmation is { IsContinueEditing: true }
            || model.MailingAddressConfirmation is { IsContinueEditing: true })
        {
            return new JsonResult(new { Url = string.Empty });
        }

        // Redirect to Page05 with RegistrationId.
        if (registrationId.HasValue)
        {
            TempData["Id"] = registrationId.Value.ToString(CultureInfo.InvariantCulture);
        }

        return new JsonResult(new { Url = $"{baseUri}/Page02/{registrationId}", Model = model });
    }
    #endregion

    #region Page02 - FR-CAND-Election-2
    /// <summary>
    /// Loader for Page02
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> Page02(
        [Required] long id,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Amend, User, registrationId: id));

        if (ModelState.IsValid)
        {
            var registration = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(id);

            if (registration is null)
            {
                return NotFound();
            }

            // Prevent jumping to directly without creating new amendment
            if (!IsCorrectStatus(registration.StatusId))
            {
                return BadRequest();
            }

            ElectionRace? electionRace = null;
            if (registration.ElectionRaceId.HasValue)
            {
                electionRace = await electionSvc.GetElectionRace(registration.ElectionRaceId.Value);
            }
            else if (registration.OriginalId.HasValue)
            {
                var originalRegistration = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(registration.OriginalId.Value);
                if (originalRegistration != null && originalRegistration.ElectionRaceId.HasValue)
                {
                    electionRace = await electionSvc.GetElectionRace(originalRegistration.ElectionRaceId.Value);
                }
            }

            var model = new AmendCandidateRegistrationStep02ViewModel()
            {
                Id = id,
                RegistrationId = id,
                SelectedElection = electionRace?.Election.Id,
                // TODO defaulting to "State" until support for other options are available.
                SelectedJurisdiction = registration.ElectionJurisdiction ?? "state",
                SelectedElectionYear = electionRace?.Election.ElectionDate.Year.ToString(),
                SelectedOffice = electionRace?.OfficeId,
                SelectedDistrict = electionRace?.DistrictId,
                SelectedPartyAffiliation = registration?.PoliticalPartyId //PartyAffiliation
            };

            return View(model);
        }

        return View();
    }

    /// <summary>
    /// Handler for all submissions to Page02
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> Page02(
        AmendCandidateRegistrationStep02ViewModel model,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Amend, User, registrationId: model.Id));

        if (model.Id is not { } id)
        {
            return NotFound();
        }
        else
        {
            switch (model.Action)
            {
                case FormAction.SaveAndClose:
                    return await Page02Save(id, model);
                case FormAction.Continue:
                    return await Page02Continue(id, model);
                case FormAction.Previous:
                    return await Page02Previous(id, model);
                case FormAction.Cancel:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
                case FormAction.Submit:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
                case FormAction.Close:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
                case null:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }

    private async Task<IActionResult> Page02Save(
        long id,
        AmendCandidateRegistrationStep02ViewModel model)
    {
        await amendCandidateRegistrationCtlSvc.Page02Submit(id, model, ModelState, false);

        if (ModelState.IsValid)
        {
            return RedirectToDashboard();
        }

        return View(model);
    }

    private async Task<IActionResult> Page02Continue(
        long id,
        AmendCandidateRegistrationStep02ViewModel model)
    {
        await amendCandidateRegistrationCtlSvc.Page02Submit(id, model, ModelState, true);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page03), new { model.Id });
        }

        return View(model);
    }

    private async Task<IActionResult> Page02Previous(
        long id,
        AmendCandidateRegistrationStep02ViewModel model)
    {
        await amendCandidateRegistrationCtlSvc.Page02Submit(id, model, ModelState, false);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page01), new { Id = id });
        }

        return View(model);
    }
    #endregion

    #region Page03 - FR-CAND-A-VEL
    /// <summary>
    /// Loads view for FR-CAND-A-VEL
    /// </summary>
    /// <returns>Page03 View</returns>
    [HttpGet]
    public async Task<IActionResult> Page03(
         [Required] long id,
         CancellationToken cancellationToken = default)
    {
        // Auth
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Amend, User, registrationId: id));

        // Current(latest) amendment
        var registration0 = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(id);

        // Handle not found
        if (registration0 is null)
        {
            return NotFound();
        }

        // Prevent jumping to directly without creating new amendment
        if (!IsCorrectStatus(registration0.StatusId))
        {
            return BadRequest();
        }

        // Get expenditure amount (Covers edge case when it's a new amendment)
        var expenditureAmount = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatementExpenditureExpenseAmount(id);

        // Redirect if page doesn't appply
        if (expenditureAmount == 0.00M)
        {
            string? referrer = Request.Headers.Referer.ToString();
            Uri uri = new(referrer);
            string referrerPath = uri.PathAndQuery;
            // Prevent loopback if expenditure page doesn't apply and comming from Verification page.
            if (referrerPath == $"/CandidateRegistration/Page04/{id}")
            {
                return RedirectToAction(nameof(Page02), new { id });
            }
            else
            {
                return RedirectToAction(nameof(Page04), new { id });
            }
        }

        // Create model
        var model = await amendCandidateRegistrationCtlSvc.Page03BuildViewModel(id);
        // Update to correct amount
        model.ExpenditureLimitAmount = expenditureAmount ?? 0.00M;
        if (ModelState.IsValid)
        {
            return View(model);
        }

        return View(new AmendCandidateRegistrationStep03ViewModel());
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"</param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Page03(
        AmendCandidateRegistrationStep03ViewModel model)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Amend, User, registrationId: model.Id));

        if (model.Id is null or 0)
        {
            return NotFound();
        }

        if (!ModelState.IsValid)
        {
            return View(model);
        }

        try
        {
            switch (model.Action)
            {
                case FormAction.Continue:
                    return await Page03Continue(model);
                case FormAction.Previous:
                    return await Page03Previuos(model);
                case FormAction.Cancel:
                    // Cancel handled by POST Cancel() below
                    break;
                case FormAction.SaveAndClose:
                    return await Page03SaveAndClose(model);
                case FormAction.Close:
                    break;
                case FormAction.Submit:
                    break;
                case null:
                    break;
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
            return View(model);
        }
        catch (ApiException ex)
        {
            this.AddGlobalAlertToViewModel(model, new GlobalAlert
            {
                Type = AlertType.Danger.ToString(),
                Message = ex.Message
            });
            return View(nameof(Page03), new AmendCandidateRegistrationStep03ViewModel() { Id = model.Id, Messages = model.Messages });
        }
    }

    /// <summary>
    /// Handler for Page03 Continue Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    private async Task<IActionResult> Page03Continue(AmendCandidateRegistrationStep03ViewModel model)
    {
        // Validate
        amendCandidateRegistrationCtlSvc.Page03ValidatePersonalFundsDate(model, ModelState);

        // If model state is valid, proceed to Page04 with the registration ID
        if (ModelState.IsValid)
        {
            // Call the service method to process the update
            await amendCandidateRegistrationCtlSvc.Page03Submit(model, ModelState, true);

            return RedirectToAction(nameof(Page04), new { id = model.Id });
        }

        // If there are validation errors, return to the view with the model
        return View(nameof(Page03), model);
    }

    /// <summary>
    /// Handler for Page03 Previous Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    private async Task<IActionResult> Page03Previuos(AmendCandidateRegistrationStep03ViewModel model)
    {
        // Validate
        amendCandidateRegistrationCtlSvc.Page03ValidatePersonalFundsDate(model, ModelState);

        // If model state is valid, proceed to Page04 with the registration ID
        if (ModelState.IsValid)
        {
            // Call the service method to process the update
            await amendCandidateRegistrationCtlSvc.Page03Submit(model, ModelState, true);

            // temp bc page 04 is broken
            return RedirectToAction(nameof(Page02), new { id = model.Id });
        }

        // If there are validation errors, return to the view with the model
        return View(nameof(Page03), model);
    }

    /// <summary>
    /// Handler for Page03 Continue Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private async Task<IActionResult> Page03SaveAndClose(AmendCandidateRegistrationStep03ViewModel model)
    {
        // Validate
        amendCandidateRegistrationCtlSvc.Page03ValidatePersonalFundsDate(model, ModelState);

        // If model state is valid, proceed to Page04 with the registration ID
        if (ModelState.IsValid)
        {
            // Call the service method to process the update
            await amendCandidateRegistrationCtlSvc.Page03Submit(model, ModelState, true);

            return RedirectToDashboard();
        }

        // If there are validation errors, return to the view with the model
        return View(nameof(Page03), model);
    }
    #endregion

    #region Page04 - FR-CAND-Verification-1/2
    /// <summary>
    /// Verification page landing to submit the final form
    /// </summary>
    /// <param name="id">Registration ID</param>
    /// <param name="cancellationToken">cancellation token</param>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult> Page04(
        [Required] long id,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Amend, User, registrationId: id));

        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var response = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(id);

        if (response is not { } res)
        {
            return NotFound();
        }

        // Prevent jumping to directly without creating new amendment
        if (!IsCorrectStatus(response.StatusId))
        {
            return BadRequest();
        }

        var isSubmission = await authorizationSvc.IsAuthorized(new(Permission.Registration_Candidate_Attest, User, registrationId: id));
        return View(new AmendCandidateRegistrationStep04ViewModel() { RegistrationId = res.Id, IsSubmission = isSubmission, CandidateName = $"{res.FirstName} {res.LastName}", ExecutedOn = dateTimeSvc.GetCurrentDateTime() });
    }

    /// <summary>
    /// Verifies and submits the candidateIntentStatement registration with attestation.
    /// </summary>
    /// <param name="model">verification viewmodel.</param>
    /// <param name="cancellationToken">cancellation token</param>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult> Page04(
        AmendCandidateRegistrationStep04ViewModel model,
        CancellationToken cancellationToken = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Attest, User, registrationId: model.Id));

        if (model.Id is null && model.RegistrationId == 0)
        {
            return NotFound();
        }
        try
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }
            if (model.Action == FormAction.SaveAndClose)
            {
                SetLocalizedToast(CommonResourceConstants.SavedMessage);

                return RedirectToDashboard();
            }
            else if (model.Action == FormAction.Previous)
            {
                return RedirectToAction(nameof(Page03), new { model.Id });
            }
            else if (model.Action == FormAction.Submit)
            {
                RegistrationResponseDto response = await candidateIntentionRegistrationSvc.SubmitCandidateIntentionStatement(model.RegistrationId);
                if (response.ValidationErrors.Count != 0)
                {
                    foreach (var error in response.ValidationErrors)
                    {
                        ModelState.AddModelError("", error.Message.Replace("{{Field Name}}", error.FieldName, StringComparison.OrdinalIgnoreCase));
                    }
                    return View(model);
                }
                else
                {
                    return model.IsSubmission
                              ? RedirectToAction(nameof(Submission), new { id = model.RegistrationId })
                              : RedirectToAction(nameof(AttestationRequestSent), new { id = model.RegistrationId });
                }
            }

            return View(model);
        }
        catch (ApiException ex)
        {

            this.AddGlobalAlertToViewModel(model, new GlobalAlert { Type = AlertType.Danger.ToString(), Message = ex.Message });

            return View(nameof(Page04), new AmendCandidateRegistrationStep04ViewModel() { RegistrationId = model.RegistrationId, Messages = model.Messages });
        }
    }
    #endregion

    /// </summary>
    /// <param name="executedAt"></param>
    /// <param name="id">Registration ID. Derived from route param</param>
    /// <param name="model">Model containing the form submission data from view.</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    [Route("[controller]/Submission/{id}")]
    public ActionResult Submission(
        [Required] int id,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        return View("Page05", new AmendCandidateRegistrationStep05ViewModel() { Id = id, IsSubmission = true, ExecutedOn = dateTimeSvc.GetCurrentDateTime() });
    }
    /// <summary>
    /// Attestation Request sent page landing after non-candidate user request to attest on the candidateIntentionStatement flow.
    /// </summary>
    /// <param name="executedAt"></param>
    /// <param name="id">Registration ID. Derived from route param</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    [Route("[controller]/AttestationRequestSent/{id}")]
    public ActionResult AttestationRequestSent(
        [Required] int id,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        // TODO: Add service to fetch pending items

        var pendingItems = new List<PendingItemSharedViewModel>
        {
            new() { Item = "Candidate Attestation", Status = "Not yet started" },
        };
        return View("Page05", new AmendCandidateRegistrationStep05ViewModel() { Id = id, IsSubmission = false, ExecutedOn = dateTimeSvc.GetCurrentDateTime(), PendingItems = pendingItems });
    }
}

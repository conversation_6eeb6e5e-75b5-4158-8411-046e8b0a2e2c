using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Models;

namespace SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration;

public class LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel
{
    public DataGridModel? LobbyingFirmsGridModel { get; set; }
    public long? Id { get; set; }
    public FormAction? Action { get; set; }
    public List<LobbyingFirmRowDto>? LobbyingFirms { get; set; }
    public required bool HasFirms { get; set; }
}

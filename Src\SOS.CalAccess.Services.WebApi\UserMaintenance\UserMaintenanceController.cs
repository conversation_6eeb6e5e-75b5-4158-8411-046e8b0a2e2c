using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;

namespace SOS.CalAccess.Services.WebApi.UserMaintenance;

#pragma warning disable S6931

/// <summary>
/// API controller responsible for routing requests related to users.
/// </summary>
[AllowAnonymous]
[ApiController]
[ApiConventionType(typeof(DefaultApiConventions))]
public sealed class UserMaintenanceController(IUserMaintenanceSvc userMaintenanceSvc) : ControllerBase, IUserMaintenanceSvc
{
    [NonAction] //temporary until implemented
    public void AddUserAddress(Address address, int userId)
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    public void AddUserEmailAddress(EmailAddress emailAddress, string userId)
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    public void AddUserPhoneNumber(PhoneNumber phoneNumber, int userId)
    {
        throw new NotImplementedException();
    }

    /// <inheritDoc/>
    [HttpGet(IUserMaintenanceSvc.GetCurrentUserPath, Name = nameof(GetCurrentUser))]
    public async Task<BasicUserDto> GetCurrentUser()
    {
        return await userMaintenanceSvc.GetCurrentUser();
    }

    [NonAction] //temporary until implemented
    public Task<IEnumerable<User>> ListUsers()
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    public void RemoveUserAddress(int userAddressId)
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    public void RemoveUserEmailAddress(int emailId)
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    public void RemoveUserPhoneNumber(int userPhoneNumberId)
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    public int RequestUserEmailVerification(string emailId)
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    public Task<IEnumerable<User>> SearchUsers(User user)
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    public void UpdateUserAddress(UserAddress address)
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    public void UpdateUserPhoneNumber(UserPhoneNumber phoneNumber)
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    public bool VerifyUserEmail(int emailId, string verificationCode)
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    public Task<User> ViewUser(int userId)
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    Task<List<User>> IUserMaintenanceSvc.GetListUsersByUserNameAsync(List<string> userNames)
    {
        throw new NotImplementedException();
    }

    [NonAction]
    public async Task<User?> GetUserById(long userId)
    {
        return await userMaintenanceSvc.GetUserById(userId);
    }

    /// <summary>
    /// Update User details with address and phone number
    /// </summary>
    /// <param name="userAccountDetails"></param>
    /// <returns></returns>
    [HttpPost(IUserMaintenanceSvc.UpdateUserPath, Name = nameof(UpdateUser))]
    public async Task UpdateUser(UserAccountDetailsDto userAccountDetails)
    {
        await userMaintenanceSvc.UpdateUser(userAccountDetails);
    }
    [NonAction] //temporary until implemented
    public Task<bool> DeactivateUser(long userId, long inactivateReason)
    {
        throw new NotImplementedException();
    }

    /// <summary>
    /// Get user account details by user id
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    [HttpGet(IUserMaintenanceSvc.GetUserAccountDetailsByUserIdPath, Name = nameof(GetUserAccountDetailsByUserId))]
    public async Task<UserAccountDetailsDto> GetUserAccountDetailsByUserId(long userId)
    {
        return await userMaintenanceSvc.GetUserAccountDetailsByUserId(userId);
    }
}

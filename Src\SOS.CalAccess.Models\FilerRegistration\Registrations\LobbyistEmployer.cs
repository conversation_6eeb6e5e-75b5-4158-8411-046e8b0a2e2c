// <copyright file="LobbyistEmployer.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations.Schema;
using SOS.CalAccess.Models.FilerRegistration.Lobbyists;

namespace SOS.CalAccess.Models.FilerRegistration.Registrations;

/// <summary>
/// Represents a lobbying employer or coalition registration.
/// </summary>
public sealed class LobbyistEmployer : LobbyingRegistration
{
    /// <summary>
    /// Gets or sets the date of qualification for this registration.
    /// </summary>
    [Documentation("Qualification date for the organization or individual.")]
    [Column(nameof(DateQualified))]
    public DateTime? DateQualified { get; set; }

    /// <summary>
    /// Gets or sets the legislative session ID during which this registration is active.
    /// </summary>
    [Documentation("Foreign Key reference to the Legislative Session Id for the registration.")]
    [Column(nameof(LegislativeSessionId))]
    public long? LegislativeSessionId { get; set; }

    /// <summary>
    /// The foreign key relationship to the LegislativeSession object.
    /// </summary>
    public LegislativeSession? LegislativeSession { get; set; }

    /// <summary>
    /// Gets or sets the employer name for a lobbyist.
    /// </summary>
    [Documentation("Lobbying firm name or employer name for a lobbyist.")]
    [Column(nameof(EmployerName))]
    public string? EmployerName { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the lobbyist will lobby the state legislature or not.
    /// </summary>
    [Documentation("Indicates if the lobbyist will lobby the state legislature.")]
    [Column(nameof(StateLegislatureLobbying))]
    public bool? StateLegislatureLobbying { get; set; }

    /// <summary>
    /// Gets or sets the employer type for this registration.
    /// </summary>
    [Documentation("Type of lobbyist employer.")]
    public string? EmployerType { get; set; }

    /// <summary>
    /// Gets or sets the business activity for this lobbyist employer registration.
    /// </summary>
    [Documentation("Business activity for a lobbyist employer.")]
    public string? BusinessActivity { get; set; }

    /// <summary>
    /// Gets or sets the description for this registration's business activity.
    /// </summary>
    [Documentation("Description of the lobbyist employer business.")]
    public string? BusinessDescription { get; set; }
    /// <summary>
    /// Gets or sets the Employer Name for individual type of nature and interest for this registration.
    /// </summary>
    [Documentation("Employer Name for individual type of nature and interest.")]
    public string? NatureAndInterestEmployerName { get; set; }

    /// <summary>
    /// Gets or sets the Nature And Purpose for this registration.
    /// </summary>
    [Documentation("Nature And Purpose.")]
    public string? NatureAndPurpose { get; set; }

    /// <summary>
    /// Gets or sets the Trade, profession from which mebership or financial support is principally derived.
    /// </summary>
    [Documentation("Prinicipally derived support.")]
    public string? DescriptionOfPrincipallyDerivedSupport { get; set; }

    /// <summary>
    /// Gets or sets the IndustryGroupClassification Idduring which this registration is active.
    /// </summary>
    [Documentation("Foreign Key reference to the IndustryGroupClassification Id for the registration.")]
    [Column(nameof(IndustryGroupClassificationId))]
    public long? IndustryGroupClassificationId { get; set; }

    /// <summary>
    /// Gets or sets the IndustryGroupClassification Idduring which this registration is active.
    /// </summary>
    [Documentation("Foreign Key reference to the NatureAndInterestType Id for the registration.")]
    [Column(nameof(NatureAndInterestTypeId))]
    public long? NatureAndInterestTypeId { get; set; }

    /// <summary>
    /// Gets or sets the Industry group classification entered other than the list of IndustryGroupClassifications.
    /// </summary>
    [Documentation("Industry Group Other Text.")]
    public string? IndustryGroupOtherText { get; set; }

    /// <summary>
    /// Gets or sets the interest type for this lobbyist employer registration.
    /// </summary>
    [Documentation("Type of interest for the lobbyist employer.")]
    public string? InterestType { get; set; }

    /// <summary>
    /// Gets or sets the industry description for this lobbyist employer registration.
    /// </summary>
    [Documentation("Description of the industry, trade, or procession segment of the lobbyist employer.")]
    public string? IndustryDescription { get; set; }

    /// <summary>
    /// Gets or sets the industry portion for this lobbyist employer registration.
    /// </summary>
    [Documentation("Description of the specific portion or faction of the industry, trade, or profession segment the lobbyist employer represents.")]
    public string? IndustryPortion { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether registration is for a lobbying coalition.
    /// </summary>
    [Documentation("Gets or sets a value indicating whether registration is for a lobbying coalition.")]
    [Column(nameof(IsLobbyingCoalition))]
    public bool? IsLobbyingCoalition { get; set; }

    /// <summary>
    /// Gets the set of members for this registration.
    /// </summary>
    public List<LobbyingEmployerGroupMember> MemberNames { get; set; } = [];

    /// <summary>
    /// Gets or sets the number of members for this lobbyist employer registration.
    /// </summary>
    [Documentation("Indicates how many members the lobbyist employer represents.")]
    public int? NumberOfMembers { get; set; }

    /// <summary>
    /// Gets or sets a value indicating threshold of members.
    /// </summary>
    [Documentation("Gets or sets a value indicating threshold of members.")]
    [Column(nameof(IsFiftyMembersOrLess))]
    public bool? IsFiftyMembersOrLess { get; set; }

    /// <summary>
    /// Gets or sets the bsiness subcategory Id for the registration.
    /// </summary>
    [Documentation("Foreign Key reference to the Business Subcategory Id for the registration.")]
    [Column(nameof(BusinessSubcategoryId))]
    public long? BusinessSubcategoryId { get; set; }

    /// <summary>
    /// The foreign key relationship to the BusinessSubcategory object.
    /// </summary>
    public BusinessSubcategory? BusinessSubcategory { get; set; }

    /// <summary>
    /// Gets or sets the LobbyingInterestId for the registration.
    /// </summary>
    [Documentation("Foreign Key reference to the LobbyingInterest Id for the registration.")]
    [Column(nameof(LobbyingInterestId))]
    public long? LobbyingInterestId { get; set; }

    /// <summary>
    /// The foreign key relationship to the LobbyingInterest object.
    /// </summary>
    public LobbyingInterest? LobbyingInterest { get; set; }

    /// <summary>
    /// Gets or sets the business subcategory other text.
    /// </summary>
    [Documentation("Business Subcategory Other Text.")]
    public string? BusinessSubcategoryOtherText { get; set; }
}

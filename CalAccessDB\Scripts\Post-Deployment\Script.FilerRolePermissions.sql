MERGE INTO FilerRolePermission
USING (
    SELECT Permission.Id as PermissionId, FilerRole.Id as FilerRoleId 
    FROM (VALUES 
	    ('Candidate/Officeholder', 'Account Manager', 'Filer_AuthorizedUsers_View'),
	    ('Candidate/Officeholder', 'Account Manager', 'Filer_AuthorizedUsers_Manage'),
	    ('Candidate/Officeholder', 'Account Manager', 'Registration_Candidate_Edit'),
	    ('Candidate/Officeholder', 'Account Manager', 'Registration_Candidate_Amend'),
	    ('Candidate/Officeholder', 'Account Manager', 'Registration_Candidate_Terminate'),
	    ('Candidate/Officeholder', 'Account Manager', 'Registration_Candidate_Withdraw'),
	    ('Candidate/Officeholder', 'Administrative Assistant', 'Filer_AuthorizedUsers_View'),
	    ('Candidate/Officeholder', 'Administrative Assistant', 'Registration_Candidate_Edit'),
	    ('Candidate/Officeholder', 'Administrative Assistant', 'Registration_Candidate_Withdraw'),
	    ('Candidate/Officeholder', 'Candidate', 'Filer_AuthorizedUsers_View'),
	    ('Candidate/Officeholder', 'Candidate', 'Filer_AuthorizedUsers_Manage'),
	    ('Candidate/Officeholder', 'Candidate', 'Registration_Candidate_Edit'),
	    ('Candidate/Officeholder', 'Candidate', 'Registration_Candidate_Amend'),
	    ('Candidate/Officeholder', 'Candidate', 'Registration_Candidate_Terminate'),
	    ('Candidate/Officeholder', 'Candidate', 'Registration_Candidate_Attest'),
	    ('Candidate/Officeholder', 'Candidate', 'Registration_Candidate_Withdraw'),
	    ('Slate Mailer Organization', 'Account Manager', 'Filer_AuthorizedUsers_View'),
	    ('Slate Mailer Organization', 'Account Manager', 'Filer_AuthorizedUsers_Manage'),
	    ('Slate Mailer Organization', 'Account Manager', 'Registration_SlateMailerOrganization_View'),
	    ('Slate Mailer Organization', 'Account Manager', 'Registration_SlateMailerOrganization_Edit'),
	    ('Slate Mailer Organization', 'Account Manager', 'Registration_SlateMailerOrganization_Amend'),
	    ('Slate Mailer Organization', 'Account Manager', 'Registration_SlateMailerOrganization_Terminate'),
	    ('Slate Mailer Organization', 'Account Manager', 'Registration_SlateMailerOrganization_Reinstate'),
	    ('Slate Mailer Organization', 'Account Manager', 'Registration_SlateMailerOrganization_Notify'),
	    ('Slate Mailer Organization', 'Account Manager', 'Disclosure_401_Notify'),
	    ('Slate Mailer Organization', 'Administrative Assistant', 'Filer_AuthorizedUsers_View'),
	    ('Slate Mailer Organization', 'Administrative Assistant', 'Registration_SlateMailerOrganization_View'),
	    ('Slate Mailer Organization', 'Administrative Assistant', 'Registration_SlateMailerOrganization_Edit'),
        ('Slate Mailer Organization', 'Officer', 'Filer_AuthorizedUsers_View'),
        ('Slate Mailer Organization', 'Officer', 'Filer_AuthorizedUsers_Manage'),
        ('Slate Mailer Organization', 'Officer', 'Registration_SlateMailerOrganization_View'),
	    ('Slate Mailer Organization', 'Officer', 'Registration_SlateMailerOrganization_Edit'),
	    ('Slate Mailer Organization', 'Officer', 'Registration_SlateMailerOrganization_Amend'),
	    ('Slate Mailer Organization', 'Officer', 'Registration_SlateMailerOrganization_Terminate'),
	    ('Slate Mailer Organization', 'Officer', 'Registration_SlateMailerOrganization_Reinstate'),
	    ('Slate Mailer Organization', 'Officer', 'Registration_SlateMailerOrganization_Notify'),
	    ('Slate Mailer Organization', 'Officer', 'Registration_SlateMailerOrganization_Attest'),
        ('Slate Mailer Organization', 'Officer', 'Disclosure_401_Notify'),
	    ('Slate Mailer Organization', 'Officer', 'Disclosure_401_Attest'),
        ('Slate Mailer Organization', 'Treasurer', 'Filer_AuthorizedUsers_View'),
        ('Slate Mailer Organization', 'Treasurer', 'Filer_AuthorizedUsers_Manage'),
        ('Slate Mailer Organization', 'Treasurer', 'Registration_SlateMailerOrganization_View'),
	    ('Slate Mailer Organization', 'Treasurer', 'Registration_SlateMailerOrganization_Edit'),
	    ('Slate Mailer Organization', 'Treasurer', 'Registration_SlateMailerOrganization_Amend'),
	    ('Slate Mailer Organization', 'Treasurer', 'Registration_SlateMailerOrganization_Terminate'),
	    ('Slate Mailer Organization', 'Treasurer', 'Registration_SlateMailerOrganization_Reinstate'),
	    ('Slate Mailer Organization', 'Treasurer', 'Registration_SlateMailerOrganization_Notify'),
	    ('Slate Mailer Organization', 'Treasurer', 'Registration_SlateMailerOrganization_Attest'),
        ('Slate Mailer Organization', 'Treasurer', 'Disclosure_401_Notify'),
	    ('Slate Mailer Organization', 'Treasurer', 'Disclosure_401_Attest'),
	    ('Slate Mailer Organization', 'Treasurer', 'Registration_SlateMailerOrganization_CompleteTreasurerAcknowledgment'),
        ('Slate Mailer Organization', 'Assistant Treasurer', 'Filer_AuthorizedUsers_View'),
        ('Slate Mailer Organization', 'Assistant Treasurer', 'Filer_AuthorizedUsers_Manage'),
        ('Slate Mailer Organization', 'Assistant Treasurer', 'Registration_SlateMailerOrganization_View'),
	    ('Slate Mailer Organization', 'Assistant Treasurer', 'Registration_SlateMailerOrganization_Edit'),
	    ('Slate Mailer Organization', 'Assistant Treasurer', 'Registration_SlateMailerOrganization_Amend'),
	    ('Slate Mailer Organization', 'Assistant Treasurer', 'Registration_SlateMailerOrganization_Terminate'),
	    ('Slate Mailer Organization', 'Assistant Treasurer', 'Registration_SlateMailerOrganization_Reinstate'),
	    ('Slate Mailer Organization', 'Assistant Treasurer', 'Registration_SlateMailerOrganization_Notify'),
	    ('Slate Mailer Organization', 'Assistant Treasurer', 'Registration_SlateMailerOrganization_Attest'),
        ('Slate Mailer Organization', 'Assistant Treasurer', 'Disclosure_401_Notify'),
	    ('Slate Mailer Organization', 'Assistant Treasurer', 'Disclosure_401_Attest'),
	    ('Slate Mailer Organization', 'Assistant Treasurer', 'Registration_SlateMailerOrganization_CompleteTreasurerAcknowledgment'),
        ('Lobbyist Employer or Coalition', 'Account Manager', 'Filer_AuthorizedUsers_View'),
        ('Lobbyist Employer or Coalition', 'Account Manager', 'Filer_AuthorizedUsers_Manage'),
        ('Lobbyist Employer or Coalition', 'Account Manager', 'Disclosure_Lobbying48H_Notify'),
        ('Lobbyist Employer or Coalition', 'Account Manager', 'Disclosure_Lobbying48H_Attest'),
        ('Lobbyist Employer or Coalition', 'Administrative Assistant', 'Filer_AuthorizedUsers_View'),
        ('Lobbyist Employer or Coalition', 'Responsible Officer', 'Filer_AuthorizedUsers_View'),
        ('Lobbyist Employer or Coalition', 'Responsible Officer', 'Filer_AuthorizedUsers_Manage'),
        ('Lobbyist Employer or Coalition', 'Responsible Officer', 'Disclosure_Lobbying48H_Notify'),
        ('Lobbyist Employer or Coalition', 'Responsible Officer', 'Disclosure_Lobbying48H_Attest'),
        ('Lobbyist Employer or Coalition', 'Account Manager', 'Disclosure_Lobbying72H_Notify'),
        ('Lobbyist Employer or Coalition', 'Account Manager', 'Disclosure_Lobbying72H_Attest'),
        ('Lobbyist Employer or Coalition', 'Responsible Officer', 'Disclosure_Lobbying72H_Notify'),
        ('Lobbyist Employer or Coalition', 'Responsible Officer', 'Disclosure_Lobbying72H_Attest'),
        ('Lobbyist Employer or Coalition', 'Account Manager', 'Disclosure_LobbyistEmployer_Notify'),
        ('Lobbyist Employer or Coalition', 'Account Manager', 'Disclosure_LobbyistEmployer_Attest'),
        ('Lobbyist Employer or Coalition', 'Responsible Officer', 'Disclosure_LobbyistEmployer_Notify'),
        ('Lobbyist Employer or Coalition', 'Responsible Officer', 'Disclosure_LobbyistEmployer_Attest'),
        ('Lobbyist', 'Account Manager', 'Filer_AuthorizedUsers_View'),
        ('Lobbyist', 'Account Manager', 'Filer_AuthorizedUsers_Manage'),
        ('Lobbyist', 'Account Manager', 'Disclosure_Lobbyist_Notify'),
        ('Lobbyist', 'Account Manager', 'Disclosure_Lobbyist_Attest'),
        ('Lobbyist', 'Account Manager', 'Registration_Lobbyist_Create'),
        ('Lobbyist', 'Account Manager', 'Registration_Lobbyist_Edit'),
        ('Lobbyist', 'Account Manager', 'Registration_Lobbyist_Amend'),
        ('Lobbyist', 'Account Manager', 'Registration_Lobbyist_Attest'),
        ('Lobbyist', 'Account Manager', 'Registration_Lobbyist_Terminate'),
        ('Lobbyist', 'Administrative Assistant', 'Filer_AuthorizedUsers_View'),
        ('Lobbyist', 'Lobbyist', 'Filer_AuthorizedUsers_View'),
        ('Lobbyist', 'Lobbyist', 'Filer_AuthorizedUsers_Manage'),
        ('Lobbyist', 'Lobbyist', 'Disclosure_Lobbyist_Notify'),
        ('Lobbyist', 'Lobbyist', 'Disclosure_Lobbyist_Attest'),
        ('Lobbyist', 'Lobbyist', 'Registration_Lobbyist_Create'),
        ('Lobbyist', 'Lobbyist', 'Registration_Lobbyist_Edit'),
        ('Lobbyist', 'Lobbyist', 'Registration_Lobbyist_Amend'),
        ('Lobbyist', 'Lobbyist', 'Registration_Lobbyist_Attest'),
        ('Lobbyist', 'Lobbyist', 'Registration_Lobbyist_Terminate')
    ) AS GroupPermission(FilerTypeName, FilerRoleName, PermissionName)
    INNER JOIN FilerType ON FilerType.Name = GroupPermission.FilerTypeName
    INNER JOIN FilerRole ON FilerRole.FilerTypeId = FilerType.Id AND FilerRole.Name = GroupPermission.FilerRoleName 
    INNER JOIN Permission ON Permission.Name = GroupPermission.PermissionName
) AS source 
ON FilerRolePermission.PermissionId = source.PermissionId
	AND FilerRolePermission.FilerRoleId = source.FilerRoleId
WHEN NOT MATCHED THEN 
    INSERT (FilerRoleId, PermissionId)
    VALUES (source.FilerRoleId, source.PermissionId);
GO



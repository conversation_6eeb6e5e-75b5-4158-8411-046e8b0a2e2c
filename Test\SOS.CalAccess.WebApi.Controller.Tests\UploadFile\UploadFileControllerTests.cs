using NSubstitute;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Services.Common.FileSystem;
using SOS.CalAccess.Services.Common.FileSystem.Models;
using SOS.CalAccess.Services.WebApi.UploadFile;

namespace SOS.CalAccess.WebApi.Tests.UploadFile;
public class UploadFileControllerTests
{
    private IUploadFileSvc _uploadFileSvc;
    private UploadFileController _controller;
    private UploadedFile _uploadedFile;
    private const string AttachedFileGuidsJson = "[\"file-guid-1\", \"file-guid-2\", \"file-guid-3\"]";

    [SetUp]
    public void SetUp()
    {
        _uploadedFile = new() { RelationshipId = 50000, RelationshipType = "DisclosureTransaction", OriginalFileName = "upload.txt", Path = "myblobcontainer" };
        _uploadFileSvc = Substitute.For<IUploadFileSvc>();
        _controller = new UploadFileController(_uploadFileSvc);
    }

    [Test]
    public async Task CreateUploadedFile()
    {
        await _controller.CreateUploadedFile(_uploadedFile);
        await _uploadFileSvc.Received(1).CreateUploadedFile(_uploadedFile);
    }

    [Test]
    public async Task FindUploadByFileNameGuid()
    {
        await _controller.FindUploadByFileNameGuid(_uploadedFile.FileName.ToString());
        await _uploadFileSvc.Received(1).FindUploadByFileNameGuid(_uploadedFile.FileName.ToString());
    }

    [Test]
    public async Task DeleteUploadedFileByFileNameGuid()
    {
        await _controller.DeleteUploadedFile(_uploadedFile);
        await _uploadFileSvc.Received(1).DeleteUploadedFile(_uploadedFile);
    }

    [Test]
    public async Task GetUploadsByRelationshipId()
    {
        await _controller.GetUploadsByRelationshipId(_uploadedFile.RelationshipId!.Value);
        await _uploadFileSvc.Received(1).GetUploadsByRelationshipId(_uploadedFile.RelationshipId!.Value);
    }

    [Test]
    public async Task UpdateUploadedFile()
    {
        await _controller.UpdateUploadedFile(_uploadedFile);
        await _uploadFileSvc.Received(1).UpdateUploadedFile(_uploadedFile);
    }

    #region UpdateUploadedFileRelationshipsAsync
    [Test]
    public void UpdateUploadedFileRelationshipsAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var request = new UpdateUploadedFileRelationshipsRequest
        {
            AttachedFileGuidsJson = "",
            RelationshipId = 1,
        };
        _ = _uploadFileSvc.UpdateUploadedFileRelationshipsAsync(Arg.Any<UpdateUploadedFileRelationshipsRequest>());

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _controller.UpdateUploadedFileRelationshipsAsync(request));
        _ = _uploadFileSvc.Received(1).UpdateUploadedFileRelationshipsAsync(request);
    }
    #endregion
}

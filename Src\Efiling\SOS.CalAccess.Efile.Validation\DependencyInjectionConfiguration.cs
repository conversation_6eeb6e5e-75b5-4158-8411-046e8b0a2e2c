using System.Diagnostics.CodeAnalysis;
using System.Text;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using SOS.CalAccess.Services.Business.Efile;
using SOS.CalAccess.Services.Business.Efile.Model;
using SOS.CalAccess.Services.Common.DataValidation;
using SOS.CalAccess.Services.WebApi.Authentication;
using SOS.CalAccess.Services.WebApi.Authorization;
using SOS.CalAccess.Services.WebApi.Authorization.ThirdParty.Decisions;
using globalDependencyInjection = SOS.CalAccess.Services.WebApi.DependencyInjectionConfiguration;

namespace SOS.CalAccess.Efile.Validation;

/// <summary>
/// Helper methods used to set up dependency injection for this project.
/// </summary>
[ExcludeFromCodeCoverage]
internal sealed class DependencyInjectionConfiguration
{
    /// <summary>
    /// Context for configuring the application host
    /// </summary>
    private readonly HostBuilderContext _builder;

    /// <summary>
    /// Common helper methods used to set up dependency injection for both Efile functions.
    /// </summary>
    private readonly CommonDependencyInjection _commonDI;

    /// <summary>
    /// The service collection used to register all D.I. services.
    /// </summary>
    private readonly IServiceCollection _services;

    /// <summary>
    /// Save configuration locally, then verify that all required settings are present.
    /// </summary>
    internal DependencyInjectionConfiguration(HostBuilderContext builder, IServiceCollection services)
    {
        _builder = builder ?? throw new ArgumentNullException(nameof(builder));
        _services = services ?? throw new ArgumentNullException(nameof(services));
        string environment = Environment.GetEnvironmentVariable("AZURE_FUNCTIONS_ENVIRONMENT") ?? "Development";
        bool isDevelopmentEnvironment = environment.Equals("Development", StringComparison.OrdinalIgnoreCase);
        _commonDI = new CommonDependencyInjection(builder.Configuration, isDevelopmentEnvironment);

    }

    /// <summary>
    /// Setup and configure dependency injection for the data layer repositories.
    /// Before adding the dependencies in this method, please ensure they are not already registered in Common DI and API project.
    /// If they are, you should not register them again here to avoid duplication and potential conflicts.
    /// </summary>
    internal void AddRepositories()
    {
        // Common dependency injection for Repositories, used for both Efile(Validation and Processing) and Api
        globalDependencyInjection.AddRepositories(_services);

        // Add Repositories Services Dependencies those are need both Efile Validateion and Processing functions.
        _commonDI.AddRepositories(_services);

        // Add Efile Validation Repositories Dependencies.
    }

    /// <summary>
    /// Setup and configure dependency injection for the Common Services layer.
    /// Before adding the dependencies in this method, please ensure they are not already registered in Common DI and API project.
    /// If they are, you should not register them again here to avoid duplication and potential conflicts.
    /// </summary>
    internal void AddCommonServices()
    {
        // Common dependency injection for Common Services, used for both Efile(Validation and Processing) and Api
        globalDependencyInjection.AddCommonServices(_services, _builder.Configuration);

        // Add Common Services Dependencies those are need both Efile Validateion and Processing functions.
        _commonDI.AddCommonServices(_services);

        // Add Efile Validation Common Services Dependencies.
    }

    /// <summary>
    /// Setup and configure dependency injection for the Business layer services.
    /// Before adding the dependencies in this method, please ensure they are not already registered in Common DI and API project.
    /// If they are, you should not register them again here to avoid duplication and potential conflicts.
    /// </summary>
    internal void AddBusinessServices()
    {
        // Common dependency injection for business services, used for both Efile(Validation and Processing) and Api
        globalDependencyInjection.AddBusinessServices(_services);

        // Add Business Services Dependencies those are need both Efile Validateion and Processing functions.
        CommonDependencyInjection.AddBusinessServices(_services);

        // Add Efile Validation Services Dependencies.
        _services.AddScoped<IEfileValidationsSvc, EfileValidationsSvc>();
        _services.AddScoped<IEfileValidationDependencies, EfileValidationDependencies>();
        _services.Configure<FileSizeSettings>(_builder.Configuration.GetSection("FileSize"));
        _services.AddScoped<IEfileValidationFunctionCommonMethods, EfileValidationFunctionCommonMethods>();
        _services.AddScoped<FileSizeSettings>();
        _services.AddScoped<IFormValidator, CampaignStatementCandidateIntentionValidator>();
        _services.AddScoped<IFormValidator, LobbyingDisclosureLobbyistValidator>();
        _services.AddScoped<IFormValidator, LobbyingCertificationLobbyistValidator>();
        _services.AddScoped<IFormValidator, CampaignDisclosureCandidateCampaignStatementShortValidator>();
        _services.AddScoped<IFormValidator, CampaignDisclosureCandidateCampaignStatementSupplementValidator>();
        _services.AddScoped<IFormValidator, CampaignTerminationSlateMailerValidator>();
        _services.AddScoped<IFormValidator, CampaignRegistrationSlateMailerValidator>();
        _services.AddScoped<IFormValidator, CampaignDisclosureSlateMailerLatePaymentsValidator>();
        _services.AddScoped<IFormValidator, CampaignDisclosureNoActivityValidator>();
        _services.AddScoped<IFormValidator, CampaignDisclosureSlateMailerValidator>();
        _services.AddScoped<IFormValidator, CampaignMemberStatementLlcValidator>();
        _services.AddScoped<IFormValidator, CampaignRegistrationRecipientCommitteeValidator>();
        _services.AddScoped<IFormValidator, LobbyingActivityPaymentsMadeValidator>();
        _services.AddScoped<IFormValidator, LobbyingDisclosurePaymentsReceivedValidator>();
        _services.AddScoped<IFormValidator, LobbyingNoticeOfTerminationValidator>();
        _services.AddScoped<IFormValidator, LobbyingNoticeOfWithdrawalValidator>();
        _services.AddScoped<IFormValidator, LobbyingRegistrationLobbyingCoalitionValidator>();

    }

    /// <summary>
    /// Setup of Services provided by Maplight Accelerator project
    /// Before adding the dependencies in this method, please ensure they are not already registered in Common DI and API project.
    /// If they are, you should not register them again here to avoid duplication and potential conflicts.
    /// These classes should eventually be able to be removed when functionality is fully implemented in the CARS applicaiton
    /// </summary>
    internal void AddMaplightAcceleratorClasses()
    {
        // Common dependency injection for Maplight Accelerator Classes, used for both Efile(Validation and Processing) and Api
        globalDependencyInjection.AddMaplightAcceleratorClasses(_services);

        // Add Maplight Accelerator Classes Dependencies those are need both Efile Validateion and Processing functions.
        CommonDependencyInjection.AddMaplightAcceleratorClasses(_services);
    }

    /// <summary>
    /// Configure JWT authentication and related services. This is required before we can access Azure
    /// resources with managed identity.
    /// </summary>
    /// <exception cref="InvalidOperationException">Thrown if JWT options or secret key are missing</exception>
    internal void SetupAuthentication()
    {
        _services.AddOptions<JwtOptions>()
            .BindConfiguration(JwtOptions.Jwt)
            .ValidateDataAnnotations()
            .ValidateOnStart();

        _services.AddAuthentication().AddJwtBearer(options =>
        {
            var jwtOptions = _builder.Configuration.GetRequiredSection(JwtOptions.Jwt).Get<JwtOptions>()
                ?? throw new InvalidOperationException("JWT options are not configured.");

            var secret = jwtOptions.Secret
                ?? throw new InvalidOperationException("JWT key is not configured.");

            options.TokenValidationParameters = new()
            {
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secret)),
                ValidAudience = jwtOptions.Audience,
                ValidIssuer = jwtOptions.Issuer,
            };
        });

        _services.AddSingleton<IJwtTokenGenerator, JwtTokenGenerator>();

        _services.AddAuthorizationBuilder()
            .SetFallbackPolicy(new AuthorizationPolicyBuilder()
            .RequireAuthenticatedUser()
            .Build());

        _services.AddScoped<IAuthorizationHandler, ResourceAccessAuthorizationHandler>();
        _services.AddScoped<IAuthorizationHandler, DecisionsAuthorizationHandler>();
    }
}

using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Mvc.Rendering;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common.Constants;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Models;
using static SOS.CalAccess.FilerPortal.Constants.DisclosureConstants;

namespace SOS.CalAccess.FilerPortal.Models.Disclosure.SmoCampaignStatement;

/// <summary>
/// ViewModel for the Campaign Statement (Slate Mailer Organization).
/// </summary>
public class SmoCampaignStatementTransactionEntryViewModel
{
    /// <summary>
    /// Gets or sets screen type to determine visibility of fields
    /// on a view.
    /// </summary>
    public SmoCampaignStatementScreenTypes? ScreenType { get; set; }

    /// <summary>
    /// Gets or sets the SMO campaign statement (filing).
    /// </summary>
    [JsonRequired]
    public long Id { get; set; }

    /// <inheritdoc />
    public FormAction? Action { get; set; }

    /// <summary>
    /// Gets or sets the FilerId.
    /// </summary>
    public long? FilerId { get; set; }

    /// <summary>
    /// Gets or sets the name of the Contact.
    /// </summary>
    public string? ContactName { get; set; }

    /// <summary>
    /// Gets or sets the Id of the Contact.
    /// </summary>
    public long? ContactId { get; set; }

    /// <summary>
    /// Gets or sets the Address Title.
    /// </summary>
    public string? AddressTitleResourceKey { get; set; }

    /// <summary>
    /// Gets or sets the participant type of the contact (e.g., Payor, Payee).
    /// </summary>
    public string? ParticipantType { get; set; }

    /// <summary>
    /// Gets or sets the payee type of the Contact.
    /// </summary>
    public string? PayeeType { get; set; }

    // --- Individual Transactor Type ---

    /// <summary>
    /// Gets or sets the first name of the individual.
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// Gets or sets the middle name of the individual.
    /// </summary>
    public string? MiddleName { get; set; }

    /// <summary>
    /// Gets or sets the last name of the individual.
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// Gets or sets the email of the individual.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets PhoneNumber of the individual.
    /// </summary>
    public PhoneNumberDto PhoneNumber { get; set; } = new PhoneNumberDto { Type = CommonConstants.PhoneNumber.TypeHome };

    /// <summary>
    /// Gets or sets FaxNumber of the individual.
    /// </summary>
    public PhoneNumberDto FaxNumber { get; set; } = new PhoneNumberDto { Type = CommonConstants.PhoneNumber.TypeFax };

    /// <summary>
    /// Gets or sets the employer of the individual.
    /// </summary>
    public string? Employer { get; set; }

    /// <summary>
    /// Gets or sets the address of the individual transactor.
    /// </summary>
    public AddressViewModel IndividualTransactorAddress { get; set; } = new() { Country = CommonConstants.DefaultCountryUS };

    // --- Individual Transactor Type (Officer) ---
    /// <summary>
    /// Gets or sets the ID of officer.
    /// </summary>
    public long? OfficerId { get; set; }

    /// <summary>
    /// Gets or sets the name of officer.
    /// </summary>
    public string? OfficerName { get; set; }

    /// <summary>
    /// Gets or sets the ID of registration that the officer belongs to.
    /// </summary>
    public long? RegistrationId { get; set; }

    // --- Committee Transactor Type ---

    /// <summary>
    /// Gets or sets the Id of the Committee.
    /// </summary>
    public long? CommitteeId { get; set; }

    /// <summary>
    /// Gets or sets the Name of the Committee.
    /// </summary>
    public string? CommitteeName { get; set; }

    // --- Candidate Transactor Type ---

    /// <summary>
    /// Gets or sets the Id of the Candidate.
    /// </summary>
    public long? CandidateId { get; set; }

    /// <summary>
    /// Gets or sets the name of the Candidate.
    /// </summary>
    public string? CandidateName { get; set; }

    /// <summary>
    /// Gets or sets the first name of the Candidate.
    /// </summary>
    public string? CandidateFirstName { get; set; }

    /// <summary>
    /// Gets or sets the middle name of the Candidate.
    /// </summary>
    public string? CandidateMiddleName { get; set; }

    /// <summary>
    /// Gets or sets the last name of the Candidate.
    /// </summary>
    public string? CandidateLastName { get; set; }

    /// <summary>
    /// Gets or sets the office sought of the Candidate.
    /// </summary>
    public string? OfficeSought { get; set; }

    /// <summary>
    /// Gets or sets the district of the Candidate.
    /// </summary>
    public string? District { get; set; }

    // --- Other Transactor Type ---

    /// <summary>
    /// Gets or sets the first name of the individual.
    /// </summary>
    public string? OrganizationName { get; set; }

    /// <summary>
    /// Gets or sets the address of the organization transactor.
    /// </summary>
    public AddressViewModel OrganizationTransactorAddress { get; set; } = new() { Country = CommonConstants.DefaultCountryUS };

    // --- About the payment ---

    /// <summary>
    /// Gets or sets the payment relationship. Candidate or Ballot Measure.
    /// </summary>
    public string? PertainsTo { get; set; }

    /// <summary>
    /// Gets or sets the Jurisdiction. State or Local
    /// </summary>
    public string? Jurisdiction { get; set; }

    /// <summary>
    /// Gets or sets the Position. Support or Oppose
    /// </summary>
    public string? Position { get; set; }

    /// <summary>
    /// Gets or sets the jurisdiction name of the Candidate or Measure.
    /// </summary>
    public string? JurisdictionName { get; set; }

    /// <summary>
    /// Gets or sets the Transaction ID
    /// </summary>
    public long? TransactionId { get; set; }

    /// <summary>
    /// Gets or sets the Transaction Amount
    /// </summary>
    public decimal? TransactionAmount { get; set; } = 0m;

    /// <summary>
    /// Gets or sets the Transaction Date 
    /// </summary>
    public DateTime? TransactionDate { get; set; }

    /// <summary>
    /// Gets or sets if the payment is a cumulative amount
    /// </summary>
    [JsonRequired]
    public bool IsIncludePreviouslyUnitemizedAmount { get; set; } = false;

    /// <summary>
    /// Gets or sets the Previously Unitemized Amount
    /// </summary>
    public decimal? UnitemizedAmount { get; set; } = 0m;

    /// <summary>
    /// Gets or sets the Cumulative Amount to Date
    /// </summary>
    public decimal? CumulativeAmountToDate { get; set; } = 0m;

    /// <summary>
    /// Gets or sets a JSON-formatted string representing an array of attached file GUIDs.
    /// </summary>
    public string? AttachedFileGuidsJson { get; set; }

    /// <summary>
    /// Gets or sets the Notes
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Gets or sets the ID of the payment (expenditure) code 
    /// </summary>
    public long? CodeId { get; set; }

    /// <summary>
    /// Gets or sets the Description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets if the payment is made by agent or independent contractor
    /// </summary>
    [JsonRequired]
    public bool IsPaidByAgentOrContractor { get; set; }

    /// <summary>
    /// Gets or sets name of agent or independent contractor
    /// </summary>
    public string? AgentOrIndependentContractorName { get; set; }

    /// <summary>
    /// Gets or sets the option list of Payment (Expenditure) Code
    /// </summary>
    public List<SelectListItem> CodeOptions { get; set; } = new();

    // --- Ballot Measure Transactor Type ---

    /// <summary>
    /// Gets or sets the Ballot Letter
    /// </summary>
    public string? BallotLetter { get; set; }

    /// <summary>
    /// Gets or sets the Ballot Letter
    /// </summary>
    public string? BallotMeasureTitle { get; set; }

    /// <summary>
    /// Gets or sets the Ballot Measure Id
    /// </summary>
    public long? BallotMeasureId { get; set; }

    /// <summary>
    /// Gets or sets the Ballot Measure Number or Letter
    /// </summary>
    public string? BallotNumberOrLetter { get; set; }

    /// <summary>
    /// Gets or sets the ID of DisclosureWithoutPaymentReceived record
    /// </summary>
    public long? DisclosureWithoutPaymentId { get; set; }

    public SmoCampaignStatementTransactionEntryViewModel()
    { }

    public SmoCampaignStatementTransactionEntryViewModel(long filingId)
    {
        Id = filingId;
    }

    public SmoCampaignStatementTransactionEntryViewModel(long filingId, long? contactId, long? transactionId)
    {
        Id = filingId;
        ContactId = contactId;
        TransactionId = transactionId;
    }

    public SmoCampaignStatementTransactionEntryViewModel(long filingId, long? filerId, long? contactId, long? transactionId)
    {
        Id = filingId;
        FilerId = filerId;
        ContactId = contactId;
        TransactionId = transactionId;
    }

    public static SmoCampaignStatementTransactionEntryViewModel MapPersonReceivingResponseToModel(PersonReceiving1000OrMoreResponseDto response)
    {
        return new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = response.FilingId,
            ContactId = response.ContactId,
            TransactionId = response.Id,
            Notes = response.Notes,
            TransactionAmount = response.Amount,
        };
    }

    public static SmoCampaignStatementTransactionEntryViewModel MapPaymentMadeResponseToModel(PaymentMadeResponseDto response)
    {
        return new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = response.FilingId,
            ContactId = response.ContactId,
            TransactionId = response.Id,
            TransactionDate = response.TransactionDate,
            Notes = response.Notes,
            TransactionAmount = response.Amount,
            CodeId = response.CodeId,
            Description = response.Description,
        };
    }

    public static SmoCampaignStatementTransactionEntryViewModel MapPaymentMadeByAgentResponseToModel(PaymentMadeByAgentOrIndependentContractorResponseDto response)
    {
        return new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = response.FilingId,
            ContactId = response.ContactId,
            TransactionId = response.Id,
            TransactionDate = response.TransactionDate,
            Notes = response.Notes,
            TransactionAmount = response.Amount,
            Description = response.Description,
            AgentOrIndependentContractorName = response.AgentOrIndependentContractorName,
            CodeId = response.CodeId,
        };
    }

    public static SmoCampaignStatementTransactionEntryViewModel MapPaymentReceivedResponseToModel(PaymentReceivedResponseDto response)
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = response.FilingId,
            ContactId = response.ContactId,
            TransactionId = response.Id,
            TransactionDate = response.TransactionDate,
            Notes = response.Notes,
            TransactionAmount = response.Amount,
            Position = response.Position,
        };

        MapStanceOnCandidateToModel(response.StanceOnCandidateDto, model);
        MapStanceOnBallotMeasureToModel(response.StanceOnBallotMeasureDto, model);

        return model;
    }

    public static SmoCampaignStatementTransactionEntryViewModel MapDisclosureWithoutPaymentReceivedResponseToModel(DisclosureWithoutPaymentReceivedDto response)
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = response.FilingId,
            DisclosureWithoutPaymentId = response.Id,
            Position = response.Position,
        };

        MapStanceOnCandidateToModel(response.StanceOnCandidate, model);
        MapStanceOnBallotMeasureToModel(response.StanceOnBallotMeasure, model);

        return model;
    }

    public static void MapStanceOnCandidateToModel(StanceOnCandidateDto? stance, SmoCampaignStatementTransactionEntryViewModel model)
    {
        if (stance != null)
        {
            model.PertainsTo = CandidateOrMeasure.Candidate;
            model.Jurisdiction = stance.CandidateId.GetValueOrDefault() != 0 ? CandidateOrMeasure.State : CandidateOrMeasure.Local;
            model.CandidateId = stance.CandidateId;
            model.CandidateName = stance.CandidateName;
            model.CandidateFirstName = stance.FirstName;
            model.CandidateLastName = stance.LastName;
            model.CandidateMiddleName = stance.MiddleName;
            model.OfficeSought = stance.OfficeSought;
            model.JurisdictionName = stance.Jurisdiction;
            model.District = stance.District;
        }
    }

    public static void MapStanceOnBallotMeasureToModel(StanceOnBallotMeasureDto? stance, SmoCampaignStatementTransactionEntryViewModel model)
    {
        if (stance != null)
        {
            model.PertainsTo = CandidateOrMeasure.BallotMeasure;
            model.Jurisdiction = stance.BallotMeasureId.GetValueOrDefault() != 0 ? CandidateOrMeasure.State : CandidateOrMeasure.Local;
            model.BallotMeasureId = stance.BallotMeasureId;
            model.BallotNumberOrLetter = stance.BallotNumberOrLetter;
            model.JurisdictionName = stance.Jurisdiction;
            model.BallotLetter = stance.BallotLetter;
            model.BallotMeasureTitle = stance.Title;
        }
    }

    public static SmoCampaignStatementTransactionEntryViewModel MapIndividualContactToModel(
        SmoCampaignStatementTransactionEntryViewModel model,
        Generated.IndividualContactResponseDto dto)
    {
        model.ParticipantType = FilerContactType.Individual.Name;
        model.FirstName = dto.FirstName ?? string.Empty;
        model.MiddleName = dto.MiddleName ?? string.Empty;
        model.LastName = dto.LastName ?? string.Empty;
        model.Employer = dto.Employer ?? string.Empty;
        model.ContactName = $"{dto.FirstName} {dto.LastName}";

        // Email
        model.Email = dto.EmailAddresses?.Count > 0 ? dto.EmailAddresses[0].Email : string.Empty;

        // Phone
        model.PhoneNumber = MapPhoneNumber(dto.PhoneNumbers, CommonConstants.PhoneNumber.TypeHome);
        model.FaxNumber = MapPhoneNumber(dto.PhoneNumbers, CommonConstants.PhoneNumber.TypeFax);

        model.IndividualTransactorAddress = MapAddress(dto.Addresses);

        return model;
    }

    public static SmoCampaignStatementTransactionEntryViewModel MapFilerCommitteeContactToModel(
        SmoCampaignStatementTransactionEntryViewModel model,
        Generated.FilerCommitteeContactResponseDto dto)
    {
        model.ParticipantType = FilerContactType.Filer.Name;
        model.CommitteeId = dto.Id;
        model.CommitteeName = dto.CommitteeName ?? string.Empty;
        model.ContactName = model.CommitteeName;

        //model.TransactorAddress = MapAddress(dto.Addresses)

        return model;
    }

    public static SmoCampaignStatementTransactionEntryViewModel MapOrganizationContactToModel(
        SmoCampaignStatementTransactionEntryViewModel model,
        Generated.OrganizationContactResponseDto dto)
    {
        model.ParticipantType = FilerContactType.Organization.Name;
        model.OrganizationName = dto.OrganizationName ?? string.Empty;
        model.ContactName = model.OrganizationName;

        model.OrganizationTransactorAddress = MapAddress(dto.Addresses);

        return model;
    }

    public static SmoCampaignStatementTransactionEntryViewModel MapCandidateContactToModel(
        SmoCampaignStatementTransactionEntryViewModel model,
        Generated.CandidateContactResponseDto dto)
    {
        model.ParticipantType = FilerContactType.Candidate.Name;

        model.CandidateFirstName = dto.FirstName ?? string.Empty;
        model.CandidateMiddleName = dto.MiddleName ?? string.Empty;
        model.CandidateLastName = dto.LastName ?? string.Empty;
        model.ContactName = $"{dto.FirstName} {dto.LastName}";

        model.OfficeSought = dto.OfficeSought ?? string.Empty;
        model.JurisdictionName = dto.Jurisdiction ?? string.Empty;
        model.District = dto.District ?? string.Empty;

        return model;
    }

    public static void MapDefaultAddressToModel(SmoCampaignStatementTransactionEntryViewModel model, string transactionType)
    {
        if (transactionType == TransactionType.PaymentReceived.Name || transactionType == TransactionType.PaymentMade.Name)
        {
            model.IndividualTransactorAddress = AddressWithDefaultCountry();
            model.OrganizationTransactorAddress = AddressWithDefaultCountry();
        }
    }

    public static SmoCampaignStatementTransactionEntryViewModel MapOfficerContactToModel(SmoCampaignStatementTransactionEntryViewModel model, SmoRegistrationContactDto officer)
    {
        model.ParticipantType = FilerContactType.Individual.Name;
        model.FirstName = officer.FirstName;
        model.LastName = officer.LastName;
        model.MiddleName = officer.MiddleName;
        model.OfficerName = $"{officer.FirstName} {officer.LastName}";
        model.Email = officer.Email;
        model.PhoneNumber = officer.PhoneNumber ?? new PhoneNumberDto();
        model.IndividualTransactorAddress = MapAddress(officer.Address);
        model.ContactId = null; // Set to null to initialize a new contact for officer

        return model;
    }

    #region Private
    private static AddressViewModel MapAddress(IReadOnlyList<Generated.AddressDto> addressDtos)
    {
        if (addressDtos == null || addressDtos.Count == 0)
        {
            return new AddressViewModel();
        }

        // Address: get Mailing or fallback to first
        var address = addressDtos.LastOrDefault(x => x.Purpose == CommonConstants.Address.PurposeMailing) ?? addressDtos[0];

        return new AddressViewModel
        {
            Id = address.Id,
            Street = address.Street ?? string.Empty,
            Street2 = address.Street2 ?? string.Empty,
            City = address.City ?? string.Empty,
            State = address.State ?? string.Empty,
            Zip = address.Zip ?? string.Empty,
            Country = address.Country ?? string.Empty,
            Type = address.Type ?? string.Empty,
            Purpose = address.Purpose ?? string.Empty
        };
    }

    private static AddressViewModel MapAddress(AddressDto? addressDto)
    {
        if (addressDto is null)
        {
            return new AddressViewModel();
        }

        return new AddressViewModel
        {
            Id = addressDto.Id,
            Street = addressDto.Street ?? string.Empty,
            Street2 = addressDto.Street2 ?? string.Empty,
            City = addressDto.City ?? string.Empty,
            State = addressDto.State ?? string.Empty,
            Purpose = addressDto.Purpose ?? string.Empty,
            Zip = addressDto.Zip ?? string.Empty,
            Country = addressDto.Country ?? string.Empty,
            Type = addressDto.Type ?? string.Empty,
        };
    }

    private static AddressViewModel AddressWithDefaultCountry()
    {
        return new AddressViewModel
        {
            Country = "United States"
        };
    }

    private static PhoneNumberDto MapPhoneNumber(IReadOnlyList<Generated.PhoneNumberDto> phoneNumberDtos, string phoneType)
    {
        var phoneNumber = phoneNumberDtos.FirstOrDefault(x => x.Type == phoneType);

        if (phoneNumber == null)
        {
            return new PhoneNumberDto { Type = phoneType };
        }

        return new PhoneNumberDto
        {
            Id = phoneNumber.Id,
            Type = phoneNumber.Type,
            SelectedCountry = phoneNumber.SelectedCountry,
            CountryCode = phoneNumber.CountryCode,
            Extension = phoneNumber.Extension,
            InternationalNumber = phoneNumber.InternationalNumber,
            Number = phoneNumber.Number
        };
    }
    #endregion
}

public enum SmoCampaignStatementScreenTypes
{
    PaymentReceived,
    PaymentMade,
    PaymentMadeByAgent,
}

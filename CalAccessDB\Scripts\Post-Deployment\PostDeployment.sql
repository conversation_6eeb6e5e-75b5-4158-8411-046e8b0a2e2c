/*
Post-Deployment Script Template							
--------------------------------------------------------------------------------------
 This file contains SQL statements that will be appended to the build script.		
 Use SQLCMD syntax to include a file in the post-deployment script.			
 Example:      :r .\myfile.sql								
 Use SQLCMD syntax to reference a variable in the post-deployment script.		
 Example:      :setvar TableName MyTable							
               SELECT * FROM [$(TableName)]

Order Scripts Alphabetically unless there is a dependency

----Ordering Dependencies----
Script.Permission.sql must run prior to Script.AuthorizationGroupPermissions.sql
Script.Permission.sql must run prior to Script.FilerRolePermissions.sql
Script.FilerType.sql must run prior to Script.FilerRole.sql
Script.FilerType.sql must run prior to Script.FilerRolePermissions.sql
Script.NotificationType.sql must run prior to Script.NotificationTemplate.sql
Script.NotificationTemplate.sql must run prior to Script.NotificationTemplateTranslation.sql
--------------------------------------------------------------------------------------
*/
:r .\Script.Permission.sql
:r .\Script.FilerType.sql
:r .\Script.ActivityExpenseType.sql
:r .\Script.AdvertisementDistributionMethod.sql
:r .\Script.Agency.sql
:r .\Script.ApiRequestStatus.sql
:r .\Script.AuthorizationGroup.sql
:r .\Script.AuthorizationGroupPermissions.sql
:r .\Script.BillHouse.sql
:r .\Script.CommitteeType.sql
:r .\Script.ContributionType.sql
:r .\Script.Country.sql
:r .\Script.DecisionsErrorMessage.sql
:r .\Script.DonorType.sql
:r .\Script.ElectionType.sql
:r .\Script.ExpenditureCode.sql
:r .\Script.FilerContactType.sql
:r .\Script.FilerRole.sql
:r .\Script.FilerRolePermissions.sql
:r .\Script.FilerStatus.sql
:r .\Script.FilerLinkType.sql
:r .\Script.FilingStatus.sql
:r .\Script.FilingSummaryStatus.sql
:r .\Script.FilingSummaryType.sql
:r .\Script.FilingType.sql
:r .\Script.JsonSchemaReference.sql
:r .\Script.LegislativeSession.sql
:r .\Script.LinkageStatus.sql
:r .\Script.MonetaryType.sql
:r .\Script.NotificationType.sql
:r .\Script.NotificationTemplate.sql
:r .\Script.NotificationTemplateTranslation.sql
:r .\Script.OfficialPosition.sql
:r .\Script.PaymentCode.sql
:r .\Script.PoliticalParty.sql
:r .\Script.RegistrationContactType.sql
:r .\Script.RegistrationStatus.sql
:r .\Script.SeedConversionUserActor.sql
:r .\Script.SeedOfficeDistrictElectionTypePoliticalParty.sql
:r .\Script.SeedExpenditureExpenseAmount.sql
:r .\Script.TransactionType.sql
:r .\Script.UploadedFileStatus.sql
:r .\Script.FilingContactSummaryType.sql
:r .\Script.FilingPeriodType.sql
:r .\Script.BusinessSubcategory.sql
:r .\Script.IndustryGroupClassificationType.sql
:r .\Script.NatureAndInterestType.sql

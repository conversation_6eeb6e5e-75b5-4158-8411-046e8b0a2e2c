using System.Text.Json.Serialization;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;

[JsonPolymorphic(UnknownDerivedTypeHandling = JsonUnknownDerivedTypeHandling.FallBackToNearestAncestor)]
[JsonDerivedType(typeof(PaymentReceivedRequest), nameof(PaymentReceived))]
[JsonDerivedType(typeof(PaymentMadeRequest), nameof(PaymentMade))]
[JsonDerivedType(typeof(PaymentMadeByAgentOrIndependentContractorRequest), $"{nameof(PaymentMade)}ByAgentOrIndependentContractor")]
[JsonDerivedType(typeof(PersonReceiving1000OrMoreRequest), nameof(PersonReceiving1000OrMore))]

/// <summary>
/// Represents the request model for a detail disclosure transaction.
/// </summary>
public abstract class TransactionDetailRequest
{
    /// <summary>
    /// Gets or sets a reference to the contact that was originally used to
    /// provide contact information for this transaction.
    /// </summary>
    public long ContactId { get; set; }

    /// <summary>
    /// Gets or sets the date that this transaction happened
    /// </summary>
    public DateTime? TransactionDate { get; set; }

    /// <summary>
    /// Gets or sets the amount for this transaction.
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// Gets or sets the note for this transaction.
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Gets or sets the CheckRequiredFieldsFlag
    /// </summary>
    public bool CheckRequiredFieldsFlag { get; set; }

    /// <summary>
    /// Gets or sets a JSON-formatted string representing an array of attached file GUIDs.
    /// </summary>
    public string? AttachedFileGuidsJson { get; set; }


    protected void MapBaseProperties(Transaction transaction)
    {
        transaction.ContactId = ContactId;
        transaction.TransactionDate = TransactionDate;
        transaction.Notes = Notes;
    }

    public virtual void MapToExistingEntity(Transaction transaction)
    {
        MapBaseProperties(transaction);

        transaction.Amount = (Currency)Amount;
    }
}

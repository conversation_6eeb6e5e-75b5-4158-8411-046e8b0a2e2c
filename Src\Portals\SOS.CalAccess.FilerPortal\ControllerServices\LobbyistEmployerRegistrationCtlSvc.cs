using System.Globalization;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Rendering;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common.Constants;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using IFilingsApi = SOS.CalAccess.FilerPortal.Generated.IFilingsApi;

namespace SOS.CalAccess.FilerPortal.ControllerServices;

public class LobbyistEmployerRegistrationCtlSvc(
    IFilingsApi filingsApi,
        IDecisionsValidationMapService decisionsValidationMapService,
    ILobbyistEmployerRegistrationSvc lobbyistEmployerRegistrationSvc,
    ILobbyistRegistrationSvc lobbyistRegistrationSvc,
    IReferenceDataSvc referenceDataSvc
    ) : ILobbyistEmployerRegistrationCtlSvc
{
    private const string BusinessAddress = "Business";
    private const string MailingAddress = "Mailing";

    #region Step01 - General Information
    public async Task<LobbyistEmployerRegistrationStep01GenInfo> GetStep01GenInfoViewModel(long? id, CancellationToken cancellationToken)
    {
        var model = new LobbyistEmployerRegistrationStep01GenInfo
        {
            Id = id.GetValueOrDefault(),
            Addresses = new List<AddressViewModel>
            {
                new() { Purpose = BusinessAddress, Country = "United States" },
                new() { Purpose = MailingAddress }
            },
        };

        if (id.HasValue && id.Value != 0)
        {
            var existingLobbyistEmployer = await lobbyistEmployerRegistrationSvc.GetLobbyistEmployer(id.Value);

            if (existingLobbyistEmployer is not null)
            {
                MappingStep01GenInfoViewModelFromDto(model, existingLobbyistEmployer);
            }
        }

        await PopulateStep01GenInfoReferenceData(model, filingsApi);

        return model;
    }

    /// <summary>
    /// Handler for creating a new or updating an existing lobbyist registration record 
    /// </summary>
    public async Task<RegistrationResponseDto> SubmitStep01GenInfoViewModel(LobbyistEmployerRegistrationStep01GenInfo model, ModelStateDictionary modelState, bool isSubmission = true)
    {
        RegistrationResponseDto response;

        var request = MapLobbyistEmployerRegistrationStep01ViewModelToLobbyistEmployerGeneralInfoRequest(model);

        if (isSubmission)
        {
            request.CheckRequiredFieldsFlag = isSubmission;
        }

        if (model.Id is null or 0)
        {
            response = await lobbyistEmployerRegistrationSvc.CreateLobbyistEmployerRegistrationPage03(request);
        }
        else
        {
            response = await lobbyistEmployerRegistrationSvc.UpdateLobbyistEmployerRegistrationPage03(model.Id.GetValueOrDefault(), request);
        }

        if (!response.Valid)
        {
            await PopulateStep01GenInfoReferenceData(model, filingsApi);
            decisionsValidationMapService.ApplyErrorsToModelState(GetLobbyistEmployerStep01GenInfoFieldValidationMap(model), response.ValidationErrors, modelState);
        }

        return response;
    }

    public LobbyistEmployerGeneralInfoRequest MapLobbyistEmployerRegistrationStep01ViewModelToLobbyistEmployerGeneralInfoRequest(LobbyistEmployerRegistrationStep01GenInfo model)
    {
        var sourceBusinessAddress = model.Addresses.FirstOrDefault(x => x.Purpose == CommonConstants.Address.TypeBusiness) ?? new AddressViewModel();
        var sourceMailingAddress = model.Addresses.FirstOrDefault(x => x.Purpose == CommonConstants.Address.PurposeMailing) ?? new AddressViewModel();

        var organizationAddress = sourceBusinessAddress.ToAddressDto();

        var mailingAddress = model.IsSameAsBusinessAddress
            ? organizationAddress
            : sourceMailingAddress.ToAddressDto();

        var request = new LobbyistEmployerGeneralInfoRequest
        {
            EmployerName = model.Name,
            Email = model.Email,
            PhoneNumber = model.PhoneNumber,
            FaxNumber = model.FaxNumber,
            BusinessAddress = organizationAddress,
            MailingAddress = mailingAddress,
            LegislativeSessionId = model.LegislativeSessionId,
            QualificationDate = model.QualificationDate,
            IsLobbyingCoalition = model.IsLobbyingCoalition,
        };

        return request;
    }

    public LobbyistEmployerStateAgenciesRequest MapLobbyistEmployerRegistrationStep01ViewModelToLobbyistEmployerStateAgenciesRequest(LobbyistEmployerRegistrationStep01Agencies model)
    {

        var request = new LobbyistEmployerStateAgenciesRequest
        {
            Agencies = model.SelectedAgencies?.Count > 0 ? [.. model.SelectedAgencies.Select(x => new RegistrationAgencyDto
            {
                RegistrationId = model.Id.GetValueOrDefault(),
                AgencyId = x
            })] : [],
            IsLobbyingStateLegislature = model.StateLegislatureLobbied
        };

        return request;
    }

    public LobbyistEmployerLobbyingInterestsRequest MapLobbyistEmployerRegistrationStep01ViewModelToLobbyistEmployerLobbyingInterestsRequest(LobbyistEmployerRegistrationStep01NatureInterests model)
    {

        var request = new LobbyistEmployerLobbyingInterestsRequest
        {
            NatureAndPurpose = model.FilingInterestsDescription
        };

        return request;
    }
    #endregion

    #region Step01 - Agencies
    public async Task<LobbyistEmployerRegistrationStep01Agencies> GetStep01AgenciesViewModel(long? id)
    {
        var model = new LobbyistEmployerRegistrationStep01Agencies
        {
            Id = id.GetValueOrDefault(),
        };

        if (id.HasValue && id.Value != 0)
        {
            var existingLobbyistEmployer = await lobbyistEmployerRegistrationSvc.GetLobbyistEmployer(id.Value);

            if (existingLobbyistEmployer is not null)
            {
                MappingStep01AgenciesViewModelFromDto(model, existingLobbyistEmployer);
            }
        }

        await PopulateStep01AgenciesReferenceData(model);

        return model;
    }

    public async Task<RegistrationResponseDto> SubmitStep01AgenciesViewModel(LobbyistEmployerRegistrationStep01Agencies model, ModelStateDictionary modelState, bool isSubmission = true)
    {
        RegistrationResponseDto response;

        var request = MapLobbyistEmployerRegistrationStep01ViewModelToLobbyistEmployerStateAgenciesRequest(model);

        if (isSubmission)
        {
            request.CheckRequiredFieldsFlag = isSubmission;
        }

        if (model.Id is null or 0)
        {
            throw new KeyNotFoundException($"No id exists for registration");
        }
        else
        {
            response = await lobbyistEmployerRegistrationSvc.UpdateLobbyistEmployerRegistrationPage04(model.Id.GetValueOrDefault(), request);
        }

        if (!response.Valid)
        {
            await PopulateStep01AgenciesReferenceData(model);
            decisionsValidationMapService.ApplyErrorsToModelState(GetLobbyistEmployerStep01AgenciesFieldValidationMap(), response.ValidationErrors, modelState);
        }

        return response;
    }


    #endregion

    #region Step01 - Lobbing Interests
    public async Task<LobbyistEmployerRegistrationStep01NatureInterests> GetStep01NatureInterestsViewModel(long? id)
    {
        var model = new LobbyistEmployerRegistrationStep01NatureInterests
        {
            Id = id.GetValueOrDefault(),
        };

        if (id.HasValue && id.Value != 0)
        {
            var existingLobbyistEmployer = await lobbyistEmployerRegistrationSvc.GetLobbyistEmployer(id.Value);

            if (existingLobbyistEmployer is not null)
            {
                MappingStep0NatureInterestsViewModelFromDto(model, existingLobbyistEmployer);
            }
        }

        return model;
    }

    public async Task<RegistrationResponseDto> SubmitStep01NatureInterestViewModel(LobbyistEmployerRegistrationStep01NatureInterests model, ModelStateDictionary modelState, bool isSubmission = true)
    {
        RegistrationResponseDto response;

        var request = MapLobbyistEmployerRegistrationStep01ViewModelToLobbyistEmployerLobbyingInterestsRequest(model);

        if (isSubmission)
        {
            request.CheckRequiredFieldsFlag = isSubmission;
        }

        if (model.Id is null or 0)
        {
            throw new KeyNotFoundException($"No id exists for registration");
        }
        else
        {
            response = await lobbyistEmployerRegistrationSvc.UpdateLobbyistEmployerRegistrationPage05(model.Id.GetValueOrDefault(), request);
        }

        if (!response.Valid)
        {
            decisionsValidationMapService.ApplyErrorsToModelState(GetLobbyistEmployerStep01NatureInterestsFieldValidationMap(), response.ValidationErrors, modelState);
        }

        return response;
    }
    #endregion

    #region Initiate Certify lobbyist
    public async Task<LobbyistCertificationViewModel> GetLobbyistCertificationViewModel(long id, CancellationToken cancellationToken)
    {
        var existingLobbyist = await lobbyistRegistrationSvc.GetLobbyistRegistration(id) ?? throw new KeyNotFoundException($"Lobbyist not found. id = {id}");
        return ConvertToLobbyistCertificationViewModel(existingLobbyist);
    }

    public async Task<RegistrationResponseDto> SaveLobbyistCertificationViewModel(LobbyistCertificationViewModel model)
    {
        LobbyistRegistrationRequestDto request = MapLobbyistViewModelToRequest(model);

        RegistrationResponseDto response = await lobbyistRegistrationSvc.UpdateLobbyistRegistration(model.Id.GetValueOrDefault(), request);

        return response;
    }
    #endregion

    private static LobbyistCertificationViewModel ConvertToLobbyistCertificationViewModel(LobbyistResponseDto dto)
    {
        var result = new LobbyistCertificationViewModel()
        {
            Id = dto.Id,
            Version = dto.Version,
            Email = dto.Email ?? string.Empty,
            SelfRegister = dto.SelfRegister,
            FirstName = dto.FirstName,
            MiddleName = dto.MiddleName,
            LastName = dto.LastName,
            LobbyistEmployerOrLobbyingFirmId = dto.LobbyistEmployerOrLobbyingFirmId ?? null,
            LegislativeSessionId = dto.LegislativeSessionId,
            PlacementAgent = dto.IsPlacementAgent,
            IsSameAsBusinessAddress = dto.IsSameAsCandidateAddress,
            SelectedAgencies = [.. dto.Agencies!.Select(x => x.AgencyId!.Value)],
            QualificationDate = dto.DateOfQualification,
            EthicsCourseCompleted = dto.CompletedEthicsCourse,
            EthicsCourseCompletedOn = dto.CompletedCourseDate,
            LobbyOnlySpecifiedAgencies = dto.LobbyOnlySpecifiedAgencies,
            IsLobbyingStateLegislature = dto.IsLobbyingStateLegislature,
            IsNewCertification = dto.IsNewCertification,
            IsDisableToSelectEmployerOrFirm = true,
            LobbyistEmployerOrLobbyingFirmName = dto.LobbyistEmployerOrLobbyingFirmName,
            Addresses = new List<AddressViewModel>
            {
                new() { Purpose = BusinessAddress, Country = "United States" },
                new() { Purpose = MailingAddress }
            }
        };
        if (dto.Addresses != null)
        {
            result.Addresses = [.. result.Addresses.Select(x =>
            {
                var address = dto.Addresses.FirstOrDefault(d => d.Purpose == x.Purpose);
                if (address is not null)
                {
                    x.Country = address.Country;
                    x.Street2 = address.Street2;
                    x.Street = address.Street;
                    x.State = address.State;
                    x.City = address.City;
                    x.Zip = address.Zip;
                }
                return x;
            })];
        }

        if (dto.PhoneNumbers != null)
        {
            var phoneNumber = dto.PhoneNumbers.FirstOrDefault(x => x.Type == "Home");
            result.PhoneNumber = string.IsNullOrWhiteSpace(phoneNumber?.Number) ? string.Empty : phoneNumber.Number;
            result.PhoneNumberCountryCode = string.IsNullOrWhiteSpace(phoneNumber?.CountryCode) ? string.Empty : phoneNumber.CountryCode;

            var faxNumber = dto.PhoneNumbers.FirstOrDefault(x => x.Type == "Fax");
            result.FaxNumber = string.IsNullOrWhiteSpace(faxNumber?.Number) ? string.Empty : faxNumber.Number;
            result.FaxNumberCountryCode = string.IsNullOrWhiteSpace(faxNumber?.CountryCode) ? string.Empty : faxNumber.CountryCode;
        }

        return result;
    }

    public LobbyistRegistrationRequestDto MapLobbyistViewModelToRequest(LobbyistCertificationViewModel model)
    {
        AddressViewModel sourceMailingAddress = model.Addresses.FirstOrDefault(x => x.Purpose == MailingAddress) ?? new AddressViewModel();
        AddressViewModel sourceBusinessAddress = model.Addresses.FirstOrDefault(x => x.Purpose == BusinessAddress) ?? new AddressViewModel();

        AddressDto businessAddress = sourceBusinessAddress.ToAddressDto();

        AddressDto mailingAddress = model.IsSameAsBusinessAddress
            ? businessAddress
            : sourceMailingAddress.ToAddressDto();

        LobbyistRegistrationRequestDto request = new()
        {
            Email = model.Email,
            FirstName = model.FirstName,
            MiddleName = model.MiddleName,
            LastName = model.LastName,
            SelfRegister = model.SelfRegister,
            EffectiveDateOfChanges = model.EffectiveDateOfChanges,
            LobbyistEmployerOrLobbyingFirmId = model.LobbyistEmployerOrLobbyingFirmId,
            PhoneNumber = GetFullPhoneNumber(model.PhoneNumberCountryCode ?? "", model.PhoneNumber ?? ""),
            FaxNumber = GetFullPhoneNumber(model.FaxNumberCountryCode ?? "", model.FaxNumber ?? ""),
            BusinessAddress = businessAddress,
            LegislativeSessionId = model.LegislativeSessionId,
            MailingAddress = mailingAddress,
            IsPlacementAgent = model.PlacementAgent,
            DateOfQualification = model.QualificationDate,
            CompletedCourseDate = model.EthicsCourseCompletedOn,
            CompletedEthicsCourse = model.EthicsCourseCompleted,
            LobbyOnlySpecifiedAgencies = model.LobbyOnlySpecifiedAgencies,
            IsLobbyingStateLegislature = model.IsLobbyingStateLegislature,
            Agencies = model.SelectedAgencies?.Count > 0 ? [.. model.SelectedAgencies.Select(x => new RegistrationAgencyDto
            {
                RegistrationId = model.Id.GetValueOrDefault(),
                AgencyId = x
            })] : [],
            IsSameAsCandidateAddress = model.IsSameAsBusinessAddress,
            Photo = model.UploadedFilename,
            IsNewCertification = model.IsNewCertification,
        };

        return request;
    }

    private static PhoneNumberDto? GetFullPhoneNumber(string countryCode, string number)
    {
        if (!string.IsNullOrEmpty(number))
        {
            string code = string.IsNullOrEmpty(countryCode) ? "" : countryCode.Split('-')[0];
            return new()
            {
                Id = 0,
                CountryCode = code ?? string.Empty,
                SelectedCountry = 0,
                InternationalNumber = false,
                Number = number,
                Extension = null,
                Type = string.Empty,
            };

        }
        return null;
    }

    #region Public - Helpers
    public async Task PopulateStep01GenInfoReferenceData(
LobbyistEmployerRegistrationStep01GenInfo model,
IFilingsApi filingsApi)
    {
        var legislativeSessions = await filingsApi.GetLegistlativeSessions();
        model.LegislativeSessionOptions = legislativeSessions.Sessions?
            .OrderBy(ls => ls.StartDate)
            .Select(ls => new SelectListItem
            {
                Value = ls.Id.ToString(CultureInfo.InvariantCulture),
                Text = $"{ls.StartDate.Year} - {ls.EndDate.Year}"
            })
            .ToList() ?? new List<SelectListItem>();
    }
    #endregion

    #region Private
    private async Task PopulateStep01AgenciesReferenceData(
LobbyistEmployerRegistrationStep01Agencies model)
    {
        var agencies = await referenceDataSvc.GetAllAgencies();
        model.Agencies = agencies!.ToDictionary(x => x.Id, x => x.Name);
    }

    private static Dictionary<string, FieldProperty> GetLobbyistEmployerStep01GenInfoFieldValidationMap(LobbyistEmployerRegistrationStep01GenInfo model)
    {

        int businessAddressIndex = model.Addresses.FindIndex(x => x.Purpose == BusinessAddress);
        int mailingAddressIndex = model.Addresses.FindIndex(x => x.Purpose == MailingAddress);

        const string prefix = "FR_LOB_Filing_LobbyistEmployer_Step1_1_Info";
        return new()
            {
            { $"{prefix}.LobbyingEntityName", new FieldProperty("Name", ResourceConstants.LobbyistEmployerRegistration03Name) },
            { $"{prefix}.PhoneNumber", new FieldProperty("PhoneNumber", ResourceConstants.LobbyistEmployerRegistration03PhoneNumber) },
            { $"{prefix}.EmailAddress", new FieldProperty("Email", ResourceConstants.LobbyistEmployerRegistration03Email) },

            { $"{prefix}.BusinessAddress.Country", new FieldProperty($"Addresses[{businessAddressIndex}].Country", ResourceConstants.LobbyistEmployerRegistration03Country) },
            { $"{prefix}.BusinessAddress.Street", new FieldProperty($"Addresses[{businessAddressIndex}].Street", ResourceConstants.LobbyistEmployerRegistration03Street) },
            { $"{prefix}.BusinessAddress.Street2", new FieldProperty($"Addresses[{businessAddressIndex}].Street2", ResourceConstants.LobbyistEmployerRegistration03Street2) },
            { $"{prefix}.BusinessAddress.City", new FieldProperty($"Addresses[{businessAddressIndex}].City", ResourceConstants.LobbyistEmployerRegistration03City) },
            { $"{prefix}.BusinessAddress.State", new FieldProperty($"Addresses[{businessAddressIndex}].State", ResourceConstants.LobbyistEmployerRegistration03State) },
            { $"{prefix}.BusinessAddress.Zip", new FieldProperty($"Addresses[{businessAddressIndex}].Zip", ResourceConstants.LobbyistEmployerRegistration03Zip) },

            { $"{prefix}.MailingAddress.Country", new FieldProperty($"Addresses[{mailingAddressIndex}].Country", ResourceConstants.LobbyistEmployerRegistration03Country) },
            { $"{prefix}.MailingAddress.Street", new FieldProperty($"Addresses[{mailingAddressIndex}].Street", ResourceConstants.LobbyistEmployerRegistration03Street) },
            { $"{prefix}.MailingAddress.Street2", new FieldProperty($"Addresses[{mailingAddressIndex}].Street2", ResourceConstants.LobbyistEmployerRegistration03Street2) },
            { $"{prefix}.MailingAddress.City", new FieldProperty($"Addresses[{mailingAddressIndex}].City", ResourceConstants.LobbyistEmployerRegistration03City) },
            { $"{prefix}.MailingAddress.State", new FieldProperty($"Addresses[{mailingAddressIndex}].State", ResourceConstants.LobbyistEmployerRegistration03State) },
            { $"{prefix}.MailingAddress.Zip", new FieldProperty($"Addresses[{mailingAddressIndex}].Zip", ResourceConstants.LobbyistEmployerRegistration03Zip) },

            { $"{prefix}.LegislativeSessionId", new FieldProperty("LegislativeSessionId", ResourceConstants.LobbyistEmployerRegistration03LegislativeSession) },
            { $"{prefix}.QualificationDate", new FieldProperty("QualificationDate", ResourceConstants.LobbyistEmployerRegistration03QualificationDate) },
        };
    }

    private static Dictionary<string, FieldProperty> GetLobbyistEmployerStep01AgenciesFieldValidationMap()
    {

        const string prefix = "FR_LOB_Filing_LobbyistEmployer_Step1_2_StateAgenciesToBeInfluenced";
        return new()
        {
            { $"{prefix}.Agencies", new FieldProperty("SelectedAgencies", ResourceConstants.LobbyistEmployerRegistration04StateAgenciesLobbied ) },
            { $"{prefix}.IsLobbyingStateLegislature", new FieldProperty("StateLegislatureLobbied", ResourceConstants.LobbyistEmployerRegistration04StateLegislatureLobbied) },
        };
    }

    private static Dictionary<string, FieldProperty> GetLobbyistEmployerStep01NatureInterestsFieldValidationMap()
    {
        return new()
        {
            { "LobbyingInterestsDescription", new FieldProperty("FilingInterestsDescription", ResourceConstants.LobbyistEmployerRegistration05DescribeInterests ) },
        };
    }


    private static void MappingStep01GenInfoViewModelFromDto(LobbyistEmployerRegistrationStep01GenInfo model, LobbyistEmployerResponseDto dto)
    {
        model.Id = dto.Id;
        model.Version = dto.Version;
        model.Email = dto.Email ?? string.Empty;
        model.Addresses = MapAddresses(model.Addresses, dto.Addresses);
        model.PhoneNumber = dto.PhoneNumbers?.FirstOrDefault(x => x.Type == "Home") ?? new();
        model.FaxNumber = dto.PhoneNumbers?.FirstOrDefault(x => x.Type == "Fax") ?? new();
        model.Name = dto.Name;
        model.IsLobbyingCoalition = false;
        model.LegislativeSessionId = dto.LegislativeSessionId;
        model.QualificationDate = dto.DateQualified;
    }

    private static void MappingStep01AgenciesViewModelFromDto(LobbyistEmployerRegistrationStep01Agencies model, LobbyistEmployerResponseDto dto)
    {
        model.Id = dto.Id;
        model.Version = dto.Version;
        model.SelectedAgencies = dto.Agencies?.Select(x => x.AgencyId!.Value).ToList();
        model.StateLegislatureLobbied = dto.StateLegislatureLobbying;
    }

    private static void MappingStep0NatureInterestsViewModelFromDto(LobbyistEmployerRegistrationStep01NatureInterests model, LobbyistEmployerResponseDto dto)
    {
        model.Id = dto.Id;
        model.Version = dto.Version;
        model.FilingInterestsDescription = dto.NatureAndPurpose;
    }

    private static List<AddressViewModel> MapAddresses(
    List<AddressViewModel> targetAddresses,
    List<AddressDtoModel>? sourceAddresses)
    {
        if (sourceAddresses == null)
        {
            return targetAddresses;
        }

        return targetAddresses
            .Select(target =>
            {
                var source = sourceAddresses.FirstOrDefault(s => s.Purpose == target.Purpose);
                if (source != null)
                {
                    target.Country = source.Country;
                    target.Street = source.Street;
                    target.Street2 = source.Street2;
                    target.City = source.City;
                    target.State = source.State;
                    target.Zip = source.Zip;
                }
                return target;
            })
            .ToList();
    }

    /// <summary>
    /// Get inhouse lobbyist registration view model
    /// </summary>
    /// <param name="lobbyistEmployerRegistrationId">lobbyist employer registration id</param>
    /// <param name="lobbyistRegistrationId">lobbyist registration id</param>
    /// <returns></returns>
    /// <exception cref="KeyNotFoundException"></exception>
    public async Task<InHouseLobbyistViewModel> GetInHouseLobbyistViewModel(long lobbyistEmployerRegistrationId, long? lobbyistRegistrationId)
    {
        var model = new InHouseLobbyistViewModel
        {
            LobbyistEmployerRegistrationId = lobbyistEmployerRegistrationId,
            LobbyistRegistrationId = lobbyistRegistrationId
        };

        if (lobbyistRegistrationId is not null or > 0)
        {
            var registration = await lobbyistRegistrationSvc.GetLobbyistRegistration(lobbyistRegistrationId.GetValueOrDefault())
                ?? throw new KeyNotFoundException($"Lobbyist registration with ID {lobbyistRegistrationId} not found.");

            model.FirstName = registration!.FirstName ?? string.Empty;
            model.LastName = registration!.LastName ?? string.Empty;
            model.MiddleName = registration.MiddleName;
            model.Email = registration!.Email ?? string.Empty;
            model.IsNotAllowToEdit = registration.StatusId == RegistrationStatus.Accepted.Id;
        }

        return model;
    }

    /// <summary>
    /// Creates or update inhouse lobbyist registration
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<RegistrationResponseDto> CreateOrUpdateInHouseLobbyist(InHouseLobbyistViewModel model)
    {
        var employerRegistration = await lobbyistEmployerRegistrationSvc.GetLobbyistEmployer(model.LobbyistEmployerRegistrationId);
        var request = new LobbyistRegistrationRequestDto()
        {
            FirstName = model.FirstName,
            LastName = model.LastName,
            MiddleName = model.MiddleName,
            Email = model.Email,
            LobbyistEmployerOrLobbyingFirmId = employerRegistration?.FilerId,
            IsNewCertification = true,
            IgnoreDecisionRule = true
        };
        if (model.LobbyistRegistrationId is null or <= 0)
        {
            request.Id = model.LobbyistRegistrationId;
            return await lobbyistRegistrationSvc.CreateLobbyistRegistrationPage03(request);
        }
        else
        {
            return await lobbyistRegistrationSvc.UpdateLobbyistRegistration(model.LobbyistRegistrationId.GetValueOrDefault(), request);
        }
    }


    public async Task<InHouseLobbyistListViewModel> GetInHouseLobbyistListViewModel(
        long? id)
    {
        var lobbyists = await lobbyistEmployerRegistrationSvc.GetLobbyistRegistrationByEmployerRegistration(id.GetValueOrDefault());

        return new InHouseLobbyistListViewModel
        {
            Id = id,
            Lobbyists = lobbyists,
            InHouseLobbyistsGridModel = CreateInHouseLobbyistsGridModel(id.GetValueOrDefault(), lobbyists)
        };
    }

    public SmallDataGridModel CreateInHouseLobbyistsGridModel(long lobbyistEmployerRegistrationId,
        List<InHouseLobbyistResponseDto> lobbyists)
    {
        return new SmallDataGridModel
        {
            GridId = "InHouseLobbyistsGrid",
            GridType = nameof(InHouseLobbyistResponseDto),
            DataSource = lobbyists,
            PrimaryKey = nameof(InHouseLobbyistResponseDto.Id),
            AllowPaging = false,
            AllowTextWrap = false,
            AllowAdding = false,
            AllowSearching = false,
            AllowDeleting = true,
            EnableExport = false,
            RowDataBound = "onRowDataBound",

            Columns = new List<DataGridColumn>
            {
                new() { Field = nameof(InHouseLobbyistResponseDto.Name), HeaderText = ResourceConstants.LobbyistEmployerRegistrationStep02LobbyistListName },
                new() { Field = nameof(InHouseLobbyistResponseDto.Id), HeaderText = ResourceConstants.LobbyistEmployerRegistrationStep02LobbyistListId },
                new() { Field = nameof(InHouseLobbyistResponseDto.CertificationStatus), HeaderText = ResourceConstants.LobbyistEmployerRegistrationStep02LobbyistListCertified },
            },
            ActionItems = new List<GridActionItem>
            {
                new() { Label = CommonResourceConstants.Edit, Action = "editRow", ControllerName= $"LobbyistEmployerRegistration", ActionName = $"{lobbyistEmployerRegistrationId}/Lobbyist" },

                new() { Label = CommonResourceConstants.Delete, Action = "deleteRow", ControllerName= $"LobbyistEmployerRegistration", ActionName = "Delete" },
                new() { Label = ResourceConstants.LobbyistEmployerRegistrationPage05Certification, Action = "callAction", ControllerName= $"LobbyistEmployerRegistration", ActionName = $"Step02InitiateCertification" },
            },
            DeleteConfirmationMessage = "Are you sure you want to delete this lobbyist from the employer/coalition? This lobbyist will be terminated and receive a notification.",
            DeleteConfirmationField = "Subject",
        };
    }

    public LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel GetLobbyistEmployerRegistrationStep03LobbyingFirmsViewModel(
    long? id)
    {
        var lobbyingFirms = new List<LobbyingFirmRowDto>
        {
            new()
            {
                Name = "Jane Smith",
                Id = "ID# 1",
                Email = "<EMAIL>"
            },
            new() {
                Name = "John Doe",
                Id = "ID# 2",
                Email = "<EMAIL>"
            },
            new() {
                Name = "Emily Johnson",
                Id = "ID# 3",
                Email = "<EMAIL>"
            },
            new()
            {
                Name = "Michael Brown",
                Id = "ID# 4",
                Email = "<EMAIL>"
            },
            new()
            {
                Name = "Sarah Lee",
                Id = "ID# 5",
                Email = "<EMAIL>"
            }
        };
        return new LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel
        {
            Id = id,
            LobbyingFirms = lobbyingFirms,
            HasFirms = lobbyingFirms.Count != 0,
            LobbyingFirmsGridModel = CreateLobbyingFirmsGridModel(lobbyingFirms)
        };
    }

    public SmallDataGridModel CreateLobbyingFirmsGridModel(
    List<LobbyingFirmRowDto> lobbyingFirms)
    {
        return new SmallDataGridModel
        {
            GridId = "LobbyingFirmsGrid",
            GridType = nameof(LobbyingFirmRowDto),
            DataSource = lobbyingFirms,
            AllowPaging = false,
            AllowTextWrap = false,
            AllowAdding = false,
            AllowSearching = false,
            AllowDeleting = true,
            EnableExport = false,
            Columns = new List<DataGridColumn>
            {
                new() { Field = nameof(LobbyingFirmRowDto.Id), HeaderText = ResourceConstants.LobbyistEmployerRegistrationStep03LobbyingFirmsTableID },
                new() { Field = nameof(LobbyingFirmRowDto.Name), HeaderText = ResourceConstants.LobbyistEmployerRegistrationStep03LobbyingFirmsTableName },
                new() { Field = nameof(LobbyingFirmRowDto.Email), HeaderText = ResourceConstants.LobbyistEmployerRegistrationStep03LobbyingFirmsTableEmail },
            },
            ActionItems = new List<GridActionItem>
            {
                new() { Label = CommonResourceConstants.Delete, Action = "deleteRow", ControllerName= $"LobbyistEmployerRegistration", ActionName = "Delete" },
            },
            DeleteConfirmationMessage = "Are you sure you want to delete this lobbying firm?",
            DeleteConfirmationField = "Subject",
        };
    }
    #endregion
}

@using SOS.CalAccess.UI.Common
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.FilerPortal.Models.Disclosure.SmoCampaignStatement
@using SOS.CalAccess.UI.Common.Enums
@using SOS.CalAccess.UI.Common.Localization;
@using FilingSummaryType = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;

@inject IHtmlLocalizer<SharedResources> Localizer

@model SmoCampaignStatementTransactionSummaryViewModel
@{
    // FD-CF-SMO-2
    // FD-CF-SMO-3
    // FD-CF-SMO-3.5
    // FD-CF-SMO-4
    // FD-CF-SMO-5
    var urlQuery = Model.AddNewTranscationUrlQuery != null ? new RouteValueDictionary(Model.AddNewTranscationUrlQuery) : null;

    var buttonBar = new ButtonBarModel
    {
        LeftButtons = new List<ButtonConfig>
        {
            new()
            {
                Type = ButtonType.Button,
                Action = FormAction.SaveAndClose,
                CssClass = "btn btn-primary me-2",
                InnerTextKey = ResourceConstants.SaveAndClose
            },
        },
        RightButtons = new List<ButtonConfig>
        {
            new ()
            {
                Type = ButtonType.Custom,
                HtmlContent = await Html.PartialAsync("_CancelDiscardDraftButton", Model.Id),
            },
        }
    };

    var transactionSummaryTitle = "";
    var transactionSummaryBody = ResourceConstants.SmoCampaignStatementTransactionSummaryBody;
    var gridTitle = "";
    switch (Model.SummaryType)
    {
        case var type when type == FilingSummaryType.PaymentReceivedSummary.Name:
            transactionSummaryTitle = ResourceConstants.SmoCampaignStatementPaymentsReceivedTitle;
            gridTitle = ResourceConstants.SmoCampaignStatementTransactionSummaryPaymentsReceived100OrMoreGridTitle;
            break;
        case var type when type == FilingSummaryType.PaymentMadeSummary.Name:
            transactionSummaryTitle = ResourceConstants.SmoCampaignStatementPaymentsMadeTitle;
            gridTitle = ResourceConstants.SmoCampaignStatementTransactionSummaryPaymentsMade100OrMoreGridTitle;
            break;
        case var type when type == FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Name:
            transactionSummaryTitle = ResourceConstants.SmoCampaignStatementPaymentsMadeByAgentOrIndependentContractorTitle;
            gridTitle = ResourceConstants.SmoCampaignStatementTransactionSummaryPaymentsMadeByAgentOrIndependentContractorGridTitle;
            break;
        case var type when type == FilingSummaryType.PersonReceiving1000OrMoreSummary.Name:
            transactionSummaryTitle = ResourceConstants.SmoCampaignStatementPersonsReceiving1000OrMoreTitle;
            gridTitle = ResourceConstants.SmoCampaignStatementTransactionSummaryPersonsReceiving1000OrMoreGridTitle;
            break;
        case var type when type == FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Name:
            transactionSummaryTitle = ResourceConstants.SmoCampaignStatementCandidatesAndMeasuresNotListedTitle;
            transactionSummaryBody = ResourceConstants.SmoCampaignStatementCandidatesAndMeasuresNotListedBody;
            gridTitle = ResourceConstants.SmoCampaignStatementTransactionSummaryCandidatesAndMeasuresNotListedGridTitle;
            break;

    }
}

@using (Html.BeginForm("TransactionSummary", "SmoCampaignStatement", FormMethod.Post))
{
    @Html.HiddenFor(m => m.Id)
    @Html.HiddenFor(m => m.FilerId)
    @Html.HiddenFor(m => m.FilingSummaryId)
    @Html.HiddenFor(m => m.SummaryType)

    <div class="p-5 d-flex flex-column gap-3">
        @Html.StepHeader(SharedLocalizer, transactionSummaryTitle)
        <div>
            @Html.TextBlock(SharedLocalizer, transactionSummaryBody)
            <a href="/Filing/SmoCampaignStatement/UnderConstruction" class="btn-link mb-5 d-flex align-items-center" style="text-decoration: none">
                <span>@SharedLocalizer[ResourceConstants.SmoCampaignStatementTransactionSummaryReviewInstructions]</span>
                <span class="ms-2 e-icons e-open-link e-medium"></span>
            </a>
        </div>

        <div>
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="me-4">@SharedLocalizer[gridTitle]</h4>

                @if (Model.SummaryType != FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Name)
                {
                    <p class="mb-0 text-nowrap">@SharedLocalizer[ResourceConstants.SmoCampaignStatementTransactionSummarySubtotal]: <strong>$@Model.TransactionSubtotal!.Value.ToString("0.00")</strong></p>
                }
            </div>

            <div class="d-flex justify-content-between align-items-center mb-1">
                <div class="d-flex gap-2 mb-3">
                    <a class="btn btn-flat-primary me-2 d-flex align-items-center" href="@Url.Action(Model.AddNewTranscationUrl, "SmoCampaignStatement", urlQuery)">
                        <i class="e-icons e-plus me-1 e-medium"></i>
                        @SharedLocalizer[ResourceConstants.AddNew]
                     </a>
                    @if (Model.SummaryType != FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Name)
                    {
                        <a class="btn btn-flat-primary me-2 d-flex align-items-center" href="@Url.Action("UnderConstruction", "SmoCampaignStatement")">
                            <i class="e-icons e-upload-1 e-medium me-1"></i>
                            @SharedLocalizer[ResourceConstants.UploadTransactions]
                    </a>
                    }
                    <a class="btn btn-flat-primary d-flex align-items-center me-2" href="@Url.Action("UnderConstruction", "SmoCampaignStatement")">
                        <i class="e-icons e-download e-medium me-1"></i>
                        @SharedLocalizer[ResourceConstants.Export]
                    </a>
                </div>
            </div>
        </div>

        <div class="mb-4">
            @switch (Model.SummaryType)
            {
                case var type when type == FilingSummaryType.PaymentReceivedSummary.Name:
                    @await Html.PartialAsync("_SmallGrid", Model.PaymentsReceivedGridModel)
                    break;
                case var type when type == FilingSummaryType.PaymentMadeSummary.Name:
                    @await Html.PartialAsync("_SmallGrid", Model.PaymentsMadeGridModel)
                    break;
                case var type when type == FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Name:
                    @await Html.PartialAsync("_SmallGrid", Model.PaymentsMadeByAgentOrContractorGridModel)
                    break;
                case var type when type == FilingSummaryType.PersonReceiving1000OrMoreSummary.Name:
                    @await Html.PartialAsync("_SmallGrid", Model.PersonsReceiving1000OrMoreGridModel)
                    break;
                case var type when type == FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Name:
                    @await Html.PartialAsync("_SmallGrid", Model.CandidatesMeasuresNotListedGridModel)
                    break;

            }
        </div>

        @if (Model.SummaryType == FilingSummaryType.PaymentReceivedSummary.Name || Model.SummaryType == FilingSummaryType.PaymentMadeSummary.Name)
        {
            <div class="d-flex justify-content-between mb-4">
                @switch (Model.SummaryType)
                {
                    case var type when type == FilingSummaryType.PaymentReceivedSummary.Name:
                        <h4 class="flex-grow-1 me-4">@SharedLocalizer[ResourceConstants.SmoCampaignStatementTransactionSummaryUnitemizedPaymentReceivedTitle]</h4>
                        break;
                    case var type when type == FilingSummaryType.PaymentMadeSummary.Name:
                        <h4 class="flex-grow-1 me-4">@SharedLocalizer[ResourceConstants.SmoCampaignStatementTransactionSummaryUnitemizedPaymentMadeTitle]</h4>
                        break;

                }
                <p class="mb-0">@SharedLocalizer[ResourceConstants.SmoCampaignStatementTransactionSummarySubtotal]: <strong>$@Model.UnitemizedPaymentSubtotal!.Value.ToString("0.00")</strong></p>
            </div>

            <div class="row mb-3 d-flex justify-content-between justify-content-center">
                <p class="mb-0 col-sm-8">@SharedLocalizer[ResourceConstants.SmoCampaignStatementTransactionSummaryUnitemizedPaymentsLessThan100]</p>
                <div class="col-sm-3">
                    @Html.CurrencyInputFor(SharedLocalizer, m => m.UnitemizedPaymentLessThan100, "", true, true, true)
                </div>
            </div>
        }

        <partial name="_ButtonBar" model="buttonBar" />

    </div>
}

<style>
    .e-content {
        position: unset !important;
    }
</style>

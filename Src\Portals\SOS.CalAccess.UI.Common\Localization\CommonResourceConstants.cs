namespace SOS.CalAccess.UI.Common.Localization;
public class CommonResourceConstants
{
    //Common Resource
    public const string Home = "Common.Home";
    public const string Edit = "Common.Edit";
    public const string Delete = "Common.Delete";
    public const string AddNew = "Common.AddNew";
    public const string DatePlaceHolder = "Common.DatePlaceHolder";
    public const string FieldIsRequired = "Common.FieldIsRequired";
    public const string Optional = "Common.Optional";
    public const string Continue = "Common.Continue";
    public const string Previous = "Common.Previous";
    public const string Save = "Common.Save";
    public const string SaveAndClose = "Common.SaveAndClose";
    public const string Create = "Common.Create";
    public const string Submit = "Common.Submit";
    public const string Cancel = "Common.Cancel";
    public const string Close = "Common.Close";
    public const string InvalidSubmission = "Common.InvalidSubmission";
    public const string SubmissionReceived = "Common.SubmissionReceived";
    public const string SavedMessage = "Common.SavedMessage";
    public const string DeleteFailureMessage = "Common.DeleteFailureMessage";
    public const string CancelHeaderText = "Common.CancelHeaderText";
    public const string CancelBodyText = "Common.CancelBodyText";
    public const string ToastCancelled = "Common.ToastCancelled";
    public const string ToastCanceled = "Common.ToastCanceled";
    public const string ToastSaved = "Common.ToastSaved";
    public const string Address = "Common.Address";
    public const string Description = "Common.Description";
    public const string Apply = "Common.Apply";
    public const string PendingItems = "Common.PendingItems";
    public const string Preview = "Common.PreviewPDF";
    public const string ExecutedOn = "Common.ExecutedOn";
    public const string NextSteps = "Common.NextSteps";
    public const string Amend = "Common.Amend";
    public const string NotPubliclyAvailable = "Common.NotPubliclyAvailable";
    public const string ReturnToDashboard = "Common.ReturnToDashboard.Message";
    public const string Reject = "Common.Reject";
    public const string Accept = "Common.Accept";
    public const string Current = "Common.Current";
    public const string Name = "Common.Name";
    public const string Role = "Common.Role";
    public const string Terminate = "Common.Terminate";
    public const string Send = "Common.Send";
    public const string Yes = "Common.Yes";
    public const string NoCancel = "Common.NoCancel";
    public const string FilerName = "Common.FilerName";
    public const string Officer = "Common.Officer";
    public const string MailingAddress = "Common.MailingAddress";
    public const string CurrencyInputPlaceHolder = "Common.CurrencyInputPlaceHolder";
    public const string SelectFiles = "Common.SelectFiles";
    public const string UnauthorizedActionToast = "Common.UnauthorizedActionToast";
    public const string UnauthorizedToAccessPage = "Common.UnauthorizedToAccessPage";
    public const string InvalidId = "Common.InvalidId";
    public const string NoSelectionError = "Common.NoSelection.ErrorMessage";

    //Common Progress Bar Modal Resources
    public const string ProgressBarModalTitle = "Common.ProgressBar.Modal.Title";
    public const string ProgressBarModalBody = "Common.ProgressBar.Modal.Body";
    public const string ProgressBarModalCloseButtonText = "Common.ProgressBar.Modal.CloseButtonText";
    public const string ProgressBarModalSubmitButtonText = "Common.ProgressBar.Modal.SubmitButtonText";

    //Common Progress Bar Resources
    public const string ProgressBarNavigableText = "Common.ProgressBar.NavigableDisplayText";

    //Field Resources
    public const string FilingStartDate = "Field.Filing.StartDate";
    public const string FilingEndDate = "Field.Filing.EndDate";

    public const string NotificationSubject = "Field.Notification.Subject";

    public const string TransactionAmount = "Field.Transaction.Amount";
    public const string TelephoneNumber = "Common.TelephoneNumber";
    public const string FaxNumber = "Common.FaxNumber";
    public const string EmailAddress = "Common.EmailAddress";
    public const string EmailAddressRequired = "Common.EmailAddressRequired";
    public const string EmailAddressInvalidError = "Common.EmailAddressInvalidError";
    public const string Country = "Common.Country";
    public const string Street = "Common.Street";
    public const string StreetAddress = "Common.StreetAddress";
    public const string Street2 = "Common.Street2";
    public const string City = "Common.City";
    public const string State = "Common.State";
    public const string ZipCode = "Common.ZipCode";
    public const string Date = "Common.Date";
    public const string CountryCode = "Common.CountryCode";
    public const string Extension = "Common.Extension";
    public const string AddressNotRecognized = "Common.AddressNotRecognized";

    public const string UploadFileTitle = "Common.UploadFile.Title";
    public const string UploadFileInstructions = "Common.UploadFile.Instructions";
}

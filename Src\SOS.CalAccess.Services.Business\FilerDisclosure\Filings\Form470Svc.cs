using SOS.CalAccess.Data.Contracts.FilerDisclosure.Filings;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Constants;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Filings;

/// <inheritdoc /> 
public sealed class Form470Svc(
    Form470SvcDependencies dependencies,
    IAttestationRepository attestationRepository,
    IFilingRelatedFilerRepository filingRelatedFilerRepository
    ) : IForm470Svc
{
    private readonly IFilingRelatedFilerRepository _filingRelatedFilerRepository = filingRelatedFilerRepository;

    /// <inheritdoc />
    public async Task<IEnumerable<Form470SearchCommitteeResponseDto>> FindPrimarilyFormedCommitteeByIdOrName(string query)
    {
        var response = await dependencies.RegistrationRepository.FindPrimarilyFormedCommitteeByIdOrName(query);
        var dtoList = response.Select(r => new Form470SearchCommitteeResponseDto(r));
        return dtoList;
    }

    /// <inheritdoc /> 
    public async Task<CandidateIntentionStatement470> GetCandidateIntentionStatementWithElectionByFilerId(long id)
    {
        var candidateIntentionStatement = await dependencies.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(id);

        return candidateIntentionStatement is null ? new() : MapToCandidateIntentionStatement470(candidateIntentionStatement);
    }

    /// <inheritdoc />
    public async Task<OfficeHolderCandidateShortForm?> GetForm470ById(long id)
    {
        return await dependencies.FilingRepository.GetForm470ById(id);
    }

    /// <inheritdoc /> 
    public async Task<long> CreateOfficeHolderCandidateShortFormFiling(long? filerId, long? filingPeriodId)
    {
        var filing = new OfficeHolderCandidateShortForm
        {
            FilerId = filerId!.Value,
            FilingPeriodId = filingPeriodId!.Value,
            Version = 0,
            StatusId = FilingStatus.Draft.Id,
        };
        var record = await dependencies.FilingRepository.Create(filing);

        record.OriginalId = record.Id;

        await dependencies.FilingRepository.Update(filing);

        return record.Id;
    }

    /// <inheritdoc />
    public async Task UpdateOfficeHolderCandidateShortFormFiling(Form470FilingRequest form470FilingRequest)
    {
        var existingFiling = await dependencies.FilingRepository.GetForm470ById(form470FilingRequest.FilingId);

        if (existingFiling == null)
        {
            throw new InvalidOperationException($"Unable to find filing record to update. FilingId={form470FilingRequest.FilingId}");
        }

        existingFiling.FilingPeriodId = form470FilingRequest.FilingPeriodId;
        await dependencies.FilingRepository.Update(existingFiling);
    }

    /// <inheritdoc />
    public async Task<Form470OverviewResponse> GetForm470Overview(long id)
    {
        var filing = await dependencies.FilingRepository.GetForm470ById(id);
        if (filing == null)
        {
            return new();
        }

        var candidateIntentionStatement = await dependencies.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filing.FilerId);
        if (candidateIntentionStatement == null)
        {
            return new();
        }

        return new Form470OverviewResponse
        {
            CandidateIntentionStatement470 = MapToCandidateIntentionStatement470(candidateIntentionStatement),
            Form470Filing = MapToForm470Filing(filing)
        };
    }

    /// <inheritdoc />
    public async Task<FilingRelatedFiler> CreateFilingRelatedFiler(long filingId, long filerId)
    {
        var filingrelatedFiler = new FilingRelatedFiler { FilingId = filingId, FilerId = filerId, Active = true };
        return await _filingRelatedFilerRepository.Create(filingrelatedFiler);
    }

    /// <inheritdoc />
    public async Task CancelFilingRelatedFiler(long id)
    {
        var filingRelatedFiler = await _filingRelatedFilerRepository.FindById(id) ?? throw new KeyNotFoundException($"Filing Related filer with ID {id} not found.");

        if (!filingRelatedFiler.Active)
        {
            throw new InvalidOperationException($"Cannot cancel a filer related filer that is not in 'Active' status. Id={id}");
        }
        await _filingRelatedFilerRepository.UpdateProperty(filingRelatedFiler, static r => r.Active, false);
    }

    /// <inheritdoc />
    public async Task<List<Form470Committee>> GetFilingRelatedFiler(long id)
    {
        var filingsResult = await dependencies.FilingRepository.GetCandidateCommitteesByFilingId(id);

        // Extract active filer IDs from the FilingRelatedFilers
        var activeFilerIds = filingsResult
             .SelectMany(filing => filing.FilingRelatedFilers ?? new List<FilingRelatedFiler>())
             .Where(frf =>
                 frf.Active &&
                 frf.Filer != null &&
                 frf.Filer.FilerStatusId == FilerStatus.Active.Id &&
                 frf.Filer.FilerTypeId == FilerType.Candidate.Id
             )
             .Select(frf => new { frf.FilerId, FilingRelatedFilerId = frf.Id })
             .Distinct()
             .ToList();

        var form470Committees = new List<Form470Committee>();
        foreach (var item in activeFilerIds)
        {
            var committee = await dependencies.RegistrationRepository.GetCommitteeByFilerId(item.FilerId);
            if (committee != null)
            {
                form470Committees.Add(new Form470Committee(committee)
                {
                    FilerRelatedFilerId = item.FilingRelatedFilerId
                });
            }
        }
        return form470Committees;
    }

    public async Task<long> GetFilerIdOfLatestAccepted501()
    {
        var user = await dependencies.UserMaintenanceSvc.GetCurrentUser();
        var filer = await dependencies.RegistrationRepository.GetFilerOfLatestAcceptedCisRegistration(user.Id);
        if (filer is null)
        {
            return 0;
        }
        return filer.Id;
    }

    public async Task<ValidatedForm470ResponseDto> SubmitCandidateStatementShortForEfile(CandidateStatementShortSubmissionDto submission)
    {
        var validationErrors = new List<WorkFlowError> { };

        var discFiling = submission.CandidateStatementShort!;
        var fullName = $"{submission.Attestation!.FirstName} {submission.Attestation!.LastName}";
        string role = submission.Attestation!.Role ?? string.Empty;

        DecisionsForm470AttestationResponse response = await ValidateForm470Attestation(dependencies, fullName, discFiling.FilerId, role, true);
        validationErrors.AddRange(response?.Result ?? new List<WorkFlowError> { });

        if (validationErrors.Count == 0)
        {
            await dependencies.FilingRepository.Create(discFiling);

            // Create the attestation for Form 470
            await CreateAttestationAsync(discFiling.Id, discFiling.FilerId, fullName);

            discFiling.OriginalId = discFiling.Id;
            discFiling.SubmittedDate = dependencies.DateTimeSvc.GetCurrentDateTime();
            discFiling.StatusId = FilingStatus.Accepted.Id;
            discFiling.ApprovedAt = dependencies.DateTimeSvc.GetCurrentDateTime();

            await dependencies.FilingRepository.Update(discFiling);
        }

        return new ValidatedForm470ResponseDto(discFiling.Id, true, validationErrors, discFiling.StatusId);
    }

    public async Task<ValidatedForm470ResponseDto> AttestOfficeholderAndCandidateCampaignStatement(
        long id,
        Form470AttestRequest request)
    {
        var validationErrors = new List<WorkFlowError> { };

        // Check if the current user has permission to attest the form
        if ((bool)!request.IsAgreedTerm!)
        {
            validationErrors.Add(new WorkFlowError(nameof(request.IsAgreedTerm), RegistrationConstants.ValidationError.ErrorTypeValidation, RegistrationConstants.ValidationError.ErrorTypeValidation, $"Penalty of Perjury statement should be checked."));
            return new ValidatedForm470ResponseDto(validationErrors);
        }

        // Get the Form 470 by ID
        var existingFiling = await dependencies.FilingRepository.GetForm470ById(id)
            ?? throw new KeyNotFoundException($"OfficeHolder Candidate Short Form not found. Id={id}");

        // Get Filer User of the current user
        var userId = await dependencies.AuthorizationSvc.GetInitiatingUserId();
        var filerUser = await dependencies.FilerSvc.GetFilerUserByUserIdAsync(existingFiling.FilerId, userId.GetValueOrDefault())
            ?? throw new InvalidOperationException($"No filer user found with registration ID {id}.");

        // Get the registration by FilerId
        var existingRegistration = await dependencies.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filerUser.FilerId)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");


        // Check if the disclosure filing already attested
        // If a disclosure filing is attested, return
        if (existingFiling.StatusId == FilingStatus.Accepted.Id)
        {
            return new ValidatedForm470ResponseDto
            {
                Id = existingFiling.Id,
                Valid = true,
                ValidationErrors = validationErrors,
                StatusId = existingFiling.StatusId,
                SubmittedDate = existingFiling.SubmittedDate
            };
        }

        var fullName = $"{existingRegistration.FirstName} {existingRegistration.LastName}";

        // Call Decisions to validate business rule - Run post-submission workflow validation
        DecisionsForm470AttestationResponse response = await ValidateForm470Attestation(dependencies, fullName, filerUser, request.CheckRequiredFieldsFlag);
        validationErrors.AddRange(response?.Result ?? new List<WorkFlowError> { });

        if (validationErrors.Count > 0)
        {
            return new ValidatedForm470ResponseDto
            {
                Id = existingFiling.Id,
                Valid = false,
                ValidationErrors = validationErrors,
                StatusId = existingFiling.StatusId,
                SubmittedDate = existingFiling.SubmittedDate
            };
        }

        // Create the attestation for Form 470
        await CreateAttestationAsync(id, existingRegistration!.Id, fullName);

        // Mark the form submitted
        existingFiling.SubmittedDate = dependencies.DateTimeSvc.GetCurrentDateTime();
        // TD: Mark as approved to enable amendments. This will change later.
        existingFiling.StatusId = FilingStatus.Accepted.Id;
        existingFiling.ApprovedAt = dependencies.DateTimeSvc.GetCurrentDateTime();

        _ = await dependencies.FilingRepository.Update(existingFiling);

        return new ValidatedForm470ResponseDto
        {
            Id = existingFiling.Id,
            Valid = true,
            ValidationErrors = validationErrors,
            StatusId = existingFiling.StatusId,
            SubmittedDate = existingFiling.SubmittedDate
        };
    }

    public async Task<ValidatedForm470ResponseDto> AttestOfficeholderAndNonCandidateCampaignStatement(long id, Form470AttestRequest request)
    {
        var validationErrors = new List<WorkFlowError> { };

        // Get the Form 470 by ID
        var existingFiling = await dependencies.FilingRepository.GetForm470ById(id)
            ?? throw new KeyNotFoundException($"OfficeHolder Candidate Short Form not found. Id={id}");

        // Get Filer User of the current user
        var userId = await dependencies.AuthorizationSvc.GetInitiatingUserId();
        var filerUser = await dependencies.FilerSvc.GetFilerUserByUserIdAsync(existingFiling.FilerId, userId.GetValueOrDefault())
            ?? throw new InvalidOperationException($"No filer user found with registration ID {id}.");

        // Get the registration by FilerId
        _ = await dependencies.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filerUser.FilerId)
             ?? throw new KeyNotFoundException($"Registration not found. Id={id}");

        // Call Decisions to validate business rule - Run post-submission workflow validation
        DecisionsForm470SendForAttestationResponse response = await ValidateForm470SendForAttestation(dependencies, filerUser, request.CheckRequiredFieldsFlag);
        validationErrors.AddRange(response?.Result ?? new List<WorkFlowError> { });

        if (validationErrors.Count > 0)
        {
            return new ValidatedForm470ResponseDto
            {
                Id = existingFiling.Id,
                Valid = false,
                ValidationErrors = validationErrors,
                StatusId = existingFiling.StatusId,
                SubmittedDate = existingFiling.SubmittedDate
            };
        }

        // Mark the form submitted
        existingFiling.SubmittedDate = dependencies.DateTimeSvc.GetCurrentDateTime();
        existingFiling.StatusId = FilingStatus.Pending.Id;

        _ = await dependencies.FilingRepository.Update(existingFiling);

        return new ValidatedForm470ResponseDto
        {
            Id = existingFiling.Id,
            Valid = true,
            ValidationErrors = validationErrors,
            StatusId = existingFiling.StatusId,
        };
    }

    #region Private

    private static Form470Filing? MapToForm470Filing(OfficeHolderCandidateShortForm officeHolderCandidateShortForm)
    {
        if (officeHolderCandidateShortForm.FilingPeriodId.HasValue)
        {
            return new Form470Filing
            {
                Id = officeHolderCandidateShortForm.Id,
                FilerId = officeHolderCandidateShortForm.FilerId,
                FilingPeriodId = officeHolderCandidateShortForm.FilingPeriodId.Value,
                FilingPeriod = new FilingPeriod
                {
                    StartDate = officeHolderCandidateShortForm.StartDate,
                    EndDate = officeHolderCandidateShortForm.EndDate
                },
                Status = officeHolderCandidateShortForm.StatusId,
                FilingTypeId = officeHolderCandidateShortForm.FilingTypeId,
                SubmittedDate = officeHolderCandidateShortForm.SubmittedDate
            };
        }
        return null;
    }

    private static CandidateIntentionStatement470 MapToCandidateIntentionStatement470(CandidateIntentionStatement candidateIntentionStatement)
    {
        var phoneNumber = Form470Shared.GetPhoneNumber(candidateIntentionStatement.PhoneNumberList);
        var faxNumber = Form470Shared.GetFaxNumber(candidateIntentionStatement.PhoneNumberList);

        var response = new CandidateIntentionStatement470
        {
            RegistrationId = candidateIntentionStatement.Id,
            FilerId = candidateIntentionStatement.FilerId,
            Name = candidateIntentionStatement.Name,
            EmailAddress = candidateIntentionStatement.Email,
            OfficeSought = candidateIntentionStatement.ElectionOfficeSought,
            Jurisdiction = candidateIntentionStatement.ElectionJurisdiction,
            DistrictNumber = candidateIntentionStatement.ElectionDistrictNumber,
            ElectionDate = candidateIntentionStatement.ElectionRace?.Election?.ElectionDate,
            AddressDto = Form470Shared.SetCandidateAddress(candidateIntentionStatement.AddressList),
            PhoneNumber = phoneNumber,
            FaxNumber = faxNumber
        };

        return response;
    }

    private async Task CreateAttestationAsync(long filingid, long registrationId, string? name)
    {
        var attestation = new Attestation
        {
            FilingId = filingid,
            RegistrationId = registrationId,
            Name = name ?? string.Empty,
            ExecutedAt = dependencies.DateTimeSvc.GetCurrentDateTime(),
            CreatedBy = 0,
            ModifiedBy = 0,
        };
        _ = await attestationRepository.Create(attestation);
    }

    private static async Task<DecisionsForm470AttestationResponse> ValidateForm470Attestation(Form470SvcDependencies dependencies, string fullName, long filerId, string filerRole, bool isSubmission)
    {
        // Should call Decisions to validate business rule - Run post-submission workflow validation
        DecisionsForm470Attestation decisionsAttestation = new()
        {
            AttestationName = fullName,
            AttestationRole = filerRole,
        };

        var decisionResponse = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsForm470Attestation, DecisionsForm470AttestationResponse>(DecisionsWorkflow.Form470AttestationRuleSet, decisionsAttestation, checkRequiredFields: isSubmission);

        // Only send notifications to filer if it's attestation flow
        if (decisionResponse?.Result?.Count == 0)
        {
            // Send Notifications
            await SendDecisionsNotifications(dependencies, decisionResponse.Notifications, filerId);
        }
        return decisionResponse!;
    }


    private static async Task<DecisionsForm470AttestationResponse> ValidateForm470Attestation(Form470SvcDependencies dependencies, string fullName, FilerUserDto filerUser, bool isSubmission)
    {
        return await ValidateForm470Attestation(dependencies, fullName, filerUser.FilerId, filerUser.FilerRole!.Name, isSubmission);
    }

    private static async Task<DecisionsForm470SendForAttestationResponse> ValidateForm470SendForAttestation(Form470SvcDependencies dependencies, FilerUserDto filerUser, bool isSubmission)
    {
        // Should call Decisions to validate business rule - Run post-submission workflow validation
        DecisionsForm470SendForAttestation input = new();

        var decisionResponse = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsForm470SendForAttestation, DecisionsForm470SendForAttestationResponse>(DecisionsWorkflow.Form470SendForAttestationRuleSet, input, checkRequiredFields: isSubmission);

        // Only send notifications to filer if it's attestation flow
        if (decisionResponse?.Result?.Count == 0)
        {
            // Send Notifications
            await SendDecisionsNotifications(dependencies, decisionResponse.Notifications, filerUser.FilerId);

        }
        return decisionResponse!;
    }

    private static async Task SendDecisionsNotifications(Form470SvcDependencies dependencies, List<NotificationTrigger>? notifications, long filerId)
    {
        if (notifications != null)
        {
            foreach (NotificationTrigger notification in notifications)
            {
                if (notification.SendNotification && notification.NotificationTemplateId is { } templateId)
                {
                    await dependencies.NotificationSvc.SendFilerNotification(new SendFilerNotificationRequest(templateId, filerId, notification.DueDate, null));
                }
            }
        }
    }

    #endregion
}

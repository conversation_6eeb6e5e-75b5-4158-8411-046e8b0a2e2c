using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Data transfer object for lobbyist response.
/// </summary>
public class InHouseLobbyistResponseDto
{
    private const string Certified = "Certified";
    private const string NotCertified = "Not certified";

    /// <summary>
    /// Initializes a new instance of the <see cref="InHouseLobbyistResponseDto"/> class.
    /// </summary>
    public InHouseLobbyistResponseDto() { }

    /// <summary>
    /// Initializes a new instance of the <see cref="InHouseLobbyistResponseDto"/> class with a specified inhouse lobbyist.
    /// </summary>
    /// <param name="lobbyist">The inhouse lobbyist to initialize the DTO with.</param>
    public InHouseLobbyistResponseDto(Lobbyist lobbyist)
    {
        Id = lobbyist.Id;
        Name = lobbyist.Name;
        Email = lobbyist.Email ?? string.Empty;
        StatusId = lobbyist.StatusId;
        FilerId = lobbyist.FilerId;
        EmployerName = lobbyist.EmployerName;
        FirstName = lobbyist.FirstName;
        MiddleName = lobbyist.MiddleName;
        LastName = lobbyist.LastName;
    }

    /// <summary>
    /// Gets the unique identifier of the lobbyist.
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// Gets the name of the lobbyist.
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets the email address of the lobbyist.
    /// </summary>
    public string? Email { get; set; } = string.Empty;

    /// <summary>
    /// Gets the status identifier of the lobbyist.
    /// </summary>
    public long StatusId { get; set; }

    /// <summary>
    /// Gets the filer identifier of the lobbyist.
    /// </summary>
    public long? FilerId { get; set; }

    /// <summary>
    /// Gets the employer name of the lobbyist.
    /// </summary>
    public string? EmployerName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets First Name
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// Gets or sets Middle Name
    /// </summary>
    public string? MiddleName { get; set; }

    /// <summary>
    /// Gets or sets Last Name
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// Gets Certification Status
    /// </summary>
    public string CertificationStatus => StatusId == RegistrationStatus.Accepted.Id ? Certified : NotCertified;
}

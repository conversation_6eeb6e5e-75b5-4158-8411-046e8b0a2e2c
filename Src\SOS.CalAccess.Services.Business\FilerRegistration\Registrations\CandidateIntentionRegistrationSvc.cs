using System.Data;
using System.Globalization;
using SendGrid.Helpers.Errors.Model;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Elections;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.Constants;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

public class CandidateIntentionRegistrationSvc(
    ICandidateIntentionDependencies dependencies,
    IAttestationRepository attestationRepository,
    IElectionRaceRepository electionRaceRepository,
    IPoliticalPartyRepository politicalPartyRepository,
    IRegistrationModelMapper modelMapper,
    IFilerUserRepository filerUserRepository
    )
    : ICandidateIntentionRegistrationSvc
{
    /// <inheritdoc />
    public async Task<RegistrationResponseDto> CreateCandidateIntentionStatement(CandidateIntentionStatementRequest request)
    {
        bool isValid = false;

        var candidateIntentionStatement = modelMapper.MapCandidateIntentionStatementRequestToModel(request);
        DecisionsCandidateIntentionStatement decisionsInput = PopulateDecisionServiceRequest(candidateIntentionStatement);
        var decisionResponse = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsCandidateIntentionStatement, List<WorkFlowError>>(DecisionsWorkflow.CandidateInformationRuleset, decisionsInput, request.CheckRequiredFieldsFlag);

        if (decisionResponse.Count == 0)
        {
            isValid = true;

            candidateIntentionStatement = await dependencies.RegistrationRepository.Create(candidateIntentionStatement);

            if (!candidateIntentionStatement.CandidateId.HasValue)
            {
                Candidate candidate = await CreateCandidateAsync(candidateIntentionStatement);
                candidateIntentionStatement.CandidateId = candidate.Id;
            }

            //Addition of Filer to Registration cannot be performed in a single EF request due to circular dependency created by Filer.CurrentRegistration property
            candidateIntentionStatement = await AssociateUserToRegistration(candidateIntentionStatement, request);
        }

        return new RegistrationResponseDto(candidateIntentionStatement?.Id, isValid, decisionResponse, candidateIntentionStatement?.StatusId);
    }

    /// <inheritDoc/>
    public async Task CancelCandidateIntentionStatement(long id)
    {
        var registration = await dependencies.RegistrationRepository.FindById(id) ?? throw new KeyNotFoundException($"Registration with ID {id} not found.");

        if (registration.StatusId != RegistrationStatus.Draft.Id)
        {
            throw new InvalidOperationException($"Cannot cancel a registration that is not in 'Draft' status. Id={id} Status={registration.StatusId}");
        }

        await dependencies.RegistrationRepository.UpdateProperty(registration, static r => r.StatusId, RegistrationStatus.Canceled.Id);
    }

    /// <inheritDoc/>
    public async Task<CandidateIntentionStatement?> GetCandidateIntentionStatementSummary(long id)
    {
        return await dependencies.RegistrationRepository.FindCandidateIntentionStatementWithElectionById(id);
    }

    /// <inheritdoc />
    public async Task<CandidateIntentionStatementResponseDto?> GetCandidateIntentionStatement(long id)
    {
        var candidateIntentionStatement = await dependencies.RegistrationRepository.FindCandidateIntentionStatementById(id) ?? throw new KeyNotFoundException($"Registration with ID {id} not found.");

        return new CandidateIntentionStatementResponseDto(candidateIntentionStatement);
    }

    /// <inheritdoc />
    public async Task<decimal?> GetCandidateIntentionStatementExpenditureExpenseAmount(long id)
    {
        var expenditureAmount = await dependencies.RegistrationRepository.FindExpenditureExpenseAmount(id) ?? throw new KeyNotFoundException($"Id {id} not found.");
        var electionType = await dependencies.RegistrationRepository.FindElectionType(id);

        decimal amount;
        string electionTypeLower = electionType?.Name.ToLower() ?? "";
        if (electionTypeLower is "primary" or "special")
        {
            amount = expenditureAmount.PrimarySpecialAmount;
        }
        else if (electionTypeLower is "general" or "runoff")
        {
            amount = expenditureAmount.GeneralRunoffAmount;
        }
        else
        {
            throw new KeyNotFoundException($"Expenditure Expense Amount with electionType {electionType} not found.");
        }
        return amount;
    }

    /// <inheritDoc />
    public async Task<Election?> GetCandidateIntentionStatementElection(long id)
    {
        var election = await dependencies.RegistrationRepository.FindElectionByRegistrationId(id);
        return election;
    }

    public async Task<RegistrationResponseDto> UpdateCandidateIntentionStatement(long id, CandidateIntentionStatementRequest request)
    {
        bool isValid = false;

        //FindById
        if (await dependencies.RegistrationRepository.FindCandidateIntentionStatementById(id) is not CandidateIntentionStatement existingRegistration)
        {
            throw new KeyNotFoundException($"Registration not Found Id={id}");
        }

        //Send to mapper        
        var registration = modelMapper.UpdateCandidateIntentionStatement(existingRegistration, request);
        registration.Id = id;

        //Call Decisions
        DecisionsCandidateIntentionStatement candidateIntentionStatementDSInput = PopulateDecisionServiceRequest(registration);
        var decisionResponse = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsCandidateIntentionStatement, List<WorkFlowError>>(DecisionsWorkflow.CandidateInformationRuleset, candidateIntentionStatementDSInput, request.CheckRequiredFieldsFlag);

        if (decisionResponse.Count == 0)
        {
            isValid = true;
            await dependencies.RegistrationRepository.Update(registration);
        }

        return new RegistrationResponseDto(registration.Id, isValid, decisionResponse, registration.StatusId);
    }

    /// <inheritdoc />
    public async Task<RegistrationResponseDto> LinkElectionToCandidateIntentionStatement(long id, UpdateRegistrationElectionRequest electionInfo)
    {
        bool isValid = false;
        long? raceId = 0;
        string valid = "valid";

        var decisionScreenValidationInput = new DecisionsElectionValidation
        {
            Jurisdiction = electionInfo.Jurisdiction ?? "",
            ElectionYear = electionInfo.ElectionYear ?? "",
            Election = electionInfo.ElectionId != 0 ? valid : "",
            Office = electionInfo.OfficeId != 0 ? valid : "",
            District = electionInfo.DistrictId != 0 ? valid : "",
            Party = electionInfo.PartyId != 0 ? valid : "",
        };

        var decisionScreenValidationResponse = await dependencies.DecisionsSvc
            .InitiateWorkflow<DecisionsElectionValidation, List<WorkFlowError>>(
                DecisionsWorkflow.CandidateInformationElectionScreenValidationRuleset,
                decisionScreenValidationInput,
                electionInfo.CheckRequiredFieldsFlag);

        if (decisionScreenValidationResponse.Count > 0)
        {
            return new RegistrationResponseDto(raceId, isValid, decisionScreenValidationResponse);
        }

        var electionRace = await electionRaceRepository.GetElectionRaceAsync(electionInfo.ElectionId, electionInfo.OfficeId, electionInfo.DistrictId);
        var politicalParty = electionInfo.PartyId.HasValue ? await politicalPartyRepository.FindById(electionInfo.PartyId.Value) : null;
        var decisionsElectionInformationInput = new DecisionsElectionInformation
        {
            AgencyName = electionRace?.Office?.AgencyName?.Contains("{{County}}", StringComparison.OrdinalIgnoreCase) == true
            ? electionRace.Office.AgencyName.Replace("{{County}}", electionRace.District?.Name ?? string.Empty, StringComparison.OrdinalIgnoreCase)
                : electionRace?.Office?.AgencyName ?? string.Empty,
            Office = electionRace?.Office?.Name ?? string.Empty,
            StateOffice = electionInfo.Jurisdiction ?? string.Empty,
            DistrictNumber = electionRace?.District.DistrictNumber.ToString(CultureInfo.InvariantCulture) ?? string.Empty,
            ElectionYear = electionRace?.Election.ElectionDate.Year.ToString(CultureInfo.InvariantCulture) ?? string.Empty,
            ElectionType = electionRace?.Election?.ElectionType?.Name ?? string.Empty,
            PoliticalParty = politicalParty?.Name ?? string.Empty,
            ElectionDate = electionRace?.Election?.ElectionDate.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) ?? string.Empty,
            DistrictName = electionRace?.District?.Name ?? string.Empty,
        };

        var decisionResponse = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsElectionInformation, List<WorkFlowError>>(DecisionsWorkflow.CandidateInformationElectionRuleset, decisionsElectionInformationInput, electionInfo.CheckRequiredFieldsFlag);

        if (decisionResponse.Count == 0)
        {
            isValid = true;
            raceId = await dependencies.RegistrationRepository.LinkElectionToCandidateIntentRegistration(id, electionInfo.Jurisdiction, electionInfo.ElectionId, electionInfo.OfficeId, electionInfo.DistrictId, electionInfo.CountyId, electionInfo.PartyId);
        }

        return new RegistrationResponseDto(raceId, isValid, decisionResponse);
    }

    /// <inheritdoc />
    public async Task<RegistrationResponseDto> UpdateCandidateIntentionStatementExpenditureLimit(long id, CandidateIntentionStatementExpenditureLimitRequest request)
    {
        bool isValid = false;
        var registration = await dependencies.RegistrationRepository.FindCandidateIntentionStatementById(id) ?? throw new KeyNotFoundException($"Registration not Found Id={id}");
        if (request == null)
        {
            throw new BadRequestException("Request is empty.");
        }

        decimal? expenditureAmount = await GetCandidateIntentionStatementExpenditureExpenseAmount(id);
        string? officeName = registration.ElectionRace?.Office?.Name;
        bool hasAccepted = request.ExpenditureLimitAccepted ?? false;

        // Call Decisions
        DecisionsExpenditureLimit decisionsInput = new()
        {
            OfficeName = officeName ?? "",
            ExpenditureLimitAccepted = hasAccepted,
        };

        var decisionResponse = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsExpenditureLimit, List<WorkFlowError>>(DecisionsWorkflow.CandidateInformationElectionLimitRuleset, decisionsInput, request.CheckRequiredFieldsFlag);

        Registration? updatedRegistration = null;
        if (decisionResponse.Count == 0)
        {
            isValid = true;
            registration.ExpenditureCeilingAmount = expenditureAmount ?? 0.00M;
            registration.ExpenditureLimitAccepted = hasAccepted;
            registration.ContributedPersonalExcessFundsOn = request.ContributedPersonalExcessFundsOn;
            registration.ExpenditureExceeded = request.ExpenditureExceeded ?? false;


            updatedRegistration = await dependencies.RegistrationRepository.Update(registration);
        }

        return new RegistrationResponseDto(updatedRegistration?.Id, isValid, decisionResponse, updatedRegistration?.StatusId);
    }

    /// <inheritdoc />
    public async Task<RegistrationResponseDto> SendForAttestation(long id)
    {
        //FindById
        if (await dependencies.RegistrationRepository.FindCandidateIntentionStatementWithElectionById(id) is not CandidateIntentionStatement registration)
        {
            throw new KeyNotFoundException($"Registration not Found Id={id}");
        }

        var response = await ValidateCandidateIntentionStatementSubmission(registration, false);

        if (response.Valid && response.StatusId.HasValue)
        {
            registration.StatusId = (long)response.StatusId;
            await dependencies.RegistrationRepository.Update(registration);
        }

        // Send Notifications      
        await SendNotifications(response.Notifications, registration.FilerId);
        await SendLinkageRequests(registration);

        return response;
    }

    /// <inheritdoc />
    public async Task<RegistrationResponseDto> SubmitCandidateIntentionStatement(long id)
    {
        //FindById
        if (await dependencies.RegistrationRepository.FindCandidateIntentionStatementWithElectionById(id) is not CandidateIntentionStatement registration)
        {
            throw new KeyNotFoundException($"Registration not Found Id={id}");
        }

        // Populate attestation entity.
        var attestation = new Attestation
        {
            CreatedBy = 0,
            ExecutedAt = dependencies.DateTimeSvc.GetCurrentDateTime(),
            ModifiedBy = 0,
            Name = registration.Name,
            RegistrationId = id,

        };

        await attestationRepository.Create(attestation);

        var response = await ValidateCandidateIntentionStatementSubmission(registration, true);

        if (response.Valid && response.StatusId.HasValue)
        {
            registration.StatusId = (long)response.StatusId;
            if (registration.Filer is not null && registration.StatusId == RegistrationStatus.Accepted.Id)
            {
                registration.Filer.FilerStatusId = FilerStatus.Active.Id;
            }
            await dependencies.RegistrationRepository.Update(registration);
        }

        // Send Notifications      
        await SendNotifications(response.Notifications, registration.FilerId);
        await SendLinkageRequests(registration);

        return response;
    }

    private async Task SendLinkageRequests(CandidateIntentionStatement registration)
    {
        var filerUsers = await filerUserRepository.FindFilerUsersByFilerId(registration.FilerId!.Value);
        var candidateUser = filerUsers
            .Where(fu => fu.FilerRoleId == FilerRole.CandidateRegistration_Candidate.Id)
            .ToList();
        if (candidateUser.Count == 0)
        {
            var sendLinkageRequestToPersonInput = new SendLinkageRequestToPersonDto
            {
                FilerId = registration.FilerId!.Value,
                RecipientEmail = registration.Email!,
                RecipientName = registration.Name!,
                FilerRoleId = FilerRole.CandidateRegistration_Candidate.Id,
            };
            await dependencies.LinkageSvc.SendLinkageRequestToPerson(sendLinkageRequestToPersonInput);
        }
    }

    /// <inheritdoc />
    public async Task<RegistrationResponseDto> SubmitCandidateIntentionStatementForEfile(CandidateIntentionStatementDto submission)
    {
        ArgumentNullException.ThrowIfNull(submission.Race);
        ArgumentNullException.ThrowIfNull(submission.Registration);

        // Handle initial filing vs amendment
        if (submission.Registration.ParentId == null)    // initial filing
        {
            submission.Registration.OriginalId = null;
            submission.Registration.Version = 0;
        }
        else    // amendment
        {
            CandidateIntentionStatement? previousFiling = await dependencies.RegistrationRepository
                .FindCandidateIntentionStatementById((long)submission.Registration.ParentId!);

            submission.Registration.OriginalId = previousFiling!.OriginalId ?? previousFiling!.Id;
            submission.Registration.Version = previousFiling!.Version++;
        }

        // Collect election info
        submission.Registration.ElectionRace = await electionRaceRepository.GetElectionRaceByIdAsync(submission.Race.Id);
        submission.Registration.ElectionOfficeSought = submission.Registration.ElectionRace!.Office?.Name;
        submission.Registration.CandidateAgency = submission.Registration.ElectionRace.Office?.AgencyName;
        submission.Registration.ElectionDistrictNumber = submission.Registration.ElectionRace.District?.DistrictNumber.ToString(CultureInfo.InvariantCulture);

        // Decisions Validation
        RegistrationResponseDto response = await ValidateCandidateIntentionStatementSubmission(submission.Registration, true);
        if (!response.Valid)
        {
            return response;
        }

        // Create a Registration
        submission.Registration.StatusId = response.StatusId ?? RegistrationStatus.Accepted.Id;
        submission.Registration.Attestations = new List<Attestation>() {
            new() {
                CreatedBy = 0,
                ExecutedAt = submission.Registration.VerificationExecutedAt!.Value,
                ModifiedBy = 0,
                Name = submission.Registration.VerificationSignature
            }
        };

        if (!string.IsNullOrWhiteSpace(submission.Registration.PoliticalParty?.Name))
        {
            var politicalParties = await politicalPartyRepository.GetAll().ConfigureAwait(false);
            submission.Registration.PoliticalPartyId =
                politicalParties.First(x => x.Name == submission.Registration.PoliticalParty.Name).Id;
        }

        await dependencies.RegistrationRepository.Create(submission.Registration);

        // Link filer to registration
        await AssociateUserToRegistration(
            submission.UserId,
            submission.Registration,
            FilerRole.CandidateRegistration_ThirdPartyVendor);

        return response;
    }

    /// <inheritdoc />
    public async Task<CandidateIntentionPatchDto.StatusResponse> PatchCandidateIntentionStatementStatusById(long id, CandidateIntentionPatchDto.StatusRequest request)
    {
        var candidateIntentionStatement = await dependencies.RegistrationRepository.FindCandidateIntentionStatementById(id) ?? throw new KeyNotFoundException($"Registration with ID {id} not found.");
        candidateIntentionStatement.StatusId = request.StatusId;
        _ = await dependencies.RegistrationRepository.Update(candidateIntentionStatement);
        return new(true);
    }

    /// <inheritdoc />
    public async Task<List<WorkFlowError>> LinkControlledCommitteeToCandidateIntentionStatement(
        long id,
        ControlledCommitteeRequestDto request)
    {
        var candidateIntention = await dependencies.RegistrationRepository.FindCandidateIntentionStatementById(id);

        if (candidateIntention is null)
        {
            return new()
            {
                new("", "", "", "Candidate Intention Statement not found.")
            };
        }

        if (request.IsRegistered is not { } isRegistered)
        {
            return new()
            {
                new("IsRegistered", "", "", "This field is required.")
            };
        }

        if (isRegistered)
        {
            if (request.ControlledCommitteeFilerId is null)
            {
                return new()
                {
                    new("CommitteeFilerId", "", "", "{{Field Name}} is required.")
                };
            }

            var filerId = (long)candidateIntention.FilerId!;

            var user = await dependencies.UserMaintenanceSvc.GetCurrentUser();

            await dependencies.FilerLinkRepository
                .LinkControlledCommitteeToCandidateIntentionStatement(filerId, request.ControlledCommitteeFilerId.Value, user.Id);
        }

        // TD: Remove link to controlled committee

        return new();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<ControlledCommitteeResponseDto>> SearchControlledCommitteeByIdOrName(string q)
    {
        var registrations = await dependencies.RegistrationRepository.FindControlledCommitteesByNameOrId(q);
        var response = registrations.Select(registration => new ControlledCommitteeResponseDto(registration));
        return response;
    }

    /// <inheritdoc />
    public async Task<ControlledCommitteeResponseDto?> GetLinkedControlledCommittee(long id)
    {
        var candidateIntention = await dependencies.RegistrationRepository
            .FindCandidateIntentionStatementById(id)
            ?? throw new BadRequestException("Candidate Intention Statement not found.");

        var filerId = (long)candidateIntention.FilerId!;

        var filerLink = await dependencies.FilerLinkRepository
            .FindByFilerIdAndLinkType(filerId, (int)FilerLinkType.ControlledCommittee.Id);


        if (filerLink is null)
        {
            return new ControlledCommitteeResponseDto { };
        }

        var controlledCommittee = await dependencies.RegistrationRepository
            .GetControlledCommitteeByFilerId(filerLink.LinkedEntityId);

        var response = new ControlledCommitteeResponseDto(controlledCommittee!);

        return response;
    }

    private async Task<CandidateIntentionStatement> AssociateUserToRegistration(CandidateIntentionStatement registration, CandidateIntentionStatementRequest request)

    {

        long? userId = await dependencies.AuthorizationSvc.GetInitiatingUserId() ?? throw new InvalidOperationException($"Unable to find User");

        var role = FilerRole.CandidateRegistration_AccountManager;

        if (request.SelfRegister ?? false)

        {

            role = FilerRole.CandidateRegistration_Candidate;

        }

        return await AssociateUserToRegistration(userId.Value, registration, role);
    }

    private async Task<CandidateIntentionStatement> AssociateUserToRegistration(long userId, CandidateIntentionStatement registration, FilerRole role)

    {

        var filerUser = new FilerUser() { UserId = userId, FilerRoleId = role.Id };

        var filer = new Filer() { CurrentRegistration = registration, FilerStatusId = FilerStatus.Draft.Id, FilerTypeId = FilerType.Candidate.Id, Users = new() { filerUser } };

        registration.Filer = filer;

        registration = (CandidateIntentionStatement)await dependencies.RegistrationRepository.Update(registration);

        return registration;

    }

    private async Task<RegistrationResponseDto> ValidateCandidateIntentionStatementSubmission(CandidateIntentionStatement registration, bool isAttesting)
    {
        var id = registration.Id;

        if (registration.StatusId != RegistrationStatus.Draft.Id)
        {
            throw new InvalidOperationException($"Cannot submit a registration that is not in 'Draft' status. Id={id} Status={registration.StatusId}");
        }

        DecisionsCandidateIntentionStatementSubmission decisionsRequest = new()
        {
            CandidateInformation = PopulateDecisionServiceRequest(registration),
            ElectionInformation = new DecisionsElectionInformation
            {
                AgencyName = registration.ElectionRace?.Office.AgencyName,
                Office = registration.ElectionRace?.Office.Name,
                StateOffice = "",
                DistrictNumber = registration.ElectionRace?.District.DistrictNumber.ToString(CultureInfo.InvariantCulture),
                ElectionYear = registration.ElectionRace?.Election.ElectionDate.Year.ToString(CultureInfo.InvariantCulture),
                ElectionType = registration.ElectionRace?.Election.ElectionType?.Name,
                PoliticalParty = registration.PoliticalParty?.Name ?? "",
                ElectionDate = registration.ElectionRace?.Election.ElectionDate.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture),
                DistrictName = registration.ElectionRace?.District.Name
            },
            ExpenditureLimit = new DecisionsExpenditureLimit { ExpenditureLimitAccepted = registration.ExpenditureLimitAccepted, OfficeName = "" }
        };

        DecisionsCandidateIntentionStatementSubmissionResponse? decisionResponse = null;
        // Call Decision service and pass the payload for final validation.
        if (isAttesting) // Candidate flow
        {
            // Add attestation
            decisionsRequest.Attestation = new DecisionsAttestation
            {
                AttestationName = registration.Name,
                AttestationRole = "Manager"
            };

            decisionResponse = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsCandidateIntentionStatementSubmission, DecisionsCandidateIntentionStatementSubmissionResponse>(
                DecisionsWorkflow.CandidateInformationCandidateSubmissionRuleset,
                decisionsRequest,
                checkRequiredFields: true);
        }
        else // Non-Candidate flow
        {
            decisionResponse = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsCandidateIntentionStatementSubmission, DecisionsCandidateIntentionStatementSubmissionResponse>(
                DecisionsWorkflow.CandidateInformationNonCandidateSubmissionRuleset,
                decisionsRequest,
                checkRequiredFields: true);
        }

        bool isValid = false;
        long statusId = registration.StatusId;
        // Set the registration status to 'Accepted' which is coming from Decision Service.
        if (decisionResponse?.Error?.Count == 0)
        {
            isValid = true;
            statusId = isAttesting
                ? RegistrationStatus.Accepted.Id
                : RegistrationStatus.Pending.Id;
        }

        return new RegistrationResponseDto(
            registration.Id,
            isValid,
            decisionResponse?.Error,
            statusId,
            decisionResponse?.Notifications);
    }

    private static DecisionsCandidateIntentionStatement PopulateDecisionServiceRequest(CandidateIntentionStatement candidateIntentionStatement)
    {
        var candidateIntentionStatementDSInput = new DecisionsCandidateIntentionStatement
        {
            FirstName = candidateIntentionStatement.FirstName,
            MiddleName = candidateIntentionStatement.MiddleName,
            LastName = candidateIntentionStatement.LastName,
            Email = candidateIntentionStatement.Email,
            FaxNumber = FormatDecisionsPhoneNumber(candidateIntentionStatement, RegistrationConstants.PhoneNumber.TypeFax),
            PhoneNumber = FormatDecisionsPhoneNumber(candidateIntentionStatement, RegistrationConstants.PhoneNumber.TypeHome),
            Address1 = MapAddress(candidateIntentionStatement.AddressList?.Addresses.FirstOrDefault(x => x.Purpose == RegistrationConstants.Address.PurposeCandidate)),
            Address2 = MapAddress(candidateIntentionStatement.AddressList?.Addresses.FirstOrDefault(x => x.Purpose == RegistrationConstants.Address.PurposeMailing)),
        };

        return candidateIntentionStatementDSInput;
    }

    private static DecisionsAddress? MapAddress(Address? address)
    {
        if (address == null)
        {
            return null;
        }

        return new DecisionsAddress
        {
            Street = address.Street,
            Street2 = address.Street2,
            City = address.City,
            Country = address.Country,
            Purpose = address.Purpose,
            State = address.State,
            Type = address.Type,
            Zip = address.Zip
        };
    }

    private static string? FormatDecisionsPhoneNumber(CandidateIntentionStatement candidateIntentionStatement, string type)
    {
        var result = string.Empty;

        var phone = candidateIntentionStatement?.PhoneNumberList?.PhoneNumbers?.FirstOrDefault(x => x.Type == type);
        if (phone != null && !string.IsNullOrWhiteSpace(phone.Number))
        {
            result = phone.CountryCode + phone.Number;
        }

        return result;
    }

    /// <summary>
    /// Sends notifications asynchronously if required.
    /// </summary>
    private async Task SendNotifications(List<NotificationTrigger>? notifications, long? filerId)
    {
        if (notifications?.Any(n => n.SendNotification) == true && filerId.HasValue)
        {
            var notificationTasks = notifications
                .Where(n => n.SendNotification && n.NotificationTemplateId != null)
                .Select(n =>
                {

                    return dependencies.NotificationSvc.SendFilerNotification(
                       new SendFilerNotificationRequest(
                           n.NotificationTemplateId!.Value,
                           filerId.Value,
                           dependencies.DateTimeSvc.GetCurrentDateTime(),
                           null
                       ));
                });

            // Try to catch error from notification service
            // Currently, the user preference has not working properly
            try
            {
                await Task.WhenAll(notificationTasks);
            }
            catch (Exception)
            {
                // Not throw to the FE
            }
        }
    }

    private async Task<Candidate> CreateCandidateAsync(CandidateIntentionStatement candidateIntentionStatement)
    {
        long? userId = null;
        if (candidateIntentionStatement.SelfRegister)
        {
            userId = await dependencies.AuthorizationSvc.GetInitiatingUserId();
        }
        else if (candidateIntentionStatement.PreviousCandidate)
        {
            var usernames = new List<string>
            {
                $"{candidateIntentionStatement.FirstName}{candidateIntentionStatement.LastName}"
            };
            var users = await dependencies.UserMaintenanceSvc.GetListUsersByUserNameAsync(usernames);
            userId = users.FirstOrDefault()?.Id;
        }
        Candidate candidate = new() { UserId = userId };
        candidate = await dependencies.CandidateRepository.Create(candidate);
        return candidate;
    }
}

public class CandidateIntentionDependencies : ICandidateIntentionDependencies
{
    public IAuthorizationSvc AuthorizationSvc { get; }
    public IDecisionsSvc DecisionsSvc { get; }
    public INotificationSvc NotificationSvc { get; }
    public IRegistrationRepository RegistrationRepository { get; }
    public ICandidateRepository CandidateRepository { get; }
    public IFilerLinkRepository FilerLinkRepository { get; }
    public IUserMaintenanceSvc UserMaintenanceSvc { get; }
    public ILinkageSvc LinkageSvc { get; }
    public IDateTimeSvc DateTimeSvc { get; }

#pragma warning disable S107 // Methods should not have too many parameters
    public CandidateIntentionDependencies(
        IAuthorizationSvc authorizationSvc,
        IDecisionsSvc decisionsSvc,
        INotificationSvc notificationSvc,
        IRegistrationRepository registrationRepository,
        ICandidateRepository candidateRepository,
        IFilerLinkRepository filerLinkRepository,
        IUserMaintenanceSvc userMaintenanceSvc,
        ILinkageSvc linkageSvc,
        IDateTimeSvc dateTimeSvc)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        AuthorizationSvc = authorizationSvc;
        DecisionsSvc = decisionsSvc;
        NotificationSvc = notificationSvc;
        RegistrationRepository = registrationRepository;
        CandidateRepository = candidateRepository;
        FilerLinkRepository = filerLinkRepository;
        UserMaintenanceSvc = userMaintenanceSvc;
        LinkageSvc = linkageSvc;
        DateTimeSvc = dateTimeSvc;
    }
}


using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Services.Common.FileSystem;
using SOS.CalAccess.Services.Common.FileSystem.Models;


namespace SOS.CalAccess.Services.WebApi.UploadFile;

/// <summary>
/// API controller responsible for routing requests related to file uploads.
/// </summary>
[ApiController]
[ApiConventionType(typeof(DefaultApiConventions))]
[AllowAnonymous]
[Route("/")]
public sealed class UploadFileController(IUploadFileSvc uploadFileSvc) : ControllerBase, IUploadFileSvc
{
    /// <inheritDoc />
    [HttpPost(IUploadFileSvc.CreateUploadedFilePath)]
    public async Task<UploadedFile> CreateUploadedFile(UploadedFile uploadedFile)
    {
        return await uploadFileSvc.CreateUploadedFile(uploadedFile);
    }

    /// <inheritDoc />
    [HttpGet(IUploadFileSvc.GetUploadsByRelationshipIdPath)]
    public async Task<IEnumerable<UploadedFile>> GetUploadsByRelationshipId(long relationshipId)
    {
        return await uploadFileSvc.GetUploadsByRelationshipId(relationshipId);
    }

    [HttpPost(IUploadFileSvc.UpdateUploadedFilePath)]
    public async Task<UploadedFile> UpdateUploadedFile(UploadedFile uploadedFile)
    {
        return await uploadFileSvc.UpdateUploadedFile(uploadedFile);
    }

    [HttpPost(IUploadFileSvc.DeleteUploadedFilePath)]
    public async Task DeleteUploadedFile(UploadedFile uploadedFile)
    {
        await uploadFileSvc.DeleteUploadedFile(uploadedFile);
    }

    [HttpGet(IUploadFileSvc.FindUploadByFileNameGuidPath)]
    public async Task<UploadedFile?> FindUploadByFileNameGuid(string fileNameGuid)
    {
        return await uploadFileSvc.FindUploadByFileNameGuid(fileNameGuid);
    }

    /// <inheritdoc/>
    [HttpPut(IUploadFileSvc.UpdateUploadedFileRelationshipsAsyncPath, Name = nameof(UpdateUploadedFileRelationshipsAsync))]
    public async Task UpdateUploadedFileRelationshipsAsync(UpdateUploadedFileRelationshipsRequest request)
    {
        await uploadFileSvc.UpdateUploadedFileRelationshipsAsync(request);
    }
}

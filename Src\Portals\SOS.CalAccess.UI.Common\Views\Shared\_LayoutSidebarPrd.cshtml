@using Microsoft.AspNetCore.Mvc.Localization
@using SOS.CalAccess.UI.Common
@using SOS.CalAccess.UI.Common.Models
@using Syncfusion.EJ2
@inject IHtmlLocalizer<SharedResources> Localizer

<div id="left-sidebar">
    <nav aria-label="Sidebar Navigation">
        <h2><a href="#">Page Title</a></h2>
        <ul>
            <li><a class="child-link" href="/Admin/Authorization/Groups">User Group</a></li>
            <li><a class="child-link" href="/Admin/Authorization/Roles">Filer Role</a></li>
            <li><a class="child-link" href="/Admin/Banners">Banner Message</a></li>
            <li><a class="child-link" href="/NotificationTemplate">Notification Template</a></li>

            <li><a class="child-link" href="/Home/UnderConstruction">Filer</a></li>
            <li><a class="child-link" href="/Home/UnderConstruction">Disclosure</a></li>
            <li><a class="child-link" href="/Home/UnderConstruction">Financial Transactions</a></li>
            <li><a class="child-link" href="/Home/UnderConstruction">Correspondence</a></li>
            <li><a class="child-link" href="/Home/UnderConstruction">Reporting</a></li>
            <li><a class="child-link" href="/Home/UnderConstruction">User&nbspAccount&nbspMaintenance</a></li>
            <li><a class="child-link" href="/Home/UnderConstruction">System Administration</a></li>
            <li><a class="child-link" href="/Home/UnderConstruction">Notifications</a></li>
            <li><a class="child-link" href="/Home/UnderConstruction">Help</a></li>
        </ul>
    </nav>
</div>


using System.Diagnostics.CodeAnalysis;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Models;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;

/// <summary>
/// Represents the response model for a payment received transaction.
/// </summary>
public class PaymentReceivedResponseDto : TransactionDetailResponseDto
{
    /// <summary>
    /// Gets or sets the payor name of this transaction
    /// </summary>
    public required string Name { get; set; }

    /// <summary>
    /// Gets or sets the candidate name or ballot measure letter/number
    /// </summary>
    public required string CandidateOrMeasure { get; set; }

    /// <summary>
    /// Gets or sets the position of Candidate or Measure, Support or Oppose
    /// </summary>
    public required string Position { get; set; }

    /// <summary>
    /// Gets or sets the candidate DTO
    /// </summary>
    public StanceOnCandidateDto? StanceOnCandidateDto { get; set; }

    /// <summary>
    /// Gets or sets the Payor Type
    /// </summary>
    public string? PayorType { get; set; }

    /// <summary>
    /// Gets or sets the ballot measure DTO
    /// </summary>
    public StanceOnBallotMeasureDto? StanceOnBallotMeasureDto { get; set; }

    public PaymentReceivedResponseDto() { }

    [SetsRequiredMembers]
    public PaymentReceivedResponseDto(long filingId, PaymentReceived transaction) : base(filingId, transaction)
    {
        var payorName = ContactResponseDto.GetContactName(transaction.Contact);
        var position = transaction.DisclosureStanceOnCandidate?.Position ?? transaction.DisclosureStanceOnBallotMeasure?.Position;
        var payorType = transaction.Contact?.FilerContactType?.Name;

        Name = payorName;
        Position = position ?? string.Empty;
        CandidateOrMeasure = string.Empty;
        PayorType = payorType ?? string.Empty;

        ResolveCandidateOrMeasure(transaction.DisclosureStanceOnCandidate, transaction.DisclosureStanceOnBallotMeasure);
    }

    private void ResolveCandidateOrMeasure(DisclosureStanceOnCandidate? stanceOnCandidate, DisclosureStanceOnBallotMeasure? stanceOnBallotMeasure)
    {
        if (stanceOnCandidate is null && stanceOnBallotMeasure is null)
        {
            return;
        }

        MapStanceOnCandidate(stanceOnCandidate);
        MapStanceOnBallotMeasure(stanceOnBallotMeasure);
    }

    private void MapStanceOnCandidate(DisclosureStanceOnCandidate? stanceOnCandidate)
    {
        if (stanceOnCandidate is null)
        {
            return;
        }

        var contactSummary = stanceOnCandidate.FilingContactSummary;
        CumulativeAmount = contactSummary?.Amount ?? CumulativeAmount;
        UnitemizedAmount = contactSummary?.PreviouslyUnitemizedAmount ?? UnitemizedAmount;
        StanceOnCandidateDto = new StanceOnCandidateDto
        {
            CandidateId = stanceOnCandidate.SubjectId,
        };

        if (stanceOnCandidate.UnregisteredCandidateSubject is not null)
        {
            var unregisteredSubject = stanceOnCandidate.UnregisteredCandidateSubject;
            CandidateOrMeasure = $"{unregisteredSubject.FirstName} {unregisteredSubject.LastName}";

            StanceOnCandidateDto.FirstName = unregisteredSubject.FirstName;
            StanceOnCandidateDto.LastName = unregisteredSubject.LastName;
            StanceOnCandidateDto.MiddleName = unregisteredSubject.MiddleName;
            StanceOnCandidateDto.OfficeSought = unregisteredSubject.OfficeSoughtOrHeld;
            StanceOnCandidateDto.Jurisdiction = unregisteredSubject.Jurisdiction;
            StanceOnCandidateDto.District = unregisteredSubject.District;

            return;
        }

        var subject = stanceOnCandidate.Subject?.Registrations.OrderByDescending(x => x.Id).FirstOrDefault();
        CandidateOrMeasure = subject?.Name ?? string.Empty;
        StanceOnCandidateDto.CandidateName = CandidateOrMeasure;
    }

    private void MapStanceOnBallotMeasure(DisclosureStanceOnBallotMeasure? stanceOnBallotMeasure)
    {
        if (stanceOnBallotMeasure is null)
        {
            return;
        }

        var contactSummary = stanceOnBallotMeasure.FilingContactSummary;
        CumulativeAmount = contactSummary?.Amount ?? CumulativeAmount;
        UnitemizedAmount = contactSummary?.PreviouslyUnitemizedAmount ?? UnitemizedAmount;
        StanceOnBallotMeasureDto = new StanceOnBallotMeasureDto
        {
            BallotMeasureId = stanceOnBallotMeasure.SubjectId,
        };

        if (stanceOnBallotMeasure.UnregisteredBallotMeasureSubject is not null)
        {
            var unregisteredSubject = stanceOnBallotMeasure.UnregisteredBallotMeasureSubject;
            CandidateOrMeasure = unregisteredSubject.BallotNumberOrLetter ?? string.Empty;

            StanceOnBallotMeasureDto.BallotLetter = unregisteredSubject.BallotNumberOrLetter;
            StanceOnBallotMeasureDto.Jurisdiction = unregisteredSubject.Jurisdiction;
            StanceOnBallotMeasureDto.Title = unregisteredSubject.FullTitleOrDescription;

            return;
        }

        CandidateOrMeasure = stanceOnBallotMeasure.Subject?.Code ?? string.Empty;
        StanceOnBallotMeasureDto.BallotNumberOrLetter = CandidateOrMeasure;
    }
}

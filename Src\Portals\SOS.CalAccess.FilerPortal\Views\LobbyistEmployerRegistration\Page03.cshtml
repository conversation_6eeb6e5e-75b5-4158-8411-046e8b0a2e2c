@using SOS.CalAccess.FilerPortal.Models.Localization;
@using SOS.CalAccess.FilerPortal.ViewHelpers
@inject IHtmlLocalizer<SharedResources> Localizer
@model SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration.LobbyistEmployerRegistrationStep01GenInfo;
@{
    // FR-LEC-FilerInformation

    var progressBar = LobbyistRegistrationProgressBarHelper.BuildProgressBar(
        version: Model.Version,
        id: Model.Id,
        type: RegistrationConstants.RegistrationType.LobbyistEmployer,
        currentStep: 1,
        ViewData
    );

    var buttonConfig = new ButtonBarModel
            {
                LeftButtons = new List<ButtonConfig>
        {
            ButtonBarModel.DefaultPrevious,
            ButtonBarModel.DefaultContinue,
        },
                RightButtons = new List<ButtonConfig>
        {
            new ()
            {
                Type = ButtonType.Custom,
                HtmlContent = await Html.PartialAsync("_CancelDraftButton", Model.Id),
            },
            ButtonBarModel.DefaultSaveAndClose,
        }
            };
}
<partial name="_LayoutProgressbar" model="progressBar" />

@Html.StepHeader(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistration03Title)
@Html.TextBlock(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistration03Body)

@using (Html.BeginForm("Page03", "LobbyistEmployerRegistration", FormMethod.Post))
{
    <partial name="_LobbyistEmployerRegistrationGenInfo" model="Model" />
    <partial name="_ButtonBar" model="buttonConfig" />
}

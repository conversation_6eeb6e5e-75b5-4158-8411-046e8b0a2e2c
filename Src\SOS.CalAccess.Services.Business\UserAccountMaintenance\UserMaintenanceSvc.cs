using SOS.CalAccess.Data.Contracts.UserAccountMaintenance;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;

namespace SOS.CalAccess.Services.Business.UserAccountMaintenance;
public class UserMaintenanceSvc(IAuthorizationSvc authorizationSvc, IUserRepository userRepository) : IUserMaintenanceSvc
{
    public void AddUserAddress(Address address, int userId)
    {
        throw new NotImplementedException();
    }

    public void AddUserEmailAddress(EmailAddress emailAddress, string userId)
    {
        throw new NotImplementedException();
    }

    public void AddUserPhoneNumber(PhoneNumber phoneNumber, int userId)
    {
        throw new NotImplementedException();
    }

    public async Task<BasicUserDto> GetCurrentUser()
    {
        var userId = await authorizationSvc.GetInitiatingUserId() ?? throw new KeyNotFoundException("Unable to find user record");
        var user = await userRepository.FindById(userId);

        return new BasicUserDto(user!);
    }

    public Task<IEnumerable<User>> ListUsers()
    {
        throw new NotImplementedException();
    }

    public void RemoveUserAddress(int userAddressId)
    {
        throw new NotImplementedException();
    }

    public void RemoveUserEmailAddress(int emailId)
    {
        throw new NotImplementedException();
    }

    public void RemoveUserPhoneNumber(int userPhoneNumberId)
    {
        throw new NotImplementedException();
    }

    public int RequestUserEmailVerification(string emailId)
    {
        throw new NotImplementedException();
    }

    public Task<IEnumerable<User>> SearchUsers(User user)
    {
        throw new NotImplementedException();
    }

    public void UpdateUserAddress(UserAddress address)
    {
        throw new NotImplementedException();
    }

    public void UpdateUserPhoneNumber(UserPhoneNumber phoneNumber)
    {
        throw new NotImplementedException();
    }

    public bool VerifyUserEmail(int emailId, string verificationCode)
    {
        throw new NotImplementedException();
    }

    public Task<User> ViewUser(int userId)
    {
        throw new NotImplementedException();
    }

    public async Task<List<User>> GetListUsersByUserNameAsync(List<string> userNames)
    {
        return await userRepository.FindUsersByUserNames(userNames);
    }

    /// <summary>
    /// Get user by id
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    public async Task<User?> GetUserById(long userId)
    {
        return await userRepository.FindById(userId);
    }

    /// <summary>
    /// Update user with account details with address and phone numbers
    /// </summary>
    /// <param name="userAccountDetails"></param>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException"></exception>
    public async Task UpdateUser(UserAccountDetailsDto userAccountDetails)
    {
        var user = await userRepository.GetUserAccountDetailsByUserId(userAccountDetails.Id) ?? throw new InvalidOperationException($"No user found with Id = {userAccountDetails.Id}");

        user.AddressList ??= new AddressList
        {
            CreatedBy = userAccountDetails.Id,
            ModifiedBy = userAccountDetails.Id,
        };

        Address? addressToUpdate = user.AddressList.Addresses.FirstOrDefault();

        var newAddress = userAccountDetails.AddressDto!;

        if (addressToUpdate == null)
        {
            // No “Candidate Address” found  create a brand-new Address row
            addressToUpdate = new Address
            {
                Street = newAddress.Street!,
                Street2 = newAddress.Street2,
                City = newAddress.City!,
                State = newAddress.State!,
                Zip = newAddress.Zip!,
                Country = newAddress.Country!,
                Type = newAddress.Type!,
                CreatedBy = userAccountDetails.Id,
                ModifiedBy = userAccountDetails.Id,
                Purpose = "Candidate Address",
                AddressList = user.AddressList
            };
            user.AddressList.Addresses.Add(addressToUpdate);
        }
        else
        {
            addressToUpdate.Street = newAddress.Street!;
            addressToUpdate.Street2 = newAddress.Street2;
            addressToUpdate.City = newAddress.City!;
            addressToUpdate.State = newAddress.State!;
            addressToUpdate.Zip = newAddress.Zip!;
            addressToUpdate.Country = newAddress.Country!;
            addressToUpdate.ModifiedBy = userAccountDetails.Id;
            addressToUpdate.Type = newAddress.Type!;
        }

        user.PhoneNumberList ??= new PhoneNumberList()
        {
            CreatedBy = userAccountDetails.Id,
            ModifiedBy = userAccountDetails.Id,
        };

        var existingPhoneNumberList = user.PhoneNumberList!;
        var existingPhoneNumbers = existingPhoneNumberList.PhoneNumbers;

        var existingById = existingPhoneNumbers
            .Where(p => p.Id > 0)
            .ToDictionary(p => p.Id);

        // Track which existing IDs we process
        var seenIds = new HashSet<long>();

        //For each incoming DTO, do either UPDATE (if Id matches) or INSERT (if no Id)
        foreach (var dto in userAccountDetails.PhoneNumberDto!)
        {
            if (dto.Id.HasValue
                && dto.Id.Value > 0
                && existingById.TryGetValue(dto.Id.Value, out var phoneEntity))
            {
                //Update
                seenIds.Add(dto.Id.Value);

                phoneEntity.InternationalNumber = dto.InternationalNumber ?? false;
                phoneEntity.CountryCode = dto.CountryCode;
                phoneEntity.Number = dto.Number ?? string.Empty;
                phoneEntity.Extension = dto.Extension;
                phoneEntity.Type = dto.Type ?? string.Empty;
                phoneEntity.ModifiedBy = userAccountDetails.Id;
                phoneEntity.IsPrimaryPhoneNumber = dto.SetAsPrimaryPhoneNumber;
            }
            else
            {
                //Insert
                var newPhone = new PhoneNumber
                {
                    InternationalNumber = dto.InternationalNumber ?? false,
                    CountryCode = dto.CountryCode,
                    Number = dto.Number ?? string.Empty,
                    Extension = dto.Extension,
                    Type = dto.Type ?? string.Empty,
                    CreatedBy = userAccountDetails.Id,
                    ModifiedBy = userAccountDetails.Id,
                    IsPrimaryPhoneNumber = dto.SetAsPrimaryPhoneNumber,
                };

                existingPhoneNumberList.PhoneNumbers.Add(newPhone);
            }
        }

        //Any existing phone whose Id was NOT “seen” in incoming DTO should be deleted
        var toRemove = existingPhoneNumbers
            .Where(p => p.Id > 0 && !seenIds.Contains(p.Id))
            .ToList();

        foreach (var orphan in toRemove)
        {
            existingPhoneNumberList.PhoneNumbers.Remove(orphan);
        }

        await userRepository.Update(user);
        await userRepository.SaveChangesAsync();
    }
    /// <inheritdoc />
    public Task<bool> DeactivateUser(long userId, long inactivateReason)
    {
        throw new NotImplementedException();
    }

    /// <summary>
    /// Get user account details by user id
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    public async Task<UserAccountDetailsDto> GetUserAccountDetailsByUserId(long userId)
    {
        AddressDto addressDto = new();
        List<PhoneNumberDto> phoneNumberDto = new();
        var user = await userRepository.GetUserAccountDetailsByUserId(userId);
        var address = user.AddressList?.Addresses.FirstOrDefault();
        var phoneNumberList = user.PhoneNumberList?.PhoneNumbers.ToList();
        if (address != null)
        {
            addressDto.Street = address.Street;
            addressDto.Street2 = address.Street2;
            addressDto.City = address.City;
            addressDto.State = address.State;
            addressDto.Country = address.Country;
            addressDto.Zip = address.Zip;
            addressDto.Type = address.Type;
            addressDto.Purpose = address.Purpose;
        }
        if (phoneNumberList != null && phoneNumberList.Count != 0)
        {
            foreach (PhoneNumber item in phoneNumberList)
            {
                PhoneNumberDto phone = new()
                {
                    Number = item.Number,
                    Extension = item.Extension,
                    InternationalNumber = item.InternationalNumber,
                    CountryCode = item.CountryCode,
                    Type = item.Type,
                    SelectedCountry = item.CountryId,
                    SetAsPrimaryPhoneNumber = item.IsPrimaryPhoneNumber,
                };
                phoneNumberDto.Add(phone);
            }
        }
        UserAccountDetailsDto userAccountDetailsDto = new()
        {
            FirstName = user.FirstName,
            MiddleName = user.MiddleName,
            LastName = user.LastName,
            UserName = user.UserName,
            Email = user.EmailAddress,
            AddressDto = addressDto,
            PhoneNumberDto = phoneNumberDto
        };

        return userAccountDetailsDto;
    }
}

﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SOS.CalAccess.Data.EntityFramework.Migrations
{
    /// <inheritdoc />
    public partial class FRLOBInterestRelationship : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "LobbyingInterestId",
                table: "RegistrationFiling",
                type: "bigint",
                nullable: true)
                .Annotation("Documentation:Description", "Foreign Key reference to the LobbyingInterest Id for the registration.")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "RegistrationFilingHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null)
                .Annotation("SqlServer:TemporalPeriodEndColumnName", "PeriodEnd")
                .Annotation("SqlServer:TemporalPeriodStartColumnName", "PeriodStart");

            migrationBuilder.CreateIndex(
                name: "IX_RegistrationFiling_LobbyingInterestId",
                table: "RegistrationFiling",
                column: "LobbyingInterestId");

            migrationBuilder.AddForeignKey(
                name: "FK_RegistrationFiling_LobbyingInterest_LobbyingInterestId",
                table: "RegistrationFiling",
                column: "LobbyingInterestId",
                principalTable: "LobbyingInterest",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RegistrationFiling_LobbyingInterest_LobbyingInterestId",
                table: "RegistrationFiling");

            migrationBuilder.DropIndex(
                name: "IX_RegistrationFiling_LobbyingInterestId",
                table: "RegistrationFiling");

            migrationBuilder.DropColumn(
                name: "LobbyingInterestId",
                table: "RegistrationFiling")
                .Annotation("SqlServer:IsTemporal", true)
                .Annotation("SqlServer:TemporalHistoryTableName", "RegistrationFilingHistory")
                .Annotation("SqlServer:TemporalHistoryTableSchema", null)
                .Annotation("SqlServer:TemporalPeriodEndColumnName", "PeriodEnd")
                .Annotation("SqlServer:TemporalPeriodStartColumnName", "PeriodStart");
        }
    }
}

<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>$(DefineConstants);TRACE;DEBUG</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <AdditionalFiles Remove="Enums\**" />
    <AdditionalFiles Remove="Models\Registrations\SlateMailerOrganization\**" />
    <AdditionalFiles Remove="wwwroot\lib\bootstrap\dist\css\**" />
    <Compile Remove="Enums\**" />
    <Compile Remove="Models\Registrations\SlateMailerOrganization\**" />
    <Compile Remove="wwwroot\lib\bootstrap\dist\css\**" />
    <Content Remove="Enums\**" />
    <Content Remove="Models\Registrations\SlateMailerOrganization\**" />
    <Content Remove="wwwroot\lib\bootstrap\dist\css\**" />
    <EmbeddedResource Remove="Enums\**" />
    <EmbeddedResource Remove="Models\Registrations\SlateMailerOrganization\**" />
    <EmbeddedResource Remove="wwwroot\lib\bootstrap\dist\css\**" />
    <None Remove="Enums\**" />
    <None Remove="Models\Registrations\SlateMailerOrganization\**" />
    <None Remove="wwwroot\lib\bootstrap\dist\css\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="LayoutConstants.cs" />
    <Compile Remove="SharedResources.cs" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="Views\Shared\_LayoutFullHeader.cshtml" />
    <Content Remove="Views\Shared\_LayoutSidebar.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Views\Shared\_LayoutFullHeader.cshtml.css" />
    <None Remove="Views\Shared\_LayoutSidebar.cshtml.css" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.1" />
    <PackageReference Include="Serilog" Version="4.1.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Syncfusion.EJ2.AspNet.Core" Version="28.1.33" />
    <PackageReference Include="Refit">
      <Version>8.0.0</Version>
    </PackageReference>
    <PackageReference Include="Refit.HttpClientFactory">
      <Version>8.0.0</Version>
    </PackageReference>
    <PackageReference Include="Refitter.SourceGenerator" Version="1.4.1">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="System.IdentityModel.Tokens.Jwt">
      <Version>8.3.0</Version>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\SOS.CalAccess.Foundation\SOS.CalAccess.Foundation.csproj" />
    <ProjectReference Include="..\..\SOS.CalAccess.Models\SOS.CalAccess.Models.csproj" />
    <ProjectReference Include="..\..\Contracts\SOS.CalAccess.Services.Business.Contracts\SOS.CalAccess.Services.Business.Contracts.csproj" />
    <ProjectReference Include="..\..\Clients\SOS.CalAccess.Services.Business.WebApiClient\SOS.CalAccess.Services.Business.WebApiClient.csproj" />
    <ProjectReference Include="..\..\Contracts\SOS.CalAccess.Services.Technical.Contracts\SOS.CalAccess.Services.Technical.Contracts.csproj" />
    <ProjectReference Include="..\..\Clients\SOS.CalAccess.Services.Technical.WebApiClient\SOS.CalAccess.Services.Technical.WebApiClient.csproj" />
    <ProjectReference Include="..\..\SOS.CalAccess.Services.Business\SOS.CalAccess.Services.Business.csproj" />
    <ProjectReference Include="..\..\SOS.CalAccess.Services.Common\SOS.CalAccess.Services.Common.csproj" />
    <ProjectReference Include="..\SOS.CalAccess.UI.Common\SOS.CalAccess.UI.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\Dashboard\" />
  </ItemGroup>

</Project>

// Ignore Spelling: Validator

using Newtonsoft.Json.Linq;
using SOS.CalAccess.Services.Common.DataValidation;


namespace SOS.CalAccess.Services.Business.Efile;

public class LobbyingDisclosurePaymentsReceivedValidator : IFormValidator
{
    private const string FormName = FormNameToIdMapping.LobbyingDisclosurePaymentsReceived;

    public LobbyingDisclosurePaymentsReceivedValidator() { }

    public bool CanHandle(string formName)
        => string.Equals(formName, FormName, StringComparison.OrdinalIgnoreCase);

    public Task<int> ValidateAsync(JObject jsonData)
    {
        return Task.FromResult(0);
    }
}

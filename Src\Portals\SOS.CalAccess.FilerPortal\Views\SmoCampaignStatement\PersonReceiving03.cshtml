@using SOS.CalAccess.Models.Common
@using SOS.CalAccess.UI.Common
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.FilerPortal.Models.Disclosure.SmoCampaignStatement
@using SOS.CalAccess.UI.Common.Enums
@using SOS.CalAccess.UI.Common.Localization;
@using SOS.CalAccess.UI.Common.Constants;
@inject IHtmlLocalizer<SharedResources> Localizer

@model SmoCampaignStatementTransactionEntryViewModel
@{
    // FR-CF-PersonReceiving-3
    var buttonConfig = new ButtonBarModel
            {
                RightButtons = new List<ButtonConfig>
        {
            new ()
            {
                HtmlContent = await Html.PartialAsync("_CancelPaymentEntryButton"),
                Type = ButtonType.Custom,
            },
        },
                LeftButtons = new List<ButtonConfig>
        {
            ButtonBarModel.DefaultPrevious,
            new ()
            {
                Action = FormAction.SaveAndClose,
                Type = ButtonType.Button,
                CssClass = "btn me-2 btn-primary",
                InnerTextKey = CommonResourceConstants.Save
            },
        },
            };
    var allowedFileExtension = DisclosureConstants.Transaction.AllowedFileExtension;
    var relationshipTypeString = Enum.GetName(typeof(RelationshipType), RelationshipType.DisclosureTransaction);
}

<div class="p-5">
    @Html.StepHeader(SharedLocalizer, ResourceConstants.SmoCampaignStatementEnterTransaction)
    <div class="p-5">
        <div class="mb-5">
            @Html.StepHeader(SharedLocalizer, ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentTitle)

            @Html.TextBlock(SharedLocalizer, ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentBody)
        </div>

        @using (Html.BeginForm("PersonReceiving03", "SmoCampaignStatement", FormMethod.Post))
        {
            @Html.AntiForgeryToken()
            @Html.HiddenFor(m => m.Id)
            @Html.HiddenFor(m => m.ContactId)
            @Html.HiddenFor(m => m.TransactionId)
            @Html.HiddenFor(m => m.AttachedFileGuidsJson)

            <div class="col-sm-6 mb-3">
                @Html.CurrencyInputFor(SharedLocalizer, m => m.TransactionAmount, ResourceConstants.TransactionAboutThePaymentAmountPaid, true, true, widthInEmUnits: null)
            </div>

            <div class="col-sm-6 mb-3">
                @Html.FileUploader(
                         SharedLocalizer,
                         allowedFileExtension,
                         relationshipId: Model.TransactionId,
                         relationshipType: relationshipTypeString,
                         titleResourceKey: ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentAttachFiles,
                         instructionsResourceKey: null,
                         onUploadSuccess: "handlePersonReceivingUploadedFile",
                         handleMultipleFiles: true
                         )
            </div>

            <div class="col-sm-6 mb-3">
                @Html.TextAreaFor(
                         Localizer,
                         m => m.Notes!,
                         ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentNotes
                         )
            </div>

            <div class="mt-5">
                <partial name="_ButtonBar" model="buttonConfig" />
            </div>
        }
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function() {
        const transactionAmountField = document.getElementById('@Html.IdFor(m => m.TransactionAmount)');

        // Disable mousewheel to changing value
        transactionAmountField.addEventListener("mousewheel", function(e) {
            e.stopImmediatePropagation();
        });
    })

    function handlePersonReceivingUploadedFile(args) {
        try {
            //Validate response value
            if (!args) return;
            let originalPersonReceivingFileName = args.file.name;

            const event = args.e;
            if (!event) return;

            //Get current uploaded files
            let personReceivingAttachmentFiles = [];
            const personReceivingAttachedFileElement = document.getElementById('@Html.IdFor(m => m.AttachedFileGuidsJson)');

            if (personReceivingAttachedFileElement.value) {
                personReceivingAttachmentFiles = JSON.parse(personReceivingAttachedFileElement.value);
            }
            let personReceivingFileName = '';

            //Add or remove uploaded file base on action
            switch (args.operation) {
                case "@CommonConstants.FileAction.Remove":
                    personReceivingFileName = originalPersonReceivingFileName;
                    if (!personReceivingFileName) return;
                    personReceivingAttachmentFiles = personReceivingAttachmentFiles.filter(f => f !== personReceivingFileName);
                    break;

                case "@CommonConstants.FileAction.Upload":
                    personReceivingFileName = args.e.target.response;
                    if (!personReceivingFileName) return;
                    if (!personReceivingAttachmentFiles.includes(personReceivingFileName)) {
                        personReceivingAttachmentFiles.push(personReceivingFileName);
                    }
                    break;    

                default:
                    break;
            }

            //Update current uploaded files
            personReceivingAttachedFileElement.value = JSON.stringify(personReceivingAttachmentFiles);

        } catch (e) {
            
        }
    }
</script>

@using SOS.CalAccess.Models.Common
@using Syncfusion.EJ2.Inputs
@using Syncfusion.EJ2
@using SOS.CalAccess.Models.UserAccountMaintenance
@using SOS.CalAccess.FilerPortal.Models.Localization;
@using SOS.CalAccess.Services.Business
@using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models
@using SOS.CalAccess.UI.Common.Constants;
@using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

@inject IReferenceDataSvc RefDataSvc
@inject IHtmlLocalizer<SharedResources> Localizer
@model AccountManagementModel

@{
    ViewBag.Title = Localizer[ResourceConstants.UserAccountManagementTitle].Value;
    long? id = null;

    var addressTypeOptions = new List<SelectListItem>
    {
    new SelectListItem{ Text = "Residential", Value = Localizer[ResourceConstants.AccountManagementResidential].Value, Selected = Model.Addresses[0]?.Type == Localizer[ResourceConstants.AccountManagementResidential].Value },
    new SelectListItem{ Text = "Business", Value = Localizer[ResourceConstants.AccountManagementBusiness].Value, Selected = Model.Addresses[0]?.Type == Localizer[ResourceConstants.AccountManagementBusiness].Value }
    };

    var accuMailValidateModalModel = new AccuMailValidationModalModel { Suggestions = Model.Suggestions, Action = Model.Action };

    var buttonConfig = new ButtonBarModel
            {
                LeftButtons = new List<ButtonConfig>
        {
            new ButtonConfig
            {
                Type = ButtonType.Custom,
                HtmlContent = Html.Button(Localizer, "Save", "btnSubmit")
            }
        },
                RightButtons = new List<ButtonConfig>
        {
            new ButtonConfig
            {
                Type = ButtonType.Custom,
                HtmlContent = await Html.PartialAsync("_ModalDialog", id)
            }
        }
            };

    var phoneButton = new AddAnotherButtonModel
    {
        Name = "action",
        Value = "AddPhone",
        CssClass = "btn btn-secondary",
        IconClass = "bi bi-plus-circle",
        Text = Localizer[ResourceConstants.AccountManagementAddAnotherPhoneButton].Value, 
        Type = "submit"
    };
}

<div style="font-size:2em;"><strong>@Localizer[ResourceConstants.AccountManagementUserDetails]</strong></div>
</br>

<div class="mb-3 col-sm-6">
    UI Text Explanation under development and review
</div>

<form method="post" asp-action="UpdateUser">

    <partial name="_AccuMailValidationModal" model="accuMailValidateModalModel" />
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.Id)
    @Html.HiddenFor(m => m.FirstName)
    @Html.HiddenFor(m => m.LastName)
    @Html.HiddenFor(m => m.MiddleName)

    <div class="mb-3 col-sm-6">
        @Html.TextFieldFor(Localizer, m => m.UserName, ResourceConstants.AccountManagementUserName, true, readOnly: true)
        <a href="">Change username</a>
    </div>

    <div class="mb-3 col-sm-6">
        @Html.TextFieldFor(Localizer, m => m.Email, ResourceConstants.AccountManagementEmailAddress, true, readOnly: true)
        <a href="">Update email address</a>
    </div>

    <hr />

    <div class="mb-3 col-sm-6">
        <div style="font-size:1em;"><strong>@Localizer[ResourceConstants.AccountManagementName]</strong></div>
    </div>
    <div class="mb-3 col-sm-6">
        @Html.TextFieldFor(Localizer, m => m.FullName, ResourceConstants.AccountManagementFullName, true, readOnly: true)
        <a href="">Change name</a>
    </div>

    <hr />

    <div class="mb-3 col-sm-6">
        <div style="font-size:1em;"><strong>@Localizer[ResourceConstants.AccountManagementAddress]</strong></div>
    </div>

    <div class="mb-3 col-sm-6">
        @Html.RadioFor(Localizer, m => m.Addresses[0]!.Type, ResourceConstants.AccountManagementAddressType, addressTypeOptions, true, false)
    </div>

    <div class="mb-3 col-sm-6">
        @Html.DropdownFor(Localizer, m => m.Addresses[0]!.Country, ResourceConstants.AccountManagementCountry, CommonConstants.CountryDropdownOptions, ResourceConstants.AccountManagementSelectCountry, true)
    </div>

    <div class="mb-3 col-sm-6">
        @Html.TextFieldFor(Localizer, m => m.Addresses[0]!.Street, ResourceConstants.AccountManagementStreetAddress, true)

        @Html.TextFieldFor(Localizer, m => m.Addresses[0]!.Street2, ResourceConstants.AccountManagementStreetAddress2, false)

        @Html.TextFieldFor(Localizer, m => m.Addresses[0]!.City, ResourceConstants.AccountManagementCity, true)
    </div>

    <div class="mb-3 col-sm-6">
        @Html.TextFieldFor(Localizer, m => m.Addresses[0]!.Zip, ResourceConstants.AccountManagementZipCode, true)
    </div>
    <div class="mb-3 col-sm-6">
        @Html.DropdownFor(Localizer, m => m.Addresses[0]!.State, ResourceConstants.AccountManagementState, CommonConstants.StateDropdownOptions, ResourceConstants.AccountManagementSelectState, true)
    </div>

    <hr />
    <div class="mb-3">
        <div style="font-size:1em;"><strong>@Localizer[ResourceConstants.AccountManagementPhoneNumber]</strong></div>
    </div>

    @for (var i = 0; i < Model.PhoneNumberDto!.Count; i++)
    {
        <div class="mb-3 phone-row" data-index="@i">
            <div class="mb-3">
                @Html.RadioFor(
                         Localizer,
                         m => Model.PhoneNumberDto[i].Type,
                         ResourceConstants.AccountManagementTypeOfPhone,
                         new List<SelectListItem> {
            new SelectListItem { Text = Localizer[ResourceConstants.AccountManagementCell].Value, Value = Localizer[ResourceConstants.AccountManagementCell].Value, Selected = (Model.PhoneNumberDto[i].Type == Localizer[ResourceConstants.AccountManagementCell].Value) },
            new SelectListItem { Text = Localizer[ResourceConstants.AccountManagementOffice].Value, Value = Localizer[ResourceConstants.AccountManagementOffice].Value, Selected = (Model.PhoneNumberDto[i].Type == Localizer[ResourceConstants.AccountManagementOffice].Value) },
            new SelectListItem { Text = Localizer[ResourceConstants.AccountManagementHome].Value, Value = Localizer[ResourceConstants.AccountManagementHome].Value, Selected = (Model.PhoneNumberDto[i].Type == Localizer[ResourceConstants.AccountManagementHome].Value) }
            },
                         required: true,
                         disabled: false,
                         readOnly: false
                         )
            </div>

            <div class="mb-3 col-sm-auto">
                @Html.PhoneNumberFor(
                         RefDataSvc,
                         Localizer,
                         m => Model.PhoneNumberDto[i],
                         ResourceConstants.AccountManagementTelephoneNumber,
                         required: true)
            </div>

            <div class="mb-3">
                @Html.CheckBoxFor(
                         Localizer,
                         m => Model.PhoneNumberDto[i].SetAsPrimaryPhoneNumber,
                         ResourceConstants.AccountManagementSetAsPrimaryPhoneNumber)
            </div>
        </div>
    }

    <div class="mb-3">
        @await Html.PartialAsync("_AddAnotherButton", phoneButton)
    </div>

    <div class="mb-3 col-sm-6">
        @Html.ValidationSummary(true, null, new { @class = "validation-summary text-danger" })
    </div>

    <hr />
    </br>
    <div class="row">
        <div class="col-sm-5"></div>
        <div class="col-sm-2">
            <partial name="_ButtonBar" model="buttonConfig" />
        </div>
        <div class="col-sm-5"></div>
    </div>
</form>

<script>
    document.addEventListener("DOMContentLoaded", function() {

      const primaryCheckboxes = document.querySelectorAll('input[name$=".SetAsPrimaryPhoneNumber"]');

      primaryCheckboxes.forEach(function(cb) {
        cb.addEventListener("change", function(e) {
          if (e.target.checked) {
            // When one checkbox is checked, uncheck all the others
            primaryCheckboxes.forEach(function(other) {
              if (other !== e.target) {
                other.checked = false;
              }
            });
          }
        });
      });
    });
</script>


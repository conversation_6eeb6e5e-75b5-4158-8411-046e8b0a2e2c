using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Disclosure.SmoCampaignStatement;
using SOS.CalAccess.UI.Common.Constants;
using AddressDto = SOS.CalAccess.FilerPortal.Generated.AddressDto;
using EmailAddressDto = SOS.CalAccess.FilerPortal.Generated.EmailAddressDto;
using FilerContactType = SOS.CalAccess.Models.FilerDisclosure.Contacts.FilerContactType;
using PhoneNumberDto = SOS.CalAccess.FilerPortal.Generated.PhoneNumberDto;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Mapper;
public static class FilerContactMapper
{
    public static UpsertFilerContactRequest BuildFilerContactPayload(SmoCampaignStatementTransactionEntryViewModel model, string filerContactForm)
    {
        var addressList = model.ParticipantType == FilerContactType.Filer.Name
            ? new List<AddressDto>() // no addresses for a Filer
            : new List<AddressDto> {
                MapAddressToDto(model)
            };

        // TD: Refactor this to not insert new record every time we update.
        var emailList = string.IsNullOrWhiteSpace(model.Email)
            ? new List<EmailAddressDto>()
            : new List<EmailAddressDto> { new(model.Email, 0L, string.Empty, string.Empty) };

        var phoneNumber = model.PhoneNumber;
        var phoneList = string.IsNullOrWhiteSpace(phoneNumber.Number)
            ? new List<PhoneNumberDto>()
            : new List<PhoneNumberDto>
            {
                new(
                    phoneNumber.CountryCode ?? string.Empty,
                    phoneNumber.Extension ?? string.Empty,
                    phoneNumber.Id,
                    phoneNumber.InternationalNumber,
                    phoneNumber.Number ?? string.Empty,
                    phoneNumber.SelectedCountry,
                    phoneNumber.SetAsPrimaryPhoneNumber,
                    phoneNumber.Type ?? string.Empty)
            };

        return model.ParticipantType switch
        {
            string type when type == FilerContactType.Individual.Name => new UpsertIndividualFilerContactRequest(
                addresses: addressList,
                emailAddresses: emailList,
                phoneNumbers: phoneList,
                employer: model.Employer ?? string.Empty,
                filerContactForm: filerContactForm,
                firstName: model.FirstName ?? string.Empty,
                lastName: model.LastName ?? string.Empty,
                middleName: model.MiddleName ?? string.Empty,
                occupation: string.Empty
            ),

            string type when type == FilerContactType.Filer.Name => new UpsertFilerCommitteeFilerContactRequest(
                addresses: addressList,
                emailAddresses: emailList,
                phoneNumbers: phoneList,
                committeeName: model.CommitteeName ?? string.Empty,
                committeeId: model.CommitteeId ?? null,
                filerContactForm: filerContactForm
            ),

            string type when type == FilerContactType.Candidate.Name => new UpsertCandidateFilerContactRequest(
                addresses: addressList,
                emailAddresses: emailList,
                phoneNumbers: phoneList,
                firstName: model.CandidateFirstName ?? string.Empty,
                lastName: model.CandidateLastName ?? string.Empty,
                middleName: model.CandidateMiddleName ?? string.Empty,
                officeSought: model.OfficeSought ?? string.Empty,
                jurisdiction: model.JurisdictionName ?? string.Empty,
                district: model.District ?? string.Empty,
                filerContactForm: filerContactForm
            ),

            string type when type == FilerContactType.Organization.Name => new UpsertOrganizationFilerContactRequest(
                addresses: addressList,
                emailAddresses: emailList,
                phoneNumbers: phoneList,
                organizationName: model.OrganizationName ?? string.Empty,
                filerContactForm: filerContactForm
            ),

            _ => throw new InvalidOperationException($"Unsupported transactor type: {model.ParticipantType}")
        };
    }

    private static AddressDto MapAddressToDto(SmoCampaignStatementTransactionEntryViewModel model)
    {
        if (model.ParticipantType == FilerContactType.Individual.Name)
        {
            return new AddressDto(
                id: model.IndividualTransactorAddress.Id.GetValueOrDefault(),
                street: model.IndividualTransactorAddress.Street ?? string.Empty,
                street2: model.IndividualTransactorAddress.Street2 ?? string.Empty,
                city: model.IndividualTransactorAddress.City ?? string.Empty,
                state: model.IndividualTransactorAddress.State ?? string.Empty,
                zip: model.IndividualTransactorAddress.Zip ?? string.Empty,
                type: model.IndividualTransactorAddress.Type ?? CommonConstants.Address.TypeBusiness,
                country: model.IndividualTransactorAddress.Country ?? string.Empty,
                purpose: model.IndividualTransactorAddress.Purpose ?? CommonConstants.Address.PurposeMailing
                );
        }

        if (model.ParticipantType == FilerContactType.Organization.Name)
        {
            return new AddressDto(
                id: model.OrganizationTransactorAddress.Id.GetValueOrDefault(),
                street: model.OrganizationTransactorAddress.Street ?? string.Empty,
                street2: model.OrganizationTransactorAddress.Street2 ?? string.Empty,
                city: model.OrganizationTransactorAddress.City ?? string.Empty,
                state: model.OrganizationTransactorAddress.State ?? string.Empty,
                zip: model.OrganizationTransactorAddress.Zip ?? string.Empty,
                type: model.OrganizationTransactorAddress.Type ?? CommonConstants.Address.TypeBusiness,
                country: model.OrganizationTransactorAddress.Country ?? string.Empty,
                purpose: model.OrganizationTransactorAddress.Purpose ?? CommonConstants.Address.PurposeMailing
                );
        }

        return default!;
    }
}

using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
public partial class Form470SvcDependencies
{
    public IDecisionsSvc DecisionsSvc { get; }
    public IAuthorizationSvc AuthorizationSvc { get; }
    public IFilerSvc FilerSvc { get; }
    public INotificationSvc NotificationSvc { get; }
    public IUserMaintenanceSvc UserMaintenanceSvc { get; }
    public IRegistrationRepository RegistrationRepository { get; }
    public IFilingRepository FilingRepository { get; }
    public IDateTimeSvc DateTimeSvc { get; }

#pragma warning disable S107 // Methods should not have too many parameters
    public Form470SvcDependencies(
            IDecisionsSvc decisionsSvc,
            IAuthorizationSvc authorizationSvc,
            IFilerSvc filerSvc,
            INotificationSvc notificationSvc,
            IUserMaintenanceSvc userMaintenanceSvc,
            IDateTimeSvc dateTimeSvc,
            IRegistrationRepository registrationRepository,
            IFilingRepository filingRepository
        )
#pragma warning restore S107 // Methods should not have too many parameters
    {
        FilerSvc = filerSvc;
        DecisionsSvc = decisionsSvc;
        NotificationSvc = notificationSvc;
        AuthorizationSvc = authorizationSvc;
        UserMaintenanceSvc = userMaintenanceSvc;
        FilingRepository = filingRepository;
        RegistrationRepository = registrationRepository;
        DateTimeSvc = dateTimeSvc;
    }
}

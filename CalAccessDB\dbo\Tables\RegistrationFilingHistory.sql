﻿CREATE TABLE [dbo].[RegistrationFilingHistory] (
    [Id]                                     BIGINT          NOT NULL,
    [ApprovedAt]                             DATETIME2 (7)   NULL,
    [TerminatedAt]                           DATETIME2 (7)   NULL,
    [FilerId]                                BIGINT          NULL,
    [Name]                                   NVARCHAR (MAX)  NOT NULL,
    [StatusId]                               BIGINT          NOT NULL,
    [OriginalId]                             BIGINT          NULL,
    [ParentId]                               BIGINT          NULL,
    [Version]                                INT             NULL,
    [CreatedBy]                              BIGINT          NOT NULL,
    [ModifiedBy]                             BIGINT          NOT NULL,
    [AuditableResourceTag]                   NVARCHAR (450)  NULL,
    [Discriminator]                          NVARCHAR (100)  NOT NULL,
    [PeriodEnd]                              DATETIME2 (7)   NOT NULL,
    [PeriodStart]                            DATETIME2 (7)   NOT NULL,
    [Email]                                  NVARCHAR (MAX)  NULL,
    [County]                                 NVARCHAR (MAX)  NULL,
    [ActivityLevel]                          NVARCHAR (MAX)  NULL,
    [QualifiedCommittee]                     BIT             NULL,
    [DateQualified]                          DATETIME2 (7)   NULL,
    [VerificationExecutedAt]                 DATETIME2 (7)   NULL,
    [VerificationSignature]                  NVARCHAR (MAX)  NULL,
    [VerifiedById]                           BIGINT          NULL,
    [AddressListId]                          BIGINT          NULL,
    [PhoneNumberListId]                      BIGINT          NULL,
    [CommitteeSubType]                       NVARCHAR (MAX)  NULL,
    [JurisdictionCounty]                     NVARCHAR (MAX)  NULL,
    [JurisdictionActive]                     NVARCHAR (MAX)  NULL,
    [FinancialInstitutionName]               NVARCHAR (MAX)  NULL,
    [FinancialInstitutionPhone]              NVARCHAR (MAX)  NULL,
    [FinancialInstitutionAccountNumber]      NVARCHAR (MAX)  NULL,
    [PreviousCandidate]                      BIT             NULL,
    [CandidateId]                            BIGINT          NULL,
    [ElectionOfficeSought]                   NVARCHAR (MAX)  NULL,
    [ElectionDistrictNumber]                 NVARCHAR (MAX)  NULL,
    [ElectionId]                             BIGINT          NULL,
    [LegalDispute]                           NVARCHAR (MAX)  NULL,
    [ActivityDescription]                    NVARCHAR (MAX)  NULL,
    [IndustryOrGroupDescription]             NVARCHAR (MAX)  NULL,
    [SponsorId]                              BIGINT          NULL,
    [CampaignCommittee]                      BIT             NULL,
    [CommitteeId]                            BIGINT          NULL,
    [SelfRegister]                           BIT             NULL,
    [ElectionJurisdiction]                   NVARCHAR (MAX)  NULL,
    [ElectionCounty]                         NVARCHAR (MAX)  NULL,
    [ExpenditureLimitAccepted]               BIT             NULL,
    [ExpenditureCeilingAmount]               DECIMAL (18, 6) NULL,
    [ExpenditureExceeded]                    BIT             NULL,
    [ContributedPersonalExcessFundsOn]       DATETIME2 (7)   NULL,
    [FirstName]                              NVARCHAR (MAX)  NULL,
    [MiddleName]                             NVARCHAR (MAX)  NULL,
    [LastName]                               NVARCHAR (MAX)  NULL,
    [IsSameAsCandidateAddress]               BIT             NOT NULL,
    [ResponsibleOfficerTitle]                NVARCHAR (MAX)  NULL,
    [WithdrawnAt]                            DATETIME2 (7)   NULL,
    [PlacementAgent]                         BIT             NULL,
    [EmployerName]                           NVARCHAR (MAX)  NULL,
    [StateLegislatureLobbying]               BIT             NULL,
    [EthicsCourseCompletionDate]             DATETIME2 (7)   NULL,
    [EmployerType]                           NVARCHAR (MAX)  NULL,
    [BusinessActivity]                       NVARCHAR (MAX)  NULL,
    [BusinessDescription]                    NVARCHAR (MAX)  NULL,
    [NatureAndPurpose]                       NVARCHAR (MAX)  NULL,
    [InterestType]                           NVARCHAR (MAX)  NULL,
    [IndustryDescription]                    NVARCHAR (MAX)  NULL,
    [IndustryPortion]                        NVARCHAR (MAX)  NULL,
    [NumberOfMembers]                        INT             NULL,
    [PoliticalPartyId]                       BIGINT          NULL,
    [SubmittedAt]                            DATETIME2 (7)   NULL,
    [CandidateAgency]                        NVARCHAR (MAX)  NULL,
    [ElectionRaceId]                         BIGINT          NULL,
    [LegislativeSessionId]                   BIGINT          NULL,
    [LlcDetailsId]                           BIGINT          NULL,
    [IsLobbyingCoalition]                    BIT             NULL,
    [DescriptionOfPrincipallyDerivedSupport] NVARCHAR (MAX)  NULL,
    [IndustryGroupClassificationId]          BIGINT          NULL,
    [IndustryGroupOtherText]                 NVARCHAR (MAX)  NULL,
    [NatureAndInterestEmployerName]          NVARCHAR (MAX)  NULL,
    [NatureAndInterestTypeId]                BIGINT          NULL,
    [CommitteeTypeId]                        BIGINT          NULL,
    [EthicsCourseCompleted]                  BIT             NULL,
    [EthicsCourseCompletedWithinPastYear]    BIT             NULL,
    [IsNewCertification]                     BIT             NULL,
    [LobbyOnlySpecifiedAgencies]             BIT             NULL,
    [EffectiveDateOfChanges]                 DATETIME2 (7)   NULL,
    [BusinessSubcategoryId]                  BIGINT          NULL,
    [BusinessSubcategoryOtherText]           NVARCHAR (MAX)  NULL,
    [IsFiftyMembersOrLess]                   BIT             NULL,
    [LobbyingInterestId]                     BIGINT          NULL
);








GO
CREATE CLUSTERED INDEX [ix_RegistrationFilingHistory]
    ON [dbo].[RegistrationFilingHistory]([PeriodEnd] ASC, [PeriodStart] ASC) WITH (DATA_COMPRESSION = PAGE);


GO



GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Indicates the committee type for the registration.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFilingHistory', @level2type = N'COLUMN', @level2name = N'CommitteeTypeId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to a CommitteeType.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'RegistrationFilingHistory', @level2type = N'COLUMN', @level2name = N'CommitteeTypeId';


GO

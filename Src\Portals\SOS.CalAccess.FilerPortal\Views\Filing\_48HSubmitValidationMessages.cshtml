@using SOS.CalAccess.FilerPortal.Models.Filings
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.UI.Common.Extensions
@model IPortalAlertsContainer
@inject IHtmlLocalizer<SharedResources> Localizer

@HtmlHelpers.RenderValidationError(Model.Messages.Validations, "LobbyingFirmName", Localizer["FilerPortal.Contact.ContactForm.OrganizationName"].Value)
@HtmlHelpers.RenderValidationError(Model.Messages.Validations, "PhoneNumber", Localizer["Common.PhoneNumber"].Value)
@HtmlHelpers.RenderValidationError(Model.Messages.Validations, "Country", Localizer["Common.Country"].Value)
@HtmlHelpers.RenderValidationError(Model.Messages.Validations, "Street", Localizer["Common.Street"].Value)
@HtmlHelpers.RenderValidationError(Model.Messages.Validations, "City", Localizer["Common.City"].Value)
@HtmlHelpers.RenderValidationError(Model.Messages.Validations, "State", Localizer["Common.State"].Value)
@HtmlHelpers.RenderValidationError(Model.Messages.Validations, "Zip", Localizer["Common.ZipCode"].Value)
@HtmlHelpers.RenderValidationError(Model.Messages.Validations, "DateLobbyingFirmHired", "A valid date is rquired")
@HtmlHelpers.RenderValidationError(Model.Messages.Validations, "Amount", Localizer[ResourceConstants.CoalitionReceivedTransaction03AmountOfPayment].Value)
@HtmlHelpers.RenderValidationError(Model.Messages.Validations, "LegislativeNumbers", "Must select from a picklist one or more legislative numbers")
@HtmlHelpers.RenderValidationError(Model.Messages.Validations, "AmendmentExplanation", Localizer["FilerPortal.Disclosure.Dashboard.AmendmentExplanation"].Value)

using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Common.FileSystem;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
public partial class SmoCampaignStatementSvcDependencies
{
    public ISmoRegistrationSvc SmoRegistrationSvc { get; }
    public ITransactionSvc TransactionSvc { get; }
    public IUploadFileSvc UploadFileSvc { get; }
    public IDisclosureWithoutPaymentReceivedRepository DisclosureWithoutPaymentReceivedRepository { get; }

    public SmoCampaignStatementSvcDependencies(
        ISmoRegistrationSvc smoRegistrationSvc,
        ITransactionSvc transactionSvc,
        IUploadFileSvc uploadFileSvc,
        IDisclosureWithoutPaymentReceivedRepository disclosureWithoutPaymentReceivedRepository)
    {
        SmoRegistrationSvc = smoRegistrationSvc;
        TransactionSvc = transactionSvc;
        UploadFileSvc = uploadFileSvc;
        DisclosureWithoutPaymentReceivedRepository = disclosureWithoutPaymentReceivedRepository;
    }
}

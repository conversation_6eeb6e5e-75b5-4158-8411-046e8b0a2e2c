
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using SOS.CalAccess.Data.Contracts.Authorization;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.FilerRegistration.Filers;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Data.Notifications;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Common.BusinessRules.Models;
using SOS.CalAccess.Services.Common.Email;
using SOS.CalAccess.Services.Common.Email.Model;
using SOS.CalAccess.Services.Common.Utility;

namespace SOS.CalAccess.Services.Business.Tests.FilerRegistration.Filers;

[TestFixture]
public class LinkageSvcTests
{
    private ILinkageRequestRepository _linkageRequestRepository;
    private IUserMaintenanceSvc _userMaintenanceSvc;
    private INotificationSvc _notificationSvc;
    private IFilerRepository _filerRepository;
    private IFilerRoleRepository _filerRoleRepository;
    private IFilerUserRepository _filerUserRepository;
    private IDecisionsSvc _decisionsSvc;
    private IRegistrationContactRepository _registrationContactRepository;
    private IEmailSvc _emailSvc;
    private INotificationTemplateRepository _notificationTemplateRepository;
    private IDateTimeSvc _dateTimeSvcMock;
    private LinkageSvc _service;

    [SetUp]
    public void SetUp()
    {
        _notificationSvc = Substitute.For<INotificationSvc>();
        _filerRepository = Substitute.For<IFilerRepository>();
        _filerRoleRepository = Substitute.For<IFilerRoleRepository>();
        _linkageRequestRepository = Substitute.For<ILinkageRequestRepository>();
        _userMaintenanceSvc = Substitute.For<IUserMaintenanceSvc>();
        _filerUserRepository = Substitute.For<IFilerUserRepository>();
        _filerUserRepository = Substitute.For<IFilerUserRepository>();
        _registrationContactRepository = Substitute.For<IRegistrationContactRepository>();
        _emailSvc = Substitute.For<IEmailSvc>();
        _notificationTemplateRepository = Substitute.For<INotificationTemplateRepository>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();
        _decisionsSvc = Substitute.For<IDecisionsSvc>();
        _service = new LinkageSvc(_linkageRequestRepository, _filerRepository, _filerUserRepository,
            _filerRoleRepository, _notificationSvc, _userMaintenanceSvc, _decisionsSvc, _dateTimeSvcMock, _registrationContactRepository,
            _emailSvc, _notificationTemplateRepository);
    }

    #region GetLinkages
    [Test]
    public void GetLinkages_Throws_WhenUserIdIsNull()
    {
        // Arrange
        _userMaintenanceSvc.GetCurrentUser().Throws(new KeyNotFoundException("Unable to find user record."));

        // Act + Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(_service.GetLinkages);
        Assert.That(ex.Message, Is.EqualTo("Unable to find user record."));
    }

    [Test]
    public async Task GetLinkages_ReturnsExpectedLinkages_WhenUserIdIsValid()
    {
        // Arrange
        var userDto = new BasicUserDto(123, "<EMAIL>", "test", "test LastName");
        long userId = 123;
        var current = new List<LinkageDto> { new() { FilerUserId = 1, FilerName = "FilerName1", FilerRoleName = "FilerRoleName1" } };
        var pending = new List<LinkageRequestDto> { new() { LinkageRequestId = 1, FilerName = "FilerName2", FilerRoleName = "FilerRoleName2", IsInvitation = false } };
        var rejected = new List<LinkageRequestDto> { new() { LinkageRequestId = 2, FilerName = "FilerName3", FilerRoleName = "FilerRoleName3", IsInvitation = false } };

        _userMaintenanceSvc.GetCurrentUser().Returns(userDto);
        _linkageRequestRepository.GetLinkagesByUserId(userId).Returns(current);
        _linkageRequestRepository.GetPendingLinkageRequestsByUserId(userId).Returns(pending);
        _linkageRequestRepository.GetRejectedLinkageRequestsByUserId(userId).Returns(rejected);

        // Act
        var result = await _service.GetLinkages();

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.CurrentLinkages, Is.EqualTo(current));
            Assert.That(result.PendingLinkages, Is.EqualTo(pending));
            Assert.That(result.RejectedLinkages, Is.EqualTo(rejected));
        });
    }
    #endregion

    #region GetLinkageRequestForReview

    [Test]
    public async Task GetLinkageRequestForReview_WhenLinkageExists_ReturnsPopulatedResponse()
    {
        // Arrange
        var request = new LinkageReviewRequestDto { InvitationCode = "INV123" };
        var linkage = new LinkageRequest
        {
            Id = 1,
            RequestedBy = new User
            {
                EmailAddress = "testemail",
                FirstName = "firstname",
                LastName = "lastname",
                EntraOid = "TestOid"
            },
            FilerRole = new()
            {
                Id = 2,
                Name = "FilerRole",
                Description = "FilerRoleDesc",
            },
            SendDate = DateTime.Now,
            Filer = new()
            {
                CurrentRegistration = new SlateMailerOrganization
                {
                    StatusId = 1,
                    Name = "FilerName"
                }
            }
        };
        _linkageRequestRepository.GetLinkageByCode("INV123").Returns(linkage);

        // Act
        var result = await _service.GetLinkageRequestForReview(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.IsValid, Is.True);
            Assert.That(result.RequestedBy, Is.EqualTo("firstname lastname"));
            Assert.That(result.Email, Is.EqualTo("testemail"));
            Assert.That(result.AssignedRole, Is.EqualTo("FilerRole"));
            Assert.That(result.FilerName, Is.EqualTo("FilerName"));
        });
    }

    [Test]
    public async Task GetLinkageRequestForReview_WhenLinkageDoesNotExist_ReturnsEmptyResponse()
    {
        // Arrange
        var request = new LinkageReviewRequestDto { InvitationCode = "INVALID" };
        _linkageRequestRepository.GetLinkageByCode("INVALID").Returns((LinkageRequest?)null);

        // Act
        var result = await _service.GetLinkageRequestForReview(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Email, Is.Null);
            Assert.That(result.IsValid, Is.False);
        });
    }

    #endregion

    #region SendLinkageRequestToFiler

    [Test]
    public void SendLinkageRequestToFiler_WhenFilerNotFound_ThrowsException()
    {
        // Arrange
        var dto = new SendLinkageRequestToFilerDto { FilerId = 10 };

        _filerRepository.GetFilerById(dto.FilerId).Returns((Filer?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _service.SendLinkageRequestToFiler(dto));
        Assert.That(ex!.Message, Is.EqualTo($"Filer with ID {dto.FilerId} not found."));
    }

    [Test]
    public void SendLinkageRequestToFiler_WhenFilerRoleNotFound_ThrowsException()
    {
        // Arrange
        var filerId = 10L;
        var filerTypeId = 5;
        var filerRoleId = 7;
        var dto = new SendLinkageRequestToFilerDto { FilerId = 10, FilerRoleId = filerRoleId };
        var filer = new Filer
        {
            Id = filerId,
            FilerTypeId = filerTypeId,
            CurrentRegistration = new Lobbyist { Name = "Test Filer", StatusId = RegistrationStatus.Accepted.Id }
        };

        _filerRepository.GetFilerById(filerId).Returns(filer);
        _filerRoleRepository.FindById(filerRoleId).Returns((FilerRole?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _service.SendLinkageRequestToFiler(dto));
        Assert.That(ex!.Message, Is.EqualTo($"No filer role found for FilerRoleId {filerRoleId}."));
    }

    [Test]
    public void SendLinkageRequestToFiler_WhenFilerRoleNotMatchFilerType_ThrowsException()
    {
        // Arrange
        var filerId = 10L;
        var filerTypeId = 5;
        var filerRoleId = 7;
        var dto = new SendLinkageRequestToFilerDto { FilerId = 10, FilerRoleId = filerRoleId };
        var filer = new Filer
        {
            Id = filerId,
            FilerTypeId = filerTypeId,
            CurrentRegistration = new Lobbyist { Name = "Test Filer", StatusId = RegistrationStatus.Accepted.Id }
        };
        var filerRole = new FilerRole
        {
            Id = filerRoleId,
            Name = "Authorized",
            Description = "Authorized description",
            FilerTypeId = 6
        };

        _filerRepository.GetFilerById(filerId).Returns(filer);
        _filerRoleRepository.FindById(filerRoleId).Returns(filerRole);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _service.SendLinkageRequestToFiler(dto));
        Assert.That(ex!.Message, Is.EqualTo($"Filer role {filerRoleId} is not valid for filer type {filer.FilerTypeId}."));
    }

    [Test]
    public async Task SendLinkageRequestToFiler_ValidInput_SendsRequestAndReturnsEntity()
    {
        // Arrange
        var filerId = 10L;
        var filerTypeId = 5;
        var filerRoleId = 7;
        var userDto = new BasicUserDto(123, "<EMAIL>", "First", "LastName");
        var filerRole = new FilerRole { Id = filerRoleId, Name = "Authorized", Description = "Authorized description", FilerTypeId = filerTypeId };
        var expectedLinkageRequest = new LinkageRequest { Id = 1 };
        var currentDate = DateTime.UtcNow.ToDefaultTimeZone();

        var dto = new SendLinkageRequestToFilerDto { FilerId = filerId, FilerRoleId = filerRoleId };

        var filer = new Filer
        {
            Id = filerId,
            FilerTypeId = filerTypeId,
            CurrentRegistration = new Lobbyist { Name = "Test Filer", StatusId = RegistrationStatus.Accepted.Id }
        };

        _filerRepository.GetFilerById(filerId).Returns(filer);
        _filerRoleRepository.FindById(filerRoleId).Returns(filerRole);
        _userMaintenanceSvc.GetCurrentUser().Returns(userDto);
        _linkageRequestRepository.Create(Arg.Any<LinkageRequest>()).Returns(expectedLinkageRequest);

        // Act
        var result = await _service.SendLinkageRequestToFiler(dto);

        // Assert
        Assert.That(result, Is.EqualTo(1));

        await _filerRepository.Received(1).GetFilerById(filerId);
        await _filerRoleRepository.Received(1).FindById(filerRoleId);
        await _userMaintenanceSvc.Received(1).GetCurrentUser();

        await _linkageRequestRepository.Received(1).Create(Arg.Is<LinkageRequest>(lr =>
            lr.FilerId == filerId &&
            lr.FilerRoleId == filerRole.Id &&
            lr.IsInvitation == false &&
            lr.RequestedById == userDto.Id &&
            lr.LinkageStatusId == LinkageStatus.Sent.Id &&
            string.IsNullOrEmpty(lr.LinkageCode)
        ));

        //await _notificationSvc.Received(1).SendFilerNotification(Arg.Is<SendFilerNotificationRequest>(req =>
        //    req.NotificationTemplateId == NotificationTemplate.LinkageRequestForFiler.Id &&
        //    req.FilerId == filerId &&
        //    req.NotificationData!["RequesterName"] == "First LastName" &&
        //    req.NotificationData["AssignedRole"] == filerRole.Name &&
        //    req.NotificationData["FilerName"] == "Test Filer" &&
        //    req.NotificationData["OrganizationName"] == "SOS" &&
        //    req.NotificationData["CreateAnAccountUrl"] == "https://sos.com/create-account"
        //));
    }

    #endregion

    #region SendLinkageRequestToEmail

    [Test]
    public void SendLinkageRequestToEmail_WhenFilerNotFound_ThrowsException()
    {
        // Arrange
        var dto = new SendLinkageRequestToEmailDto { FilerId = 10, RecipientEmail = "<EMAIL>" };

        _filerRepository.GetFilerById(dto.FilerId).Returns((Filer?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _service.SendLinkageRequestToEmail(dto));
        Assert.That(ex!.Message, Is.EqualTo($"Filer with ID {dto.FilerId} not found."));
    }

    [Test]
    public async Task SendLinkageRequestToEmail_ValidInput_SendsRequestAndReturnsEntity()
    {
        // Arrange
        var filerId = 10L;
        var filerTypeId = 5;
        var filerRoleId = 7;
        var userDto = new BasicUserDto(123, "<EMAIL>", "test", "test LastName");
        var filerRole = new FilerRole { Id = filerRoleId, Name = "Authorized", Description = "Authorized description", FilerTypeId = filerTypeId };
        var expectedLinkageRequest = new LinkageRequest { Id = 1 };
        var currentDate = DateTime.UtcNow.ToDefaultTimeZone();

        var dto = new SendLinkageRequestToEmailDto { FilerId = filerId, FilerRoleId = filerRoleId, RecipientEmail = "<EMAIL>" };

        var filer = new Filer
        {
            Id = filerId,
            FilerTypeId = filerTypeId,
            CurrentRegistration = new Lobbyist { Name = "Test Filer", StatusId = RegistrationStatus.Accepted.Id }
        };
        var template = new NotificationTemplate
        {
            Translations = new()
            {
                new()
                {
                    EmailTemplateId = null,
                    Subject = "subject",
                    Message = "message",
                }
            }
        };

        _filerRepository.GetFilerById(filerId).Returns(filer);
        _filerRoleRepository.FindById(filerRoleId).Returns(filerRole);
        _userMaintenanceSvc.GetCurrentUser().Returns(userDto);
        _linkageRequestRepository.Create(Arg.Any<LinkageRequest>()).Returns(expectedLinkageRequest);
        _notificationTemplateRepository.FindById(Arg.Any<long>()).Returns(template);

        // Act
        var result = await _service.SendLinkageRequestToEmail(dto);

        // Assert
        Assert.That(result, Is.EqualTo(1));

        await _filerRepository.Received(1).GetFilerById(filerId);
        await _filerRoleRepository.Received(1).FindById(filerRoleId);
        await _userMaintenanceSvc.Received(1).GetCurrentUser();
        await _emailSvc.Received(1).SendPlainEmail(Arg.Any<EmailMessageRequest>());

        await _linkageRequestRepository.Received(1).Create(Arg.Is<LinkageRequest>(lr =>
            lr.FilerId == filerId &&
            lr.FilerRoleId == filerRole.Id &&
            lr.IsInvitation == true &&
            lr.Email == dto.RecipientEmail &&
            lr.RequestedById == userDto.Id &&
            lr.LinkageStatusId == LinkageStatus.Sent.Id &&
            !string.IsNullOrEmpty(lr.LinkageCode)
        ));

        //await _notificationSvc.Received(1).SendFilerNotification(Arg.Is<SendFilerNotificationRequest>(req =>
        //    req.TemplateId == 51 &&
        //    req.FilerId == filerId &&
        //    req.NotificationData!["RequesterName"] == username &&
        //    req.NotificationData["AssignedRole"] == filerRole.Name &&
        //    req.NotificationData["FilerName"] == "Test Filer" &&
        //    req.NotificationData["OrganizationName"] == "SOS" &&
        //    req.NotificationData["CreateAnAccountUrl"] == "https://sos.com/create-account"
        //));
    }

    #endregion

    #region SendLinkageRequestToPerson

    [Test]
    public void SendLinkageRequestToPerson_WhenFilerNotFound_ThrowsException()
    {
        // Arrange
        var dto = new SendLinkageRequestToPersonDto { FilerId = 10, RecipientEmail = "<EMAIL>", RecipientName = "Test RecipientName" };

        _filerRepository.GetFilerById(dto.FilerId).Returns((Filer?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _service.SendLinkageRequestToPerson(dto));
        Assert.That(ex!.Message, Is.EqualTo($"Filer with ID {dto.FilerId} not found."));
    }

    [Test]
    public async Task SendLinkageRequestToPerson_ValidInput_SendsRequestAndReturnsEntity()
    {
        // Arrange
        var filerId = 10L;
        var filerTypeId = 5;
        var filerRoleId = 7;
        var registrationContactId = 3;
        var userDto = new BasicUserDto(123, "<EMAIL>", "test", "test LastName");
        var filerRole = new FilerRole { Id = filerRoleId, Name = "Authorized", Description = "Authorized description", FilerTypeId = filerTypeId };
        var expectedLinkageRequest = new LinkageRequest { Id = 1 };
        var currentDate = DateTime.UtcNow.ToDefaultTimeZone();

        var dto = new SendLinkageRequestToPersonDto { FilerId = filerId, FilerRoleId = filerRoleId, RecipientEmail = "<EMAIL>", RecipientName = "Test RecipientName", RegistrationContactId = registrationContactId };

        var filer = new Filer
        {
            Id = filerId,
            FilerTypeId = filerTypeId,
            CurrentRegistration = new Lobbyist { Name = "Test Filer", StatusId = RegistrationStatus.Accepted.Id }
        };
        var template = new NotificationTemplate
        {
            Translations = new()
            {
                new()
                {
                    EmailTemplateId = "1",
                    Subject = "subject",
                    Message = "message",
                }
            }
        };

        _filerRepository.GetFilerById(filerId).Returns(filer);
        _filerRoleRepository.FindById(filerRoleId).Returns(filerRole);
        _userMaintenanceSvc.GetCurrentUser().Returns(userDto);
        _linkageRequestRepository.Create(Arg.Any<LinkageRequest>()).Returns(expectedLinkageRequest);
        _notificationTemplateRepository.FindById(Arg.Any<long>()).Returns(template);

        // Act
        var result = await _service.SendLinkageRequestToPerson(dto);

        // Assert
        Assert.That(result, Is.EqualTo(1));

        await _filerRepository.Received(1).GetFilerById(filerId);
        await _filerRoleRepository.Received(1).FindById(filerRoleId);
        await _userMaintenanceSvc.Received(1).GetCurrentUser();
        await _emailSvc.Received(1).SendTemplatedEmail(Arg.Any<EmailTemplateRequest>());

        await _linkageRequestRepository.Received(1).Create(Arg.Is<LinkageRequest>(lr =>
            lr.FilerId == filerId &&
            lr.FilerRoleId == filerRole.Id &&
            lr.RegistrationContactId == registrationContactId &&
            lr.IsInvitation == true &&
            lr.Email == dto.RecipientEmail &&
            lr.RequestedById == userDto.Id &&
            lr.LinkageStatusId == LinkageStatus.Sent.Id &&
            !string.IsNullOrEmpty(lr.LinkageCode)
        ));

        //await _notificationSvc.Received(1).SendFilerNotification(Arg.Is<SendFilerNotificationRequest>(req =>
        //    req.TemplateId == 51 &&
        //    req.FilerId == filerId &&
        //    req.NotificationData!["RequesterName"] == username &&
        //    req.NotificationData["AssignedRole"] == filerRole.Name &&
        //    req.NotificationData["FilerName"] == "Test Filer" &&
        //    req.NotificationData["OrganizationName"] == "SOS" &&
        //    req.NotificationData["CreateAnAccountUrl"] == "https://sos.com/create-account"
        //));
    }

    #endregion

    #region GetFilersBySearchCriteria

    [TestCase(true, false, false, false)]
    [TestCase(true, true, true, true)]
    [TestCase(false, false, false, false)]
    public async Task GetFilersBySearchCriteria_ReturnsExpectedResults(bool hasRegContact, bool hasAddress, bool hasPhone, bool hasEmail)
    {
        // Arrange
        var request = new FilerSearchRequestDto
        {
            Query = "Test",
            FilerTypeId = 1
        };

        var filer = new Filer
        {
            Id = 101,
            FilerTypeId = 1,
            FilerStatusId = FilerStatus.Active.Id,
            FilerType = new FilerType { Id = FilerType.Lobbyist.Id },
            FilerLinks = new List<FilerLink>
        {
            new()
            {
                FilerId = 101,CreatedBy = 1, EffectiveDate = DateTime.UtcNow,FilerLinkTypeId = 3,LinkedEntityId = 11,ModifiedBy = 1,
                FilerLinkType = new FilerLinkType { Id = FilerLinkType.LobbyistEmployer.Id, Name = "Description",Description ="TEST description", CreatedBy = 1, ModifiedBy = 1  },
                LinkedEntity = new Filer
                {
                    Id = 11,
                    CurrentRegistration = new Lobbyist { Name = "LinkedEntityName", StatusId = RegistrationStatus.Accepted.Id }
                }
            }
        },
            CurrentRegistration = new Lobbyist
            {
                StatusId = RegistrationStatus.Accepted.Id,
                Name = "Test Filer",
                RegistrationRegistrationContacts = new(),
                AddressList = new()
                {
                    Addresses = new()
                },
                PhoneNumberList = new()
                {
                    PhoneNumbers = new()
                }
            }
        };

        if (hasEmail)
        {
            filer.CurrentRegistration.Email = "<EMAIL>";
        }
        if (hasAddress)
        {
            filer.CurrentRegistration.AddressList.Addresses = new()
            {
                new()
                {
                    Country = "United States",
                    Street = "Street1",
                    Street2 = "Street2",
                    City = "Honolulu",
                    State = "HI",
                    Zip = "96812",
                    Type = "Residential",
                    Purpose = "PrincipalOfficer"
                }
            };
        }
        if (hasPhone)
        {
            filer.CurrentRegistration.PhoneNumberList.PhoneNumbers = new()
            {
                new()
                {
                    InternationalNumber = false,
                    CountryCode = "1",
                    Number = "1231234",
                    Type = "Business"
                }
            };
        }

        if (hasRegContact)
        {
            var newRrc = new RegistrationRegistrationContact
            {
                CapitalContributionOver10K = false,
                PercentOfOwnership = 100,
                CumulativeCapitalContributions = 0,
                TenPercentOrGreater = false,
                Active = true,
                Role = "test",
                Title = "TestTitle",
                CreatedBy = 1,
                ModifiedBy = 1,
                CanAuthorizeSlateMailerContents = true,
                RegistrationId = 1,
                RegistrationContact = new RegistrationContact
                {
                    Id = 1,
                    FirstName = "Sri",
                    MiddleName = string.Empty,
                    LastName = "S",
                }
            };

            filer.CurrentRegistration.RegistrationRegistrationContacts.Add(newRrc);
        }

        _filerRepository
            .SearchFilers(request.Query, request.FilerTypeId)
            .Returns(new List<Filer> { filer });

        // Act
        var result = (await _service.GetFilersBySearchCriteria(request)).ToList();

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Has.Count.EqualTo(1));

            var dto = result.First();
            Assert.That(dto.FilerId, Is.EqualTo(filer.Id));
            Assert.That(dto.FilerName, Is.EqualTo(filer.CurrentRegistration.Name));
            Assert.That(dto.LobbyistEmployerOrFirm, Is.EqualTo("LinkedEntityName"));

            if (hasRegContact)
            {
                Assert.That(dto.OfficerName, Is.EqualTo("Sri S"));
            }
            if (hasEmail)
            {
                Assert.That(dto.Email, Is.EqualTo("<EMAIL>"));
            }
            if (hasAddress)
            {
                Assert.That(dto.Addresses, Has.Count.EqualTo(1));
            }
            if (hasPhone)
            {
                Assert.That(dto.TelephoneNumber, Has.Length.GreaterThan(0));
            }
        });

        await _filerRepository.Received(1).SearchFilers(request.Query, request.FilerTypeId);
    }

    #endregion

    #region GetPendingUserLinkages

    [Test]
    public async Task GetPendingUserLinkages_ShouldMapToLinkageReviewResponseDto()
    {
        var filerId = 1L;

        var linkageRequestDto = new List<LinkageRequestDto>
        {
        new() { UserFullName = "Alice Smith", IsInvitation = true, FilerRoleName = "Treasurer", LinkageRequestId = 123, FilerName = "TEST Filer Name" }
        };

        _linkageRequestRepository.GetPendingUserLinkagesByFilerId(filerId).Returns(linkageRequestDto);

        // Act
        var result = (await _service.GetPendingUserLinkages(filerId)).ToList();

        // Assert
        Assert.That(result, Has.Count.EqualTo(1));
        Assert.Multiple(() =>
        {
            Assert.That(result[0].UserFullName, Is.EqualTo("Alice Smith"));
            Assert.That(result[0].IsInvitation, Is.True);
            Assert.That(result[0].FilerRoleName, Is.EqualTo("Treasurer"));
        });
    }

    [Test]
    public async Task GetPendingUserLinkages_ShouldReturnEmptyList_WhenRepositoryReturnsEmptyList()
    {
        var filerId = 1L;

        _linkageRequestRepository.GetPendingUserLinkagesByFilerId(filerId).Returns(Task.FromResult(new List<LinkageRequestDto>()));

        var result = await _service.GetPendingUserLinkages(filerId);

        Assert.That(result, Is.Empty);
    }

    #endregion

    #region GetCurrentAuthorizedUsers

    [Test]
    public async Task GetCurrentAuthorizedUsers_ShouldMapToLinkageReviewResponseDto()
    {
        var filerId = 1L;

        var linkageDtos = new List<LinkageDto>
        {
        new() { FilerUserId = 101, UserFullName = "Will Smith", FilerRoleName = "Assistant Treasurer", FilerName = "TEST Filer Name" }
        };

        _linkageRequestRepository.GetCurrentAuthorizedUsersByFilerId(filerId).Returns(linkageDtos);

        var result = (await _service.GetCurrentAuthorizedUsers(filerId)).ToList();

        Assert.That(result, Has.Count.EqualTo(1));
        Assert.Multiple(() =>
        {
            Assert.That(result[0].FilerUserId, Is.EqualTo(101));
            Assert.That(result[0].UserFullName, Is.EqualTo("Will Smith"));
            Assert.That(result[0].FilerRoleName, Is.EqualTo("Assistant Treasurer"));
        });
    }

    [Test]
    public async Task GetCurrentAuthorizedUsers_ShouldReturnEmptyList_WhenRepositoryReturnsEmptyList()
    {
        var filerId = 1L;

        _linkageRequestRepository.GetCurrentAuthorizedUsersByFilerId(filerId).Returns(Task.FromResult(new List<LinkageDto>()));

        var result = await _service.GetCurrentAuthorizedUsers(filerId);

        Assert.That(result, Is.Empty);
    }

    #endregion

    #region GetOfficers

    [Test]
    public async Task GetOfficers_ShouldReturnMappedOfficerResponseDto()
    {
        var filerId = 1L;
        var filer = new Filer
        {
            CurrentRegistration = new SlateMailerOrganization
            {
                StatusId = RegistrationStatus.Accepted.Id,
                Email = "<EMAIL>",
                Name = "test registration",
                RegistrationRegistrationContacts = new List<RegistrationRegistrationContact>
                {
                    new()
                    {
                        Role = "Treasurer", Title = "Officer", CreatedBy = 101, ModifiedBy = 102,
                        RegistrationContact = new RegistrationContact
                        {
                            FirstName = "Alice",
                            MiddleName = "B.",
                            LastName = "Smith", CreatedBy = 101, ModifiedBy = 102
                        }
                    }
                }
            }
        };

        _filerRepository.GetFilerById(filerId).Returns(filer);

        var result = (await _service.GetOfficers(filerId)).ToList();

        Assert.That(result, Has.Count.EqualTo(1));
        Assert.Multiple(() =>
        {
            Assert.That(result[0].FullName, Is.EqualTo("Alice B. Smith"));
            Assert.That(result[0].Role, Is.EqualTo("Treasurer"));
        });
    }

    [Test]
    public async Task GetOfficers_ShouldReturnEmptyList_WhenCurrentRegistrationIsNull()
    {
        var filerId = 1L;
        var filer = new Filer { CurrentRegistration = null };

        _filerRepository.GetFilerById(filerId).Returns(filer);

        var result = await _service.GetOfficers(filerId);

        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task GetOfficers_ShouldReturnEmptyList_WhenRegistrationRegistrationContactsIsEmpty()
    {
        var filerId = 2L;
        var filer = new Filer
        {
            CurrentRegistration = new SlateMailerOrganization
            {
                RegistrationRegistrationContacts = new List<RegistrationRegistrationContact>(),
                Email = "<EMAIL>",
                Name = "Officer Name",
                StatusId = RegistrationStatus.Accepted.Id
            }
        };

        _filerRepository.GetFilerById(filerId).Returns(filer);

        var result = await _service.GetOfficers(filerId);

        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task GetOfficers_ShouldIgnoreContactsWithNullRegistrationContact()
    {
        var filerId = 1L;
        var filer = new Filer
        {
            CurrentRegistration = new SlateMailerOrganization
            {
                Email = "<EMAIL>",
                Name = "test registration",
                StatusId = RegistrationStatus.Accepted.Id,
                RegistrationRegistrationContacts = new List<RegistrationRegistrationContact>
                {
                    new() { RegistrationContact = null, Role = "Account Administration", CreatedBy = 101, ModifiedBy = 102, Title = "Officer" }
                }
            }
        };

        _filerRepository.GetFilerById(filerId).Returns(filer);

        var result = await _service.GetOfficers(filerId);

        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task GetOfficers_ShouldHandleMissingNamePartsGracefully()
    {
        var filerId = 1L;
        var filer = new Filer
        {
            CurrentRegistration = new SlateMailerOrganization
            {
                Email = "<EMAIL>",
                Name = "test registration",
                StatusId = RegistrationStatus.Accepted.Id,
                RegistrationRegistrationContacts = new List<RegistrationRegistrationContact>
                {
                    new()
                    {
                        Role = "Account Administration", Title = "Officer", CreatedBy = 101, ModifiedBy = 102,
                        RegistrationContact = new RegistrationContact
                        {
                            FirstName = "John",
                            MiddleName = "",
                            LastName = "Smith", CreatedBy = 101, ModifiedBy = 102
                        }
                    }
                }
            }
        };

        _filerRepository.GetFilerById(filerId).Returns(filer);

        var result = (await _service.GetOfficers(filerId)).ToList();

        Assert.That(result, Has.Count.EqualTo(1));
        Assert.That(result[0].FullName, Is.EqualTo("John Smith"));
    }

    #endregion

    #region TerminateLinkageRequestForUser

    [Test]
    public void TerminateLinkageForUser_WhenFilerUserNotFound_ThrowsException()
    {
        // Arrange
        long id = 999;
        _filerUserRepository.FindById(id).Returns(Task.FromResult<FilerUser?>(null));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(
            () => _service.TerminateLinkageForUser(id));
        Assert.That(ex!.Message, Is.EqualTo($"No filer user found for FilerUserId {id}."));
    }

    [Test]
    public void TerminateLinkageForUser_WhenFilerNotFound_ThrowsException()
    {
        // Arrange
        long id = 123;
        long filerId = 42;
        var filerUser = GetSampleFileUser(id, filerId);
        filerUser.Filer = null;

        _filerUserRepository.GetFilerUserById(id).Returns(Task.FromResult<FilerUser?>(filerUser));
        _filerUserRepository.Delete(filerUser).Returns(Task.FromResult(true));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(
            () => _service.TerminateLinkageForUser(id));
        Assert.That(ex!.Message, Is.EqualTo($"Filer with ID {filerUser.FilerId} not found."));
    }

    [Test]
    public async Task TerminateLinkageForUser_DeleteReturnsFalse_DoesNotCallNotifications()
    {
        // Arrange
        long id = 55;
        long filerId = 42;
        var filerUser = GetSampleFileUser(id, filerId);
        _filerUserRepository.GetFilerUserById(id).Returns(Task.FromResult<FilerUser?>(filerUser));
        _filerUserRepository.Delete(filerUser).Returns(Task.FromResult(false));

        var filer = new Filer { Id = 7 };
        _filerRepository.GetFilerById(7).Returns(Task.FromResult<Filer?>(filer));

        var notifications = new List<NotificationTrigger>
        {
            new(true, 57, null, NotificationRecipient.InitiatingUser),
            new(true, 58, null, NotificationRecipient.FilerUsers)
        };
        _decisionsSvc
            .InitiateWorkflow<string, DecisionResponse>(DecisionsWorkflow.TerminateLinkageRuleset, string.Empty)
            .Returns(Task.FromResult(new DecisionResponse
            {
                Notifications = notifications
            }));

        // Act
        AsyncTestDelegate act = async () => await _service.TerminateLinkageForUser(id);

        // Assert
        var ex = Assert.ThrowsAsync<IOException>(act);

        // Assert
        await _notificationSvc.DidNotReceive()
            .SendUserNotification(Arg.Any<SendUserNotificationRequest>());
        await _notificationSvc.DidNotReceive()
            .SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());
    }

    [Test]
    public async Task TerminateLinkageForUser_DeleteReturnsTrue_CallsBothNotifications()
    {
        // Arrange
        long id = 77;
        long filerId = 42;
        var filerUser = GetSampleFileUser(id, filerId);
        _filerUserRepository.GetFilerUserById(id).Returns(Task.FromResult<FilerUser?>(filerUser));
        _filerUserRepository.Delete(filerUser).Returns(Task.FromResult(true));

        var currentUser = new BasicUserDto(3, "u@v", "First", "Last");
        _userMaintenanceSvc.GetCurrentUser().Returns(Task.FromResult(currentUser));

        var filer = new Filer
        {
            Id = 9,
        };
        _filerRepository.GetFilerById(9).Returns(Task.FromResult<Filer?>(filer));

        var notifications = new List<NotificationTrigger>
        {
            new(true, 57, null, NotificationRecipient.InitiatingUser),
            new(true, 58, null, NotificationRecipient.FilerUsers)
        };
        _decisionsSvc
            .InitiateWorkflow<string, DecisionResponse>(DecisionsWorkflow.TerminateLinkageRuleset, string.Empty)
            .Returns(Task.FromResult(new DecisionResponse
            {
                Notifications = notifications
            }));

        _notificationSvc
            .SendUserNotification(Arg.Any<SendUserNotificationRequest>())
            .Returns(Task.CompletedTask);
        _notificationSvc
            .SendFilerNotification(Arg.Any<SendFilerNotificationRequest>())
            .Returns(Task.CompletedTask);

        // Act
        await _service.TerminateLinkageForUser(id);

        // Assert
        await _notificationSvc.Received(1)
            .SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());
    }

    #endregion

    #region TerminateFilerUser

    [Test]
    public void TerminateFilerUser_InValidUser_Throws()
    {
        // Arrange
        var filerId = 100;
        var filerUserId = 200;

        // Act
        AsyncTestDelegate act = async () => await _service.TerminateFilerUser(filerId, filerUserId);

        // Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(act);
        Assert.That(ex.Message, Is.EqualTo($"FilerUser not found. Id={filerUserId}"));
    }
    [Test]
    public void TerminateFilerUser_InValidFiler_Throws()
    {
        // Arrange
        var filerId = 100;
        var filerUserId = 200;
        FilerUser user = new()
        {
            FilerId = 0,
        };
        _filerUserRepository.FindById(Arg.Any<long>()).Returns(user);

        // Act
        AsyncTestDelegate act = async () => await _service.TerminateFilerUser(filerId, filerUserId);

        // Assert
        var ex = Assert.ThrowsAsync<ArgumentException>(act);
        Assert.That(ex.Message, Is.EqualTo($"FilerId {user.FilerId} not assigned to this FilerUser"));
    }
    [Test]
    public void TerminateFilerUser_DeleteFailure_Throws()
    {
        // Arrange
        var id = 200;
        var filerId = 100;
        var filerUser = GetSampleFileUser(id, filerId);
        _filerUserRepository.FindById(Arg.Any<long>()).Returns(filerUser);
        _filerUserRepository.GetFilerUserById(id).Returns(Task.FromResult<FilerUser?>(filerUser));
        _filerUserRepository.Delete(Arg.Any<FilerUser>()).Returns(false);

        var notifications = new List<NotificationTrigger>
        {
            new(true, 57, null, NotificationRecipient.InitiatingUser),
            new(true, 58, null, NotificationRecipient.FilerUsers)
        };
        _decisionsSvc
            .InitiateWorkflow<string, DecisionResponse>(DecisionsWorkflow.TerminateLinkageRuleset, string.Empty)
            .Returns(Task.FromResult(new DecisionResponse
            {
                Notifications = notifications
            }));

        // Act
        AsyncTestDelegate act = async () => await _service.TerminateFilerUser(filerId, id);

        // Assert
        var ex = Assert.ThrowsAsync<IOException>(act);
        Assert.That(ex.Message, Is.EqualTo($"Unable to delete record.  Id={id}"));
    }
    [Test]
    public async Task TerminateFilerUser_ValidId_NoError()
    {
        // Arrange
        var id = 200;
        var filerId = 100;
        var filerUser = GetSampleFileUser(id, filerId);
        _filerUserRepository.FindById(Arg.Any<long>()).Returns(filerUser);
        _filerUserRepository.GetFilerUserById(id).Returns(Task.FromResult<FilerUser?>(filerUser));
        _filerUserRepository.Delete(Arg.Any<FilerUser>()).Returns(true);

        var notifications = new List<NotificationTrigger>
        {
            new(true, 57, null, NotificationRecipient.InitiatingUser),
            new(true, 58, null, NotificationRecipient.FilerUsers)
        };
        _decisionsSvc
            .InitiateWorkflow<string, DecisionResponse>(DecisionsWorkflow.TerminateLinkageRuleset, string.Empty)
            .Returns(Task.FromResult(new DecisionResponse
            {
                Notifications = notifications
            }));

        // Act
        bool valid = false;
        await _service.TerminateFilerUser(filerId, id);
        valid = true;

        // Assert
        Assert.That(valid, Is.EqualTo(true));
    }

    #endregion

    #region AcceptLinkageRequest

    [Test]
    [TestCase(true)]
    [TestCase(false)]
    public async Task AcceptLinkageRequest_ShouldUpdateAndSendNotifications(bool isInvitation)
    {
        // Arrange
        var userDto = new BasicUserDto(123, "<EMAIL>", "test", "test LastName");
        var linkage = new LinkageRequest
        {
            Id = 1,
            FilerId = 100,
            FilerRoleId = 10,
            LinkageStatusId = LinkageStatus.Sent.Id,
            RegistrationContactId = 200,
            RequestedById = 11,
            IsInvitation = isInvitation
        };
        var filer = new Filer
        {
            Id = 100,
            CurrentRegistration = new CandidateIntentionStatement { Name = "Test Filer", StatusId = 1 }
        };
        var notifications = new List<NotificationTrigger>
        {
            new(true, 53, null, NotificationRecipient.InitiatingUser ),
            new(true, 54, null, NotificationRecipient.FilerUsers)
        };
        var updatedLinkage = new LinkageRequest
        {
            Id = 1,
            FilerId = 100,
            FilerRoleId = 10,
            LinkageStatusId = LinkageStatus.Accepted.Id,
            RegistrationContactId = 500
        };
        var updatedRegistrationContact = new RegistrationContact
        {
            LastName = "S",
            Id = 500
        };


        _linkageRequestRepository.FindById(1).Returns(linkage);
        _filerRepository.GetFilerById(100).Returns(filer);
        _userMaintenanceSvc.GetCurrentUser().Returns(userDto);
        _linkageRequestRepository.Update(Arg.Any<LinkageRequest>()).Returns(updatedLinkage);
        _registrationContactRepository.Update(Arg.Any<RegistrationContact>()).Returns(updatedRegistrationContact);
        _registrationContactRepository.FindById(Arg.Any<long>()).Returns(updatedRegistrationContact);
        _decisionsSvc
            .InitiateWorkflow<string, DecisionResponse>(
                DecisionsWorkflow.AcceptLinkageRequestRuleset,
                string.Empty,
                false)
            .Returns(Task.FromResult(new DecisionResponse
            {
                Notifications = notifications
            }));
        _notificationSvc
           .SendFilerNotification(Arg.Any<SendFilerNotificationRequest>())
           .Returns(Task.CompletedTask);

        // Act
        await _service.AcceptLinkageRequest(1);

        // Assert
        await _notificationSvc.Received().SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());
        await _notificationSvc.Received().SendUserNotification(Arg.Any<SendUserNotificationRequest>());
        if (isInvitation)
        {
            await _filerUserRepository.Received().Update(Arg.Is<FilerUser>(
            x => x.UserId == 123));
        }
        else
        {
            await _filerUserRepository.Received().Update(Arg.Is<FilerUser>(
            x => x.UserId == 11));
        }
    }

    [Test]
    public void AcceptLinkageRequest_ShouldThrow_WhenLinkageNotFound()
    {
        _linkageRequestRepository.FindById(1).Returns((LinkageRequest?)null);

        Assert.ThrowsAsync<InvalidOperationException>(() => _service.AcceptLinkageRequest(1));
    }

    [Test]
    public void AcceptLinkageRequest_ShouldThrow_WhenLinkageNotSent()
    {
        var linkage = new LinkageRequest { Id = 3, LinkageStatusId = 999 };
        _linkageRequestRepository.FindById(3).Returns(linkage);

        Assert.ThrowsAsync<InvalidOperationException>(() => _service.AcceptLinkageRequest(3));
    }

    [Test]
    public void AcceptLinkageRequest_ShouldThrow_WhenFilerNotFound()
    {
        var linkage = new LinkageRequest { Id = 4, FilerId = 123, LinkageStatusId = LinkageStatus.Sent.Id };
        _linkageRequestRepository.FindById(4).Returns(linkage);
        _filerRepository.GetFilerById(123).Returns((Filer?)null);

        Assert.ThrowsAsync<InvalidOperationException>(() => _service.AcceptLinkageRequest(4));
    }

    [Test]
    public async Task AcceptLinkageRequest_ShouldHandleEmptyOrNullNotifications()
    {
        var userDto = new BasicUserDto(123, "<EMAIL>", "test", "test LastName");
        var linkage = new LinkageRequest
        {
            Id = 5,
            FilerId = 300,
            FilerRoleId = 30,
            LinkageStatusId = LinkageStatus.Sent.Id
        };
        var filer = new Filer { Id = 300 };

        var updatedLinkage = new LinkageRequest
        {
            Id = 5,
            FilerId = 300,
            FilerRoleId = 30,
            LinkageStatusId = LinkageStatus.Accepted.Id
        };

        var updatedRegistrationContact = new RegistrationContact
        {
            LastName = "S",
        };

        _linkageRequestRepository.FindById(5).Returns(linkage);
        _filerRepository.GetFilerById(300).Returns(filer);
        _userMaintenanceSvc.GetCurrentUser().Returns(userDto);
        _linkageRequestRepository.Update(Arg.Any<LinkageRequest>()).Returns(updatedLinkage);
        _registrationContactRepository.Update(Arg.Any<RegistrationContact>()).Returns(updatedRegistrationContact);
        _decisionsSvc
            .InitiateWorkflow<string, DecisionResponse>(
                DecisionsWorkflow.AcceptLinkageRequestRuleset,
                string.Empty,
                false)
            .Returns(Task.FromResult(new DecisionResponse { Notifications = [] }));

        // Act
        await _service.AcceptLinkageRequest(5);

        // Assert
        await _notificationSvc.DidNotReceive().SendUserNotification(Arg.Any<SendUserNotificationRequest>());
        await _notificationSvc.DidNotReceive().SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());
    }
    #endregion

    #region RejectLinkageRequest

    [Test]
    public async Task RejectLinkageRequest_ShouldNotCreateFilerUser()
    {
        // Arrange
        var userDto = new BasicUserDto(123, "<EMAIL>", "test", "test LastName");
        var linkage = new LinkageRequest
        {
            Id = 2,
            FilerId = 200,
            FilerRoleId = 20,
            LinkageStatusId = LinkageStatus.Sent.Id
        };
        var filer = new Filer
        {
            Id = 200,
            CurrentRegistration = null
        };
        var notifications = new List<NotificationTrigger>
    {
        new(false, 10, null),
        new(false, 11, null)
    };
        var updatedLinkage = new LinkageRequest
        {
            Id = 2,
            FilerId = 200,
            FilerRoleId = 20,
            LinkageStatusId = LinkageStatus.Rejected.Id
        };
        _linkageRequestRepository.FindById(2).Returns(linkage);
        _filerRepository.GetFilerById(200).Returns(filer);
        _userMaintenanceSvc.GetCurrentUser().Returns(userDto);
        _linkageRequestRepository.Update(Arg.Any<LinkageRequest>()).Returns(updatedLinkage);
        _decisionsSvc
            .InitiateWorkflow<string, DecisionResponse>(
                DecisionsWorkflow.RejectLinkageRequestRuleset,
                string.Empty,
                false)
            .Returns(Task.FromResult(new DecisionResponse
            {
                Notifications = notifications
            }));
        _notificationSvc
           .SendFilerNotification(Arg.Any<SendFilerNotificationRequest>())
           .Returns(Task.CompletedTask);
        _notificationSvc
           .SendUserNotification(Arg.Any<SendUserNotificationRequest>())
           .Returns(Task.CompletedTask);

        // Act
        await _service.RejectLinkageRequest(2);

        // Assert
        await _notificationSvc.DidNotReceive().SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());
    }

    [Test]
    public async Task RejectLinkageRequest_SendNotifications_ShouldSkipInvalidTriggers()
    {
        var userDto = new BasicUserDto(123, "<EMAIL>", "test", "test LastName");
        var linkage = new LinkageRequest
        {
            Id = 6,
            FilerId = 400,
            FilerRoleId = 40,
            LinkageStatusId = LinkageStatus.Sent.Id
        };
        var filer = new Filer { Id = 400 };
        var invalidNotifications = new List<NotificationTrigger>
        {
            new(false, 55, null, NotificationRecipient.InitiatingUser),
            new(true, 56, null, NotificationRecipient.FilerUsers),
        };

        var updatedLinkage = new LinkageRequest
        {
            Id = 6,
            FilerId = 400,
            FilerRoleId = 40,
            LinkageStatusId = LinkageStatus.Rejected.Id
        };

        _linkageRequestRepository.FindById(6).Returns(linkage);
        _filerRepository.GetFilerById(400).Returns(filer);
        _userMaintenanceSvc.GetCurrentUser().Returns(userDto);
        _linkageRequestRepository.Update(Arg.Any<LinkageRequest>()).Returns(updatedLinkage);
        _decisionsSvc
            .InitiateWorkflow<string, DecisionResponse>(
                DecisionsWorkflow.RejectLinkageRequestRuleset,
                string.Empty,
                false)
            .Returns(Task.FromResult(new DecisionResponse { Notifications = invalidNotifications }));

        // Act
        await _service.RejectLinkageRequest(6);

        // Assert
        await _notificationSvc.Received().SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());
        await _notificationSvc.DidNotReceive().SendUserNotification(Arg.Any<SendUserNotificationRequest>());
    }

    #endregion

    #region private

    private static FilerUser GetSampleFileUser(long filerUserId, long filerId)
    {
        var filer = new Filer
        {
            Id = filerId
        };
        var user = new User
        {
            Id = 1,
            FirstName = "First",
            LastName = "Last",
            EmailAddress = "<EMAIL>",
            EntraOid = "SOMEID",
        };
        var filerUser = new FilerUser
        {
            Id = filerUserId,
            FilerId = filerId,
            Filer = filer,
            User = user,
        };
        return filerUser;
    }

    #endregion
}


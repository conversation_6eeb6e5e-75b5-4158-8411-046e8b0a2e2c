using System.ComponentModel.DataAnnotations;
using System.Globalization;
using SOS.CalAccess.FilerPortal.Generated;

namespace SOS.CalAccess.FilerPortal.Models.Disclosure;

/// <summary>
/// ViewModel for general information about lobbying disclosure.
/// </summary>
public class DisclosureLobbyingGeneralInfoViewModel
{
    /// <summary>
    /// Initializes a new instance of the <see cref="DisclosureLobbyingGeneralInfoViewModel"/> class.
    /// </summary>
    public DisclosureLobbyingGeneralInfoViewModel()
    {
        Sessions = new LegislativeSessionResponseList(new List<LegislativeSessionResponse>());
    }

    public LegislativeSessionResponseList Sessions { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="DisclosureLobbyingGeneralInfoViewModel"/> class with specified registration and filing data.
    /// </summary>
    /// <param name="lobbyist">The lobbyist registration data.</param>
    /// <param name="filing">The lobbyist filing data.</param>
    public DisclosureLobbyingGeneralInfoViewModel(LobbyistResponseDto? lobbyist, LobbyistReportResponse? filing)
    {
        Id = filing?.Id ?? 0;
        FilerId = filing?.FilerId ?? 0;

        if (filing != null)
        {
            StartDate = filing.StartDate;
            EndDate = filing.EndDate;
            Name = lobbyist?.Name;
            EmployerName = lobbyist?.EmployerName;
            Email = lobbyist?.Email;
            FilerIdNumber = filing.FilerId.ToString(CultureInfo.InvariantCulture);
        }

        if (lobbyist?.Addresses != null && lobbyist.Addresses.Count != 0)
        {
            var addresses = lobbyist.Addresses.Select(a =>
                    new AddressDtoModel(a.City, a.Country, a.Purpose, a.State, a.Street, a.Street2, a.Type, a.Zip))
                .ToList();
            BusinessAddress = GetFormattedAddress(addresses.FirstOrDefault(a => a.Type == "Business"));
            MailingAddress = GetFormattedAddress(addresses.FirstOrDefault(a => a.Type == "Mailing"));
        }

        if (lobbyist?.PhoneNumbers != null && lobbyist.PhoneNumbers.Count != 0)
        {
            var primaryPhone = lobbyist.PhoneNumbers.FirstOrDefault(p => p.Type == "Primary");
            var faxPhone = lobbyist.PhoneNumbers.FirstOrDefault(p => p.Type == "Fax");

            PhoneNumber = GetFormattedPhoneNumber(primaryPhone);
            FaxNumber = GetFormattedPhoneNumber(faxPhone);
        }

        Sessions = new LegislativeSessionResponseList(new List<LegislativeSessionResponse>());
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="DisclosureLobbyingGeneralInfoViewModel"/> class with specified registration and filing data.
    /// </summary>
    /// <param name="lobbyist">The lobbyist registration data.</param>
    /// <param name="filing">The lobbyist filing data.</param>
    public DisclosureLobbyingGeneralInfoViewModel(LobbyistEmployerResponseDto? lobbyistEmployer, LobbyistEmployerReportResponse? filing)
    {
        Id = filing?.Id ?? 0;
        FilerId = filing?.FilerId ?? 0;

        if (filing != null)
        {
            StartDate = filing.StartDate;
            EndDate = filing.EndDate;
            Name = lobbyistEmployer?.Name;
            Email = lobbyistEmployer?.Email;
            FilerIdNumber = filing.FilerId.ToString(CultureInfo.InvariantCulture);
        }

        if (lobbyistEmployer?.Addresses != null && lobbyistEmployer.Addresses.Count != 0)
        {
            var addresses = lobbyistEmployer.Addresses.Select(a =>
                    new AddressDtoModel(a.City, a.Country, a.Purpose, a.State, a.Street, a.Street2, a.Type, a.Zip))
                .ToList();
            BusinessAddress = GetFormattedAddress(addresses.FirstOrDefault(a => a.Type == "Business"));
            MailingAddress = GetFormattedAddress(addresses.FirstOrDefault(a => a.Type == "Mailing"));
        }

        if (lobbyistEmployer?.PhoneNumbers != null && lobbyistEmployer.PhoneNumbers.Count != 0)
        {
            var phoneNumbers = lobbyistEmployer.PhoneNumbers
                .Select(x => new PhoneNumberDto(x.CountryCode, x.Extension, x.Id, x.InternationalNumber, x.Number, x.SelectedCountry, x.SetAsPrimaryPhoneNumber, x.Type))
                .ToList();
            var primaryPhone = phoneNumbers.FirstOrDefault(p => p.Type == "Primary");
            var faxPhone = phoneNumbers.FirstOrDefault(p => p.Type == "Fax");

            PhoneNumber = GetFormattedPhoneNumber(primaryPhone);
            FaxNumber = GetFormattedPhoneNumber(faxPhone);
        }

        Sessions = new LegislativeSessionResponseList(new List<LegislativeSessionResponse>());
    }

    /// <summary>
    /// Gets or sets the start date of the filing period.
    /// </summary>
    [DataType(DataType.Date)]
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Gets or sets the end date of the filing period.
    /// </summary>
    [DataType(DataType.Date)]
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Gets or sets the ID of the filing.
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// Gets or sets the filer ID.
    /// </summary>
    public long? FilerId { get; set; }

    /// <summary>
    /// Gets or sets the filing type ID.
    /// </summary>
    public long FilingTypeId { get; set; }

    /// <summary>
    /// Gets or sets the name of the filer.
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Gets or sets the filer ID number.
    /// </summary>
    public string? FilerIdNumber { get; set; }

    /// <summary>
    /// Gets or sets the business address.
    /// </summary>
    public string? BusinessAddress { get; set; }

    /// <summary>
    /// Gets or sets the mailing address.
    /// </summary>
    public string? MailingAddress { get; set; }

    /// <summary>
    /// Gets or sets the phone number.
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets the email address.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets the fax number.
    /// </summary>
    public string? FaxNumber { get; set; }

    /// <summary>
    /// Gets or sets the employer name.
    /// </summary>
    public string? EmployerName { get; set; }

    /// <summary>
    /// Gets or sets the firm name.
    /// </summary>
    public string? FirmName { get; set; }

    /// <summary>
    /// Gets or sets the report type.
    /// </summary>
    public string? ReportType { get; set; }

    /// <summary>
    /// Gets or sets the legislative session ID.
    /// </summary>
    public long? LegislativeSessionId { get; set; }

    /// <summary>
    /// Gets or sets the cumulative period start date of the filing period.
    /// </summary>
    [DataType(DataType.Date)]
    public DateTime? CumulativePeriodStartDate { get; set; }

    /// <summary>
    /// Converts the phone number to a formatted string.
    /// </summary>
    /// <param name="phoneNumber">The phone number to format.</param>
    /// <returns>The formatted phone number.</returns>
    private static string? GetFormattedPhoneNumber(PhoneNumberDto? phoneNumber)
    {
        //TODO: refactor to remove area code
        if (phoneNumber == null)
        {
            return null;
        }

        return $"{phoneNumber.Number[..3]}-{phoneNumber.Number[3..]}".Trim();
    }

    /// <summary>
    /// Gets the formatted address as a string.
    /// </summary>
    /// <param name="address">The address to format.</param>
    /// <returns>The formatted address.</returns>
    private static string? GetFormattedAddress(AddressDtoModel? address)
    {
        if (address == null)
        {
            return null;
        }

        return $"{address.Street} {address.Street2}".Trim() +
               $", {address.City}, {address.State} {address.Zip}";
    }
}

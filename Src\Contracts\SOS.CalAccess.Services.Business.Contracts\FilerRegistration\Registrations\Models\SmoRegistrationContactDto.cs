using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Mapping;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Slate Mailer Organization Officer DTO.
/// </summary>
public class SmoRegistrationContactDto
{
    /// <summary>
    /// Gets or Sets Officer Registration Contact Id
    /// </summary>
    public long? Id { get; set; }

    /// <summary>
    /// Gets or Sets the smo FilerRole
    /// </summary>
    public long RoleId { get; set; }

    /// <summary>
    /// Gets or Sets User Id if user states they are
    /// </summary>
    public long? UserId { get; set; }

    /// <summary>
    /// Gets or Sets the Start Date
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Gets or sets person's title
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// Gets or sets First Name
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// Gets or sets Middle Name
    /// </summary>
    public string? MiddleName { get; set; }

    /// <summary>
    /// Gets or sets Last Name
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// Gets or sets Email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// User can authorize content
    /// </summary>
    public bool? CanAuthorize { get; set; }

    /// <summary>
    /// Gets or sets the Address.
    /// </summary>
    public AddressDto? Address { get; set; }

    /// <summary>
    /// Gets or sets the Phone Number.
    /// </summary>
    public PhoneNumberDto? PhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets the CheckRequiredFieldsFlag
    /// </summary>
    public bool CheckRequiredFieldsFlag { get; set; }

    /// <summary>
    /// Gets or sets the status of acknowledgement completion
    /// </summary>
    public bool? HasAcknowledged { get; set; }

    /// <summary>
    /// Gets or sets the acknowledgement completion date
    /// </summary>
    public DateTime? AcknowledgedOn { get; set; }

    /// <summary>
    /// Gets or sets the role of contact
    /// </summary>
    public string? Role { get; set; }

    /// <summary>
    /// Gets or sets the indicator of if user is treasurer of contact or not
    /// </summary>
    public bool? IsUserTreasurer { get; set; }

    /// <summary>
    /// Gets or sets the indicator of if user is specifically that of the assistant treasurer or officer role.
    /// </summary>
    public bool? IsOfficer { get; set; }

    /// <summary>
    /// Gets or sets the ID of registration that contact belongs to.
    /// </summary>
    public long RegistrationId { get; set; }

    public static SmoRegistrationContactDto MapToDto(RegistrationRegistrationContact contact, IDateTimeSvc dateTimeSvc)
    {
        if (contact.RegistrationContact is not { } contactDetail)
        {
            return new SmoRegistrationContactDto();
        }

        var phoneNumber = contactDetail.PhoneNumberList?.PhoneNumbers.FirstOrDefault();
        var address = contactDetail.AddressList?.Addresses.FirstOrDefault();

        return new SmoRegistrationContactDto
        {
            Id = contact.Id,
            CanAuthorize = contact.CanAuthorizeSlateMailerContents,
            RoleId = GetRoleIdFromName(contact.Role),
            Role = contact.Role,
            UserId = (contact.CreatedBy == 0) ? null : contact.CreatedBy, // Fix by looking up Role in FilerUser -- issue with multiple officers
            StartDate = dateTimeSvc.GetCurrentDateTime(), //Fix when doing amendments
            Title = contact.Title,
            FirstName = contactDetail.FirstName,
            MiddleName = contactDetail.MiddleName,
            LastName = contactDetail.LastName,
            Email = contactDetail.Email,
            Address = (address != null) ? new()
            {
                Id = address.Id,
                Purpose = address.Purpose,
                Type = address.Type,
                Country = address.Country,
                Street = address.Street,
                Street2 = address.Street2,
                City = address.City,
                State = address.State,
                Zip = address.Zip,
            } : null,
            PhoneNumber = (phoneNumber != null) ? new(phoneNumber) : null,
            CheckRequiredFieldsFlag = false,
            RegistrationId = contact.RegistrationId,
        };
    }

    /// <summary>
    /// Gets the roleId from the name
    /// </summary>
    /// <param name="role">Role Name</param>
    /// <returns></returns>
    public static long GetRoleIdFromName(string role)
    {
        if (ContactTitleRoleMapping.TitleOrRoleNameToRoleId.TryGetValue(role, out var roleId))
        {
            return roleId;
        }

        return default;
    }
}


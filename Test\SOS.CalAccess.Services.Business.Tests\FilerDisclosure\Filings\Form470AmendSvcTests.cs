using NSubstitute;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Filings;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerDisclosure.Filings;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Services.Business.Tests.FilerDisclosure.Filings;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]

public class Form470AmendSvcTests
{
    private IDecisionsSvc _decisionsSvcMock;
    private IRegistrationRepository _registrationRepositoryMock;
    private IAttestationRepository _attestationRepositoryMock;
    private IFilingRelatedFilerRepository _filingRelatedFilerRepositoryMock;
    private IAuthorizationSvc _authorizationSvcMock;
    private IFilerSvc _filerSvcMock;
    private INotificationSvc _notificationSvcMock;
    private IUserMaintenanceSvc _userMaintenanceSvcMock;
    private IFilingRepository _filingRepository;
    private Form470SvcDependencies _dependenices;
    private IForm470Svc _form470SvcMock;
    private Form470AmendSvc _service;
    private IDateTimeSvc _dateTimeSvcMock;

    [SetUp]
    public void SetUp()
    {
        _decisionsSvcMock = Substitute.For<IDecisionsSvc>();
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();
        _filerSvcMock = Substitute.For<IFilerSvc>();
        _notificationSvcMock = Substitute.For<INotificationSvc>();
        _userMaintenanceSvcMock = Substitute.For<IUserMaintenanceSvc>();
        _registrationRepositoryMock = Substitute.For<IRegistrationRepository>();
        _attestationRepositoryMock = Substitute.For<IAttestationRepository>();
        _filingRelatedFilerRepositoryMock = Substitute.For<IFilingRelatedFilerRepository>();
        _filingRepository = Substitute.For<IFilingRepository>();
        _form470SvcMock = Substitute.For<IForm470Svc>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();

        _dependenices = new Form470SvcDependencies
        (
            _decisionsSvcMock,
            _authorizationSvcMock,
            _filerSvcMock,
            _notificationSvcMock,
            _userMaintenanceSvcMock,
            _dateTimeSvcMock,
            _registrationRepositoryMock,
            _filingRepository
        );
        _service = new Form470AmendSvc(_dependenices, _form470SvcMock);
    }


    [Test]
    public async Task Initialize470Amendment_ValidId_NoError()
    {
        {
            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            CandidateIntentionStatement statement = GenerateSampleCis(1, 1, "Test");
            RegistrationRepository registrationRepository = new(context);
            _ = await registrationRepository.Create(statement);
            Filing filing = GenerateSampleForm470(1, 1);
            FilingRepository filingRepository = new(context);
            _ = await filingRepository.Create(filing);

            Form470SvcDependencies dependenices = new
            (
                _decisionsSvcMock,
                _authorizationSvcMock,
                _filerSvcMock,
                _notificationSvcMock,
                _userMaintenanceSvcMock,
                _dateTimeSvcMock,
                registrationRepository,
                filingRepository
            );

            _service = new Form470AmendSvc(dependenices, _form470SvcMock);

            // Act
            var result = await _service.Initialize470Amendment(1);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.Multiple(() =>
            {
                Assert.That(result.Valid, Is.EqualTo(true));
                Assert.That(result.Id, Is.EqualTo(2));
                Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Draft.Id));
            });
        }
    }

    [Test]
    public async Task Initialize470Amendment_InValidStatus_Exception()
    {
        {
            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            CandidateIntentionStatement statement = GenerateSampleCis(1, 1, "Test");
            RegistrationRepository registrationRepository = new(context);
            _ = await registrationRepository.Create(statement);
            Filing filing = GenerateSampleForm470(1, 1);
            filing.StatusId = FilingStatus.Draft.Id;
            FilingRepository filingRepository = new(context);
            _ = await filingRepository.Create(filing);

            Form470OverviewResponse overview = GenerateSampleOverview(1, 1, 1);
            _form470SvcMock.GetForm470Overview(Arg.Any<long>()).Returns(overview);

            Form470SvcDependencies dependenices = new
            (
                _decisionsSvcMock,
                _authorizationSvcMock,
                _filerSvcMock,
                _notificationSvcMock,
                _userMaintenanceSvcMock,
                _dateTimeSvcMock,
                registrationRepository,
                filingRepository
            );

            _service = new Form470AmendSvc(dependenices, _form470SvcMock);

            // Act
            var result = await _service.Initialize470Amendment(1);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.Multiple(() =>
            {
                Assert.That(result.Valid, Is.EqualTo(false));
                Assert.That(result.ErrorMessage, Is.EqualTo($"Draft already exists for this filer."));
            });
        }
    }

    [Test]
    public async Task Initialize470Amendment_InValidId_KeyNotFound()
    {
        {
            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            CandidateIntentionStatement statement = GenerateSampleCis(1, 1, "Test");
            RegistrationRepository registrationRepository = new(context);
            _ = await registrationRepository.Create(statement);
            Filing filing = GenerateSampleForm470(1, 1);
            FilingRepository filingRepository = new(context);
            _ = await filingRepository.Create(filing);

            Form470OverviewResponse overview = GenerateSampleOverview(1, 1, 1);
            _form470SvcMock.GetForm470Overview(Arg.Any<long>()).Returns(overview);

            Form470SvcDependencies dependenices = new
            (
                _decisionsSvcMock,
                _authorizationSvcMock,
                _filerSvcMock,
                _notificationSvcMock,
                _userMaintenanceSvcMock,
                _dateTimeSvcMock,
                registrationRepository,
                filingRepository
            );

            _service = new Form470AmendSvc(dependenices, _form470SvcMock);

            // Act
            var result = await _service.Initialize470Amendment(999);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.Multiple(() =>
            {
                Assert.That(result.Valid, Is.EqualTo(false));
                Assert.That(result.ErrorMessage, Is.EqualTo($"Filing not Found for Filer Id 999"));
            });

        }
    }



    #region private
    private static CandidateIntentionStatement GenerateSampleCis(long filingId, long filerId, string name)
    {
        var cis = new CandidateIntentionStatement
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = RegistrationStatus.Accepted.Id,
            Name = name,
        };
        return cis;
    }
    private static Filing GenerateSampleForm470(long filingId, long filerId)
    {
        var filing = new Filing
        {
            Id = filingId,
            FilerId = filerId,
            FilingPeriodId = 1,
            StatusId = FilingStatus.Accepted.Id,
            FilingTypeId = FilingType.OfficeHolderCandidateShortForm.Id,
            FilingRelatedFilers = new()
            {
                new()
                {
                    Id = 1,
                    FilerId = filerId,
                    FilingId = 2
                }
            },
        };
        return filing;
    }
    private static Form470OverviewResponse GenerateSampleOverview(long registrationId, long filingId, long filerId)
    {
        Form470OverviewResponse overview = new()
        {
            CandidateIntentionStatement470 = new()
            {
                RegistrationId = registrationId,
                FilerId = filerId,
            },
            Form470Filing = new()
            {
                Id = filingId,
                FilerId = filerId,
            }
        };
        return overview;
    }
    #endregion

}

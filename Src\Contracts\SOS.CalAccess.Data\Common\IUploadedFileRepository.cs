using SOS.CalAccess.Models.Common;

namespace SOS.CalAccess.Data.Contracts.Common;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// Defines database interactions for the UploadedFile table.
/// </p>
/// <p>
/// Architectural Design: This repository represents a Data Service invoked by Business Services.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// Every entity has an Id field containing a unique identifier that can be used to retrieve a single record.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this repository is to handle data persistence functions acting as an intermediary between business logic and data storage.
/// </p>
/// <h4>Feature</h4>
/// <p>
/// N/A
/// </p>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this repository.
/// </p>
/// | Service                 | Operation                    | Description                         |
/// | ----------------------- | ---------------------------- | ----------------------------------- |
/// | N/A | N/A | N/A |
#endregion

public interface IUploadedFileRepository : IRepository<UploadedFile, long>
{
    /// <summary>
    /// Get the set of uploads by RelationshipId
    /// </summary>
    /// <param name="relationshipId"></param>
    /// <returns></returns>
    Task<IEnumerable<UploadedFile>> GetUploadsByRelationshipId(long relationshipId);

    /// <summary>
    /// Update an UploadedFile record
    /// </summary>
    /// <param name="uploadedFile"></param>
    /// <returns></returns>
    Task<UploadedFile> UpdateUploadedFile(UploadedFile uploadedFile);

    /// <summary>
    /// Delete an UploadedFile record
    /// </summary>
    /// <param name="uploadedFile"></param>
    /// <returns></returns>
    Task DeleteUploadedFile(UploadedFile uploadedFile);

    /// <summary>
    /// Find an UploadedFile record by file name Guid
    /// </summary>
    /// <param name="fileNameGuid"></param>
    /// <returns></returns>
    Task<UploadedFile?> FindUploadByFileNameGuid(string fileNameGuid);

    /// <summary>
    /// Retrieves uploaded files based on a list of file name GUIDs.
    /// </summary>
    /// <param name="fileNameGuids">The list of file name GUIDs to search for.</param>
    /// <returns>A list of matching <see cref="UploadedFile"/> records.</returns>
    Task<List<UploadedFile>> FindUploadFilesByFileNameGuids(List<Guid> fileNameGuids);

    /// <summary>
    /// Updates the relationships for a collection of uploaded files.
    /// </summary>
    /// <param name="uploadedFiles">The uploaded files with updated relationship values.</param>
    /// <returns></returns>
    Task UpdateRelationships(List<UploadedFile> uploadedFiles);

}

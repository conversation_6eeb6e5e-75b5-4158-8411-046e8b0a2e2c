using System.Globalization;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Constants;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Mapping;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Common.FileSystem.Models;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Filings;

/// <inheritdoc />
public class SmoCampaignStatementSvc(
    IDateTimeSvc dateTimeSvc,
    SmoCampaignStatementSvcDependencies dependencies,
    FilingSharedServicesDependencies servicesDependencies,
    FilingSharedRepositoriesDependencies repositoriesDependencies
    ) : ISmoCampaignStatementSvc
{
    /// <inheritdoc />
    public async Task<SmoGeneralInformationResponseDto> GetSmoGeneralInformationById(long id)
    {
        if (await repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(id) is not SmoCampaignStatement filing)
        {
            throw new KeyNotFoundException($"Filing not Found Id={id}");
        }

        // If there is no RegistrationId link to, get the information from the latest Accepted registration.
        // Otherwise, get it from the RegistrationId (capture when the statement is submitted)
        long registrationId;
        if (filing.RegistrationId is not null)
        {
            registrationId = filing.RegistrationId.GetValueOrDefault();
        }
        else
        {
            long filerId = filing.FilerId;
            if (await repositoriesDependencies.RegistrationRepository.FindSmoRegistrationLatestAcceptedByFilerId(filerId) is not SlateMailerOrganization registration)
            {
                throw new KeyNotFoundException($"Registration not found with Filer Id={filerId}");
            }

            registrationId = registration.Id;
        }

        return await dependencies.SmoRegistrationSvc.GetSmoGeneralInformationAsync(registrationId);
    }

    /// <inheritdoc /> 
    public async Task<long> CreateSmoCampaignStatementAsync(SmoCampaignStatementRequest request)
    {
        var registration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationBasicById(request.RegistrationId.GetValueOrDefault())
            ?? throw new KeyNotFoundException($"Registration not found. Id={request.RegistrationId}");

        // Check authorization, can only create Campaign Statement from the Registration linked to the user.
        var userId = await servicesDependencies.AuthorizationSvc.GetInitiatingUserId();
        var filerUsers = await servicesDependencies.FilerSvc.GetFilerUsersAsync(registration.FilerId.GetValueOrDefault());
        if (!filerUsers.Any(x => x.UserId == userId))
        {
            throw new UnauthorizedAccessException($"You do not have permission to create campaign statement for registration Id={request.RegistrationId}");
        }

        // Check if the registration is Accepted (approved)
        if (registration.StatusId != RegistrationStatus.Accepted.Id)
        {
            throw new InvalidOperationException($"Cannot create campaign statement from unapproved registration Id={request.RegistrationId}");
        }

        var newFiling = new SmoCampaignStatement
        {
            FilerId = registration.FilerId.GetValueOrDefault(),
            Version = 0,
            StatusId = FilingStatus.Draft.Id,
            FilingSummaries = CreateFilingSummaries(), // Create FilingSummary
            CreatedBy = userId.GetValueOrDefault(),
        };
        await repositoriesDependencies.FilingRepository.Create(newFiling);

        // Update the OriginalId
        newFiling.OriginalId = newFiling.Id;
        await repositoriesDependencies.FilingRepository.Update(newFiling);

        return newFiling.Id;
    }

    /// <inheritdoc />
    public async Task UpdateSmoCampaignStatementAsync(long id, SmoCampaignStatementRequest request)
    {
        var filing = await repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");

        if (filing.StatusId == FilingStatus.Accepted.Id)
        {
            throw new InvalidOperationException($"Cannot modify a Filing with {FilingStatus.Accepted.Name} status. Id={id}");
        }

        // Check if there is existing active SMO campaign statement of a Filer in a FilingPeriod
        var requestedFilingPeriodId = request.FilingPeriodId.GetValueOrDefault();
        var isExistingStatement = await repositoriesDependencies.FilingRepository.IsExistingActiveSmoCampaignStatement(filing.FilerId, requestedFilingPeriodId);
        if (filing.FilingPeriodId != requestedFilingPeriodId)
        {
            if (isExistingStatement)
            {
                throw new InvalidOperationException($"The statement in this Filing Period already existed. FilingPeriodId={request.FilingPeriodId}");
            }

            var filingPeriod = await repositoriesDependencies.FilingPeriodRepository.FindById(requestedFilingPeriodId)
                ?? throw new KeyNotFoundException($"Filing period not found. Id={requestedFilingPeriodId}");
            filing.FilingPeriodId = requestedFilingPeriodId;
            filing.StartDate = filingPeriod.StartDate;
            filing.EndDate = filingPeriod.EndDate;

            // If changing period, re-calculate YTD amount
            await UpdateYearToDateAmount(filing.FilingSummaries, filing.FilerId, filingPeriod.StartDate.Date, null);
        }

        await repositoriesDependencies.FilingRepository.Update(filing);
    }

    /// <inheritdoc /> 
    public async Task<List<FilingSummaryResponseDto>> GetSmoFilingSummaryByFilingId(long id)
    {
        var filingSummaries = await repositoriesDependencies.FilingSummaryRepository.FindFilingSummariesSmoCampaignStatementByFilingId(id);

        return [.. filingSummaries.Select(FilingSummaryResponseDto.MapToDto)];
    }

    /// <inheritdoc />
    public async Task<List<FilingPeriodResponseDto>> GetUnreportedFilingPeriodsByFilerAsync(long id)
    {
        var filing = await repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");

        var filingPeriods = await repositoriesDependencies.FilingPeriodRepository.FindAllUnreportedFilingPeriodsSmoCampaignStatementByFiler(filing.FilerId);

        return [.. filingPeriods.Select(FilingPeriodResponseDto.MapToDto)];
    }

    /// <inheritdoc />
    public async Task<FilingOverviewResponseDto> GetSmoCampaignStatementOverviewAsync(long id)
    {
        var filing = await repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");

        return FilingOverviewResponseDto.MapToDto(filing);
    }

    /// <inheritdoc />
    public async Task MarkFilingSummaryAsNothingToReportAsync(long id, long filingSummaryId)
    {
        var filing = await repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");
        var filingSummary = filing.FilingSummaries.FirstOrDefault(x => x.Id == filingSummaryId) ?? throw new KeyNotFoundException($"Filing summary not found. Id={filingSummaryId}");

        filingSummary.FilingSummaryStatusId = FilingSummaryStatus.NothingToReport.Id;
        await repositoriesDependencies.FilingSummaryRepository.Update(filingSummary);
    }

    /// <inheritdoc />
    public async Task<TransactionSummaryResponseDto> GetTransactionSummaryAsync(long id, long filingSummaryTypeId)
    {
        var filing = await repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");
        var filingSummary = filing.FilingSummaries.FirstOrDefault(x => x.FilingSummaryTypeId == filingSummaryTypeId) ?? throw new KeyNotFoundException($"Filing summary not found with requested type. Id={filingSummaryTypeId}");

        var transactionDtos = new List<TransactionDetailResponseDto>();
        switch (filingSummary.FilingSummaryTypeId)
        {
            case var typeId when typeId == FilingSummaryType.PaymentReceivedSummary.Id:
                var paymentReceivedResponses = await dependencies.TransactionSvc.GetAllTransactionsByFilingAsync<PaymentReceived, PaymentReceivedResponseDto>(
                    id,
                    transaction => new PaymentReceivedResponseDto(id, transaction));
                transactionDtos = [.. paymentReceivedResponses.Cast<TransactionDetailResponseDto>()];
                break;
            case var typeId when typeId == FilingSummaryType.PaymentMadeSummary.Id:
                var paymentMadeResponses = await dependencies.TransactionSvc.GetAllTransactionsByFilingAsync<PaymentMade, PaymentMadeResponseDto>(
                    id,
                    transaction => new PaymentMadeResponseDto(id, transaction));
                transactionDtos = [.. paymentMadeResponses.Cast<TransactionDetailResponseDto>()];
                break;
            case var typeId when typeId == FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Id:
                var paymentMadeByAgentResponses = await dependencies.TransactionSvc.GetAllTransactionsByFilingAsync<PaymentMade, PaymentMadeByAgentOrIndependentContractorResponseDto>(
                    id,
                    transaction => new PaymentMadeByAgentOrIndependentContractorResponseDto(id, transaction));
                transactionDtos = [.. paymentMadeByAgentResponses.Cast<TransactionDetailResponseDto>()];
                break;
            case var typeId when typeId == FilingSummaryType.PersonReceiving1000OrMoreSummary.Id:
                var personReceiving1000Reponses = await dependencies.TransactionSvc.GetAllTransactionsByFilingAsync<PersonReceiving1000OrMore, PersonReceiving1000OrMoreResponseDto>(
                    id,
                    transaction => new PersonReceiving1000OrMoreResponseDto(id, transaction));
                transactionDtos = [.. personReceiving1000Reponses.Cast<TransactionDetailResponseDto>()];
                break;
            default:
                break;
        }

        return new TransactionSummaryResponseDto
        {
            FilerId = filing.FilerId,
            TransactionResponseDtos = transactionDtos,
            SummaryResponseDto = FilingSummaryResponseDto.MapToDto(filingSummary),
        };
    }

    /// <inheritdoc/>
    public async Task<FilingSummaryResponseDto> UpdateFilingSummaryAsync(long id, long filingSummaryId, SmoCampaignStatementFilingSummaryRequest request)
    {
        bool isValid = false;
        var filing = await repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");

        var filingSummaryToUpdate = filing.FilingSummaries.FirstOrDefault(x => x.Id == filingSummaryId)
            ?? throw new KeyNotFoundException($"Filing summary not found. Id={filingSummaryId}");

        var varianceAmount = request.UnitemizedAmount - filingSummaryToUpdate.UnitemizedAmount.GetValueOrDefault();
        filingSummaryToUpdate.PeriodAmount += Math.Max(varianceAmount, -filingSummaryToUpdate.PeriodAmount);
        filingSummaryToUpdate.ToDateAmount += Math.Max(varianceAmount, -filingSummaryToUpdate.ToDateAmount.GetValueOrDefault());
        filingSummaryToUpdate.UnitemizedAmount = request.UnitemizedAmount;

        // Two different Decisions workflows for Payments received and Payments made transactions
        var validationErrors = new List<WorkFlowError>();

        if (filingSummaryToUpdate.FilingSummaryTypeId == FilingSummaryType.PaymentReceivedSummary.Id)
        {
            SmoCampaignStatementUnitemizedPaymentsReceivedDs decisionsInput = new()
            {
                UnitemizedPaymentsReceivedTotal = filingSummaryToUpdate.UnitemizedAmount
            };
            validationErrors = await servicesDependencies.DecisionsSvc.InitiateWorkflow<SmoCampaignStatementUnitemizedPaymentsReceivedDs, List<WorkFlowError>>(DecisionsWorkflow.SmoUnitemizedPaymentsReceivedRuleSet, decisionsInput, false) ?? new List<WorkFlowError>();
        }

        if (filingSummaryToUpdate.FilingSummaryTypeId == FilingSummaryType.PaymentMadeSummary.Id)
        {
            SmoCampaignStatementUnitemizedPaymentsMadeDs decisionsInput = new()
            {
                UnitemizedPaymentsMadeTotal = filingSummaryToUpdate.UnitemizedAmount
            };
            validationErrors = await servicesDependencies.DecisionsSvc.InitiateWorkflow<SmoCampaignStatementUnitemizedPaymentsMadeDs, List<WorkFlowError>>(DecisionsWorkflow.SmoUnitemizedPaymentsMadeRuleSet, decisionsInput, false) ?? new List<WorkFlowError>();
        }

        if (validationErrors.Count == 0)
        {
            isValid = true;
            // Prevent updating the status when user not input any value (first time)
            if (request.UnitemizedAmount > 0m)
            {
                filingSummaryToUpdate.FilingSummaryStatusId = StartFilingSummaryIfApplicable(filingSummaryToUpdate.FilingSummaryStatusId.GetValueOrDefault());
            }
            await repositoriesDependencies.FilingSummaryRepository.Update(filingSummaryToUpdate);
        }

        return new FilingSummaryResponseDto(filingSummaryId, id, isValid, validationErrors);
    }

    /// <inheritdoc />
    public async Task<ValidatedSmoCampaignStatementResponseDto> AttestStatementAsync(long id)
    {
        var validationErrors = new List<WorkFlowError> { };

        var (filing, filerUser, registration) = await GetFilingContextAsync(id);

        // The statement already completed, do nothing
        if (filing.StatusId == FilingStatus.Accepted.Id)
        {
            return new ValidatedSmoCampaignStatementResponseDto(id, true, validationErrors, filing.StatusId);
        }

        // If there is any summary has not been started, throw error
        if (filing.FilingSummaries.Any(x => x.FilingSummaryStatusId == FilingSummaryStatus.NotStarted.Id))
        {
            throw new InvalidOperationException($"Cannot submit this statement due to a Not Started transaction type. Filing Id={id}");
        }

        var registrationContacts = await repositoriesDependencies.RegistrationRepository.FindSmoRegistrationContactsById(registration.Id);

        var matchedContact = registrationContacts.FirstOrDefault(x =>
                x.Role == filerUser.FilerRole?.Name &&
                filerUser.User?.FirstName == x.RegistrationContact?.FirstName &&
                filerUser.User?.LastName == x.RegistrationContact?.LastName)
            ?? throw new InvalidOperationException($"No contact matched with current user. Id={filerUser.UserId}");

        var fullName = $"{matchedContact.RegistrationContact?.FirstName} {matchedContact.RegistrationContact?.LastName}";

        DecisionsSmoCampaignStatementSubmissionResponse response = await ValidateSmoCampaignStatementAttestation(filing, filerUser, true);
        validationErrors.AddRange(response.Errors ?? new List<WorkFlowError> { });

        if (validationErrors.Count > 0)
        {
            return new ValidatedSmoCampaignStatementResponseDto(id, false, validationErrors, filing.StatusId);
        }

        // Create Attestation
        var attestation = new Attestation
        {
            RegistrationId = registration.Id,
            FilingId = id,
            Name = fullName ?? string.Empty,
            Title = matchedContact.Title ?? string.Empty,
            ExecutedAt = dateTimeSvc.GetCurrentDateTime(),
            CreatedBy = 0,
            ModifiedBy = 0,
        };

        await repositoriesDependencies.AttestationRepository.Create(attestation);

        await CreateAttestationAsync(id, registration, filerUser);
        await UpdateSmoCampaignStatementSubmissionAsync(filing, response.Status);
        await SendFilerNotificationsAsync(filing.Id, filing.FilerId, response.Notifications);
        return new ValidatedSmoCampaignStatementResponseDto(filing.Id, true, validationErrors, filing.StatusId, filing.SubmittedDate);
    }

    /// <inheritdoc />
    public async Task<ValidatedSmoCampaignStatementResponseDto> SendForAttestationAsync(long id, SmoRegistrationSendForAttestationRequest request)
    {
        var validationErrors = new List<WorkFlowError> { };
        try
        {
            var (filing, filerUser, registration) = await GetFilingContextAsync(id);

            if (filing.StatusId == FilingStatus.Accepted.Id)
            {
                return new ValidatedSmoCampaignStatementResponseDto(filing.Id, true, validationErrors, filing.StatusId);
            }

            // If there is any summary has not been started, throw error
            if (filing.FilingSummaries.Any(x => x.FilingSummaryStatusId == FilingSummaryStatus.NotStarted.Id))
            {
                throw new InvalidOperationException($"Cannot submit this statement due to a {FilingSummaryStatus.NotStarted.Name} transaction type. Filing Id={id}");
            }

            DecisionsSmoCampaignStatementSubmissionResponse response = await ValidateSmoCampaignStatementAttestation(filing, filerUser, false);
            validationErrors.AddRange(response.Errors ?? new List<WorkFlowError> { });

            if (validationErrors.Count > 0)
            {
                return new ValidatedSmoCampaignStatementResponseDto(filing.Id, false, validationErrors, filing.StatusId);
            }

            await UpdateSmoCampaignStatementSubmissionAsync(filing, response.Status);

            // Send notification to selected user
            var officers = await GetSmoCampaignStatementResponsibleOfficerContactsAsync(id);
            var selectedOfficerUserIds = officers.Where(x => request.RegistrationRegistrationContactIds.Contains(x.Id.GetValueOrDefault())).Select(s => s.UserId).ToList();
            await SendUserNotificationsAsync(id, filing.FilerId, response.Notifications, selectedOfficerUserIds);

            return new ValidatedSmoCampaignStatementResponseDto(filing.Id, true, validationErrors, filing.StatusId);
        }
        catch (Exception)
        {
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<SmoRegistrationContactDto>> GetSmoCampaignStatementResponsibleOfficerContactsAsync(long id)
    {
        var (_, _, registration) = await GetFilingContextAsync(id);

        return await dependencies.SmoRegistrationSvc.GetResponsibleOfficerContactsAsync(registration.Id);
    }

    /// <inheritdoc />
    public async Task<List<PendingItemDto>> GetSmoCampaignStatementPendingItemsAsync(long id)
    {
        var pendingItems = new List<PendingItemDto> { };

        var attestation = await repositoriesDependencies.AttestationRepository.FindByFilingId(id);
        if (attestation is null)
        {
            pendingItems.Add(new PendingItemDto
            {
                Item = PendingItemAttestation,
                Status = PendingItemStatusInProgress
            });
        }

        return pendingItems;
    }

    /// <inheritdoc />
    public async Task<SmoCampaignStatementAttestationResponseDto> GetSmoCampaignStatementAttestationAsync(long id)
    {
        var (_, _, registration) = await GetFilingContextAsync(id);
        var attestation = await repositoriesDependencies.AttestationRepository.FindByFilingId(registration.Id);

        if (attestation == null)
        {
            return new SmoCampaignStatementAttestationResponseDto();
        }

        var (firstName, lastName) = SplitName(attestation.Name);
        return new SmoCampaignStatementAttestationResponseDto
        {
            FirstName = firstName,
            LastName = lastName,
            Title = attestation.Title,
            ExecutedAt = attestation.ExecutedAt,
        };
    }

    /// <inheritdoc />
    public async Task<List<CandidateOrMeasureNotListedPaymentReceivedResponseDto>> GetCandidatesOrMeasuresNotListedOnPaymentReceivedAsync(long id)
    {
        _ = await repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");

        var result = await dependencies.DisclosureWithoutPaymentReceivedRepository.FindAllDisclosureWithoutPaymentReceiveds(id);

        return [.. result.Select(CandidateOrMeasureNotListedPaymentReceivedResponseDto.MapToDto)];
    }

    /// <inheritdoc/>
    public async Task<TransactionResponseDto> CreateSmoCampaignStatementTransactionAsync(long id, TransactionDetailRequest request)
    {
        var filing = await repositoriesDependencies.FilingRepository.FindById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");
        var filerId = filing.FilerId;

        return await (request switch
        {
            PaymentReceivedRequest paymentReceivedRequest => CreatePaymentReceivedTransactionAsync(filing, paymentReceivedRequest, null),
            PaymentMadeByAgentOrIndependentContractorRequest paymentMadeByAgentRequest => CreatePaymentMadeByAgentTransactionAsync(id, filerId, paymentMadeByAgentRequest),
            PaymentMadeRequest paymentMadeRequest => CreatePaymentMadeTransactionAsync(id, filerId, paymentMadeRequest),
            PersonReceiving1000OrMoreRequest personRequest => CreatePersonReceiving1000OrMoreTransactionAsync(id, filerId, personRequest),
            _ => throw new NotSupportedException($"Unsupported request type: {request.GetType().Name}")
        });
    }

    /// <inheritdoc/>
    public async Task<TransactionResponseDto> UpdateSmoCampaignStatementTransactionAsync(long id, long transactionId, TransactionDetailRequest request)
    {
        var transaction = await GetTransactionByRequestTypeAsync(transactionId, id, request)
            ?? throw new KeyNotFoundException($"Transaction not found. Id={transactionId}");
        var varianceAmount = Math.Max(request.Amount - transaction.Amount, -transaction.Amount);

        return await (request switch
        {
            PaymentReceivedRequest paymentReceivedRequest when transaction is PaymentReceived paymentReceived =>
                UpdatePaymentReceivedTransactionAsync(id, paymentReceived, paymentReceivedRequest, varianceAmount),
            PaymentMadeByAgentOrIndependentContractorRequest paymentMadeByAgentRequest when transaction is PaymentMade paymentMade =>
                UpdatePaymentMadeByAgentTransactionAsync(id, paymentMade, paymentMadeByAgentRequest, varianceAmount),
            PaymentMadeRequest paymentMadeRequest when transaction is PaymentMade paymentMade =>
                UpdatePaymentMadeTransactionAsync(id, paymentMade, paymentMadeRequest, varianceAmount),
            PersonReceiving1000OrMoreRequest personRequest when transaction is PersonReceiving1000OrMore personReceiving =>
                UpdatePersonReceiving1000OrMoreTransactionAsync(id, personReceiving, personRequest, varianceAmount),
            _ => throw new NotSupportedException($"Unsupported request type: {request.GetType().Name}")
        });
    }

    /// <inheritdoc/>
    public async Task<TransactionDetailResponseDto> GetSmoCampaignStatementTransactionAsync(long id, long transactionId, string transactionType)
    {
        if (!TransactionType.TryParse(transactionType, null, out var parsedType))
        {
            throw new ArgumentException($"Invalid transaction type: {transactionType}.");
        }

        var transaction = await GetTransactionByTypeAsync(transactionId, id, parsedType)
            ?? throw new KeyNotFoundException($"Transaction not found. Id={transactionId}");

        return transaction switch
        {
            PaymentReceived paymentReceived => new PaymentReceivedResponseDto(id, paymentReceived),
            PaymentMade paymentMade when !string.IsNullOrWhiteSpace(paymentMade.AgentOrIndependentContractorName)
                => new PaymentMadeByAgentOrIndependentContractorResponseDto(id, paymentMade),
            PaymentMade paymentMade => new PaymentMadeResponseDto(id, paymentMade),
            PersonReceiving1000OrMore personReceiving => new PersonReceiving1000OrMoreResponseDto(id, personReceiving),
            _ => new TransactionDetailResponseDto(),
        };
    }

    /// <inheritdoc/>
    public async Task<DisclosureWithoutPaymentReceivedDto> GetDisclosureWithoutPaymentReceivedByIdAsync(long id, long disclosureWithoutPaymentId)
    {
        _ = await repositoriesDependencies.FilingRepository.FindById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");
        var record = await dependencies.DisclosureWithoutPaymentReceivedRepository.GetDisclosureWithoutPaymentReceivedWithStanceById(disclosureWithoutPaymentId)
            ?? throw new KeyNotFoundException($"Record not found. Id={disclosureWithoutPaymentId}");

        return DisclosureWithoutPaymentReceivedDto.MapToDto(record);
    }

    /// <inheritdoc> />
    public async Task<TransactionResponseDto> CreateDisclosureWithoutPaymentReceivedAsync(long id, DisclosureWithoutPaymentReceivedDto request)
    {
        var filing = await repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(id)
            ?? throw new KeyNotFoundException($"Filing not found. Id={id}");

        var validationErrors = await ValidateDisclosureWithoutPaymentReceivedDecisions(request);
        if (validationErrors.Count == 0)
        {
            DisclosureWithoutPaymentReceived disclosure = request.MapToEntity(id);
            var newRecorded = await dependencies.DisclosureWithoutPaymentReceivedRepository.Create(disclosure);

            await StartCandidateOrMeasuresNotListedPaymentReceivedSummary(filing.FilingSummaries);

            return new TransactionResponseDto(newRecorded.Id, true, validationErrors);
        }
        return new TransactionResponseDto(null, false, validationErrors);
    }

    /// <inheritdoc/>
    public async Task<TransactionResponseDto> UpdateDisclosureWithoutPaymentReceivedAsync(long id, long disclosureWithoutPaymentId, DisclosureWithoutPaymentReceivedDto request)
    {
        _ = await repositoriesDependencies.FilingRepository.FindById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");
        var record = await dependencies.DisclosureWithoutPaymentReceivedRepository.GetDisclosureWithoutPaymentReceivedWithStanceById(disclosureWithoutPaymentId)
                    ?? throw new KeyNotFoundException($"Record not found. Id={disclosureWithoutPaymentId}");

        var validationErrors = await ValidateDisclosureWithoutPaymentReceivedDecisions(request);
        if (validationErrors.Count == 0)
        {
            DisclosureWithoutPaymentReceived disclosure = request.MapToExistingEntity(record);

            await dependencies.DisclosureWithoutPaymentReceivedRepository.Update(disclosure);

            return new TransactionResponseDto(record.Id, true, validationErrors);
        }
        return new TransactionResponseDto(null, false, validationErrors);
    }
    /// <inheritdoc/>
    public async Task DeleteDisclosureWithoutPaymentReceivedAsync(long id, long disclosureWithoutPaymentId)
    {
        _ = await repositoriesDependencies.FilingRepository.FindById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");
        var record = await dependencies.DisclosureWithoutPaymentReceivedRepository.GetDisclosureWithoutPaymentReceivedWithStanceById(disclosureWithoutPaymentId)
                    ?? throw new KeyNotFoundException($"Record not found. Id={disclosureWithoutPaymentId}");

        // If the record is already soft-deleted, do nothing.
        if (!record.Active)
        {
            return;
        }

        await dependencies.DisclosureWithoutPaymentReceivedRepository.UpdateProperty(record, x => x.Active, false);
    }

    /// <inheritdoc />
    public async Task<FilingSummaryResponseDto> UpdateAmendmentExplanationAsync(long id, FilingSummaryAmendmentExplanationRequest request)
    {
        var filing = await repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");
        var filingSummary = filing.FilingSummaries.FirstOrDefault(x => x.FilingSummaryTypeId == FilingSummaryType.AmendmentExplanation.Id)
            ?? throw new KeyNotFoundException($"Filing summary not found. Type={FilingSummaryType.AmendmentExplanation.Name}");

        // Decisions
        var decisionsData = new DecisionsAmendmentExplanation(request.AmendmentExplanation);

        List<WorkFlowError> validationErrors = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsAmendmentExplanation, List<WorkFlowError>>(DecisionsWorkflow.SmoCampaignStatementAmendmentExplanation, decisionsData, true);
        if (validationErrors is null || validationErrors.Count == 0)
        {
            // Update Amendment Explanation
            filing.AmendmentExplanation = request.AmendmentExplanation;

            // Update Filing summary status
            filingSummary.FilingSummaryStatusId =
                filingSummary.FilingSummaryStatusId == FilingSummaryStatus.NotStarted.Id ||
                filingSummary.FilingSummaryStatusId == FilingSummaryStatus.NothingToReport.Id
                    ? FilingSummaryStatus.InProgress.Id
                    : filingSummary.FilingSummaryStatusId;
            await repositoriesDependencies.FilingRepository.Update(filing);

            return new FilingSummaryResponseDto(filingSummary.Id, id, true, validationErrors);
        }
        return new FilingSummaryResponseDto(filingSummary.Id, id, false, validationErrors);

    }

    /// <inheritdoc />
    public async Task<long> InitializeSmoCampaignStatementAmendmentAsync(long id)
    {
        var filing = await repositoriesDependencies.FilingRepository.FindSmoCampaignStatementByIdAsNoTracking(id)
            ?? throw new KeyNotFoundException($"Filing not found. Id={id}");

        ValidateFilingStatusForAmendment(filing.StatusId);

        // Check if there is any active amendment (Draft and Pending status)
        await ValidateActiveAmendmentAsync(id);

        // Reset information for amendment
        await ResetFilingInformationForNewAmendmentAsync(filing);

        // Clone other information - not related to transaction
        CloneInformationForNewAmendment(filing);

        // Add Amendment Explanation
        AddFilingSummaryAmendmentExplanation(filing.FilingSummaries);

        // Recalculate YTD amount
        await UpdateYearToDateAmount(filing.FilingSummaries, filing.FilerId, filing.StartDate, id);

        await repositoriesDependencies.FilingRepository.Create(filing);

        // Continue clone transaction information
        var filingTransactions = await repositoriesDependencies.TransactionRepository.FindAllFilingTransactionsByFilingId(id);
        var contactSummaries = await repositoriesDependencies.FilingContactSummaryRepository.FindAllByFilingId(id);

        var filingContactSummaryMap = new Dictionary<long, FilingContactSummary>();
        CloneFilingContactSummaries(filing.Id, contactSummaries, filingContactSummaryMap);
        CloneTransactions(filing, filingTransactions, filingContactSummaryMap);

        await repositoriesDependencies.FilingContactSummaryRepository.AddFilingContactSummaries(contactSummaries);
        await repositoriesDependencies.FilingRepository.Update(filing);

        return filing.Id;
    }

    /// <inheritdoc/>
    public async Task<TransactionResponseDto> ValidatePaymentReceivedAsync(long id, PaymentReceivedValidationRequestDto request)
    {
        var validationErrors = await ValidatePaymentReceivedInformationDecisions(request);

        if (validationErrors.Count == 0)
        {
            return new TransactionResponseDto(id, true, validationErrors);
        }

        return new TransactionResponseDto(null, false, validationErrors);
    }

    /// <inheritdoc />
    public async Task<bool> IsSmoCampaignStatementAmendmentAsync(long id)
    {
        var filing = await repositoriesDependencies.FilingRepository.FindById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");

        return filing.Id != filing.OriginalId;
    }

    /// <inheritdoc />
    public async Task<TransactionResponseDto> ValidatePersonReceivingOfficerAsync(long id, PersonReceiving1000ValidationRequest request)
    {
        var decisionsInput = new SmoCampaignStatementOfficerDs
        {
            OfficerId = request.OfficerId,
        };

        var validationErrors = await servicesDependencies.DecisionsSvc
            .InitiateWorkflow<SmoCampaignStatementOfficerDs, List<WorkFlowError>>(DecisionsWorkflow.PersonsReceivingOfficerRuleSet, decisionsInput, true) ?? new List<WorkFlowError>();

        var isValid = validationErrors.Count == 0;
        return new TransactionResponseDto(id, isValid, validationErrors);
    }

    /// <inheritdoc />
    public async Task<TransactionResponseDto> ValidateCandidateOrMeasureNotListedP1Async(long id, CandidateOrMeasureNotListedValidationRequest request)
    {
        var decisionsInput = new SmoCampaignStatementCandidateAndMeasureNotListedEntryDs
        {
            Jurisdiction = request.Jurisdiction,
            PayorType = request.CandidateOrMeasure,
            Position = request.Position,
        };

        var validationErrors = await servicesDependencies.DecisionsSvc
            .InitiateWorkflow<SmoCampaignStatementCandidateAndMeasureNotListedEntryDs, List<WorkFlowError>>(DecisionsWorkflow.SmoCandidateOrMeasureNotListedRuleP1Set, decisionsInput, true) ?? new List<WorkFlowError>();

        var isValid = validationErrors.Count == 0;
        return new TransactionResponseDto(id, isValid, validationErrors);
    }

    /// <inheritdoc />
    public async Task<PaymentsReceivedCumulativeAmountDto> GetPaymentReceivedCumulativeAmountAsync(long id, PaymentReceivedRequest request)
    {
        // Set the incoming Transaction to null to just pull the existing information
        request.Amount = 0;
        request.UnitemizedAmount = null;
        long? transactionId = null;

        var filing = await repositoriesDependencies.FilingRepository.FindById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");
        var (_, _, newContactSummary) = await dependencies.TransactionSvc.GetSmoCampaignStatementFilingContactSummaryChanges(filing, request, transactionId);

        return new PaymentsReceivedCumulativeAmountDto(newContactSummary.FilerContactId, newContactSummary.PreviouslyUnitemizedAmount, newContactSummary.Amount);
    }

    #region Private

    private const string PendingItemAttestation = "Attestation";
    private const string PendingItemStatusInProgress = "In Progress";

    private static List<FilingSummary> CreateFilingSummaries()
    {
        var newFilingSummaries = new List<FilingSummary>();
        var filingSummaryTypeIds = new List<long>
        {
            FilingSummaryType.PaymentReceivedSummary.Id,
            FilingSummaryType.PaymentMadeSummary.Id,
            FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Id,
            FilingSummaryType.PersonReceiving1000OrMoreSummary.Id,
            FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Id,
        };

        foreach (var id in filingSummaryTypeIds)
        {
            var newFilingSummary = new FilingSummary
            {
                FilingSummaryTypeId = id,
                FilingSummaryStatusId = FilingSummaryStatus.NotStarted.Id,
                PeriodAmount = 0m,
                ToDateAmount = 0m,
                UnitemizedAmount = 0m,
            };
            newFilingSummaries.Add(newFilingSummary);
        }

        return newFilingSummaries;
    }

    private async Task UpdateYearToDateAmount(List<FilingSummary> filingSummaries, long filerId, DateTime currentPeriodStartDate, long? parentId)
    {
        var previousStatements = await repositoriesDependencies.FilingRepository.FindPreviousSmoCampaignStatementsInCalendarYear(filerId, currentPeriodStartDate);

        if (previousStatements.Any(x => x.FilingSummaries is null))
        {
            return;
        }

        foreach (var summary in filingSummaries)
        {
            var totalPreviousPeriodAmount = previousStatements
                .SelectMany(x => x.FilingSummaries)
                .Where(x => x.FilingSummaryTypeId == summary.FilingSummaryTypeId &&
                            (parentId is null || x.FilingId != parentId)) // Exclude from the previous/parent statement (already include while cloning)
                .Sum(s => s.PeriodAmount);
            summary.ToDateAmount = summary.PeriodAmount + totalPreviousPeriodAmount;
        }
    }

    private async Task<(SmoCampaignStatement filing, FilerUserDto filerUser, SlateMailerOrganization registration)>
        GetFilingContextAsync(long id)
    {
        var filing = await repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(id)
            ?? throw new KeyNotFoundException($"SMO CampaignStatement not found. Id={id}");

        var userId = await servicesDependencies.AuthorizationSvc.GetInitiatingUserId();
        var filerUser = await servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(filing.FilerId, userId.GetValueOrDefault())
            ?? throw new InvalidOperationException($"No filer user found with filing. Id={id}");

        var registration = await repositoriesDependencies.RegistrationRepository.FindSmoRegistrationLatestAcceptedByFilerId(filing.FilerId)
            ?? throw new InvalidOperationException($"Registration not found with filer. Id={filing.FilerId}");

        return (filing, filerUser, registration);
    }

    private static (string? FirstName, string? LastName) SplitName(string? fullName)
    {
        if (string.IsNullOrWhiteSpace(fullName))
        {
            return (null, null);
        }

        var splitParts = fullName.Split(' ', 2, StringSplitOptions.RemoveEmptyEntries);
        return (splitParts.ElementAtOrDefault(0), splitParts.ElementAtOrDefault(1));
    }

    private async Task<TransactionResponseDto> CreatePaymentReceivedTransactionAsync(Filing filing, PaymentReceivedRequest request, long? transactionId)
    {
        var paymentsReceivedValidationErrors = await ValidatePaymentReceivedDecisions(request);

        long filingId = filing.Id;
        long filerId = filing.FilerId;

        var (_, _, newContactSummary) = await dependencies.TransactionSvc.GetSmoCampaignStatementFilingContactSummaryChanges(filing, request, transactionId);

        if (paymentsReceivedValidationErrors.Count == 0)
        {
            var transaction = request.MapToEntity();
            FilingContactSummary? newSummary = null;
            transaction.FilerId = filerId;
            if (newContactSummary.Id == 0)
            {
                newSummary = await repositoriesDependencies.FilingContactSummaryRepository.Create(newContactSummary);
            }
            else
            {
                newSummary = await repositoriesDependencies.FilingContactSummaryRepository.Update(newContactSummary);
            }

            if (transaction.DisclosureStanceOnBallotMeasure != null)
            {
                transaction.DisclosureStanceOnBallotMeasure.FilingContactSummaryId = newSummary.Id;
            }

            if (transaction.DisclosureStanceOnCandidate != null)
            {
                transaction.DisclosureStanceOnCandidate.FilingContactSummaryId = newSummary.Id;
            }

            await dependencies.TransactionSvc.CreateTransactionWrapper(transaction, filingId);

            // Update relationship of uploaded files if any
            await UpdateUploadedFileRelationships(transaction.Id, request.AttachedFileGuidsJson ?? string.Empty);

            return new TransactionResponseDto(transaction.Id, true, paymentsReceivedValidationErrors);
        }

        return new TransactionResponseDto(null, false, paymentsReceivedValidationErrors);
    }

    private async Task<TransactionResponseDto> CreatePaymentMadeTransactionAsync(long filingId, long filerId, PaymentMadeRequest request)
    {
        var paymentsMadeValidationErrors = await ValidatePaymentsMadeDecisions(request);

        if (paymentsMadeValidationErrors.Count == 0)
        {
            var transaction = request.MapToEntity();
            transaction.FilerId = filerId;

            await dependencies.TransactionSvc.CreateTransactionWrapper(transaction, filingId);

            // Update relationship of uploaded files if any
            await UpdateUploadedFileRelationships(transaction.Id, request.AttachedFileGuidsJson ?? string.Empty);

            return new TransactionResponseDto(transaction.Id, true, paymentsMadeValidationErrors);
        }

        return new TransactionResponseDto(null, false, paymentsMadeValidationErrors);
    }

    private async Task<TransactionResponseDto> CreatePaymentMadeByAgentTransactionAsync(
        long filingId,
        long filerId,
        PaymentMadeByAgentOrIndependentContractorRequest request)
    {
        var paymentsMadeByAgentValidationErrors = await ValidatePaymentsMadeDecisions(request);
        if (paymentsMadeByAgentValidationErrors.Count == 0)
        {
            var transaction = request.MapToEntity();
            transaction.FilerId = filerId;

            await dependencies.TransactionSvc.CreateTransactionWrapper(transaction, filingId);

            // Update relationship of uploaded files if any
            await UpdateUploadedFileRelationships(transaction.Id, request.AttachedFileGuidsJson ?? string.Empty);

            return new TransactionResponseDto(transaction.Id, true, paymentsMadeByAgentValidationErrors);
        }

        return new TransactionResponseDto(null, false, paymentsMadeByAgentValidationErrors);
    }

    private async Task<TransactionResponseDto> CreatePersonReceiving1000OrMoreTransactionAsync(long filingId, long filerId, PersonReceiving1000OrMoreRequest request)
    {
        bool isValid = false;

        // Decisions
        var validationErrors = await ValidatePersonReceiving1000TransactionDecisions(request);
        if (validationErrors.Count == 0)
        {
            isValid = true;
            var transaction = request.MapToEntity();
            transaction.FilerId = filerId;

            await dependencies.TransactionSvc.CreateTransactionWrapper(transaction, filingId);

            // Update relationship of uploaded files if any
            await UpdateUploadedFileRelationships(transaction.Id, request.AttachedFileGuidsJson ?? string.Empty);

            return new TransactionResponseDto(transaction.Id, isValid, validationErrors);
        }

        return new TransactionResponseDto(null, isValid, validationErrors);
    }

    private async Task<TransactionResponseDto> UpdatePaymentReceivedTransactionAsync(long filingId, PaymentReceived transaction, PaymentReceivedRequest request, decimal varianceAmount)
    {
        var paymentReceivedValidationErrors = await ValidatePaymentReceivedDecisions(request);
        var filing = await repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(filingId)
            ?? throw new KeyNotFoundException($"Filing not found. Id={filingId}");

        (PaymentReceived? _, FilingContactSummary? previousContactSummary, FilingContactSummary newContactSummary) =
            await dependencies.TransactionSvc.GetSmoCampaignStatementFilingContactSummaryChanges(filing, request, transaction.Id);

        if (paymentReceivedValidationErrors.Count == 0)
        {
            // Create/Update the summary
            FilingContactSummary? newSummary = null;
            if (newContactSummary.Id == 0)
            {
                newSummary = await repositoriesDependencies.FilingContactSummaryRepository.Create(newContactSummary);
            }
            else
            {
                newSummary = await repositoriesDependencies.FilingContactSummaryRepository.Update(newContactSummary);
            }

            // Set the Id
            if (transaction.DisclosureStanceOnBallotMeasure != null)
            {
                transaction.DisclosureStanceOnBallotMeasure.FilingContactSummaryId = newSummary?.Id ?? newContactSummary.Id;
            }
            if (transaction.DisclosureStanceOnCandidate != null)
            {
                transaction.DisclosureStanceOnCandidate.FilingContactSummaryId = newSummary?.Id ?? newContactSummary.Id;
            }

            request.MapToExistingEntity(transaction);
            await dependencies.TransactionSvc.UpdateTransactionWrapperAsync(transaction, varianceAmount, filingId, request.ContactId);

            // update previous FilingContactSummary
            if (previousContactSummary != null)
            {
                _ = repositoriesDependencies.FilingContactSummaryRepository.Update(previousContactSummary);
            }

            return new TransactionResponseDto(transaction.Id, true, paymentReceivedValidationErrors);
        }
        return new TransactionResponseDto(transaction.Id, false, paymentReceivedValidationErrors);
    }

    private async Task<TransactionResponseDto> UpdatePaymentMadeTransactionAsync(long filingId, PaymentMade transaction, PaymentMadeRequest request, decimal varianceAmount)
    {
        var paymentMadeValidationErrors = await ValidatePaymentsMadeDecisions(request);

        if (paymentMadeValidationErrors.Count == 0)
        {
            request.MapToExistingEntity(transaction);
            await dependencies.TransactionSvc.UpdateTransactionWrapperAsync(transaction, varianceAmount, filingId, request.ContactId);
            return new TransactionResponseDto(transaction.Id, true, paymentMadeValidationErrors);
        }
        return new TransactionResponseDto(transaction.Id, false, paymentMadeValidationErrors);
    }

    private async Task<TransactionResponseDto> UpdatePaymentMadeByAgentTransactionAsync(
        long filingId,
        PaymentMade transaction,
        PaymentMadeByAgentOrIndependentContractorRequest request,
        decimal varianceAmount)
    {
        var paymentMadeByAgentValidationErrors = await ValidatePaymentsMadeDecisions(request);

        if (paymentMadeByAgentValidationErrors.Count == 0)
        {
            request.MapToExistingEntity(transaction);
            await dependencies.TransactionSvc.UpdateTransactionWrapperAsync(transaction, varianceAmount, filingId, request.ContactId);
            return new TransactionResponseDto(transaction.Id, true, paymentMadeByAgentValidationErrors);
        }
        return new TransactionResponseDto(transaction.Id, false, paymentMadeByAgentValidationErrors);
    }

    private async Task<TransactionResponseDto> UpdatePersonReceiving1000OrMoreTransactionAsync(
        long filingId,
        PersonReceiving1000OrMore transaction,
        PersonReceiving1000OrMoreRequest request,
        decimal varianceAmount)
    {
        bool isValid = false;

        // Decisions
        var validationErrors = await ValidatePersonReceiving1000TransactionDecisions(request);
        if (validationErrors.Count == 0)
        {
            isValid = true;
            request.MapToExistingEntity(transaction);
            await dependencies.TransactionSvc.UpdateTransactionWrapperAsync(transaction, varianceAmount, filingId, request.ContactId);
            return new TransactionResponseDto(transaction.Id, isValid, validationErrors);
        }

        return new TransactionResponseDto(transaction.Id, isValid, validationErrors);
    }

    private async Task<Transaction?> GetTransactionByTypeAsync(long transactionId, long filingId, TransactionType? transactionType)
    {
        return transactionType switch
        {
            var type when type == TransactionType.PaymentReceived =>
                await repositoriesDependencies.TransactionRepository.FindByIdAndFilingId<PaymentReceived>(transactionId, filingId),
            var type when type == TransactionType.PaymentMade =>
                await repositoriesDependencies.TransactionRepository.FindByIdAndFilingId<PaymentMade>(transactionId, filingId),
            var type when type == TransactionType.PersonReceiving1000OrMore =>
                await repositoriesDependencies.TransactionRepository.FindByIdAndFilingId<PersonReceiving1000OrMore>(transactionId, filingId),
            _ => throw new InvalidOperationException($"Unsupported transaction type: {transactionType}")
        };
    }

    private async Task<Transaction?> GetTransactionByRequestTypeAsync(long transactionId, long filingId, TransactionDetailRequest request)
    {
        return request switch
        {
            PaymentReceivedRequest =>
                await repositoriesDependencies.TransactionRepository.FindByIdAndFilingId<PaymentReceived>(transactionId, filingId),
            PaymentMadeRequest or PaymentMadeByAgentOrIndependentContractorRequest =>
                await repositoriesDependencies.TransactionRepository.FindByIdAndFilingId<PaymentMade>(transactionId, filingId),
            PersonReceiving1000OrMoreRequest =>
                await repositoriesDependencies.TransactionRepository.FindByIdAndFilingId<PersonReceiving1000OrMore>(transactionId, filingId),
            _ => throw new InvalidOperationException($"Unsupported transaction type: {request}")
        };
    }

    private static long StartFilingSummaryIfApplicable(long statusId)
    {
        return statusId == FilingSummaryStatus.NotStarted.Id ? FilingSummaryStatus.InProgress.Id : statusId;
    }

    private async Task StartCandidateOrMeasuresNotListedPaymentReceivedSummary(List<FilingSummary> filingSummaries)
    {
        var filingSummary = filingSummaries.FirstOrDefault(x => x.FilingSummaryTypeId == FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Id)
            ?? throw new InvalidOperationException();

        if (filingSummary.FilingSummaryStatusId == FilingSummaryStatus.NotStarted.Id ||
            filingSummary.FilingSummaryStatusId == FilingSummaryStatus.NothingToReport.Id)
        {
            filingSummary.FilingSummaryStatusId = FilingSummaryStatus.InProgress.Id;
            await repositoriesDependencies.FilingSummaryRepository.Update(filingSummary);
        }
    }

    private async Task CaptureRegistrationAsOfSubmissionAsync(SmoCampaignStatement filing)
    {
        var registrationId = await servicesDependencies.FilerSvc.GetFilerRegistrationAsOfAsync(filing.FilerId, dateTimeSvc.GetCurrentDateTime());

        if (registrationId == 0)
        {
            throw new InvalidOperationException($"Cannot found latest registration of this filer. FilerId={filing.FilerId}");
        }

        filing.RegistrationId = registrationId;
    }

    private async Task<List<WorkFlowError>> ValidatePaymentsMadeDecisions(object request)
    {
        var decisionsInput = new SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs();

        if (request is PaymentMadeRequest paymentMadeRequest)
        {
            decisionsInput = new()
            {
                Description = paymentMadeRequest.Description,
                Amount = paymentMadeRequest.Amount,
                DatePaid = paymentMadeRequest.TransactionDate,
                CodeId = paymentMadeRequest.CodeId == 0 ? null : paymentMadeRequest.CodeId,
            };
        }

        if (request is PaymentMadeByAgentOrIndependentContractorRequest paymentMadeByAgentRequest)
        {
            decisionsInput = new()
            {
                Description = paymentMadeByAgentRequest.Description,
                Amount = paymentMadeByAgentRequest.Amount,
                DatePaid = paymentMadeByAgentRequest.TransactionDate,
                CodeId = paymentMadeByAgentRequest.CodeId == 0 ? null : paymentMadeByAgentRequest.CodeId,
                IsPaidByAgentOrIndependent = paymentMadeByAgentRequest.IsPaidByAgentOrIndependentContractor,
                AgentOrIndependentContractorName = string.IsNullOrWhiteSpace(paymentMadeByAgentRequest.AgentOrIndependentContractorName) ? null : paymentMadeByAgentRequest.AgentOrIndependentContractorName,
            };
        }

        var validationErrors = await servicesDependencies.DecisionsSvc
            .InitiateWorkflow<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs, List<WorkFlowError>>(
                DecisionsWorkflow.SmoPaymentsMadeBySmoOrAgentContractorRuleSet,
                decisionsInput,
                true) ?? new List<WorkFlowError>();

        return validationErrors;
    }

    /// <summary>
    /// Calls Decisions for Smo Campaign Statement Payment Received Transaction
    /// </summary>
    /// <param name="request">PaymentReceived Transaction Request Body</param>
    /// <returns>List of Decisions Errors</returns>
    private async Task<List<WorkFlowError>> ValidatePaymentReceivedDecisions(PaymentReceivedRequest request)
    {
        string? paymentAssociation = SetPaymentAssociationAndPayorType(request.StanceOnCandidate, request.StanceOnBallotMeasure);
        string? jurisdiction = request.Jurisdiction;
        var validationPaymentRequest = new PaymentReceivedValidationRequestDto
        {
            CandidateOrMeasure = paymentAssociation,
            Position = request.Position,
            Amount = request.Amount,
            Notes = request.Notes,
            Jurisdiction = jurisdiction,
            TransactionDate = request.TransactionDate,
        };
        var validationPaymentErrors = await ValidatePaymentReceivedInformationDecisions(validationPaymentRequest);

        SmoCampaignStatementPaymentReceivedCandidateOrMeasureInfoDs candidateOrMeasureDsInput = new()
        {
            PayorType = paymentAssociation,
            Jurisdiction = jurisdiction,
            CandidateId = request.StanceOnCandidate?.CandidateId,
            BallotId = request.StanceOnBallotMeasure?.BallotMeasureId,
            CandidateFirstName = request.StanceOnCandidate?.FirstName,
            CandidateMiddleName = request.StanceOnCandidate?.MiddleName,
            CandidateLastName = request.StanceOnCandidate?.LastName,
            OfficeSought = request.StanceOnCandidate?.OfficeSought,
            JurisdictionName = request.StanceOnCandidate?.Jurisdiction ?? request.StanceOnBallotMeasure?.Jurisdiction,
            District = request.StanceOnCandidate?.District,
            BallotLetter = request.StanceOnBallotMeasure?.BallotLetter,
            FullBallotTitle = request.StanceOnBallotMeasure?.Title,
            IsCumulativeAmount = request.IsCumulativeAmount,
            UnitemizedAmount = request.UnitemizedAmount,
            CumulativeAmountToDate = request.CumulativeAmountToDate
        };

        var candidateOrMeasureErrors = await servicesDependencies.DecisionsSvc.InitiateWorkflow<SmoCampaignStatementPaymentReceivedCandidateOrMeasureInfoDs, List<WorkFlowError>>
            (DecisionsWorkflow.SmoPaymentReceivedAssociationRuleSet, candidateOrMeasureDsInput, true) ?? new List<WorkFlowError>();

        return [.. validationPaymentErrors, .. candidateOrMeasureErrors];
    }

    /// <summary>
    /// Calls Decisions for Smo Campaign Statement Payment Received Information Decision
    /// </summary>
    /// <param name="request">PaymentReceived Transaction Request Body</param>
    /// <returns>List of Decisions Errors</returns>
    private async Task<List<WorkFlowError>> ValidatePaymentReceivedInformationDecisions(PaymentReceivedValidationRequestDto request)
    {
        SmoCampaignStatementPaymentReceivedInformationDs decisionsInput = new()
        {
            PayorType = request.CandidateOrMeasure,
            Jurisdiction = request.Jurisdiction,
            Position = request.Position,
            AmountReceived = request.Amount,
            DateReceived = request.TransactionDate,
            Notes = request.Notes,
        };

        return await servicesDependencies.DecisionsSvc.InitiateWorkflow<SmoCampaignStatementPaymentReceivedInformationDs, List<WorkFlowError>>
            (DecisionsWorkflow.SmoPaymentReceivedInformationRuleSet, decisionsInput, true) ?? new List<WorkFlowError>();
    }

    private async Task<List<WorkFlowError>> ValidatePersonReceiving1000TransactionDecisions(PersonReceiving1000OrMoreRequest request)
    {
        var decisionsInput = new SmoCampaignStatementPersonReceiving1000OrMoreDs
        {
            AmountPaid = request.Amount,
            Notes = request.Notes,
        };

        return await servicesDependencies.DecisionsSvc
            .InitiateWorkflow<SmoCampaignStatementPersonReceiving1000OrMoreDs, List<WorkFlowError>>(DecisionsWorkflow.PersonsReceivingTransactionRuleSet, decisionsInput, true) ?? new List<WorkFlowError>();
    }

    private async Task<DecisionsSmoCampaignStatementSubmissionResponse> ValidateSmoCampaignStatementAttestation(SmoCampaignStatement filing, FilerUserDto filerUser, bool isAttesting)
    {
        var isAmending = filing.Id != filing.OriginalId;
        var decisionsRuleSet = ResolveDecisionsWorkflowPostSubmission(isAmending, isAttesting);

        var fullName = filerUser.User is not null ? $"{filerUser.User.FirstName} {filerUser.User.LastName}" : string.Empty;
        var filerUserRole = filerUser.FilerRole?.Name;

        DecisionsAttestation decisionsAttestationInput = new()
        {
            AttestationName = fullName,
            AttestationRole = filerUserRole
        };

        if (isAttesting)
        {
            var decisionsAttestationResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsAttestation, DecisionsSmoCampaignStatementSubmissionResponse>(
                DecisionsWorkflow.SmoCampaignStatementAttestationRuleSet,
                decisionsAttestationInput,
                checkRequiredFields: true);

            if (decisionsAttestationResponse?.Errors?.Count > 0)
            {
                return decisionsAttestationResponse!;
            }
        }

        DecisionsSmoCampaignStatementSubmission decisionsSubmissionInput = await PopulateDecisionsSmoSubmission(filing, isAmending);

        var decisionsResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsSmoCampaignStatementSubmission, DecisionsSmoCampaignStatementSubmissionResponse>(
            decisionsRuleSet,
            decisionsSubmissionInput,
            checkRequiredFields: true);

        return decisionsResponse;
    }

    private async Task<DecisionsSmoCampaignStatementSubmission> PopulateDecisionsSmoSubmission(SmoCampaignStatement filing, bool isAmending)
    {
        var paymentReceivedFilingSummary = filing.FilingSummaries.FirstOrDefault(x => x.FilingId == filing.Id && x.FilingSummaryTypeId == FilingSummaryType.PaymentReceivedSummary.Id);
        var paymentMadeFilingSummary = filing.FilingSummaries.FirstOrDefault(x => x.FilingId == filing.Id && x.FilingSummaryTypeId == FilingSummaryType.PaymentMadeSummary.Id);

        // Payment Received
        var paymentReceivedTransactions = await dependencies.TransactionSvc.GetAllTransactionsByFilingAsync<PaymentReceived, PaymentReceivedResponseDto>(
            filing.Id,
            transaction => new PaymentReceivedResponseDto(filing.Id, transaction));

        List<SmoCampaignStatementPaymentReceivedInformationDs> paymentsReceived = [.. paymentReceivedTransactions
            .Select(x => new SmoCampaignStatementPaymentReceivedInformationDs
            {
                PayorType = SetPaymentAssociationAndPayorType(x.StanceOnCandidateDto, x.StanceOnBallotMeasureDto),
                Jurisdiction = SetJurisdiction(x.StanceOnCandidateDto, x.StanceOnBallotMeasureDto),
                Position = x.Position,
                AmountReceived = x.Amount,
                DateReceived = x.TransactionDate,
                Notes = x.Notes ?? string.Empty
            })];

        // Payor Information
        var payorInformationList = (await dependencies.TransactionSvc.GetAllTransactionsForFiling(filing.Id))
            .Where(x => x.TypeId == TransactionType.PaymentReceived.Id && x.Contact != null)
            .Select(x =>
            {
                var contact = x.Contact;
                var individual = contact as IndividualContact;
                var organization = contact as OrganizationContact;
                var candidate = contact as CandidateContact;
                var filerCommittee = contact as FilerCommitteeContact;

                var address = MapAddress(contact!.AddressList!);
                var payorType = contact.FilerContactType!.Name;

                return new SmoCampaignStatementTransactorInformationDs
                {
                    CommitteeId = filerCommittee?.Id ?? null,
                    FirstName = contact.FirstName,
                    LastName = contact.LastName,
                    Address = address,
                    Employer = individual?.Employer ?? string.Empty,
                    OrganizationName = organization?.OrganizationName ?? string.Empty,
                    OfficeSought = candidate?.OfficeSought ?? string.Empty,
                    JurisdictionName = candidate?.Jurisdiction ?? string.Empty,
                    District = candidate?.District ?? string.Empty,
                    PayorType = payorType
                };
            })
            .ToList();

        // Person Receiving 1000
        var personReceiving1000Responses = await dependencies.TransactionSvc.GetAllTransactionsByFilingAsync<PersonReceiving1000OrMore, PersonReceiving1000OrMoreResponseDto>(
            filing.Id,
            transaction => new PersonReceiving1000OrMoreResponseDto(filing.Id, transaction));

        var personReceiving1000Payments = personReceiving1000Responses
            .Select(x => new SmoCampaignStatementPersonReceiving1000OrMoreDs
            {
                AmountPaid = x.Amount,
                Notes = x.Notes
            })
            .ToList();

        // Person Receiving 1000 Officer
        var officers = personReceiving1000Responses
            .Select(x => new SmoCampaignStatementOfficerDs
            {
                OfficerId = x.ContactId // We don't store officerId. When create a transaction, we would map the officer information to a contact
            })
            .ToList();

        // Payment Received Candidate or Measure information
        var paymentReceivedCandidateOrMeasureInfo = paymentReceivedTransactions
            .Select(x => new SmoCampaignStatementPaymentReceivedCandidateOrMeasureInfoDs
            {
                PayorType = SetPaymentAssociationAndPayorType(x.StanceOnCandidateDto, x.StanceOnBallotMeasureDto),
                PaymentAssociation = SetPaymentAssociationAndPayorType(x.StanceOnCandidateDto, x.StanceOnBallotMeasureDto),
                Jurisdiction = SetJurisdiction(x.StanceOnCandidateDto, x.StanceOnBallotMeasureDto),
                JurisdictionName = x.StanceOnCandidateDto?.Jurisdiction ?? x.StanceOnBallotMeasureDto?.Jurisdiction,
                CandidateId = x.StanceOnCandidateDto?.CandidateId,
                CandidateFirstName = x.StanceOnCandidateDto?.FirstName ?? string.Empty,
                CandidateMiddleName = x.StanceOnCandidateDto?.MiddleName ?? string.Empty,
                CandidateLastName = x.StanceOnCandidateDto?.LastName ?? string.Empty,
                OfficeSought = x.StanceOnCandidateDto?.OfficeSought ?? string.Empty,
                District = x.StanceOnCandidateDto?.District ?? string.Empty,
                BallotId = x.StanceOnBallotMeasureDto?.BallotMeasureId,
                BallotLetter = x.StanceOnBallotMeasureDto?.BallotLetter ?? string.Empty,
                FullBallotTitle = x.StanceOnBallotMeasureDto?.Title ?? string.Empty,
                IsCumulativeAmount = x.CumulativeAmount != 0m,
                CumulativeAmountToDate = x.CumulativeAmount,
                UnitemizedAmount = x.UnitemizedAmount
            })
            .ToList();

        // Payment Made
        var paymentsMadeTransactions = await dependencies.TransactionSvc.GetAllTransactionsByFilingAsync<PaymentMade, PaymentMadeResponseDto>(
            filing.Id,
            transaction =>
                new PaymentMadeResponseDto(filing.Id, transaction));

        var paymentsMade = paymentsMadeTransactions
            .Select(x => new SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs
            {
                Description = x.Description,
                Amount = x.Amount,
                DatePaid = x.TransactionDate,
                CodeId = x.CodeId,
                IsPaidByAgentOrIndependent = false,
            })
            .ToList();

        // Payment Made By Agent
        var paymentsMadeByAgentTransactions = await dependencies.TransactionSvc.GetAllTransactionsByFilingAsync<PaymentMade, PaymentMadeByAgentOrIndependentContractorResponseDto>(
            filing.Id,
            transaction =>
                new PaymentMadeByAgentOrIndependentContractorResponseDto(filing.Id, transaction));

        var paymentsMadeByAgent = paymentsMadeByAgentTransactions
            .Select(x => new SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs
            {
                Description = x.Description,
                Amount = x.Amount,
                DatePaid = x.TransactionDate,
                CodeId = x.CodeId,
                IsPaidByAgentOrIndependent = true,
                AgentOrIndependentContractorName = x.AgentOrIndependentContractorName,
            })
            .ToList();

        // Candidate or Measure not Listed on Payment Received
        var candidateAndMeasureNotListedEntries = (await GetCandidatesOrMeasuresNotListedOnPaymentReceivedAsync(filing.Id))
            .Select(x => new SmoCampaignStatementCandidateAndMeasureNotListedEntryDs
            {
                PayorType = SetPaymentAssociationAndPayorType(x.StanceOnCandidateDto, x.StanceOnBallotMeasureDto),
                Jurisdiction = SetJurisdiction(x.StanceOnCandidateDto, x.StanceOnBallotMeasureDto),
                JurisdictionName = x.Jurisdiction,
                CandidateId = x.StanceOnCandidateDto?.CandidateId,
                CandidateFirstName = x.StanceOnCandidateDto?.FirstName ?? string.Empty,
                CandidateMiddleName = x.StanceOnCandidateDto?.MiddleName ?? string.Empty,
                CandidateLastName = x.StanceOnCandidateDto?.LastName ?? string.Empty,
                OfficeSought = x.StanceOnCandidateDto?.OfficeSought,
                District = x.StanceOnCandidateDto?.District,
                BallotId = x.StanceOnBallotMeasureDto?.BallotMeasureId,
                BallotLetter = x.StanceOnBallotMeasureDto?.BallotLetter,
                FullBallotTitle = x.StanceOnBallotMeasureDto?.Title ?? string.Empty
            })
            .ToList();

        // Payee Information
        var payeeInformationList = (await dependencies.TransactionSvc.GetAllTransactionsForFiling(filing.Id))
            .Where(x => x.TypeId == TransactionType.PaymentMade.Id && x.Contact != null)
            .Select(x =>
            {
                var contact = x.Contact;
                var individual = contact as IndividualContact;
                var organization = contact as OrganizationContact;

                var address = MapAddress(contact!.AddressList!);
                var payeeType = contact.FilerContactType!.Name;

                return new SmoCampaignStatementTransactorInformationDs
                {
                    PayeeType = payeeType,
                    FirstName = contact.FirstName,
                    LastName = contact.LastName,
                    Employer = individual?.Employer ?? string.Empty,
                    OrganizationName = organization?.OrganizationName ?? string.Empty,
                    Address = address
                };
            })
            .ToList();

        return new()
        {
            PaymentsReceivedInformation = paymentsReceived,
            PayorsInformation = payorInformationList,
            UnitemizedPaymentsReceived = new()
            {
                UnitemizedPaymentsReceivedTotal = paymentReceivedFilingSummary?.UnitemizedAmount,
            },
            UnitemizedPaymentsMade = new()
            {
                UnitemizedPaymentsMadeTotal = paymentMadeFilingSummary?.UnitemizedAmount,
            },
            OfficerIds = officers,
            PersonReceivingPayments = personReceiving1000Payments,
            PaymentsMadeBySmoAgentsOrContactors = [.. paymentsMade, .. paymentsMadeByAgent],
            PaymentsReceivedCandidateOrMeasureInfo = paymentReceivedCandidateOrMeasureInfo,
            CandidatesAndMeasuresNotListed = candidateAndMeasureNotListedEntries,
            PayeesInformation = payeeInformationList,
            AmendmentExplanation = isAmending ? new(SetAmendmentExplanationDecisionsInput(filing)) : null
        };
    }

    private static DecisionServiceAddress? MapAddress(AddressList? addressList, string county = "")
    {
        if (addressList?.Addresses == null || addressList.Addresses.Count == 0)
        {
            return null;
        }

        var address = addressList.Addresses.FirstOrDefault();
        if (address == null)
        {
            return null;
        }

        return new DecisionServiceAddress
        {
            City = address.City ?? string.Empty,
            Street = address.Street ?? string.Empty,
            Street2 = address.Street2 ?? string.Empty,
            Zip = address.Zip,
            Type = address.Type ?? string.Empty,
            County = county,
            State = address.State ?? string.Empty,
            IsMailingAddress = false,
            Country = address.Country ?? string.Empty,
        };
    }

    private async Task UpdateSmoCampaignStatementSubmissionAsync(SmoCampaignStatement filing, string? decisionStatus)
    {
        // Mark the registration submitted
        filing.SubmittedDate = dateTimeSvc.GetCurrentDateTime();

        var statusId = FilingStatus.Pending.Id;
        if (!string.IsNullOrWhiteSpace(decisionStatus))
        {
            statusId = FilingStatusMapping.GetIdByName(decisionStatus);
        }

        // Complete filing
        if (statusId == FilingStatus.Accepted.Id)
        {
            filing.ApprovedAt = dateTimeSvc.GetCurrentDateTime();
        }

        // Update Filing Status
        filing.StatusId = statusId;

        // Capture the latest Accepted registration at the point of submitting the statement.
        // Any change to the registration afterward will not be reflected on the statement.
        await CaptureRegistrationAsOfSubmissionAsync(filing);
        await repositoriesDependencies.FilingRepository.Update(filing);
    }

    private async Task CreateAttestationAsync(long id, SlateMailerOrganization registration, FilerUserDto filerUser)
    {
        var fullName = $"{filerUser?.User?.FirstName} {filerUser?.User?.LastName}";
        var attestation = new Attestation
        {
            RegistrationId = registration.Id,
            FilingId = id,
            Name = fullName,
            Role = filerUser?.FilerRole?.Name,
            Title = filerUser?.FilerRole?.Name,
            ExecutedAt = dateTimeSvc.GetCurrentDateTime(),
            CreatedBy = 0,
            ModifiedBy = 0,
        };

        await repositoriesDependencies.AttestationRepository.Create(attestation);
    }

    /// <summary>
    /// Send the notifications from decisions
    /// </summary>
    /// <param name="filingId">Filing Id</param>
    /// <param name="filerId">Filer Id</param>
    /// <param name="notifications">List of Notifications</param>
    /// <returns></returns>
    private async Task SendFilerNotificationsAsync(long filingId, long filerId, List<NotificationTrigger>? notifications)
    {
        if (notifications == null)
        {
            return;
        }

        var notificationData = new Dictionary<string, string>
        {
            { NotificationTemplateConstants.DocumentIdPlaceholder, filingId.ToString(CultureInfo.InvariantCulture) }
        };

        foreach (var notification in notifications.Where(n => n.SendNotification && n.NotificationTemplateId.HasValue))
        {
            var request = new SendFilerNotificationRequest(
                notification.NotificationTemplateId!.Value,
                filerId,
                notification.DueDate,
                notificationData);

            await servicesDependencies.NotificationSvc.SendFilerNotification(request);
        }
    }

    /// <summary>
    /// Send the notifications from decisions
    /// </summary>
    /// <param name="filingId">Filing Id</param>
    /// <param name="filerId">Filer Id</param>
    /// <param name="notifications">List of Notifications</param>
    /// <param name="notifications">List of userId</param>
    /// <returns></returns>
    private async Task SendUserNotificationsAsync(long filingId, long filerId, List<NotificationTrigger>? notifications, List<long?> userIds)
    {
        if (notifications == null)
        {
            return;
        }

        var notificationData = new Dictionary<string, string>
        {
            { NotificationTemplateConstants.DocumentIdPlaceholder, filingId.ToString(CultureInfo.InvariantCulture) }
        };

        foreach (var notification in notifications.Where(n => n.SendNotification && n.NotificationTemplateId.HasValue))
        {
            foreach (var userId in userIds)
            {
                if (userId is null)
                {
                    continue;
                }

                var request = new SendUserNotificationRequest(
                    notification.NotificationTemplateId!.Value,
                    userId.Value,
                    filerId,
                    notification.DueDate,
                    notificationData);

                await servicesDependencies.NotificationSvc.SendUserNotification(request);
            }
        }
    }

    private static string SetPaymentAssociationAndPayorType(StanceOnCandidateDto? stanceOnCandidate, StanceOnBallotMeasureDto? stanceOnBallotMeasure)
    {
        if (stanceOnCandidate != null)
        {
            return DisclosureConstants.Transactions.PaymentAssociationCandidate;
        }
        else if (stanceOnBallotMeasure != null)
        {
            return DisclosureConstants.Transactions.PaymentAssociationBallotMeasure;
        }

        return string.Empty;
    }

    private static string SetJurisdiction(StanceOnCandidateDto? stanceOnCandidate, StanceOnBallotMeasureDto? stanceOnBallotMeasure)
    {
        return stanceOnCandidate?.CandidateId != null ||
            stanceOnBallotMeasure?.BallotMeasureId != null
            ? DisclosureConstants.Transactions.JurisdictionState
            : DisclosureConstants.Transactions.JurisdictionLocal;
    }

    private static void ValidateFilingStatusForAmendment(long statusId)
    {
        var validStatuses = new List<long>()
        {
            FilingStatus.Accepted.Id,
            FilingStatus.Incomplete.Id
        };

        if (!validStatuses.Contains(statusId))
        {
            throw new InvalidOperationException($"Filing status is not valid for starting a new amendment.");
        }
    }

    private async Task ValidateActiveAmendmentAsync(long id)
    {
        var activeAmendment = await repositoriesDependencies.FilingRepository.FindActiveSmoCampaignStatementAmendment(id);

        if (activeAmendment is not null)
        {
            throw new InvalidOperationException($"There is an existing active amendment for this statement. Id={id}");
        }
    }

    private async Task ResetFilingInformationForNewAmendmentAsync(SmoCampaignStatement filing)
    {
        var userId = await servicesDependencies.AuthorizationSvc.GetInitiatingUserId();

        filing.Version = (filing.Version ?? 0) + 1;
        filing.ParentId = filing.Id;
        filing.Id = 0;
        filing.StatusId = FilingStatus.Draft.Id;
        filing.SubmittedDate = null;
        filing.ApprovedAt = null;
        filing.CreatedBy = userId.GetValueOrDefault();
        filing.RegistrationId = null;
        filing.AmendmentExplanation = null;
    }

    private static void CloneInformationForNewAmendment(SmoCampaignStatement filing)
    {
        CloneFilingSummaries(filing.FilingSummaries);
        CloneDisclosureWithoutPaymentReceiveds(filing.DisclosureWithoutPaymentReceiveds);
    }

    private static void CloneFilingSummaries(List<FilingSummary> filingSummaries)
    {
        if (filingSummaries.Count == 0)
        {
            return;
        }

        // Remove amendment explanation from the list of filing summaries to clone
        var amendmentExplanationSummary = filingSummaries.FirstOrDefault(x => x.FilingSummaryTypeId == FilingSummaryType.AmendmentExplanation.Id);
        if (amendmentExplanationSummary != null)
        {
            filingSummaries.Remove(amendmentExplanationSummary);
        }

        foreach (var item in filingSummaries)
        {
            item.Id = 0;
        }
    }

    private static void CloneTransactions(SmoCampaignStatement filing, List<FilingTransaction> filingTransactions, Dictionary<long, FilingContactSummary> filingContactSummaryMap)
    {
        if (filingTransactions.Count == 0)
        {
            return;
        }

        foreach (var item in filingTransactions)
        {
            item.Id = 0;

            if (item.Transaction is null)
            {
                continue;
            }

            var transaction = item.Transaction;

            transaction.Id = 0;
            if (transaction is PaymentMade paymentMade)
            {
                paymentMade.ExpenditureCode = default!;
            }

            if (transaction is PaymentReceived paymentReceived)
            {
                CloneDisclosureStanceOnCandidate(paymentReceived.DisclosureStanceOnCandidate, filingContactSummaryMap);
                CloneDisclosureStanceOnBallotMeasure(paymentReceived.DisclosureStanceOnBallotMeasure, filingContactSummaryMap);
            }
        }

        filing.FilingTransactions = filingTransactions;
    }

    private static void CloneDisclosureWithoutPaymentReceiveds(List<DisclosureWithoutPaymentReceived> disclosureWithoutPayments)
    {
        if (disclosureWithoutPayments.Count == 0)
        {
            return;
        }

        foreach (var item in disclosureWithoutPayments)
        {
            item.Id = 0;

            CloneDisclosureStanceOnCandidate(item.DisclosureStanceOnCandidate, null);
            CloneDisclosureStanceOnBallotMeasure(item.DisclosureStanceOnBallotMeasure, null);
        }
    }

    private static void CloneDisclosureStanceOnCandidate(DisclosureStanceOnCandidate? candidate, Dictionary<long, FilingContactSummary>? filingContactSummaryMap)
    {
        if (candidate is null)
        {
            return;
        }

        candidate.Id = 0;
        if (filingContactSummaryMap is not null && candidate.FilingContactSummaryId is not null)
        {
            candidate.FilingContactSummary = filingContactSummaryMap[candidate.FilingContactSummaryId.Value];
        }

        if (candidate.UnregisteredCandidateSubject is { } unregistered)
        {
            unregistered.Id = 0;
        }
    }

    private static void CloneDisclosureStanceOnBallotMeasure(DisclosureStanceOnBallotMeasure? measure, Dictionary<long, FilingContactSummary>? filingContactSummaryMap)
    {
        if (measure is null)
        {
            return;
        }

        measure.Id = 0;
        if (filingContactSummaryMap is not null && measure.FilingContactSummaryId is not null)
        {
            measure.FilingContactSummary = filingContactSummaryMap[measure.FilingContactSummaryId.Value];
        }

        if (measure.UnregisteredBallotMeasureSubject is { } unregistered)
        {
            unregistered.Id = 0;
        }
    }

    private static void CloneFilingContactSummaries(long filingId, List<FilingContactSummary> contactSummaries, Dictionary<long, FilingContactSummary> filingContactSummaryMap)
    {
        if (contactSummaries.Count == 0)
        {
            return;
        }

        foreach (var item in contactSummaries)
        {
            filingContactSummaryMap[item.Id] = item;
            item.Id = 0;
            item.DisclosureFilingId = filingId;
        }

    }

    private static void AddFilingSummaryAmendmentExplanation(List<FilingSummary> filingSummaries)
    {
        var amendmentFs = new FilingSummary
        {
            FilingSummaryTypeId = FilingSummaryType.AmendmentExplanation.Id,
            FilingSummaryStatusId = FilingSummaryStatus.NotStarted.Id,
            PeriodAmount = 0m,
            ToDateAmount = 0m,
        };

        filingSummaries.Add(amendmentFs);
    }

    /// <summary>
    /// Decisions Validation for SmoCampaign Statement Payment Received with Candidate/Ballot 
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private async Task<List<WorkFlowError>> ValidateDisclosureWithoutPaymentReceivedDecisions(DisclosureWithoutPaymentReceivedDto request)
    {
        string? paymentAssociation = SetPaymentAssociationAndPayorType(request.StanceOnCandidate, request.StanceOnBallotMeasure);
        string? jurisdiction = request.Jurisdiction;

        SmoCampaignStatementCandidateAndMeasureNotListedEntryDs decisionsInput = new()
        {
            Jurisdiction = jurisdiction,
            PayorType = paymentAssociation,
            BallotId = request.StanceOnBallotMeasure?.BallotMeasureId,
            BallotLetter = request.StanceOnBallotMeasure?.BallotLetter,
            FullBallotTitle = request.StanceOnBallotMeasure?.Title,
            CandidateId = request.StanceOnCandidate?.CandidateId,
            CandidateFirstName = request.StanceOnCandidate?.FirstName,
            CandidateLastName = request.StanceOnCandidate?.LastName,
            CandidateMiddleName = request.StanceOnCandidate?.MiddleName,
            District = request.StanceOnCandidate?.District,
            OfficeSought = request.StanceOnCandidate?.OfficeSought,
            JurisdictionName = request.StanceOnCandidate?.Jurisdiction ?? request.StanceOnBallotMeasure?.Jurisdiction,
        };
        var validationErrors = await servicesDependencies.DecisionsSvc.InitiateWorkflow<SmoCampaignStatementCandidateAndMeasureNotListedEntryDs, List<WorkFlowError>>
            (DecisionsWorkflow.SmoCandidateOrMeasureNotListedRuleSet, decisionsInput, true) ?? new List<WorkFlowError>();
        return validationErrors;
    }


    private static DecisionsWorkflow ResolveDecisionsWorkflowPostSubmission(bool isAmending, bool isAttesting)
    {
        if (isAmending)
        {
            return isAttesting
                ? DecisionsWorkflow.SmoCampaignStatementPostSubmissionAmendmentRuleSet
                : DecisionsWorkflow.SmoCampaignStatementSendForAttestationAmendmentRuleSet;
        }

        return isAttesting
            ? DecisionsWorkflow.SmoCampaignStatementPostSubmissionRuleSet
            : DecisionsWorkflow.SmoCampaignStatementSendForAttestationRuleSet;
    }

    private static string? SetAmendmentExplanationDecisionsInput(Filing filing)
    {
        string? amendmentExplanation = null;
        var amendmentExplanationSummary = filing.FilingSummaries.FirstOrDefault(x => x.FilingId == filing.Id && x.FilingSummaryTypeId == FilingSummaryType.AmendmentExplanation.Id);
        if (amendmentExplanationSummary?.FilingSummaryStatusId == FilingSummaryStatus.NothingToReport.Id)
        {
            // Pass white space to decisions if amendment explanation is marked as nothing to report
            amendmentExplanation = " ";
        }
        else
        {
            amendmentExplanation = filing.AmendmentExplanation;
        }
        return amendmentExplanation;
    }

    private async Task UpdateUploadedFileRelationships(long transactionId, string attachedFileGuidsJson)
    {
        if (string.IsNullOrWhiteSpace(attachedFileGuidsJson))
        {
            return;
        }

        var request = new UpdateUploadedFileRelationshipsRequest
        {
            RelationshipId = transactionId,
            AttachedFileGuidsJson = attachedFileGuidsJson
        };

        await dependencies.UploadFileSvc.UpdateUploadedFileRelationshipsAsync(request);
    }
    #endregion
}

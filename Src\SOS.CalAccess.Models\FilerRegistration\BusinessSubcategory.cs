using System.ComponentModel.DataAnnotations.Schema;
using SOS.CalAccess.Models.Common;

namespace SOS.CalAccess.Models.FilerRegistration;

/// <summary>
/// Entity that describes the BusinessSubcategory.
/// </summary>
[Table("BusinessSubcategory")]
[Documentation("", Context = "This table holds BusinessSubcategorys.")]
public class BusinessSubcategory : IIdentifiable<long>
{
    /// <summary>
    /// Gets or sets the primary id for the BusinessSubcategory.
    /// </summary>
    [Documentation("BusinessSubcategory Identifier.")]
    public long Id { get; set; }

    /// <summary>
    /// Gets or sets the name of the BusinessSubcategory.
    /// </summary>
    [Documentation("BusinessSubcategory Name.")]
    public required string Name { get; set; }


    //TODO: Clean up models below this line

    /// <summary>
    /// Gets or sets the created by user identifier.
    /// </summary>
    [Documentation("The identifier of the user who created this record.")]
    public long CreatedBy { get; set; }

    /// <summary>
    /// Gets or sets the modified by user identifier.
    /// </summary>
    [Documentation("The identifier of the user who last modified this record.")]
    public long ModifiedBy { get; set; }

}

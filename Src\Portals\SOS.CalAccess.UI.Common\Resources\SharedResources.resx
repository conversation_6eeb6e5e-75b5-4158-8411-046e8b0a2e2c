<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
      <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Title" xml:space="preserve">
    <value>Lobbying firms</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Description" xml:space="preserve">
    <value>Please identify all lobbying firms with which you contract.</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobyingFirms.Add" xml:space="preserve">
    <value>Add lobbying firm</value>
  </data>
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Common.Help" xml:space="preserve">
    <value />
  </data>
  <data name="Logout" xml:space="preserve">
    <value />
  </data>
  <data name="Message" xml:space="preserve">
    <value />
  </data>
  <data name="Messages" xml:space="preserve">
    <value />
  </data>
  <data name="Notification.Channels" xml:space="preserve">
    <value />
  </data>
  <data name="Notification.Title" xml:space="preserve">
    <value />
  </data>
  <data name="StatusType" xml:space="preserve">
    <value />
  </data>
  <data name="Urgency" xml:space="preserve">
    <value />
  </data>
  <data name="Welcome" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Profile" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.FirstName" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.MiddleName" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.LastName" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.EmailAddress" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.PhoneNumber" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.FaxNumber" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.Country" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.Street" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.Street2" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.City" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.State" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.Zip" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.TypeOfAddress" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.SameAsCandidateAddress" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.Street" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.Street2" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.City" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.State" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.Zip" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.Country" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.TypeOfAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.FilingSummary01" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.FilingSummary02" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.TransactionEntry" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.View" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.PreviewPdf" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Start" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Submit" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Cancel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.NotStarted" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.InProgress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.DontHaveAnything" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.FilingPeriod" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.TemporaryDashboard.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.TemporaryDashboard.ReportName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.TemporaryDashboard.CreatedDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.TemporaryDashboard.ReportPeriod" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.TemporaryDashboard.Status" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.ReportDetails" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.LegislativeSession" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.ReportCoversPeriod" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.CumulativePeriod" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.ReportType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.EmployerName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.FilerID" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.BusinessAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Phone" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Email" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Fax" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.MailingAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.From" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Through" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.EmployerSubtext" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.SaveAndClose" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessageDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessageSubject" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessageType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessageDueDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessagePriority" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessageActionRequired" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessageCenterTitle" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Yes" xml:space="preserve">
    <value />
  </data>
  <data name="Common.No" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.HasCandidateRegisteredBefore" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.RegisteringYourselfOrSomeoneElse" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Myself" xml:space="preserve">
    <value />
  </data>
  <data name="Common.SomeoneElse" xml:space="preserve">
    <value />
  </data>
  <data name="Common.SelectCandidate" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Jurisdiction" xml:space="preserve">
    <value />
  </data>
  <data name="Common.State" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Year" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Election" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Office" xml:space="preserve">
    <value />
  </data>
  <data name="Common.District" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Party" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.DiligenceAgreement" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Residential" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Business" xml:space="preserve">
    <value />
  </data>
  <data name="Common.SelectCountry" xml:space="preserve">
    <value />
  </data>
  <data name="Common.SelectState" xml:space="preserve">
    <value />
  </data>
  <data name="Common.SelectFilingPeriod" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.ReviewInstructions" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.Subtotal" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.TotalPaymentsLabel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.EnterAmount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.SaveAndClose" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.Cancel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.SaveAndClose" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.Cancel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.ReviewInstruction" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.DisclosureStatements" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.ContainedInDisclosureStatements" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.MajorDonorOrRecipientCommittee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.Export" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.Next" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.Previous" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.AddNew" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Selection.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Selection.SearchHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Selection.SearchPlaceholder" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Selection.ErrorMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.GeneralInformation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.FilingSummaryHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.FilingSummary" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.PaymentsReceived" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.PaymentsMade" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.PaymentsMadeByAgentOrContractor" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.PersonsReceiving1000OrMore" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.CandidatesMeasuresNotListed" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.AmendmentExplanation.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.AmendmentExplanation.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.AmendmentExplanation.AmendmentExplanationText" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Smo" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.SmoName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.SmoId" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.StreetAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.MailingAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Phone" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Email" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Fax" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Treasurer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.TreasurerName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.RecipientCommittee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.CommitteeName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.CommitteeId" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.ThisStatement" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.TotalYTD" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.TotalPaymentsReceived" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.TotalPaymentsMade" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsReceivedTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsMadeTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsMadeByAgentOrIndependentContractorTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PersonsReceiving1000OrMore" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.CandidatesAndMeasuresNotListedTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.ReviewInstructions" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsReceived100OrMoreGridTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsMade100OrMoreGridTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsMadeByAgentOrIndependentContractorGridTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PersonsReceiving1000OrMoreGridTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.CandidatesAndMeasuresNotListedGridTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridDateHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridNameHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridCandidateOrMeasureHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridSupportOpposeHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridAmountHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridAgentOrContractorHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridDescriptionHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridCumulativeAmountHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridOfficeOrMeasureHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridJurisdictionHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.UnitemizedPaymentsReceivedTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.UnitemizedPaymentsMadeTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.DeletePaymentConfirmation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.Subtotal" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.UnitemizedPaymentsLessThan100" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.UnitemizedPaymentsLessThan100Placeholder" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.CandidatesMeasuresNotListed.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.CandidatesMeasuresNotListed01.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.ChooseContact" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.EnterIdOrName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Verification.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Verification.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AddNewContact" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.CandidatesMeasuresNotListed02.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Individual" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Committee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Candidate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Other" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.FirstName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.MiddleName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.LastName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AddressOfPayor" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Employer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.EmployerDescription" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.SearchForCommittee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.EnterCommitteeNameOrId" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.CandidateMiddleName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.Transactors.CandidateLastName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.OfficeSought" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.JurisdictionName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.District" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.OrganizationName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.PaymentPertain" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.Candidate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.BallotMeasure" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.Jurisdiction" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.State" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.Local" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.Position" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.AmountReceived" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.DateReceived" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.AttachFiles" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.Notes" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.AddCandidate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.AddMeasure" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.EnterIdOrBallotOrTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.BallotLetter" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.FullTitleBallotMeasure" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.ChooseOfficer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.EnterName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.AboutThePayment.AmountPaid" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.FilingPeriod" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.PublicationDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.DistributionMethod" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.DistributionMethodListSelectedHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.OtherDescription" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.Legislator" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.AdditionalInformation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.Amount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.Save" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page01.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page01.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Common.GeneralInformation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Common.PayFees" xml:space="preserve">
    <value />
  </data>
  <data name="Common.StartRegistration" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Common.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Common.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Common.Organization" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Common.Officers" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Common.IndividualAuthorizers" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Common.Submit" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Common.Submit" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page02.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page02.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.FirstName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.MiddleName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Email" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.LastName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page02.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page02.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page02.Question" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page02.Lobbyist" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.Search" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.EnterInformation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.Add" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.FirstName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.MiddleName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.LastName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.Email" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Certification" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Add" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.Name" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.Id" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.Certified" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.NotCertified" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Description" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Add" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Table.Id" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Table.Name" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Table.Email" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.OrganizationName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Email" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.TelephoneNumber" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.FaxNumber" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.OrganizationAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Country" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Street1" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Street1Caption" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Street2" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.City" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.State" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.County" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.TypeOfAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Residential" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Business" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.MailingAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.SameAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.OrganizationLevelOfActivity" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.IsOrganizationQualified" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.DateQualifiedAsSMO" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.IsOrganizationCampaignCommittee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.IsOrganizationCampaignCommitteeCaption" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.CommitteeIDOrName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.CommitteeIDOrNameMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page05.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page05.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page06.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page06.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page06.IsTreasurer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.TitleAdd" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.TitleEdit" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.FirstName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.MiddleName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.LastName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.Email" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.TelephoneNumber" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.Country" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.Address" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.Address2" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.City" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.State" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.ZipCode" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.TypeOfAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.IsUserAuthorized" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.AddOfficerMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.AddOfficer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.AddAnotherOfficer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.OfficerName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.OfficerTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.OfficerStartDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.OfficerPhoneNumber" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.DeleteOfficerModalTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.DeleteOfficerModalMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page09.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page09.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page09.IsOfficer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.TitleAdd" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.TitleEdit" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.OfficerTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.SelectOfficerTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.FirstName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.MiddleName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.LastName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.Email" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.TelephoneNumber" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.Country" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.Address" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.Address2" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.City" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.State" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.ZipCode" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.TypeOfAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.IsUserAuthorized" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.CancelOfficerText" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.CancelOfficerHeaderText" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page11.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page11.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page12.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page12.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page12.AddAuthorizerMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page12.AddAuthorizerButton" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page12.DeleteAuthorizerModalTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page12.DeleteAuthorizerModalMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page13.AuthorizerAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page03.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page03.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page03.AreYouAmending" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page03.Option1" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page03.Option2" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page03.Option3" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page03.Option4" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Registration.TerminateRegistrationDisplayText" xml:space="preserve">
    <value />
  </data>
  <data name="Common.AmendTerminiateRegistration" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Termination" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Cancel" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Previous" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Continue" xml:space="preserve">
    <value />
  </data>
  <data name="Common.SaveAndClose" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Create" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.SectionTitle" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.SectionDescription" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.ExpenditureLimit.YearText" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.ExpenditureLimit.RadioLabel" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.ExpenditureLimit.Accept" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.ExpenditureLimit.Decline" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.ActionLobbied" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.VerifyHeading" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.VerifyInstructions" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.VerifyNotice" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.VerifySendforAttestation" xml:space="preserve">
    <value />
  </data>
  <data name="Common.SavedMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.Subtotal" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.ZipCode" xml:space="preserve">
    <value />
  </data>
  <data name="Common.FieldIsRequired" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Optional" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Home" xml:space="preserve">
    <value />
  </data>
  <data name="progress-status-pending" xml:space="preserve">
    <value />
  </data>
  <data name="progress-status-completed" xml:space="preserve">
    <value />
  </data>
  <data name="progress-status-inprogress" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Edit" xml:space="preserve">
    <value />
  </data>
  <data name="Common.View" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Start" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Delete" xml:space="preserve">
    <value />
  </data>
  <data name="Common.AddNew" xml:space="preserve">
    <value />
  </data>
  <data name="Common.UploadTransactions" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Export" xml:space="preserve">
    <value />
  </data>
  <data name="Common.CancelConfirmationHeader" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.NotificationTemplate.Manage.NotificationType" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.NotificationTemplate.Manage.NotificationName" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.NotificationTemplate.Manage.FilerType" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.NotificationTemplate.Manage.Priority" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.NotificationTemplate.Manage.ActionRequired" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.NotificationTemplate.Manage.Title" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.NotificationTemplate.Manage.DeleteConfirmationMessage" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Email" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.Type" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.Type" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Home.ReferencePage.RegistrationDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Home.ReferencePage.FilingDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Home.ReferencePage.ExpenditureErrorMessage" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.ElectionYear" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.OfficeSought" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.DistrictNumber" xml:space="preserve">
    <value />
  </data>
  <data name="Common.County" xml:space="preserve">
    <value />
  </data>
  <data name="Common.City" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.AgencyName" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Election02.Title" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Election02.Body01" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Election02.Body02" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.CancelReportSuccessMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.UpdateTransactionSuccessMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.UpdateFilingPeriodSuccessMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.UpdateFailMessage" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.ListGroups.UserGroupTitle" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.ListGroups.DeleteUserGroupConfirmationMessage" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.ListGroups.UserGroupName" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.ListGroups.UserGroupDescription" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Common.Title" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Common.Title" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Common.Breadcrumb" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Candidate" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Verification" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.New.Title" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.New.Body" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page01.Title" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page01.Body" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page02.Title" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page02.Body" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Step" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page03.Title" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page03.Body" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page04.Title" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page04.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.AddNewContact" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.SubmissionReceived" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Submit" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.VerifyTitle" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.VerifyMessage" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.SubmissionMessageNonCandidate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.AboutThePayee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.EnterPayee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.TransactionDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.IsRecipientCommittee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.NonCommitteeRecipentName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.RecipientCommitteeFilerId" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.Amount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.NonFilerContributorName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.SeparateAccountName" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page05.Title" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page05.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.CancelTransactionSuccessMessage" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page06.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Next" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Previous" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Amount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.ID" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.LobbyingCoalition" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Instruction" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Subtotal" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.AddNew" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Export" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Cancel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.SaveAndClose" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.LobbyingCoalitionMember" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Index.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LobbyingCampaignContribution.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LobbyistCampaignContribution.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.Lobbyist.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.LobbyistEmployer.Title" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page07.Title" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page07.Body" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page08.Title" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.DistrictOrCounty" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.MultipleTransaction" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessageFilerName" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.ExpenditureLimit.Title" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Close" xml:space="preserve">
    <value />
  </data>
  <data name="Common.InvalidSubmission" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Common.CreatedSuccess" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.NameOfLobbyingCoalition" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Page02.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Page02.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Instructions" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.Instructions" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Subtotal" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.Subtotal" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.AddNew" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Export" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.Export" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Previous" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Next" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.SaveAndClose" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.SaveAndClose" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Cancel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.Cancel" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Attest" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Terminate" xml:space="preserve">
    <value />
  </data>
  <data name="Common.SendForAttestation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.CommonFields.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.CommonFields.Street2" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Street" xml:space="preserve">
    <value />
  </data>
  <data name="Common.StreetAddress" xml:space="preserve">
    <value />
  </data>
  <data name="Common.ZipCode" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Country" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ContactForm.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ContactForm.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ContactForm.PayeeType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ContactForm.Individual" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ContactForm.Organization" xml:space="preserve">
    <value />
  </data>
  <data name="Common.FirstName" xml:space="preserve">
    <value />
  </data>
  <data name="Common.MiddleName" xml:space="preserve">
    <value />
  </data>
  <data name="Common.LastName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ContactForm.OrganizationName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ContactForm.CreateSuccessMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.LobbyingFirm.CreateSuccessMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.LobbyingFirm.UpdateSuccessMessage" xml:space="preserve">
    <value />
  </data>
  <data name="Common.APIRequestError" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ContactForm.CreateErrorMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.UploadTransaction" xml:space="preserve">
    <value />
  </data>
  <data name="Common.TelephoneNumber" xml:space="preserve">
    <value />
  </data>
  <data name="Common.FaxNumber" xml:space="preserve">
    <value />
  </data>
  <data name="Common.EmailAddress" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Street2" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.PermissionName" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.PermissionDescription" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.ChooseExistingContact" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.Title" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Select" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.SubmitExpense" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.AboutTheExpense" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Date" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.ChargedOnCreditCard" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.CreditCardCompany" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.ActivityExpenseType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.AdditionalInfo" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.ReportablePersonsTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.AddReportablePerson" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.Amount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.AdditionalInfoTip" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.ActivityDescription" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.CreateErrorMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.LobbyingFirm" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.LobbyistEmployer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.SubmitReportSuccessMessage" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.GroupPageTitle" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.PermissionGridTitle" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.EntraIdGroupIdentifier" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.AuthorizationGroupName" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.AuthorizationGroupDescription" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Notifications" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.CancelText" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.CancelHeaderText" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.CancelText" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.CancelHeaderText" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.EditGroupPageTitle" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.CheckboxName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.SubTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.DeleteFailureMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Details.Subject" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Details.DeleteConfirmation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Details.Message" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Details.DateReceived" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Details.DueDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Details.FilerType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Details.NotificationType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Details.ActionRequired" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Details.PriorityNotification" xml:space="preserve">
    <value />
  </data>
  <data name="Common.DeleteFailureMessage" xml:space="preserve">
    <value />
  </data>
  <data name="Common.CancelHeaderText" xml:space="preserve">
    <value />
  </data>
  <data name="Common.CancelBodyText" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.MultipleTransaction" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.MultipleTransaction" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page02.Body2" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Save" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.01_SelectContact.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.01_SelectContact.SubTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.SubTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.01_SelectContact.OrAddContact" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.01_SelectContact.EnterContactInfo" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.02_EnterContact.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.02_EnterContact.SubTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.03_EnterAmount.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.03_EnterAmount.SubTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.Description" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.Description" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.Preview" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.ReportPeriod" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.CumulativeDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.TotalActivityExpenses" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.TotalCampaignContributions" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.SaveAndClose" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.Cancel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Lobbyist.FilingSummary.TotalActivityExpenses" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Lobbyist.FilingSummary.TotalCampaignContributions" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Lobbyist.FilingSummary.ReportPeriod" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Lobbyist.FilingSummary.CumulativeDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.Title" xml:space="preserve">
    <value />
  </data>
  <data name="Common.ProgressBar.Modal.Title" xml:space="preserve">
    <value />
  </data>
  <data name="Common.ProgressBar.Modal.Body" xml:space="preserve">
    <value />
  </data>
  <data name="Common.ProgressBar.Modal.CloseButtonText" xml:space="preserve">
    <value />
  </data>
  <data name="Common.ProgressBar.Modal.SubmitButtonText" xml:space="preserve">
    <value />
  </data>
  <data name="Common.ProgressBar.NavigableDisplayText" xml:space="preserve">
    <value />
  </data>
  <data name="Common.DatePlaceHolder" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Page03.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Page03.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ContactForm.EmailAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ContactForm.ContactType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ContactForm.AddressOfCoalitionMember" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ContactForm.ContactTypeTooltip" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.ReviewInstructions" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.UploadTransactions" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.Export" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.Subtotal" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendCandidateRegistration.Page03.PersonalFunds.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendCandidateRegistration.Page03.PersonalFunds.Checkbox" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.CreateTransactionSuccessMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.03_EnterAmount.AmountOfPayment" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendCandidateRegistration.Page03.Amendment.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendCandidateRegistration.Page03.Amendment.Checkbox" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.IsOrganizationQualifiedTooltip" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.IsOrganizationCampaignCommitteeTooltip" xml:space="preserve">
    <value />
  </data>
  <data name="CopyrightStatement" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccuMailValidation.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccuMailValidation.StreetAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccuMailValidation.MailingAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccuMailValidation.Entered" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccuMailValidation.Suggested" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccuMailValidation.UseSuggested" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccuMailValidation.SaveAsEntered" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccuMailValidation.ContinueEditing" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Apply" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page06.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page14.IsTreasurerTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page14.IsNotTreasurerTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page14.TreasurerAcknowledgement" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page14.SendForAcknowledgement" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page14And15.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page14And15.Body2" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page14And15.ResponsibleOfficers" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page15.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page15.VerificationCertification" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page15.OfficerSelectRequirement" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page15.OfficerDropdownHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page15.OfficerDropdown" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page15.Attest" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page15.SendForAttestation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.SubmissionReceived" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.SubmissionBody" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.PendingItems" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page06.MakeTreasurer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page06.MakeOfficerTreasurer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page06.RemoveCurrentTreasurer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page06.ReplaceCurrentTreasurer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page06.Cancel" xml:space="preserve">
    <value />
  </data>
  <data name="Common.PreviewPDF" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.SubmissionMessageCandidate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.FullName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.OfficialPosition" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.OfficialPositionDescription" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.Agency" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.AgencyDescription" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.Amount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.PayeeInfo" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.NameOfPayee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.PayeeType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.AddressOfPayee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.PersonBenefittingActivity" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpenseRP.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpenseRP.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpenseRP.PersonBenefitingActivity" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpenseRP.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpenseRP.AddNewContact" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpenseRP.EnterInformation" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.FilerRole.ListGroups.FilingRole" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.FilerRole.ListGroups.DeleteUserFilingRoleConfirmationMessage" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Description" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MarkAsRead" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MarkUnread" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationMessage.Details.FilerName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.CancelSuccessMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.HeaderBar.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.HomeScreen.Text" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.FilerRole.ListFilerRoles.Title" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.FilerRole.AddRole.Title" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.FilerRole.AddRole.FilingRoleName" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.FilerRole.AddRole.FilerType" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.FilerRole.EditRole.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.Body01" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.Body02" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Body01" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Body02" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.Body01" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.GeneralInformation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.Body02" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.Body03" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.NonRegisteredLobbyistsTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.RegisteredLobbyistsTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.RegisteredLobbyistsBody" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.PUCActivity.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.PUCActivity.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Description" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.AddNew" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionConfirm.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LumpPayment.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CoalitionConfirm.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LumpPayment.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.Verification.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.Verification.DiligenceStatement" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.Verification.Name" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.Verification.ExecutedOn" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.Verification.PreviewPDF" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.Verification.TitleRow" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.Verification.Attest" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.Submitted.SubmissionReceived" xml:space="preserve">
    <value />
  </data>
  <data name="Common.SubmissionReceived" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.Submitted.Note" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.SubmitReportErrorMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.Payee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.PayeeType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.PersonBenefiting" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.AmountBenefiting" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page13.CancelAuthorizerHeaderText" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page13.CancelAuthorizerText" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Page01.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Page01.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Page01.ContinueValidation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.Description" xml:space="preserve">
    <value />
  </data>
  <data name="Common.ToastCancelled" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.CampaignContributionSummary" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.LobbyistReportsSummary" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.MadeToLobbyingFirmsSummary" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.ActivityExpenseSummary" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.PucActivitySummary" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.PaymentsToInHouseLobbyists" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.OtherPaymentsToInfluenceSummary" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.ToLobbyingCoalitionSummary" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.AdHocFilingSummary" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.RecieveLobbyingCoalitionSummary" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.RecieveLobbyingCoalitionSummary" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.Body1" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.LumpSums" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.ReviewInstructions" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.ItemizedPayments" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.ItemizedTotal" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.Export" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.AddNew" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.UnitemizedTotal" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.ItemizedDescription" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.OverheadExpenses" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.ForLobbyingActivity" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.SaveAndClose" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.Cancel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.EnterAmount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.BodyLobbyistEmployer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.BodyLobbyist" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.TitleConfirmTreasurer" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Address" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPaymentPayee.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPaymentPayee.AboutThePayee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPaymentPayee.Description" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPaymentPayee.ChooseExistingContact" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPaymentPayee.AddNewContact" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPaymentPayee.EnterPayee" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.CandidateAttestationRequestSent" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470Attestation.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470Attestation.Verification.Term" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470Attestation.Confirmation.SubmissionReceived" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470Attestation.Verification.Preview" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470Attestation.Verification.Name" xml:space="preserve">
    <value />
  </data>
  <data name="Common.ExecutedOn" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470Attestation.Description" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.SubTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.01_SelectContact.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.01_SelectContact.SubTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.01_SelectContact.SearchContact" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.01_SelectContact.OrAddContact" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.01_SelectContact.OrAddContactButton" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.02_EnterContact.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.02_EnterContact.SubTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.02_EnterContact.AddressTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.03_EnterAmount.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.03_EnterAmount.SubTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.03_EnterAmount.FeesAndRetainersAmount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.03_EnterAmount.ReimbursementOfExpensesAmount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.03_EnterAmount.AdvancesOrOtherPaymentsAmount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.03_EnterAmount.AdvancesOrOtherpaymentsExplanation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.03_EnterAmount.TotalAmount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.02_EnterContact.NameLabel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.AboutThePayment" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.NameOfPayee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.PayeeType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Address" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.PaymentCode" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Description" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.AdminActions" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Legislation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Other" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.AdvertCheckboxes" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.AssemblyBills" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.BillNumber" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.BillTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.SenateBills" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.AgencyOffice" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.OtherActionsLobbied" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Amount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.CumulativeAmount" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page09.SubHeader" xml:space="preserve">
    <value />
  </data>
  <data name="Common.NextSteps" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page09.Body" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page09.Label" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page09.Action" xml:space="preserve">
    <value />
  </data>
  <data name="Common.ReturnToDashboard.Message" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee01.SubTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee01.Description" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee01.Label" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee01.Yes" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee01.No" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee01.SubSectionTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee01.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee01.Body2" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee.CommitteeName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee.Id" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee.Email" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee.Address" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee.Phone" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee.MailAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee02.SubTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LinkCommittee02.Description" xml:space="preserve">
    <value />
  </data>
  <data name="Common.AmendRegistration" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Amend" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page05.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page05.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.AgencyMultiSelectPopUp.SelectAgencies" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.AgencyMultiSelectPopUp.SearchAgencies" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.AgencyMultiSelectPopUp.SelectedAgencies" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.AgencyMultiSelectPopUp.NoAgenciesSelected" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.AgencyMultiSelectPopUp.AddSelected" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LegAssyBillPopUp.AddAssyBill" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LegAssyBillPopUp.SelectedAssyBills" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LegAssyBillPopUp.NoBillsSelected" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LegAssyBillPopUp.AddSelected" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LegSenateBillPopUp.AddSenateBill" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LegSenateBillPopUp.SelectedSenateBills" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LegSenateBillPopUp.NoBillsSelected" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.LegSenateBillPopUp.AddSelected" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SendForAttest.Submit" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SendForAttest.ResponsibleOfficer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SendForAttest.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SendForAttest.OfficerMustBeSelected" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page01.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470S.Page01.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470S.Page01.DateContributions.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page01.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470S.Page01.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470S.Page01.DateContributions.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page01.CalendarYearCovered" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page01.CandidateHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page01.CandidateName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page01.OfficeSoughtHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page01.OfficeSoughtName" xml:space="preserve">
    <value />
  </data>
  <data name="Common.PhoneNumber" xml:space="preserve">
    <value />
  </data>
  <data name="Common.AddressNotFound" xml:space="preserve">
    <value />
  </data>
  <data name="Common.DistrictNumber" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page01.DateOfElection" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.List.Title" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.List.BannerMessage" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.List.DeleteConfirmation" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.List.FilerType" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.List.EventDate" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.List.EndDate" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.Edit.AddNewBannerTitle" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.Edit.EditBannerTitle" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.Edit.EventDate" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.Edit.EventDatePlaceHolder" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.Edit.DisplayBefore" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.Edit.DisplayAfter" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.Edit.DisplayUntil" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.Edit.DisplayDuration" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.Edit.DurationDays" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.Edit.DurationDaysPlaceholder" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.Edit.FilerTypes" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.Edit.Message" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.Edit.SaveButton" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.Banners.Edit.CancelButton" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.ActionsLobbiedSummary" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.LobbyingAdvertisementSummary" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.Report72H.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470Attestation.Verification.ExecutedOn" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page02.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page02.Description" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page02.TableCommitteeName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page02.TableId" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page02.TableAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page02.TableTreasurerName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page02.AddButton" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page03.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page03.Description1" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page03.Description2" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470Attestation.Verification.NonCandidate.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470Attestation.Verification.NonCandidate.Body2" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470Attestation.Verification.NonCandidate.Body1" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470Attestation.Confirmation.AttestationRequestSent" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470Attestation.Confirmation.AttestationRequestSent.Message" xml:space="preserve">
    <value />
  </data>
  <data name="Common.PendingItems" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewPayee.OtherPayments" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.EditActivityExpense.EditErrorMessage" xml:space="preserve">
    <value />
  </data>
  <data name="Common.ToastSaved" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.CreateSuccessMessage" xml:space="preserve">
    <value />
  </data>
  <data name="Common.NotPubliclyAvailable" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.AmendmentExplanation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470.Page02.DeleteMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Form470S.Page02.NextSteps.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.EditActivityExpense.UpdateSuccessMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.01_SelectContact.ContinueValidation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.01_SelectContact.ContinueValidation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.SaveSuccessMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.SendForAttestationLobbyistReport.SendSuccess" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.SendForAttestationLobbyistReport.SendError" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.SendForAttestation.Exception" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.AmendmentExplanation.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.AmendmentExplanation.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.AmendmentExplanation.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.EnterTransaction" xml:space="preserve">
    <value />
  </data>
  <data name="CandidateIntentionStatement.Page09.Action1" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.Report48H.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.EndOfSessionLobbyingSummary" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.TemporaryDashboard.DocumentId" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.PUCActivity.TotalPaymentsLabel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.LobbyistEmployer.Body03" xml:space="preserve">
    <value />
  </data>
  <data name="Common.CancelConfirmationTitle" xml:space="preserve">
    <value />
  </data>
  <data name="Common.CancelConfirmationBody" xml:space="preserve">
    <value />
  </data>
  <data name="Common.CancelConfirmationClose" xml:space="preserve">
    <value />
  </data>
  <data name="Common.CancelConfirmationSubmit" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.CandidateLastName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.Support" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.Oppose" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.CandidateFirstName" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Accept" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Reject" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Description" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.InputLabel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Btn" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.RequestedBy" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.Email" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.AssignedRole" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.DateOfRequest" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.Filer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Registration.TerminateRegistration" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Registration.YesTerminate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Registration.NoContinueAmendment" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Registration.NoticeOfTermination.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Registration.NoticeOfTermination.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Registration.NoticeOfTermination.EffectiveDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Registration.AmendmentVerification.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Registration.TerminationVerification.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Registration.TerminationVerification.Certification" xml:space="preserve">
    <value />
  </data>
  <data name="Common.SelectOption" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Linkages.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Linkages.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.MyLinkagesTable.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.PendingLinkageRequests.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.RequestNewLinkage.Title" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Name" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Current" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Role" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.RequestNewLinkage.Description" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.RequestNewLinkage.RequestLinkageBtn" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.LobbyistEmployer.ActionsLobbied.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.LobbyistEmployer.ActionsLobbied.SubDescription" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.LobbyistEmployer.ActionsLobbied.OtherSubDescription" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.LobbyistEmployer.ActionsLobbied.Description" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.LobbyistEmployer.ActionsLobbied.Bill.DeleteConfirmation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.LobbyistEmployer.ActionsLobbied.Agency.DeleteConfirmation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Lobbyist.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.LobbyistEmployer.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Report72h.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.LobbyistEmployer.Name" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Lobbyist.Name" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Report72h.Name" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.LobbyistEmployer.FilerDetails" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Lobbyist.FilerDetails" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Report72h.FilerDetails" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Report48h.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Report48h.FilerDetails" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Report48h.Name" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbyingSummary.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbyingSummary.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbyingSummary.ReviewInstructions" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbyingSummary.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbying.Id" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbying.LobbyingFirmName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbying.LobbyingFirmHiringDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbying.AmountIncurred" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.MyLinkagesTable.TerminateLinkageMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.CandidatesMeasuresNotListed.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.CandidatesMeasuresNotListed.Description" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.SelectAnOption" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade01.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade01.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade01.EnterPayeeInfo" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade02.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade02.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived01.EnterPayorInfo" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived02.Title" xml:space="preserve">
    <value />
  </data>
  <data name="Common.ViewReport" xml:space="preserve">
    <value />
  </data>
  <data name="Common.IdNumberHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.LobbyistName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.DeleteConfirmation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Page02.Body1" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived02.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Contact.ContactForm.PayorType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived01.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived01.Body" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Send" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.LinkageRequest" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.FilerType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.SearchFilerLabel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.SearchFilerPlaceholder" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.LobbyistEmployerFirmLabel" xml:space="preserve">
    <value />
  </data>
  <data name="Common.SelectOfficer" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.SendForAttestationFailMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.SendForAttestationSuccessMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.DatePaid" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Amount" xml:space="preserve">
    <value />
  </data>
  <data name="Common.PaymentCode" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade03.Checkbox" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade03.TextboxNameOf" xml:space="preserve">
    <value />
  </data>
  <data name="Common.SelectCode" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.AboutThePayment" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.LobbyingFirm" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.FirmAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.DateHired" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.Amount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.AssemblyBills" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page02.RegisteringQuestion" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Zip" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.UploadRules" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Upload" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.WillYouLobby" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.FullList" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.AgenciesLobbied1" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.AgenciesLobbied2" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.StateAgenciesLobbied" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.ChooseOption" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.AgenciesLobbied" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.EthicsNew" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.EthicsNotTaken" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.EthicsRenewal" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.EthicsCompleted" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.EthicsCourse" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.PlacementAgent" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.QualificationDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.LegislativeSession" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.State" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.City" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Street" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Street2" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Country" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.PhoneNumber" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.FaxNumber" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.BusinessAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.MailingAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.SameAs" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Apartment" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.EthicsRenewal1" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page04.Title" xml:space="preserve">
    <value>You need to pay a lobby registration fee.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page04.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page05.Title" xml:space="preserve">
    <value>Lobbyist registration fee</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page05.Body" xml:space="preserve">
    <value>This is a placeholder to indicate where a lobbyist will be assed their registration fee.</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.PreviewPDF" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.Overall" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.ThisReportingPeriod" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.CumulativeToDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.SummaryGrandTotal" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.PaymentsPUCActivities" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.Totals" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.PaymentsToLobbyingFirms" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.TotalsAllFirms" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.PaymentsToLobbyingCoalitions" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.TotalsAllLobbyingCoalitions" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.PaymentsReceivedByCoalitionMember" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.TotalsAllCoalitionMembers" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.SystemNotificationHeading" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.FilingNotificationHeading" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.AllFilersHeading" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.SystemNotifications" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.ReminderNotifications" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.PriorityNotifications" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.OtherNotifications" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.Email" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.CancelDialog" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.SaveSuccess" xml:space="preserve">
    <value />
  </data>
  <data name="Common.UploadFile.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Street1" xml:space="preserve">
    <value />
  </data>
  <data name="Common.CountryCode" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Extension" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.SectionTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.SectionBody" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentOfficers.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.TerminateTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.TerminateBody" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.TerminateSuccessToast" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.TerminateFailureToast" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.RequestType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.AcceptTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.AcceptBody" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.AcceptSuccessToast" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.AcceptFailureToast" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.RejectTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.RejectBody" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.RejectSuccessToast" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.RejectFailureToast" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.RequestLinkage" xml:space="preserve">
    <value />
  </data>
  <data name="Common.CurrencyInputPlaceHolder" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.EnterNameOrId" xml:space="preserve">
    <value />
  </data>
  <data name="Common.ToastCanceled" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page06.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page06.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page07.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page07.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.SendSuccessToast" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.SendFailureToast" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.FilerRoleRequiredError" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.FilerRole" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.SearchFilerRequiredError" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.FilerTypeRequiredError" xml:space="preserve">
    <value />
  </data>
  <data name="Common.FilerName" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Officer" xml:space="preserve">
    <value />
  </data>
  <data name="Common.MailingAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.InvitationCodeRequiredError" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.NotFound" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.LookupError" xml:space="preserve">
    <value />
  </data>
  <data name="Common.EmailAddressRequired" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.FilerRoleRequiredError" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.SendSuccessToast" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.SendFailureToast" xml:space="preserve">
    <value />
  </data>
  <data name="Common.EmailAddressInvalidError" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.FilingHeader" xml:space="preserve">
    <value />
  </data>
  <data name="Common.FilerId" xml:space="preserve">
    <value />
  </data>
  <data name="Common.DocumentId" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Form" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Submitted" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Status" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.SmoName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.HeaderOrganizationAddress" xml:space="preserve">
    <value />
  </data>
  <data name="Common.TypeOfAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.HeaderMailingAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.HeaderOrganizationDetails" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.LevelOfActivity" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.IsQualified" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.DateQualified" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.IsCampaignCommittee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.CommitteeIdentifier" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Officers.Header" xml:space="preserve">
    <value />
  </data>
  <data name="Common.Title" xml:space="preserve">
    <value />
  </data>
  <data name="Common.StartDate" xml:space="preserve">
    <value />
  </data>
  <data name="Common.EndDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.IndividualAuthorizers.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Verificaiton.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Verificaiton.PendingItems" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Verificaiton.TreasurerAcknowledgement" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Verificaiton.TreasurerAcknowledgementCheckbox" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Verificaiton.Verify" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Verificaiton.VerifyCheckbox" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Verificaiton.HelperText" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.EditModel.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.EditModel.Body" xml:space="preserve">
    <value />
  </data>
  <data name="Common.NoCancel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.UserDetails" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.UserName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.EmailAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.FullName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.AddressType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.Country" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.StreetAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.StreetAddress2" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.City" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.ZipCode" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.State" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.TypeOfPhone" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.TelephoneNumber" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.Residential" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.Business" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.SelectCountry" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.SelectState" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.Cell" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.Office" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.Home" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.Name" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.Address" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.MailingAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.PhoneNumber" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.SameAsAbove" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.SetAsPrimaryPhoneNumber" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.Instruction" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page07.Subtitle1" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page07.Verification" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page07.Name" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page07.ExecutedOn" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page08.SubmissionReceived" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page08.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page08.PendingItems" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.AddMajorDonor" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.AddRecipientCommittee" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.RelatedFilers.Name" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.RelatedFilers.Type" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.RelatedFilers.FilerId" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.RelatedFilers.NoRecordAddedYet" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.SearchMajorDonorLabel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.SearchMajorDonorPlaceholder" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.SearchRecipientCommitteeLabel" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.SearchRecipientCommitteePlaceholder" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.RelatedFilers.Add" xml:space="preserve">
    <value />
  </data>
  <data name="Common.UploadFile.Instructions" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.LobbyistEmployerOrLobbyingFirm" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.FilerInformation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.CandidatesAndMeasuresNotListedBody" xml:space="preserve">
    <value />
  </data>
  <data name="Common.NotApplicable" xml:space="preserve">
    <value />
  </data>
  <data name="Common.WithdrawRegistration" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawLobbyistRegistration.NoticeOfWithdrawal" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawLobbyistRegistration.Verification" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawLobbyistRegistration.EffectiveDateOfWithdrawal" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawLobbyistRegistration.LobbyistName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawLobbyistRegistration.FirmOrEmployerName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.ConfirmTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.ConfirmBody" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.TerminationLobbyistRegistration.NoticeOfTermination" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.TerminationLobbyistRegistration.Verification" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.Body01" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.Body02" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.Body03" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.WithdrawalVerification.Body01" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.Name" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.ExecutedOn" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.LobbyistEmail" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Filing.Verification.Attestation" xml:space="preserve">
    <value />
  </data>
  <data name="Common.TerminationRegistration" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.UIText" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Termination.Confirmation.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Withdraw.Confirmation.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Amend.Confirmation.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Renewal.Confirmation.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Confirmation.Body" xml:space="preserve">
    <value />
  </data>
  <data name="Common.WithdrawalRegistration" xml:space="preserve">
    <value />
  </data>
  <data name="Common.RenewalRegistration" xml:space="preserve">
    <value />
  </data>
  <data name="FilePortal.Disclosure.Dashboard.LobbyingAdvertisment.InstructionsTooltip" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.SendForAttestation.Success" xml:space="preserve">
    <value />
  </data>
  <data name="Common.CandidateName" xml:space="preserve">
    <value />
  </data>
  <data name="Common.CandidateEmail" xml:space="preserve">
    <value />
  </data>
  <data name="Common.CandidateAddress" xml:space="preserve">
    <value />
  </data>
  <data name="Common.OfficeSought" xml:space="preserve">
    <value />
  </data>
  <data name="Common.ElectionYear" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Page01.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Page01.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Step01" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Step02" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Breadcrumb" xml:space="preserve">
    <value />
  </data>
  <data name="PRDPortal.FilerRole.ListGroups.DeleteFailureMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.AmendTitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.EffectiveDateOfChanges" xml:space="preserve">
    <value />
  </data>
  <data name="Common.UnauthorizedActionToast" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Common.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page01.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Common.GeneralInformation" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Common.InHouseLobbyists" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Common.LobbyingFirms" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Common.ResponsibleOfficers" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Common.Submit" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page02.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page02.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Common.Title2" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.LobbyistName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.TerminateLobbyistRegistration.RequiredTerminatedAt" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.TerminateLobbyistRegistration.TerminatedAt" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.IsCumulativeAmount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.PreviouslyUnitemizedAmount" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.CumulativeAmountToDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Page02.Header" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Page02.Checkbox" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Page03.CandHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Page03.CandBody" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Lobbyist.ReportType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.LobbyistEmployer.ReportType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Report72h.ReportType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Report48h.ReportType" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.Name" xml:space="preserve">
    <value />
  </data>
  <data name="Common.BusinessAddress" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.SameAs" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.LegislativeSession" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.QualificationDate" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.FirstName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.LastName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.MiddleName" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.Email" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.Detail" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Page02.NonCand.Body01" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Page02.NonCand.Body02" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Page02.NonCand.Body03" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Page03.NonCandHeader" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.WithdrawalCis.Page03.NonCandBody" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.IsLobbyingCoalition" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page04.StateAgenciesLobbied" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page04.FullList" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page04.StateLegislatureLobbied" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Example1" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Example2" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.DescribeInterests" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Example1Body" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Example2Body" xml:space="preserve">
    <value />
  </data>
  <data name="Common.NoSelection.ErrorMessage" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.AccountManagement.AddAnotherPhoneButton" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.InitiateCertifyLobbyist.Title" xml:space="preserve">
    <value />
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.InitiateCertifyLobbyist.SummaryText" xml:space="preserve">
    <value />
  </data>
</root>


using Microsoft.AspNetCore.Authorization;
using NSubstitute;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Elections;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.WebApi.Registrations;

namespace SOS.CalAccess.WebApi.Tests.Registrations;

[TestFixture]
public class CandidateIntentionControllerTests()
{
    private IDecisionsSvc _decisionsSvc;
    private IAuthorizationService _authorizationService;
    private IAuthorizationSvc _authorizationSvc;
    private IAttestationRepository _attestationRepository;
    private IElectionRaceRepository _electionRaceRepository;
    private IPoliticalPartyRepository _politicalPartyRepository;
    private IRegistrationRepository _registrationRepository;
    private ICandidateRepository _candidateRepository;
    private IFilerLinkRepository _filerLinkRepository;
    private INotificationSvc _notificationSvc;
    private IAuditService _auditService;
    private IRegistrationModelMapper _modelMapper;
    private CandidateIntentionRegistrationSvc _service;
    private CandidateIntentionStatementController _controller;
    private ICandidateIntentionRegistrationSvc _candidateIntentionRegistrationSvc;
    private IUserMaintenanceSvc _userMaintenanceSvc;
    private ILinkageSvc _linkageSvc;
    private IFilerUserRepository _filerUserRepository;
    private IDateTimeSvc _dateTimeSvcMock;

    /// <summary>
    /// Sets up the unit tests for this fixture.
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _authorizationService = Substitute.For<IAuthorizationService>();
        _authorizationSvc = Substitute.For<IAuthorizationSvc>();
        _decisionsSvc = Substitute.For<IDecisionsSvc>();
        _registrationRepository = Substitute.For<IRegistrationRepository>();
        _candidateRepository = Substitute.For<ICandidateRepository>();
        _attestationRepository = Substitute.For<IAttestationRepository>();
        _modelMapper = Substitute.For<IRegistrationModelMapper>();
        _electionRaceRepository = Substitute.For<IElectionRaceRepository>();
        _politicalPartyRepository = Substitute.For<IPoliticalPartyRepository>();
        _filerLinkRepository = Substitute.For<IFilerLinkRepository>();
        _notificationSvc = Substitute.For<INotificationSvc>();
        _auditService = Substitute.For<IAuditService>();
        _candidateIntentionRegistrationSvc = Substitute.For<ICandidateIntentionRegistrationSvc>();
        _userMaintenanceSvc = Substitute.For<IUserMaintenanceSvc>();
        _linkageSvc = Substitute.For<ILinkageSvc>();
        _filerUserRepository = Substitute.For<IFilerUserRepository>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();


        var dependencies = new CandidateIntentionDependencies(
            _authorizationSvc,
            _decisionsSvc,
            _notificationSvc,
            _registrationRepository,
            _candidateRepository,
            _filerLinkRepository,
            _userMaintenanceSvc,
            _linkageSvc,
            _dateTimeSvcMock);

        _service = new CandidateIntentionRegistrationSvc(
            dependencies,
            _attestationRepository,
            _electionRaceRepository,
            _politicalPartyRepository,
            _modelMapper,
            _filerUserRepository);

        _controller = new CandidateIntentionStatementController(
                 _authorizationService,
                 _candidateIntentionRegistrationSvc,
                 _auditService);
    }

    [Test]
    public async Task PatchCandidateIntentionStatementStatusById_setStatusId_Ok()
    {
        // Arrange
        var mappedCandidateIntentionStatement = new CandidateIntentionStatement()
        {
            Id = 41381, //would normally only be set after repository create method called
            Name = "Test",
            StatusId = RegistrationStatus.Draft.Id,
            FirstName = "First",
            MiddleName = "Middle",
            LastName = "Last",
            Email = "Email",
            PhoneNumberList = new()
            {
                PhoneNumbers = new() {
                    new() {Number= "1231231234", Type = "Home"},
                    new() {Number= "9879879876", Type = "Fax"}
                }
            },
            AddressList = new()
            {
                Addresses = new() {
                    new() { Street = "1234 Main St", City="Anytown", Country="United States", Purpose="Mailing", State="CA", Type="Home", Zip=(ZipCode)"12345"},
                    new() { Street = "9876 Main St", City="Plainsville", Country="United States", Purpose="Candidate", State="CA", Type="Business", Zip=(ZipCode)"12345"}
                }
            },

        };

        _modelMapper.MapCandidateIntentionStatementRequestToModel(Arg.Any<CandidateIntentionStatementRequest>())
            .Returns(mappedCandidateIntentionStatement);

        _registrationRepository.FindCandidateIntentionStatementById(Arg.Any<long>()).Returns(mappedCandidateIntentionStatement);

        _registrationRepository.Update(Arg.Any<Registration>())
            .Returns(mappedCandidateIntentionStatement);

        var request = new CandidateIntentionPatchDto.StatusRequest()
        {
            StatusId = 3
        };

        // Act
        var controller = new CandidateIntentionStatementController(_authorizationService, _service, _auditService);
        var result = await controller.PatchCandidateIntentionStatementStatusById(41381, request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.CompletedOk, Is.EqualTo(true));
    }

    [Test]
    public async Task GetCandidateIntentionStatementSummary_ReturnsStatement_WhenExists()
    {
        // Arrange
        long id = 123;
        var statement = new CandidateIntentionStatement
        {
            Id = id,
            Name = "Test Candidate",
            StatusId = RegistrationStatus.Accepted.Id
        };

        _candidateIntentionRegistrationSvc
             .GetCandidateIntentionStatementSummary(id)
             .Returns(Task.FromResult<CandidateIntentionStatement?>(statement));


        // Act
        var result = await _controller.GetCandidateIntentionStatementSummary(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result?.Id, Is.EqualTo(id));
            Assert.That(result?.Name, Is.EqualTo("Test Candidate"));
        });

        await _candidateIntentionRegistrationSvc.Received(1)
             .GetCandidateIntentionStatementSummary(id);
    }

    [Test]
    public async Task GetCandidateIntentionStatementSummary_ReturnsNull_WhenNotFound()
    {
        // Arrange
        long id = 999;
        _candidateIntentionRegistrationSvc
            .GetCandidateIntentionStatementSummary(id)
            .Returns(Task.FromResult<CandidateIntentionStatement?>(null));

        // Act
        var result = await _controller.GetCandidateIntentionStatementSummary(id);

        // Assert
        Assert.That(result, Is.Null);
        await _candidateIntentionRegistrationSvc.Received(1)
            .GetCandidateIntentionStatementSummary(id);
    }
    #region LinkControlledCommitteeToCandidateIntentionStatement
    [Test]
    public async Task LinkControlledCommitteeToCandidateIntentionStatement_Valid()
    {
        // Arrange
        long id = 999;
        var request = new ControlledCommitteeRequestDto()
        {
            ControlledCommitteeFilerId = 1
        };
        var result = new List<WorkFlowError>();
        _candidateIntentionRegistrationSvc
            .LinkControlledCommitteeToCandidateIntentionStatement(id, request)
            .Returns(result);

        // Act
        await _controller.LinkControlledCommitteeToCandidateIntentionStatement(id, request);

        // Assert
        await _candidateIntentionRegistrationSvc.Received(1)
            .LinkControlledCommitteeToCandidateIntentionStatement(id, request);
    }
    #endregion
    #region SearchControlledCommitteeByIdOrName
    [Test]
    public async Task SearchControlledCommitteeByIdOrName_Valid()
    {
        // Arrange
        var q = "name";
        var expectedResults = new List<ControlledCommitteeResponseDto>
        {
            new()
            {
                Id = 1,
                Name = "Test Committee",
                Email = "<EMAIL>",
                Address = new()
                {
                        new() { Street = "1234 Main St", City="Anytown", Country="United States", Purpose="Mailing", State="CA", Type="Home", Zip=(ZipCode)"12345"},
                        new() { Street = "9876 Main St", City="Plainsville", Country="United States", Purpose="Candidate", State="CA", Type="Business", Zip=(ZipCode)"12345"}
                }
            }
        };

        _candidateIntentionRegistrationSvc
            .SearchControlledCommitteeByIdOrName(q)
            .Returns(expectedResults);

        // Act
        var result = await _controller.SearchControlledCommitteeByIdOrName(q);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(expectedResults.Count));
            Assert.That(result.First().Name, Is.EqualTo("Test Committee"));
        });
        await _candidateIntentionRegistrationSvc.Received(1)
            .SearchControlledCommitteeByIdOrName(q);
    }
    #endregion
    #region LinkControlledCommitteeToCandidateIntentionStatement
    [Test]
    public async Task GetLinkedControlledCommittee()
    {
        // Arrange
        long id = 999;
        var request = new ControlledCommitteeRequestDto()
        {
            ControlledCommitteeFilerId = 1
        };
        var expectedComtrolledCommittee = new ControlledCommitteeResponseDto()
        {
            Id = 1,
            Name = "Test Committee",
            Email = "<EMAIL>",
            Address = new()
                {
                        new() { Street = "1234 Main St", City="Anytown", Country="United States", Purpose="Mailing", State="CA", Type="Home", Zip=(ZipCode)"12345"},
                        new() { Street = "9876 Main St", City="Plainsville", Country="United States", Purpose="Candidate", State="CA", Type="Business", Zip=(ZipCode)"12345"}
                }
        };

        _candidateIntentionRegistrationSvc
            .GetLinkedControlledCommittee(id)
            .Returns(expectedComtrolledCommittee);

        // Act
        var result = await _controller.GetLinkedControlledCommittee(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Id, Is.EqualTo(expectedComtrolledCommittee.Id));
            Assert.That(result.Name, Is.EqualTo(expectedComtrolledCommittee.Name));
            Assert.That(result.Email, Is.EqualTo(expectedComtrolledCommittee.Email));
            Assert.That(result.Address, Is.EqualTo(expectedComtrolledCommittee.Address));
        });
    }
    #endregion
    #region SendForAttestation
    [Test]
    public async Task SendForAttestation_ReturnsResponse()
    {
        // Arrange
        var response = new RegistrationResponseDto();
        _candidateIntentionRegistrationSvc.SendForAttestation(Arg.Is<long>(1)).Returns(response);

        // Act
        var result = await _controller.SendForAttestation(1);

        // Assert
        Assert.That(result, Is.Not.Null);
        await _candidateIntentionRegistrationSvc.Received(1).SendForAttestation(Arg.Is<long>(1));
    }
    #endregion
    #region Private
    #endregion
}

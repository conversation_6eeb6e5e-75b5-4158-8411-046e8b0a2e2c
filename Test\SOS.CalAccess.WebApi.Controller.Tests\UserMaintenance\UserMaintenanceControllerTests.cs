using NSubstitute;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;
using SOS.CalAccess.Services.WebApi.UserMaintenance;

namespace SOS.CalAccess.WebApi.Tests.UserMaintenance;

/// <summary>
/// User maintenance controller tests
/// </summary>
[TestFixture]
public class UserMaintenanceControllerTests
{

    private UserMaintenanceController _controller;
    private IUserMaintenanceSvc _userMaintenanceSvc;

    /// <summary>
    /// Setup dependencies
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _userMaintenanceSvc = Substitute.For<IUserMaintenanceSvc>();
        _controller = new UserMaintenanceController(_userMaintenanceSvc);
    }

    /// <summary>
    /// Get user account details by user id
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task GetUserAccountDetailsByUserId_ShouldReturnResult_WithExpectedData()
    {
        // Arrange
        long userId = 1;

        AddressDto addressDto = new()
        {
            Street = "test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Type = "Test type"
        };

        List<PhoneNumberDto> phoneNumberDto = new()
            {
                new(){
                CountryCode = "Test country code",
                Extension = "Test extension",
                Id = 1,
                InternationalNumber = true,
                Number = "Test number",
                SelectedCountry = 1,
                SetAsPrimaryPhoneNumber = true,
                Type = "Test type",
                }
            };

        UserAccountDetailsDto userDto = new()
        {
            Id = userId,
            Email = "Test email",
            FirstName = "Test first name",
            LastName = "Test last name",
            UserName = "Test user name",
            AddressDto = addressDto,
            PhoneNumberDto = phoneNumberDto
        };

        // Act
        var userDetails = _userMaintenanceSvc.GetUserAccountDetailsByUserId(Arg.Any<long>()).Returns(userDto);

        var result = await _controller.GetUserAccountDetailsByUserId(userId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EqualTo(userDto));
    }

    /// <summary>
    /// Update user
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task UpdateUser_WithExpectedData()
    {
        // Arrange
        long userId = 1;

        AddressDto addressDto = new()
        {
            Street = "test street",
            City = "Test city",
            State = "Test state",
            Zip = "Test zip",
            Country = "Test country",
            Type = "Test type"
        };

        List<PhoneNumberDto> phoneNumberDto = new()
            {
                new(){
                CountryCode = "Test country code",
                Extension = "Test extension",
                Id = 1,
                InternationalNumber = true,
                Number = "Test number",
                SelectedCountry = 1,
                SetAsPrimaryPhoneNumber = true,
                Type = "Test type",
                }
            };

        UserAccountDetailsDto userDto = new()
        {
            Id = userId,
            Email = "Test email",
            FirstName = "Test first name",
            LastName = "Test last name",
            UserName = "Test user name",
            AddressDto = addressDto,
            PhoneNumberDto = phoneNumberDto
        };

        // Act
        await _controller.UpdateUser(userDto);

        // Assert
        await _userMaintenanceSvc.Received(1).UpdateUser(userDto);
    }

    /// <summary>
    /// Get user by id
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task GetUserById_ShouldReturnResult_WithExpectedData()
    {
        // Arrange
        long userId = 1;

        UserAccountDetailsDto userDto = new()
        {
            Id = userId,
            Email = "Test email",
            FirstName = "Test first name",
            LastName = "Test last name",
            UserName = "Test user name",
        };

        User? userObject = new()
        {
            Id = userId,
            EmailAddress = userDto.Email,
            FirstName = userDto.FirstName,
            LastName = userDto.LastName,
            EntraOid = "123"
        };

        // Act
        var userDetails = _userMaintenanceSvc.GetUserById(Arg.Any<long>()).Returns(userObject);
        var user = await _controller.GetUserById(userId);

        // Assert
        Assert.That(user, Is.Not.Null);
        Assert.That(user, Is.EqualTo(userObject));
    }
}

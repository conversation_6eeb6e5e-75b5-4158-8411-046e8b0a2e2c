using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Filings;

#pragma warning disable S107 // Methods should not have too many parameters
public class FilingSharedServicesDependencies(
    IFilerSvc filerSvc,
    IDecisionsSvc decisionsSvc,
    IAuthorizationSvc authorizationSvc,
    IUserMaintenanceSvc userMaintenanceSvc,
    INotificationSvc notificationSvc,
    IReferenceDataSvc referenceDataSvc,
    ILinkageSvc linkageSvc,
    IDateTimeSvc dateTimeSvc)
#pragma warning restore S107 // Methods should not have too many parameters
{
    public IFilerSvc FilerSvc { get; set; } = filerSvc;
    public IDecisionsSvc DecisionsSvc { get; } = decisionsSvc;
    public IAuthorizationSvc AuthorizationSvc { get; } = authorizationSvc;
    public IUserMaintenanceSvc UserMaintenanceSvc { get; } = userMaintenanceSvc;
    public INotificationSvc NotificationSvc { get; } = notificationSvc;
    public IReferenceDataSvc ReferenceDataSvc { get; } = referenceDataSvc;
    public ILinkageSvc LinkageSvc { get; } = linkageSvc;
    public IDateTimeSvc DateTimeSvc { get; set; } = dateTimeSvc;
}

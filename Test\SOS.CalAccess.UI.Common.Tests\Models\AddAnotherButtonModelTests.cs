using System.ComponentModel.DataAnnotations;
using NUnit.Framework;
using SOS.CalAccess.UI.Common.Models;

namespace SOS.CalAccess.UI.Common.Tests.Models;

/// <summary>
/// Add another button model
/// </summary>
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
public class AddAnotherButtonModelTests
{
    /// <summary>
    /// Validate model
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private static List<ValidationResult> ValidateModel(object model)
    {
        var results = new List<ValidationResult>();
        var context = new ValidationContext(model, null, null);
        Validator.TryValidateObject(model, context, results, true);
        return results;
    }

    /// <summary>
    /// Add another button with valid data
    /// </summary>
    [Test]
    public void AddAnotherButtonModel_WithValidData_ShouldBeValid()
    {
        // Act
        var model = new AddAnotherButtonModel
        {
            CssClass = "Test css",
            IconClass = "Test icon",
            Name = "Test name",
            Text = "Test text",
            Type = "Test type",
            Value = "Test value",
            Id = "Test id",
        };

        // Arrange
        var results = ValidateModel(model);
        // Assert
        Assert.That(results, Is.Empty);
    }

    /// <summary>
    /// Add another button with missing data
    /// </summary>
    [Test]
    public void AddAnotherButtonModel_WithMissingData_ShouldBeValid()
    {
        // Act
        var model = new AddAnotherButtonModel
        {
            CssClass = "Test css",
            IconClass = "Test icon",
            Name = "Test name",
            Text = "Test text",
            Type = "Test type",
        };

        // Arrange
        var results = ValidateModel(model);
        // Assert
        Assert.That(results, Is.Empty);
    }
}

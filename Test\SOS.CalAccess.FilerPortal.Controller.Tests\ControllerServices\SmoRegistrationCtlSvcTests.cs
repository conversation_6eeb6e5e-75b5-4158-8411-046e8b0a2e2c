using System.Reflection;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using Moq;
using NSubstitute;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.ControllerServices.SharedServices;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.Registrations.SmoRegistration;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Mapping;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Constants;
using SOS.CalAccess.UI.Common.Services;
using RegistrationModels = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.FilerPortal.Tests.ControllerServices;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
public class SmoRegistrationCtlSvcTests
{
    private Mock<ISmoRegistrationSvc> _mockSmoRegistrationSvc;
    private Mock<IStringLocalizer<SharedResources>> _mockLocalizer;
    private Mock<Generated.IUsersApi> _mockUsersApi;
    private Mock<Generated.IFilersApi> _mockFilersApi;
    private Mock<IAccuMailValidatorService> _mockAccuMailValidatorSvc;
    private SmoRegistrationCtlSvc _smoRegistrationCtlSvc;
    private Mock<ISharedSmoRegistrationCtlSvc> _sharedSmoRegistrationCtlSvcMock;
    private Mock<IDecisionsValidationMapService> _decisionsValidationMapServiceMock;
    private IDateTimeSvc _dateTimeSvcMock;

    [SetUp]
    public void Setup()
    {
        _mockSmoRegistrationSvc = new Mock<ISmoRegistrationSvc>();
        _mockLocalizer = new Mock<IStringLocalizer<SharedResources>>();
        _mockUsersApi = new Mock<Generated.IUsersApi>();
        _mockFilersApi = new Mock<Generated.IFilersApi>();
        _mockAccuMailValidatorSvc = new Mock<IAccuMailValidatorService>();
        _sharedSmoRegistrationCtlSvcMock = new Mock<ISharedSmoRegistrationCtlSvc>();
        _decisionsValidationMapServiceMock = new Mock<IDecisionsValidationMapService>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();

        var localizedString = new LocalizedString("key", "text");
        _mockLocalizer
            .Setup(x => x[It.IsAny<string>()]).Returns(localizedString);

        _smoRegistrationCtlSvc = new SmoRegistrationCtlSvc(
            _mockSmoRegistrationSvc.Object,
            _dateTimeSvcMock,
            _mockLocalizer.Object,
            _mockUsersApi.Object,
            _mockFilersApi.Object,
            _mockAccuMailValidatorSvc.Object,
            _sharedSmoRegistrationCtlSvcMock.Object,
            _decisionsValidationMapServiceMock.Object);
    }

    [Test]
    public async Task GetPage03ViewModel_WhenRecordFound_ReturnsViewModel()
    {
        // Arrange
        long id = 1;
        var response = new Services.Business.FilerRegistration.Registrations.Models.SmoRegistrationResponseDto
        {
            Id = 1,
        };
        _mockSmoRegistrationSvc
            .Setup(x => x.GetRegistrationFilingById(It.IsAny<long>()))
            .ReturnsAsync(response);

        // Act
        var result = await _smoRegistrationCtlSvc.GetPage03ViewModel(id, new CancellationToken());

        // Assert
        Assert.That(result, Is.InstanceOf<SmoRegistrationStep01ViewModel>());
        Assert.That(result.Id, Is.EqualTo(1));
    }

    [Test]
    public async Task GetPage03ViewModel_WhenRecordNotFound_ReturnsNull()
    {
        // Arrange
        long id = 1;
        var response = new RegistrationModels.SmoRegistrationResponseDto
        {
            Id = 1,
        };
        _mockSmoRegistrationSvc
            .Setup(x => x.GetRegistrationFilingById(It.IsAny<long>()))
            .ReturnsAsync((RegistrationModels.SmoRegistrationResponseDto?)null);

        // Act
        var result = await _smoRegistrationCtlSvc.GetPage03ViewModel(id, new CancellationToken());

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task Page03Submit_WhenNewRegistration_CallsCreateSmoRegistration()
    {
        // Arrange
        var model = new SmoRegistrationStep01ViewModel
        {
            Id = null,
            FaxNumber = new PhoneNumberDto
            {
                Number = "9876543",
                Type = CommonConstants.PhoneNumber.TypeFax,
            },
            PhoneNumber = new PhoneNumberDto
            {
                Number = "9876543",
                Type = CommonConstants.PhoneNumber.TypeHome,
            },
        };
        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto { Id = 123, Valid = true };
        var capturedRequest = new SmoContactRequest();

        _sharedSmoRegistrationCtlSvcMock
            .Setup(x => x.MapSmoViewModelToRequest(It.IsAny<SmoRegistrationStep01ViewModel>()))
            .Returns(capturedRequest);
        _sharedSmoRegistrationCtlSvcMock
            .Setup(x => x.SubmitSmoRegistrationForm(It.IsAny<SmoRegistrationStep01ViewModel>(), It.IsAny<ModelStateDictionary>(), It.IsAny<SmoContactRequest>()))
            .Returns(Task.FromResult(response));

        // Act
        var result = await _smoRegistrationCtlSvc.Page03Submit(model, modelState, true);

        // Assert
        Assert.That(result, Is.EqualTo(123));
    }

    [Test]
    public async Task Page04ContinueSubmit_WhenResponseIsInvalid_AddsErrorsToModelState()
    {
        // Arrange
        var model = new SmoRegistrationDetailsStep01ViewModel { Id = 100 };
        var modelState = new ModelStateDictionary();

        var response = new RegistrationResponseDto
        {
            Id = null,
            Valid = false,
            ValidationErrors = new List<CalAccess.Models.Common.WorkFlowError>
            {
                new("OrganizationLevelOfActivity", "ErrGlobal0001", "Validation", "Invalid organization activity level.")
            }
        };

        _mockSmoRegistrationSvc
            .Setup(svc => svc.UpdateSmoRegistration(It.IsAny<long>(), It.IsAny<SlateMailerOrganizationRequest>()))
            .ReturnsAsync(response);

        _mockLocalizer
            .Setup(loc => loc[It.IsAny<string>()])
            .Returns(new LocalizedString("OrganizationLevelOfActivity", "Organization Level of Activity"));

        // Act
        var result = await _smoRegistrationCtlSvc.Page04ContinueSubmit(model, modelState, true);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.Not.Null);
            var errors = modelState["OrganizationLevelOfActivity"]?.Errors;
            Assert.That(errors, Is.Not.Empty);
        });
    }

    [Test]
    public void AddValidationErrorsToModelStateDynamically_WhenFieldNameExists_AddsErrorToModelState()
    {
        // Arrange
        var errors = new List<CalAccess.Models.Common.WorkFlowError>
        {
            new("Email", "ErrGlobal0001", "Validation", "Invalid email format.")
        };
        var model = new SmoRegistrationStep01ViewModel();
        var modelState = new ModelStateDictionary();

        // Act
        _smoRegistrationCtlSvc.AddValidationErrorsToModelStateDynamically(errors, modelState, model);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(modelState.ContainsKey("Email"), Is.True);
            var emailErrors = modelState["Email"]?.Errors;
            Assert.That(emailErrors, Is.Not.Empty);
            Assert.That(emailErrors?.FirstOrDefault()?.ErrorMessage, Is.EqualTo("Invalid email format."));
        });
    }

    [Test]
    public async Task GetPage06AndPage07ViewModel_WhenResponseWithOfficerRole_IdReturnsViewModel()
    {
        // Arrange
        var smoOfficer = new SmoRegistrationContactDto
        {
            Id = 1,
            FirstName = "First name",
            LastName = "Last name",
            Email = "<EMAIL>",
            PhoneNumber = new PhoneNumberDto
            {
                InternationalNumber = false,
                CountryCode = "+1-USA",
                Extension = "",
                Number = "1112222",
                Type = "Phone"
            },
            Address = new AddressDto
            {
                Id = 1,
                Street = "Street",
                Street2 = "Street2",
                City = "City",
                State = "CA",
                Zip = "90001",
                Type = "Residential",
                Country = "United States",
                Purpose = "Officer"
            }
        };
        _mockSmoRegistrationSvc
            .Setup(svc => svc.GetSmoTreasurerPage02(123))
            .ReturnsAsync(smoOfficer);

        _mockSmoRegistrationSvc
            .Setup(svc => svc.GetCurrentUserRoleByIdAsync(It.IsAny<long>()))
            .ReturnsAsync(96);

        // Act
        var result = await _smoRegistrationCtlSvc.GetPage06AndPage07ViewModel(123, default);

        // Assert
        Assert.That(result, Is.InstanceOf<SmoRegistrationStep02ViewModel>());
    }

    [Test]
    public async Task GetPage06AndPage07ViewModel_WhenResponseWithTreasurerRole_IdReturnsViewModel()
    {
        // Arrange
        var smoOfficer = new SmoRegistrationContactDto
        {
            Id = 1,
            FirstName = "First name",
            LastName = "Last name",
            Email = "<EMAIL>",
            PhoneNumber = new PhoneNumberDto
            {
                InternationalNumber = false,
                CountryCode = "+1-USA",
                Extension = "",
                Number = "1112222",
                Type = "Phone"
            },
            Address = new AddressDto
            {
                Id = 1,
                Street = "Street",
                Street2 = "Street2",
                City = "City",
                State = "CA",
                Zip = "90001",
                Type = "Residential",
                Country = "United States",
                Purpose = "Officer"
            }
        };
        _mockSmoRegistrationSvc
            .Setup(svc => svc.GetSmoTreasurerPage02(123))
            .ReturnsAsync(smoOfficer);

        _mockSmoRegistrationSvc
            .Setup(svc => svc.GetCurrentUserRoleByIdAsync(It.IsAny<long>()))
            .ReturnsAsync(93);

        // Act
        var result = await _smoRegistrationCtlSvc.GetPage06AndPage07ViewModel(123, default);

        // Assert
        Assert.That(result, Is.InstanceOf<SmoRegistrationStep02ViewModel>());
    }

    [Test]
    public async Task Page07Prefill_WhenResponse_IsTreasurerReturnsModel()
    {
        // Arrange
        var userItemResponse = new Generated.UserItemResponse
        (
            "First Name",
            "Last Name",
            1,
            "<EMAIL>"
        );
        var modelState = new ModelStateDictionary();
        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto { Id = 123, Valid = true, StatusId = 1 };
        _mockUsersApi
            .Setup(svc => svc.GetSelf(default))
            .ReturnsAsync(userItemResponse);

        // Act
        var result = await _smoRegistrationCtlSvc.PagePrefill(1, null, true, default);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Page07Prefill_WhenResponse_IsTreasurerReturnsNull()
    {
        // Arrange
        var modelState = new ModelStateDictionary();
        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto { Id = 123, Valid = true, StatusId = 1 };
        // Act
        var result = await _smoRegistrationCtlSvc.PagePrefill(1, null, false, default);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task Page07Submit_WhenResponse_IsTreasurerReturnsNullErrors()
    {
        // Arrange
        var modelState = new ModelStateDictionary();
        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto { Id = 123, Valid = true, StatusId = 1 };

        // Act
        var result = await _smoRegistrationCtlSvc.PagePrefill(1, null, null, default);

        // Assert
        Assert.That(result, Is.Null);
    }


    [Test]
    public async Task Page07Submit_WhenResponse_CallsCreateSmoOfficer()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel { Id = 123, IsUserTreasurer = true };
        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto { Id = 123, Valid = true, StatusId = 1 };
        _mockSmoRegistrationSvc
            .Setup(svc => svc.PostSmoRegistrationContactsPage05(123, It.IsAny<SmoRegistrationContactDto>(), It.IsAny<bool>()))
            .ReturnsAsync(response);
        var userItemResponse = new Generated.UserItemResponse
        (
            "First Name",
            "Last Name",
            1,
            "<EMAIL>"
        );
        _mockUsersApi
            .Setup(svc => svc.GetSelf(default))
            .ReturnsAsync(userItemResponse);

        // Act
        var result = await _smoRegistrationCtlSvc.Page07Or10Submit(model, modelState, default, true);

        // Assert
        Assert.That(result, Is.EqualTo(123));
    }

    [Test]
    public async Task Page07Submit_WhenResponse_CallsUpdateSmoOfficer()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel { Id = 123, ContactId = 123, ContactTitle = "PrincipalOfficer" };
        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto { Id = 123, Valid = true, StatusId = 1 };
        _mockSmoRegistrationSvc
            .Setup(svc => svc.PostSmoRegistrationContactsPage05(123, It.IsAny<SmoRegistrationContactDto>(), It.IsAny<bool>()))
            .ReturnsAsync(response);
        var userItemResponse = new Generated.UserItemResponse
        (
            "First Name",
            "Last Name",
            1,
            "<EMAIL>"
        );
        _mockUsersApi
            .Setup(svc => svc.GetSelf(default))
            .ReturnsAsync(userItemResponse);

        // Act
        var result = await _smoRegistrationCtlSvc.Page07Or10Submit(model, modelState, default, true);

        // Assert
        Assert.That(result, Is.EqualTo(123));
    }

    [Test]
    public async Task Page07Submit_WhenResponse_IsInvalid()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel { Id = 123, ContactId = 123, ContactTitle = "Chairperson" };
        var modelState = new ModelStateDictionary();
        var validationErrors = new List<CalAccess.Models.Common.WorkFlowError>
        {
            new("Email", "ErrGlobal0001", "Validation", "Invalid email format")
        };
        var response = new RegistrationResponseDto { Id = 123, Valid = false, StatusId = 1, ValidationErrors = validationErrors };
        _mockSmoRegistrationSvc
            .Setup(svc => svc.PostSmoRegistrationContactsPage05(123, It.IsAny<SmoRegistrationContactDto>(), It.IsAny<bool>()))
            .ReturnsAsync(response);
        var userItemResponse = new Generated.UserItemResponse
        (
            "First Name",
            "Last Name",
            1,
            "<EMAIL>"
        );
        _mockUsersApi
            .Setup(svc => svc.GetSelf(default))
            .ReturnsAsync(userItemResponse);

        // Act
        var result = await _smoRegistrationCtlSvc.Page07Or10Submit(model, modelState, default, true);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.EqualTo(123));
            Assert.That(modelState.IsValid, Is.False);
        });
    }

    [Test]
    [TestCase(null)]
    [TestCase(1)]
    public async Task GetPage04ViewModel_ValidId_ReturnsViewModel(long? filerId)
    {
        // Arrange
        var validId = 123; // A valid ID to simulate a proper response
        var mockResponse = new RegistrationModels.SmoRegistrationResponseDto
        {
            Id = 123,
            ActivityLevel = "city",
            QualifiedCommittee = true,
            CampaignCommittee = true,
            DateQualified = DateTime.Now,
            CommitteeType = "Political",
            FilerId = filerId,
        };

        // Mock the service call to return a valid response for the provided ID
        _mockSmoRegistrationSvc.Setup(svc => svc.GetRegistrationFilingById(validId))
            .ReturnsAsync(mockResponse);

        // Act
        var result = await _smoRegistrationCtlSvc.GetPage04ViewModel(validId, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(validId));  // Ensure the Id matches the one provided
            Assert.That(result.OrganizationLevelOfActivity, Is.EqualTo(mockResponse.ActivityLevel));  // Verify ActivityLevel mapping
        });
        Assert.Multiple(() =>
        {
            Assert.That(result.IsOrganizationQualified, Is.EqualTo(mockResponse.QualifiedCommittee));  // Verify QualifiedCommittee mapping
            Assert.That(result.IsOrganizationCampaignCommittee, Is.EqualTo(mockResponse.CampaignCommittee));  // Verify CampaignCommittee mapping
        });
        Assert.Multiple(() =>
        {
            Assert.That(result.DateQualifiedAsSMO, Is.EqualTo(mockResponse.DateQualified));  // Verify DateQualifiedAsSMO mapping
        });
    }

    [Test]
    public async Task GetPage08ViewModel_WhenResponse_GetSmoOfficers()
    {
        // Arrange
        var registrationId = 1;
        var smoOfficer = new List<SmoOfficerGridDto>
        {
            new ()
            {
                CanAuthorize = true,
                OfficerName = "Officer Name",
                Id = 1,
                Title = "President",
                Email = "<EMAIL>",
                StartDate = DateTime.Now,
                PhoneNumber = new PhoneNumberDto
                {
                    InternationalNumber = false,
                    CountryCode = "+1",
                    Extension = "123",
                    Number = "4567890",
                    Type = "Phone"
                },
            }
        };
        var userResponse = new Generated.UserItemResponse("<EMAIL>", "Test", registrationId, "Test");
        var smoResponse = new SmoRegistrationResponseDto
        {
            Id = registrationId,
            FilerId = 1,
            Email = "Test",
        };
        var filerRole = new Generated.FilerRole(true, 0, "", default!, 1, 1, false, false, 0, FilerRole.SlateMailerOrg_PrincipalOfficer.Name, default!);
        var filerUserResponse = new Generated.FilerUserDto(1, filerRole, 1, 1, default!, 1);

        _mockUsersApi
            .Setup(x => x.GetSelf(It.IsAny<CancellationToken>()))
            .ReturnsAsync(userResponse);
        _mockSmoRegistrationSvc
            .Setup(x => x.GetRegistrationFilingById(It.IsAny<long>()))
            .ReturnsAsync(smoResponse);
        _mockFilersApi
            .Setup(x => x.GetFilerUserByUserIdAsync(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(filerUserResponse);
        _mockSmoRegistrationSvc
            .Setup(svc => svc.GetSmoOfficers(It.IsAny<long>()))
            .ReturnsAsync(smoOfficer);

        // Act
        var result = await _smoRegistrationCtlSvc.GetPage08ViewModel(registrationId, default);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoRegistrationStep02ViewModel>());
            Assert.That(result?.Id, Is.EqualTo(registrationId));
        });
    }

    [Test]
    public async Task GetPage08ViewModel_WhenResponse_GetSmoOfficers_NoOfficer_ReturnEmptyView()
    {
        // Arrange
        var registrationId = 1;
        var smoOfficer = new List<SmoOfficerGridDto>
        {
            new ()
            {
                CanAuthorize = true,
                OfficerName = "Officer Name",
                Id = 1,
                Title = "President",
                Email = "<EMAIL>",
                StartDate = DateTime.Now,
                PhoneNumber = new PhoneNumberDto
                {
                    InternationalNumber = false,
                    CountryCode = "+1",
                    Extension = "123",
                    Number = "4567890",
                    Type = "Phone"
                },
                Role = FilerRole.SlateMailerOrg_AccountManager.Name
            }
        };
        var userResponse = new Generated.UserItemResponse("<EMAIL>", "Test", registrationId, "Test");
        var smoResponse = new SmoRegistrationResponseDto
        {
            Id = registrationId,
            FilerId = 1,
            Email = "Test",
        };
        var filerRole = new Generated.FilerRole(true, 0, "", default!, 1, 1, false, false, 0, FilerRole.SlateMailerOrg_PrincipalOfficer.Name, default!);
        var filerUserResponse = new Generated.FilerUserDto(1, filerRole, 1, 1, default!, 1);

        _mockUsersApi
            .Setup(x => x.GetSelf(It.IsAny<CancellationToken>()))
            .ReturnsAsync(userResponse);
        _mockSmoRegistrationSvc
            .Setup(x => x.GetRegistrationFilingById(It.IsAny<long>()))
            .ReturnsAsync(smoResponse);
        _mockFilersApi
            .Setup(x => x.GetFilerUserByUserIdAsync(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(filerUserResponse);
        _mockSmoRegistrationSvc
            .Setup(svc => svc.GetSmoOfficers(It.IsAny<long>()))
            .ReturnsAsync(smoOfficer);

        // Act
        var result = await _smoRegistrationCtlSvc.GetPage08ViewModel(registrationId, default);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoRegistrationStep02ViewModel>());
            Assert.That(result?.Id, Is.EqualTo(registrationId));
        });
    }

    [Test]
    public void Page07Or0GetEmptyViewModel_ReturnsDefaultViewModel()
    {
        // Arrange
        long id = 1;

        // Act
        var result = _smoRegistrationCtlSvc.Page07Or10GetEmptyViewModel(id);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.Id, Is.EqualTo(1));
            Assert.That(result.Addresses, Has.Count.EqualTo(1));
        });
    }

    [Test]
    public void Page13GetEmptyViewModel_ReturnsDefaultViewModel()
    {
        // Arrange
        long id = 1;

        // Act
        var result = _smoRegistrationCtlSvc.Page13GetEmptyViewModel(id);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.Id, Is.EqualTo(1));
            Assert.That(result.Addresses, Has.Count.EqualTo(1));
        });
    }

    [Test]
    public void Page13Save_WhenModelIdIsNull_ThrowsKeyNotFoundException()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel { Id = null };
        var modelState = new ModelStateDictionary();

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _smoRegistrationCtlSvc.Page13Save(model, modelState, CancellationToken.None));
        Assert.That(ex.Message, Is.EqualTo("No id exists for registration"));
    }

    [Test]
    public async Task Page13Save_WhenResponseIsValid_ReturnsResponseId()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel { Id = 123, ContactTitle = "AssistantTreasurer" };
        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto { Id = 456, Valid = true };

        _ = _mockSmoRegistrationSvc
            .Setup(svc => svc.PostSmoRegistrationContactsPage05(It.IsAny<long>(), It.IsAny<SmoRegistrationContactDto>(), It.IsAny<bool>()))
            .ReturnsAsync(response);


        // Act
        var result = await _smoRegistrationCtlSvc.Page13Save(model, modelState, CancellationToken.None);

        // Assert
        Assert.That(result, Is.EqualTo(456));
    }

    [Test]
    public async Task Page13Save_WhenResponseIsInvalid_AddsErrorsToModelState()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel { Id = 123 };
        var modelState = new ModelStateDictionary();
        var validationErrors = new List<CalAccess.Models.Common.WorkFlowError>
        {
            new("Email", "ErrGlobal0001", "Validation", "Invalid email format")
        };
        var response = new RegistrationResponseDto
        {
            Id = 123,
            Valid = false,
            ValidationErrors = validationErrors
        };

        _ = _mockSmoRegistrationSvc
            .Setup(svc => svc.PostSmoRegistrationContactsPage05(It.IsAny<long>(), It.IsAny<SmoRegistrationContactDto>(), It.IsAny<bool>()))
            .ReturnsAsync(response);

        // Act
        await _smoRegistrationCtlSvc.Page13Save(model, modelState, CancellationToken.None);

        // Assert
        Assert.That(modelState["Email"]?.Errors[0].ErrorMessage, Is.EqualTo("Invalid email format"));
    }

    [Test]
    public async Task Page13Save_WhenResponseIsInvalidAndNoErrors_DoesNotAddModelErrors()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel { Id = 123 };
        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto
        {
            Id = 123,
            Valid = false,
            ValidationErrors = new List<CalAccess.Models.Common.WorkFlowError>()
        };

        _mockSmoRegistrationSvc
            .Setup(svc => svc.PostSmoRegistrationContactsPage05(It.IsAny<long>(), It.IsAny<SmoRegistrationContactDto>(), It.IsAny<bool>()))
            .ReturnsAsync(response);

        // Act
        var result = await _smoRegistrationCtlSvc.Page13Save(model, modelState, CancellationToken.None);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(modelState.IsValid); // Should have no errors
            Assert.That(result, Is.EqualTo(123)); // Returns original ID
        });
    }

    [Test]
    public void GetPage14ViewModel_WhenIdIsNull_ThrowException()
    {
        // Arrange
        var isAuthorizedToCompleteTreasurerAck = true;

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _smoRegistrationCtlSvc.GetPage14ViewModel(null, isAuthorizedToCompleteTreasurerAck, It.IsAny<CancellationToken>()));
        Assert.That(ex.Message, Is.EqualTo($"No id exists for registration"));
    }

    [Test]
    public void GetPage14ViewModel_WhenIdIsInvalid_ThrowException()
    {
        // Arrange
        var isAuthorizedToCompleteTreasurerAck = true;

        // Act & Assert
        var ex = Assert.ThrowsAsync<ArgumentException>(() => _smoRegistrationCtlSvc.GetPage14ViewModel(1, isAuthorizedToCompleteTreasurerAck, It.IsAny<CancellationToken>()));
        Assert.That(ex.Message, Is.EqualTo($"Invalid ID."));
    }

    [TestCaseSource(nameof(GetPage14ViewModelTestCases))]
    public async Task GetPage14ViewModel_ReturnViewModel(List<SmoRegistrationContactDto> contacts, string role)
    {
        // Arrange
        var registrationId = 1;
        var response = new TreasurerAcknowledgementContactResponseDto
        {
            FilerId = 1,
            Contacts = contacts
        };
        var userResponse = new Generated.UserItemResponse("<EMAIL>", "Test", registrationId, "Test");
        var smoResponse = new SmoRegistrationResponseDto
        {
            Id = registrationId,
            FilerId = 1,
            Email = "Test",
        };
        var filerRole = new Generated.FilerRole(true, 0, "", default!, 1, 1, false, false, 0, role, default!);
        var filerUserResponse = new Generated.FilerUserDto(1, filerRole, 1, 1, default!, 1);
        var isAuthorizedToCompleteTreasurerAck = true;

        _mockSmoRegistrationSvc
            .Setup(x => x.GetTreasurerAcknowledgementContactsAsync(It.IsAny<long>()))
            .ReturnsAsync(response);
        _mockUsersApi
            .Setup(x => x.GetSelf(It.IsAny<CancellationToken>()))
            .ReturnsAsync(userResponse);
        _mockSmoRegistrationSvc
            .Setup(x => x.GetRegistrationFilingById(It.IsAny<long>()))
            .ReturnsAsync(smoResponse);
        _mockFilersApi
            .Setup(x => x.GetFilerUserByUserIdAsync(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(filerUserResponse);

        // Act
        var result = await _smoRegistrationCtlSvc.GetPage14ViewModel(registrationId, isAuthorizedToCompleteTreasurerAck, It.IsAny<CancellationToken>());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoRegistrationStep04ViewModel>());
            Assert.That(result.Id, Is.EqualTo(registrationId));
        });
    }

    [Test]
    public void Page14Submit_WhenIdIsNull_ThrowException()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel { };

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _smoRegistrationCtlSvc.Page14Submit(model));
        Assert.That(ex.Message, Is.EqualTo($"No id exists for registration"));
    }

    [TestCaseSource(nameof(GetPage14ViewModelTestCases))]
    public async Task Page14Submit_WhenModelIsValide_ShouldSubmitSuccessfullyAndReturnModel(List<SmoRegistrationContactDto> contacts, string _)
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
        };
        var response = new TreasurerAcknowledgementContactResponseDto
        {
            FilerId = 1,
            Contacts = contacts
        };

        _mockSmoRegistrationSvc
            .Setup(x => x.CompleteTreasurerAcknowledgementAsync(It.IsAny<long>()));
        _mockSmoRegistrationSvc
            .Setup(x => x.GetTreasurerAcknowledgementContactsAsync(It.IsAny<long>()))
            .ReturnsAsync(response);

        // Act
        var result = await _smoRegistrationCtlSvc.Page14Submit(model);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoRegistrationStep04ViewModel>());
            Assert.That(result.Id, Is.EqualTo(model.Id));
        });
    }

    [Test]
    public void Page14SendForAcknowledgement_WhenIdIsNull_ThrowException()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel { };

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _smoRegistrationCtlSvc.Page14SendForAcknowledgement(model));
        Assert.That(ex.Message, Is.EqualTo($"No id exists for registration"));
    }

    [Test]
    public async Task Page14SendForAcknowledgement_WhenModelIsValid_ShouldSendNotification()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
        };
        _mockSmoRegistrationSvc.Setup(x => x.SendAcknowledgementNotificationsAsync(It.IsAny<long>()));

        // Act
        await _smoRegistrationCtlSvc.Page14SendForAcknowledgement(model);

        // Assert
        _mockSmoRegistrationSvc.Verify(x => x.SendAcknowledgementNotificationsAsync(It.IsAny<long>()), Times.Once);
    }

    [Test]
    public async Task Page13Prefill_WhenResponse_ReturnsViewModel()
    {
        // Arrange
        _ = _mockSmoRegistrationSvc.Setup(svc => svc.GetSmoOfficer(It.IsAny<long>(), It.IsAny<long>())).ReturnsAsync(new RegistrationModels.SmoRegistrationContactDto());

        // Act
        var result = await _smoRegistrationCtlSvc.GetPage13ViewModel(1, 1, default);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Page13Prefill_WhenResponse_ReturnsNull()
    {
        // Act
        var result = await _smoRegistrationCtlSvc.GetPage13ViewModel(1, 1, default);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task PagePrefill_WhenContactIdNotNull_ReturnsViewModel()
    {
        // Arrange
        _ = _mockSmoRegistrationSvc.Setup(svc => svc.GetSmoOfficer(It.IsAny<long>(), It.IsAny<long>())).ReturnsAsync(new RegistrationModels.SmoRegistrationContactDto());

        // Act
        var result = await _smoRegistrationCtlSvc.PagePrefill(1, 1, null, default);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task GetPage12ViewModel_WhenResponse_ReturnsViewModel()
    {
        var mockData = new List<SmoOfficerGridDto>
        {
            new()
            {
                Id = 1,
                FirstName = "Test",
                LastName = "Test1",
                CanAuthorize = true,
                AcknowledgedOn = DateTime.Now,
                Title = FilerRole.SlateMailerOrg_Treasurer.Name,
            }
        };
        // Arrange
        _ = _mockSmoRegistrationSvc.Setup(svc => svc.GetSmoOfficers(It.IsAny<long>())).ReturnsAsync(mockData);

        // Act
        var result = await _smoRegistrationCtlSvc.GetPage12ViewModel(1, default);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task GetPage12ViewModel_WhenResponse_ReturnsEmptyViewModel()
    {
        var mockData = new List<SmoOfficerGridDto>
        {
            new()
            {
                Id = 1,
                FirstName = "Test",
                LastName = "Test1",
                CanAuthorize = false,
                AcknowledgedOn = DateTime.Now,
                Title = FilerRole.SlateMailerOrg_Treasurer.Name,
            }
        };
        // Arrange
        _ = _mockSmoRegistrationSvc.Setup(svc => svc.GetSmoOfficers(It.IsAny<long>())).ReturnsAsync(mockData);

        // Act
        var result = await _smoRegistrationCtlSvc.GetPage12ViewModel(1, default);

        // Assert
        Assert.That(result, Is.InstanceOf<SmoRegistrationStep03ViewModel>());
    }

    [Test]
    public async Task IsTreasurer_WhenResponse_ReturnsNoError()
    {
        // Arrange
        var mockData = new SmoRegistrationContactDto()
        {
            Id = 1,
            FirstName = "Test",
            LastName = "Test1",
            CanAuthorize = true,
            AcknowledgedOn = DateTime.Now,
            Title = FilerRole.SlateMailerOrg_Treasurer.Name,
            Role = FilerRole.SlateMailerOrg_Treasurer.Name,
        };
        _ = _mockSmoRegistrationSvc.Setup(svc => svc.GetSmoOfficer(It.IsAny<long>(), It.IsAny<long>())).ReturnsAsync(mockData);

        // Act
        var result = await _smoRegistrationCtlSvc.IsTreasurer(1, 1, default);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Page12DeleteIndividualAuthorizer_WhenValidId_ReturnsUpdatedViewModel()
    {
        // Arrange
        var individualAuthorizerId = 1;
        var registrationId = 123;
        var mockViewModel = new SmoRegistrationStep03ViewModel
        {
            Id = registrationId,
            ContactTitle = "Authorized User"
        };

        _mockSmoRegistrationSvc
            .Setup(svc => svc.DeleteIndividualAuthorizer(registrationId, individualAuthorizerId));

        _mockSmoRegistrationSvc
            .Setup(svc => svc.GetSmoOfficers(registrationId))
            .ReturnsAsync(new List<SmoOfficerGridDto>
            {
                new()
                {
                    Id = 1,
                    FirstName = "Test",
                    MiddleName = "Middle",
                    LastName = "User",
                    CanAuthorize = true,
                    Title = "Authorized User"
                }
            });

        // Act
        var result = await _smoRegistrationCtlSvc.Page12DeleteIndividualAuthorizer(registrationId, individualAuthorizerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoRegistrationStep03ViewModel>());
            Assert.That(result!.Id, Is.EqualTo(registrationId));
        });
    }

    [Test]
    public async Task Page12DeleteIndividualAuthorizer_WhenInvalidId_ReturnsEmptyViewModel()
    {
        // Arrange
        var individualAuthorizerId = 1;
        var registrationId = 1;

        _mockSmoRegistrationSvc
            .Setup(svc => svc.DeleteIndividualAuthorizer(registrationId, individualAuthorizerId));

        // Act
        var result = await _smoRegistrationCtlSvc.Page12DeleteIndividualAuthorizer(registrationId, individualAuthorizerId);

        // Assert
        Assert.That(result, Is.InstanceOf<SmoRegistrationStep03ViewModel>());
    }

    [Test]
    public void Page15GetViewModell_WhenIdIsNull_ThrowException()
    {
        // Arrange
        var isAuthorizedToAttest = true;

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _smoRegistrationCtlSvc.GetPage15ViewModel(null, isAuthorizedToAttest));
        Assert.That(ex.Message, Is.EqualTo($"No id exists for registration"));
    }

    [Test]
    public void Page15GetViewModel_WhenIdIsInvalid_ThrowException()
    {
        // Arrange
        var isAuthorizedToAttest = true;

        // Act & Assert
        var ex = Assert.ThrowsAsync<ArgumentException>(() => _smoRegistrationCtlSvc.GetPage15ViewModel(1, isAuthorizedToAttest));
        Assert.That(ex.Message, Is.EqualTo($"Invalid ID."));
    }

    [TestCaseSource(nameof(GetPage14ViewModelTestCases))]
    public async Task GetPage15ViewModel_ReturnViewModel(List<SmoRegistrationContactDto> contacts, string role)
    {
        // Arrange
        var registrationId = 1;
        var userResponse = new Generated.UserItemResponse("<EMAIL>", "Test", registrationId, "Test");
        var smoResponse = new SmoRegistrationResponseDto
        {
            Id = registrationId,
            FilerId = 1,
            Email = "Test",
        };
        var attestationResponse = new SmoRegistrationAttestationResponseDto
        {
            FirstName = "Test",
            LastName = "Test",
            ExecutedAt = DateTime.Now,
            Title = "Treasurer",
        };
        ContactTitleRoleMapping.TitleOrRoleNameToRoleId.TryGetValue(role, out var roleId);
        var filerRole = new Generated.FilerRole(true, 0, "", default!, roleId, 1, false, false, 0, role, default!);
        var filerUserResponse = new Generated.FilerUserDto(1, filerRole, roleId, 1, default!, 1);
        var isAuthorizedToAttest = true;

        _mockUsersApi
            .Setup(x => x.GetSelf(It.IsAny<CancellationToken>()))
            .ReturnsAsync(userResponse);
        _mockSmoRegistrationSvc
            .Setup(x => x.GetRegistrationFilingById(It.IsAny<long>()))
            .ReturnsAsync(smoResponse);
        _mockSmoRegistrationSvc
            .Setup(x => x.GetResponsibleOfficerContactsAsync(It.IsAny<long>()))
            .ReturnsAsync(contacts);
        _mockSmoRegistrationSvc
            .Setup(x => x.GetRegistrationAttestationAsync(It.IsAny<long>()))
            .ReturnsAsync(attestationResponse);
        _mockFilersApi
            .Setup(x => x.GetFilerUserByUserIdAsync(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(filerUserResponse);

        // Act
        var result = await _smoRegistrationCtlSvc.GetPage15ViewModel(registrationId, isAuthorizedToAttest);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoRegistrationStep04ViewModel>());
            Assert.That(result.Id, Is.EqualTo(registrationId));
        });
    }

    [Test]
    public void Page15AttestRegistration_WhenIdIsNull_ThrowException()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel { };
        var modelState = new ModelStateDictionary();

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _smoRegistrationCtlSvc.Page15AttestRegistration(model, modelState));
        Assert.That(ex.Message, Is.EqualTo($"No id exists for registration"));
    }

    [Test]
    public async Task Page15AttestRegistration_WhenModelIsValid_ShouldAttestRegistrationSuccessfully()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
        };
        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto
        {
            Valid = true,
        };

        _mockSmoRegistrationSvc.Setup(x => x.AttestRegistrationAsync(It.IsAny<long>())).ReturnsAsync(response);

        // Act
        await _smoRegistrationCtlSvc.Page15AttestRegistration(model, modelState);

        // Assert
        _mockSmoRegistrationSvc.Verify(x => x.AttestRegistrationAsync(It.IsAny<long>()), Times.Once);
        Assert.That(modelState.IsValid, Is.True);
    }

    [Test]
    public async Task Page15AttestRegistration_WhenModelIsInvalid_ShouldAddErrorToModel()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
        };
        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto
        {
            Valid = false,
            ValidationErrors = new List<CalAccess.Models.Common.WorkFlowError>
            {
                new("Test", "ErrGlobal0001", "Validation", "Test." ),
                new("Test", "ErrGlobal0001", "Validation", "Missing required field {{Field Name}}." ),

            }
        };

        _mockSmoRegistrationSvc.Setup(x => x.AttestRegistrationAsync(It.IsAny<long>())).ReturnsAsync(response);

        // Act
        await _smoRegistrationCtlSvc.Page15AttestRegistration(model, modelState);

        // Assert
        _mockSmoRegistrationSvc.Verify(x => x.AttestRegistrationAsync(It.IsAny<long>()), Times.Once);
        Assert.That(modelState.IsValid, Is.False);
    }

    [Test]
    public void Page15SendForAttestation_WhenIdIsNull_ThrowException()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel { };
        var modelState = new ModelStateDictionary();

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _smoRegistrationCtlSvc.Page15SendForAttestation(model, modelState));
        Assert.That(ex.Message, Is.EqualTo($"No id exists for registration"));
    }

    [Test]
    public async Task Page15SendForAttestation_WhenModelIsValid_ShouldSendForAttestationSuccessfully()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
        };
        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto
        {
            Valid = true,
        };

        _mockSmoRegistrationSvc.Setup(x => x.SendRegistrationForAttestationAsync(It.IsAny<long>(), It.IsAny<SmoRegistrationSendForAttestationRequest>())).ReturnsAsync(response);

        // Act
        await _smoRegistrationCtlSvc.Page15SendForAttestation(model, modelState);

        // Assert
        _mockSmoRegistrationSvc.Verify(x => x.SendRegistrationForAttestationAsync(It.IsAny<long>(), It.IsAny<SmoRegistrationSendForAttestationRequest>()), Times.Once);
        Assert.That(modelState.IsValid, Is.True);
    }

    [Test]
    public async Task Page15SendForAttestation_WhenModelIsInvalid_ShouldAddErrorToModel()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
        };
        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto
        {
            Valid = false,
            ValidationErrors = new List<CalAccess.Models.Common.WorkFlowError>
            {
                new("Test", "ErrGlobal0001", "Validation", "Test." )
            }
        };

        _mockSmoRegistrationSvc.Setup(x => x.SendRegistrationForAttestationAsync(It.IsAny<long>(), It.IsAny<SmoRegistrationSendForAttestationRequest>())).ReturnsAsync(response);

        // Act
        await _smoRegistrationCtlSvc.Page15SendForAttestation(model, modelState);

        // Assert
        _mockSmoRegistrationSvc.Verify(x => x.SendRegistrationForAttestationAsync(It.IsAny<long>(), It.IsAny<SmoRegistrationSendForAttestationRequest>()), Times.Once);
        Assert.That(modelState.IsValid, Is.False);
    }

    [Test]
    public void Page16GetViewModel_WhenIdIsNull_ThrowException()
    {
        // Arrange

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _smoRegistrationCtlSvc.Page16GetViewModel(null));
        Assert.That(ex.Message, Is.EqualTo($"No id exists for registration"));
    }

    [Test]
    public void Page16GetViewModel_WhenIdInvalid_ThrowException()
    {
        // Arrange
        var registrationId = 1;

        // Act & Assert
        var ex = Assert.ThrowsAsync<ArgumentException>(() => _smoRegistrationCtlSvc.Page16GetViewModel(registrationId));
        Assert.That(ex.Message, Is.EqualTo($"Invalid ID."));
    }

    [Test]
    public async Task Page16GetViewModel_WhenModelIsValid_ShouldAttestRegistrationSuccessfully()
    {
        // Arrange
        var registrationId = 1;
        var smoRegistrationResponse = new SmoRegistrationResponseDto
        {
            Id = registrationId,
            SubmittedAt = DateTime.Now,
        };
        var pendingItemDtos = new List<PendingItemDto>
        {
            new()
            {
                Item = "Test",
                Status = "Test",
            }
        };

        _mockSmoRegistrationSvc.Setup(x => x.GetRegistrationFilingById(It.IsAny<long>())).ReturnsAsync(smoRegistrationResponse);
        _mockSmoRegistrationSvc.Setup(x => x.GetPendingItemsAsync(It.IsAny<long>())).ReturnsAsync(pendingItemDtos);

        // Act
        var result = await _smoRegistrationCtlSvc.Page16GetViewModel(registrationId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ConfirmationViewModel>());
            Assert.That(result.Id, Is.EqualTo(registrationId));
            Assert.That(result.ExecutedOn, Is.EqualTo(smoRegistrationResponse.SubmittedAt));
            Assert.That(result.PendingItems, Is.Not.Empty);
        });
    }

    [Test]
    public async Task SummaryGetViewModel_ValidId_ReturnsExpectedModel()
    {

        // Act
        var smoRegistrationFilingSummaryResponse = new SmoRegistrationFilingSummaryDto
        {
            FilerName = "Name",
            FilingStatus = "Pending",
            Registration = new SmoRegistrationResponseDto
            {
                Id = 1,
                DateQualified = DateTime.Now,
                SubmittedAt = DateTime.Now,
                ActivityLevel = "State",
                QualifiedCommittee = true,
                Address = new List<AddressDto>
                {
                    new()
                    {
                        Purpose = "Organization"
                    },
                    new()
                    {
                        Purpose = "Mailing"
                    }
                },
                PhoneNumbers = new List<PhoneNumberDto>
                {
                    new()
                    {
                        Type = "Phone"
                    },
                    new()
                    {
                        Type = "Fax"
                    }
                },

            },
            Officers = new List<SmoOfficerGridDto>() { },
            IndividualAuthorizers = new List<SmoOfficerGridDto>() { },
            PendingItems = new List<PendingItemDto>() { },
            Attestation = new SmoRegistrationAttestationResponseDto(),
            IsTreasurerAcknowledgement = true
        };
        _mockSmoRegistrationSvc.Setup(x => x.GetSmoRegistrationFilingSummary(It.IsAny<long>())).ReturnsAsync(smoRegistrationFilingSummaryResponse);

        var result = await _smoRegistrationCtlSvc.SummaryGetViewModel(1);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(1));
            Assert.That(result.OfficersGrid, Is.Not.Null);
            Assert.That(result.IndividualAuthorizersGrid, Is.Not.Null);
        });
    }

    [Test]
    public async Task SummaryEdit_WhenModelIsValid_ShouldExecuteSuccessfully()
    {
        // Act
        var result = await _smoRegistrationCtlSvc.HandleRegistrationEditAsync(1);
        // Assert
        _mockSmoRegistrationSvc.Verify(x => x.HandleRegistrationEditAsync(It.IsAny<long>()), Times.Once);
        Assert.That(result, Is.InstanceOf<MethodResult>());
    }

    [Test]
    public async Task SummaryEdit_Exception_ShouldThrowError()
    {
        // Arrange
        _mockSmoRegistrationSvc.Setup(x => x.HandleRegistrationEditAsync(It.IsAny<long>()))
            .ThrowsAsync(new InvalidOperationException("Exception"));

        // Act
        var result = await _smoRegistrationCtlSvc.HandleRegistrationEditAsync(1);
        // Assert
        _mockSmoRegistrationSvc.Verify(x => x.HandleRegistrationEditAsync(It.IsAny<long>()), Times.Once);
        Assert.That(result, Is.InstanceOf<MethodResult>());
        Assert.That(result.IsError(), Is.True);
    }

    [Test]
    public void ResolveRoleId_RequestRoleIdIsNotZero_ReturnRequestRoleId()
    {
        // Act
        var requestRoleId = 93L;
        var methodInfo = typeof(SmoRegistrationCtlSvc).GetMethod("ResolveRoleId", BindingFlags.NonPublic | BindingFlags.Static)!;

        // Arrange
        var result = methodInfo.Invoke(_smoRegistrationCtlSvc, [requestRoleId]);

        // Assert
        Assert.That(result, Is.EqualTo(requestRoleId));
    }

    #region Private
    private static IEnumerable<object[]> GetPage14ViewModelTestCases()
    {
        yield return new object[]
        {
            new List<SmoRegistrationContactDto>
            {
                new()
                {
                    Id = 1,
                    FirstName = "Test",
                    LastName = "Test1",
                }
            },
            FilerRole.SlateMailerOrg_AccountManager.Name
        };
        yield return new object[]
        {
            new List<SmoRegistrationContactDto>
            {
                new()
                {
                    Id = 1,
                    FirstName = "Test",
                    LastName = "Test",
                    Role = FilerRole.SlateMailerOrg_Treasurer.Name,
                    HasAcknowledged = true,
                    Title = FilerRole.SlateMailerOrg_Treasurer.Name,
                    AcknowledgedOn = DateTime.Now,
                },
            },
            FilerRole.SlateMailerOrg_Treasurer.Name
        };

        yield return new object[]
        {
            new List<SmoRegistrationContactDto>
            {
                new()
                {
                    Id = 1,
                    FirstName = "Test",
                    LastName = "Test",
                    Role = FilerRole.SlateMailerOrg_AssistantTreasurer.Name,
                    HasAcknowledged = false,
                    Title = FilerRole.SlateMailerOrg_AssistantTreasurer.Name,
                },
            },
            FilerRole.SlateMailerOrg_AssistantTreasurer.Name
        };
    }
    #endregion
}

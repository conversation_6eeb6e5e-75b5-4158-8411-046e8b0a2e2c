@using SOS.CalAccess.FilerPortal.Models.Transactions;

@model LobbyistSearchViewModel;
@{
    var entityName = Model.ContactName ?? "";
}

<div class="form-group">
    @Html.InputLabel(SharedLocalizer, Model.SearchLabelKey, Model.SearchLabelText, true)

    <input type="hidden" id="FilerId" name="FilerId" value="@Model.FilerId" />
    <input type="hidden" id="LobbyistId" name="LobbyistId" value="@Model.LobbyistId" />

    <div id="@Model.SearchDivId" class="candidate-search">
        <div class="search-container">
            @Html.InputField(SharedLocalizer, Model.SearchInputKey, Model.SearchInputLabel, Model.Required, entityName, "Enter name or ID#")
        </div>
    </div>
</div>

<script type="application/javascript">
    const SEARCH_URL = '@Url.Action(Model.SearchActionName)';
    const INPUT_DEBOUNCE_MS = 200;
    const TEXT_NAME = 'Name';
    const TEXT_EMAIL = 'Email'
    const TEXT_ID = 'Id'
    const TEXT_SELECT = 'Select';
    const TEXT_NO_RESULTS_FOUND = 'No results found.';

    // Use class to isolate variables from global scope
    class Search extends EventTarget {
      activeResultItemElems = [];

      constructor(querySelector) {
        super();
        this.root = document.querySelector(querySelector);
        if (!this.root) {
          throw new Error('Unable to initialize search component.');
        }

        this.resultContainer = document.createElement('div');
        this.resultContainer.classList.add('result-container');
        this.root.appendChild(this.resultContainer);

        const resultAbsolute = document.createElement('div');
        resultAbsolute.classList.add('result-absolute');
        this.resultContainer.replaceChildren(resultAbsolute);

        this.results = document.createElement('div');
        this.results.classList.add('result-left');

        this.resultDetail = document.createElement('div');
        this.resultDetail.classList.add('result-right');

        resultAbsolute.replaceChildren(this.results, this.resultDetail);

        this.searchContainer = this.root.querySelector('.search-container');
        this.clearIcon = document.createElement('i');
        this.clearIcon.classList.add('clear-icon', 'e-icons', 'e-close');
        this.searchContainer.appendChild(this.clearIcon);

        this.selection = this.root.querySelector('.candidate-search input');

        this.clearIcon.onclick = () => {
            const contactId = document.getElementById('ContactId');
            if (contactId) contactId.value = null;
            const registrationFilingId = document.getElementById('RegistrationFilingId');
            if (registrationFilingId) registrationFilingId.value = null;
            this.clearValue();
        }

        this.selection.onfocus = () => {
          if (this.selection.value) {
            this.resultContainer.classList.add('active');
          }
        };

        document.addEventListener('mousedown', (e) => {
          const outsideSearch = !e.composedPath().includes(this.selection)
          const outsideResult = !e.composedPath().includes(this.resultContainer)
          if (outsideSearch && outsideResult) {
            this.hideDropdown();
          }
        });

        const debounce = function (func, delay) {
          let timeoutId;
          return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => {
              func.apply(this, args);
            }, delay);
          }
        }

        this.selection.oninput = debounce((event) => {
          const filerId = document.getElementById('FilerId').value;
          const searchValue = event.target.value;
          if (searchValue) {
              this.clearIcon.classList.add('active');
          }
          else {
              this.clearIcon.classList.remove('active');
          }

          const params = {
              search: searchValue,
              ...(filerId !== "0" && { filerId })
          }
          this.ajaxGet(SEARCH_URL, params, (err, data) => {
            this.clearResults();
            this.setResults(this.results, data);
            this.showDropdown();
          })
        }, INPUT_DEBOUNCE_MS);

        this.clearResults();
      }

      on(eventName, callback, options) {
          this.addEventListener(eventName, callback, options);
      }

      ajaxGet(url, params, callback) {
        const xhr = new XMLHttpRequest();
        const urlParams = new URLSearchParams(params);
        const fullUrl = `${url}?${urlParams.toString()}`;
        xhr.open('GET', fullUrl.toString());
        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            callback(null, JSON.parse(xhr.responseText));
          }
          else {
            console.error('Request failed', xhr.status, xhr.responseText);
            callback(xhr.responseText);
          }
        }
        xhr.onerror = (e) => {
          callback(e);
        }
        xhr.send();
      }

      showDropdown() {
        this.resultContainer.classList.add('active');
      }
      hideDropdown() {
        this.resultContainer.classList.remove('active');
      }

      setValue(value) {
        this.selection.value = value;
      }
      clearValue() {
        this.selection.value = '';
        this.clearIcon.classList.remove('active');
        this.selection.focus();
      }

      clearResults = () => {
        this.results.replaceChildren();
        this.clearResultDetail();
      };
      clearActiveResultItemElems = () => {
        this.activeResultItemElems = [];
      }
      clearSelectedResult = () => {
        this.activeResultItemElems.forEach(resultItem => {
          resultItem.classList.remove('selected');
        })
      };
      resultOnClick = (element, record) => {
        return () => {
          // Remove 'selected' from all other results.
          this.clearSelectedResult();
          element.classList.add('selected');
          this.setResultDetail(record);
        }
      };
      clearResultDetail = () => {
        this.resultDetail.replaceChildren();
      }
      setResultDetail = (record) => {
        // content node
        const detailContent = document.createElement('div');
        detailContent.classList.add('result-detail-content');

        // Name group
        const contentGroupName = document.createElement('div');
        contentGroupName.classList.add('result-detail-content-group');
        const nameLabel = document.createElement('strong');
        nameLabel.innerText = TEXT_NAME;
        const nameContent = document.createElement('span');
        nameContent.innerText = record.Name;
        contentGroupName.replaceChildren(nameLabel, nameContent);

        // ID group
        const contentId = document.createElement('div');
        contentId.classList.add('result-detail-content-group');
        const idLabel = document.createElement('strong');
        idLabel.innerText = TEXT_ID;
        const idContent = document.createElement('span');
        idContent.innerText = record.Id;
        contentId.replaceChildren(idLabel, idContent);

        // Email group
        const contentGroupEmail = document.createElement('div');
        contentGroupEmail.classList.add('result-detail-content-group');
        const emailLabel = document.createElement('strong');
        emailLabel.innerText = TEXT_EMAIL;
        const emailContent = document.createElement('span');
        emailContent.innerText = record.Email || 'N/A';
        contentGroupEmail.replaceChildren(emailLabel, emailContent);

        // Add all groups to detail content
        detailContent.replaceChildren(contentGroupName, contentGroupEmail, contentId);

        // action node
        const detailActions = document.createElement('div');
        detailActions.classList.add('result-detail-actions');
        const selectCandidateButton = document.createElement('button');
        selectCandidateButton.classList.add('button');
        selectCandidateButton.setAttribute('type', 'button')
        selectCandidateButton.innerText = TEXT_SELECT;
        if (record.IsDisabled) {
          selectCandidateButton.disabled = true;
          selectCandidateButton.classList.add('disabled');
        } else {
          selectCandidateButton.onclick = () => {
            this.dispatchEvent(new CustomEvent('selected', { detail: record }));
            this.setValue(record.Name);
            this.hideDropdown();
          };
        }
        detailActions.replaceChildren(selectCandidateButton);
        this.resultDetail.replaceChildren(detailContent, detailActions);
      }
      createResultItem = (record) => {
        const resultItem = document.createElement('div');
        resultItem.classList.add('result-item');
        resultItem.onclick = this.resultOnClick(resultItem, record);

        const resultDesc = document.createElement('div');
        resultDesc.classList.add('result-description');

        const resultName = document.createElement('div');
        resultName.classList.add('result-name');
        resultName.innerText = record.Name;

        const resultCaption = document.createElement('div');
        resultCaption.classList.add('result-caption');
        resultCaption.innerText = record.FilerId ? `ID# ${record.FilerId}` : '';

        resultDesc.replaceChildren(resultName, resultCaption);

        const resultAction = document.createElement('div');
        resultAction.classList.add('result-action');

        const arrow = document.createElement('span');
        arrow.classList.add('e-icons');
        arrow.classList.add('e-chevron-right');
        resultAction.replaceChildren(arrow);

        resultItem.replaceChildren(resultDesc, resultAction);
        return resultItem;
      };
      noResultsElem = () => {
        const resultItem = document.createElement('div');
        resultItem.classList.add('result-item');
        resultItem.classList.add('disabled');

        const resultDesc = document.createElement('div');
        resultDesc.classList.add('result-description');

        const resultName = document.createElement('div');
        resultName.classList.add('italicized');
        resultName.innerText = TEXT_NO_RESULTS_FOUND;
        resultDesc.replaceChildren(resultName);

        resultItem.replaceChildren(resultDesc);

        return resultItem;
      }
      setResults = (resultsElem, resultData) => {
        this.clearActiveResultItemElems();
        const resultElements = [];
        resultData.forEach((result) => {
          const resultItem = this.createResultItem(result);
          resultElements.push(resultItem);
          this.activeResultItemElems.push(resultItem);
        });

        if (resultData.length === 0) {
          resultElements.push(this.noResultsElem());
        }

        resultsElem.replaceChildren(...resultElements);
      };
    }

    const search = new Search('#@Model.SearchDivId');
    search.on('selected', (e) => {
      if (e.detail) {
        const contactId = document.getElementById('ContactId');
        contactId.value = e.detail.ContactId;
        const registrationFilingId = document.getElementById('RegistrationFilingId');
        registrationFilingId.value = e.detail.RegistrationFilingId;
        const firmId = document.getElementById('LobbyistEmployerOrLobbyingFirmId');
        if (firmId) firmId.value = e.detail.Id;
      }
    })
</script>

<style>
    .result-detail-actions .button.disabled,
    .result-detail-actions .button:disabled {
        background-color: #ccc;
        color: #666;
        cursor: not-allowed;
        border-color: #ccc;
        pointer-events: none;
    }
</style>

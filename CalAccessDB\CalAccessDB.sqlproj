<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>CalAccessDB</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{0fd6ea3a-69c9-4ede-8cab-18da15fc4625}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.Sql160DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>CalAccessDB</RootNamespace>
    <AssemblyName>CalAccessDB</AssemblyName>
    <ModelCollation>1033, CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="dbo\" />
    <Folder Include="dbo\Tables\" />
    <Folder Include="Scripts" />
    <Folder Include="Scripts\Post-Deployment" />
    <Folder Include="dbo\Views" />
    <Folder Include="Scripts\DbSetup" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="dbo\Tables\RegistrationLobbyingFirm.sql" />
    <Build Include="dbo\Tables\RegistrationContactType.sql" />
    <Build Include="dbo\Tables\RegistrationContact.sql" />
    <Build Include="dbo\Tables\RegistrationCommittee.sql" />
    <Build Include="dbo\Tables\LobbyingInterest.sql" />
    <Build Include="dbo\Tables\LobbyingEmployerGroupMember.sql" />
    <Build Include="dbo\Tables\EmailAddressList.sql" />
    <Build Include="dbo\Tables\EmailAddress.sql" />
    <Build Include="dbo\Tables\ElectionType.sql" />
    <Build Include="dbo\Tables\ElectionCycle.sql" />
    <Build Include="dbo\Tables\Office.sql" />
    <Build Include="dbo\Tables\District.sql" />
    <Build Include="dbo\Tables\ApiRequestStatus.sql" />
    <Build Include="dbo\Tables\ApiRequest.sql" />
    <Build Include="dbo\Tables\ApiError.sql" />
    <Build Include="dbo\Tables\UnregisteredLobbyingFirm.sql" />
    <Build Include="dbo\Tables\TransactionType.sql" />
    <None Include="Scripts\Post-Deployment\Script.TransactionType.sql" />
    <Build Include="dbo\Tables\RegistrationStatus.sql" />
    <Build Include="dbo\Tables\RegistrationStanceOnCandidate.sql" />
    <Build Include="dbo\Tables\RegistrationStanceOnBallotMeasure.sql" />
    <Build Include="dbo\Tables\RegistrationRegistrationContact.sql" />
    <Build Include="dbo\Tables\RegistrationLobbyingClientLobbyingInterest.sql" />
    <Build Include="dbo\Tables\RegistrationLobbyingClientAgency.sql" />
    <Build Include="dbo\Tables\RegistrationLobbyingClient.sql" />
    <Build Include="dbo\Tables\RegistrationFiling.sql" />
    <Build Include="dbo\Tables\RegistrationAgency.sql" />
    <Build Include="dbo\Tables\PhoneNumberList.sql" />
    <Build Include="dbo\Tables\PhoneNumber.sql" />
    <Build Include="dbo\Tables\MultipurposeOrganization.sql" />
    <Build Include="dbo\Tables\MonetaryType.sql" />
    <Build Include="dbo\Tables\LobbyingFirm.sql" />
    <Build Include="dbo\Tables\LobbyingClient.sql" />
    <Build Include="dbo\Tables\FilingType.sql" />
    <None Include="Scripts\Post-Deployment\Script.FilingType.sql" />
    <Build Include="dbo\Tables\FilingTransaction.sql" />
    <Build Include="dbo\Tables\FilingStatus.sql" />
    <Build Include="dbo\Tables\FilingPeriod.sql" />
    <Build Include="dbo\Tables\FilerStatus.sql" />
    <Build Include="dbo\Tables\FilerContactType.sql" />
    <Build Include="dbo\Tables\FilerContact.sql" />
    <Build Include="dbo\Tables\Filer.sql" />
    <Build Include="dbo\Tables\Election.sql" />
    <Build Include="dbo\Tables\DisclosureTransaction.sql" />
    <Build Include="dbo\Tables\DisclosureFiling.sql" />
    <Build Include="dbo\Tables\DecisionsErrorMessage.sql" />
    <Build Include="dbo\Tables\ContributionType.sql" />
    <Build Include="dbo\Tables\Candidate.sql" />
    <Build Include="dbo\Tables\BallotMeasure.sql" />
    <Build Include="dbo\Tables\AuditLog.sql" />
    <Build Include="dbo\Tables\Agency.sql" />
    <Build Include="dbo\Tables\AddressList.sql" />
    <Build Include="dbo\Tables\Address.sql" />
    <Build Include="dbo\Tables\ActivityExpenseType.sql" />
    <Build Include="dbo\Tables\TransactionReportablePerson.sql" />
    <None Include="Scripts\Post-Deployment\Script.DecisionsErrorMessage.sql" />
    <None Include="Scripts\Post-Deployment\Script.FilerStatus.sql" />
    <None Include="Scripts\Post-Deployment\Script.RegistrationStatus.sql" />
    <Build Include="dbo\Tables\UnregisteredLobbyingFirmHistory.sql" />
    <Build Include="dbo\Tables\TransactionTypeHistory.sql" />
    <Build Include="dbo\Tables\TransactionReportablePersonHistory.sql" />
    <Build Include="dbo\Tables\RegistrationStatusHistory.sql" />
    <Build Include="dbo\Tables\RegistrationStanceOnCandidateHistory.sql" />
    <Build Include="dbo\Tables\RegistrationStanceOnBallotMeasureHistory.sql" />
    <Build Include="dbo\Tables\RegistrationRegistrationContactHistory.sql" />
    <Build Include="dbo\Tables\RegistrationLobbyingFirmHistory.sql" />
    <Build Include="dbo\Tables\RegistrationLobbyingClientLobbyingInterestHistory.sql" />
    <Build Include="dbo\Tables\RegistrationLobbyingClientHistory.sql" />
    <Build Include="dbo\Tables\RegistrationLobbyingClientAgencyHistory.sql" />
    <Build Include="dbo\Tables\RegistrationFilingHistory.sql" />
    <Build Include="dbo\Tables\RegistrationContactTypeHistory.sql" />
    <Build Include="dbo\Tables\RegistrationContactHistory.sql" />
    <Build Include="dbo\Tables\RegistrationCommitteeHistory.sql" />
    <Build Include="dbo\Tables\RegistrationAgencyHistory.sql" />
    <Build Include="dbo\Tables\PhoneNumberListHistory.sql" />
    <Build Include="dbo\Tables\PhoneNumberHistory.sql" />
    <Build Include="dbo\Tables\PermissionHistory.sql" />
    <Build Include="dbo\Tables\Permission.sql" />
    <Build Include="dbo\Tables\OfficeHistory.sql" />
    <Build Include="dbo\Tables\MultipurposeOrganizationHistory.sql" />
    <Build Include="dbo\Tables\MonetaryTypeHistory.sql" />
    <Build Include="dbo\Tables\LobbyingInterestHistory.sql" />
    <Build Include="dbo\Tables\LobbyingFirmHistory.sql" />
    <Build Include="dbo\Tables\LobbyingEmployerGroupMemberHistory.sql" />
    <Build Include="dbo\Tables\LobbyingClientHistory.sql" />
    <Build Include="dbo\Tables\FilingTypeHistory.sql" />
    <Build Include="dbo\Tables\FilingTransactionHistory.sql" />
    <Build Include="dbo\Tables\FilingStatusHistory.sql" />
    <Build Include="dbo\Tables\FilingPeriodHistory.sql" />
    <Build Include="dbo\Tables\FilerUserHistory.sql" />
    <Build Include="dbo\Tables\FilerUser.sql" />
    <Build Include="dbo\Tables\FilerStatusHistory.sql" />
    <Build Include="dbo\Tables\FilerHistory.sql" />
    <Build Include="dbo\Tables\FilerContactTypeHistory.sql" />
    <Build Include="dbo\Tables\FilerContactHistory.sql" />
    <Build Include="dbo\Tables\EmailAddressListHistory.sql" />
    <Build Include="dbo\Tables\EmailAddressHistory.sql" />
    <Build Include="dbo\Tables\ElectionTypeHistory.sql" />
    <Build Include="dbo\Tables\ElectionHistory.sql" />
    <Build Include="dbo\Tables\ElectionCycleHistory.sql" />
    <Build Include="dbo\Tables\DistrictHistory.sql" />
    <Build Include="dbo\Tables\DisclosureTransactionHistory.sql" />
    <Build Include="dbo\Tables\DisclosureFilingHistory.sql" />
    <Build Include="dbo\Tables\ContributionTypeHistory.sql" />
    <Build Include="dbo\Tables\CandidateHistory.sql" />
    <Build Include="dbo\Tables\BallotMeasureHistory.sql" />
    <Build Include="dbo\Tables\AuditLogHistory.sql" />
    <Build Include="dbo\Tables\ApiRequestStatusHistory.sql" />
    <Build Include="dbo\Tables\ApiRequestHistory.sql" />
    <Build Include="dbo\Tables\ApiErrorHistory.sql" />
    <Build Include="dbo\Tables\AgencyHistory.sql" />
    <Build Include="dbo\Tables\AddressListHistory.sql" />
    <Build Include="dbo\Tables\AddressHistory.sql" />
    <Build Include="dbo\Tables\ActivityExpenseTypeHistory.sql" />
    <Build Include="dbo\Tables\__EFMigrationsHistory.sql" />
    <Build Include="dbo\Tables\AuthorizationGroupPermissionHistory.sql" />
    <Build Include="dbo\Tables\AuthorizationGroupPermission.sql" />
    <Build Include="dbo\Tables\AuthorizationGroupHistory.sql" />
    <Build Include="dbo\Tables\AuthorizationGroup.sql" />
    <Build Include="dbo\Tables\DirtyDataMetadata.sql" />
    <Build Include="dbo\Tables\NotificationMessage.sql" />
    <Build Include="dbo\Tables\SmsMessageHistory.sql" />
    <Build Include="dbo\Tables\SmsMessage.sql" />
    <Build Include="dbo\Tables\AttestationHistory.sql" />
    <Build Include="dbo\Tables\Attestation.sql" />
    <Build Include="dbo\Tables\PoliticalParty.sql" />
    <None Include="Scripts\Post-Deployment\Script.SeedOfficeDistrictElectionTypePoliticalParty.sql" />
    <Build Include="dbo\Tables\PoliticalPartyHistory.sql" />
    <Build Include="dbo\Tables\ExpenditureExpenseAmount.sql" />
    <Build Include="dbo\Tables\ExpenditureExpenseAmountHistory.sql" />
    <None Include="Scripts\Post-Deployment\Script.SeedExpenditureExpenseAmount.sql" />
    <None Include="Scripts\Post-Deployment\Script.NotificationType.sql" />
    <Build Include="dbo\Tables\EmailMessageHistory.sql" />
    <Build Include="dbo\Tables\EmailMessage.sql" />
    <Build Include="dbo\Tables\NotificationTypeHistory.sql" />
    <Build Include="dbo\Tables\NotificationType.sql" />
    <Build Include="dbo\Tables\NotificationTemplateTranslationHistory.sql" />
    <Build Include="dbo\Tables\NotificationTemplateTranslation.sql" />
    <Build Include="dbo\Tables\NotificationTemplateHistory.sql" />
    <Build Include="dbo\Tables\NotificationTemplate.sql" />
    <Build Include="dbo\Tables\FilerTypeHistory.sql" />
    <Build Include="dbo\Tables\FilerType.sql" />
    <None Include="Scripts\Post-Deployment\Script.FilerType.sql" />
    <Build Include="dbo\Tables\NotificationMessageHistory.sql" />
    <Build Include="dbo\Tables\JsonSchemaReferenceHistory.sql" />
    <Build Include="dbo\Tables\JsonSchemaReference.sql" />
    <None Include="Scripts\Post-Deployment\Script.ApiRequestStatus.sql" />
    <None Include="Scripts\Post-Deployment\Script.JsonSchemaReference.sql" />
    <None Include="Scripts\Post-Deployment\Script.AuthorizationGroup.sql" />
    <None Include="Scripts\Post-Deployment\Script.AuthorizationGroupPermissions.sql" />
    <None Include="Scripts\Post-Deployment\Script.Permission.sql" />
    <None Include="Scripts\Post-Deployment\Script.FilerRole.sql" />
    <None Include="Scripts\Post-Deployment\Script.FilerRolePermissions.sql" />
    <Build Include="dbo\Tables\FilerRolePermissionHistory.sql" />
    <Build Include="dbo\Tables\FilerRolePermission.sql" />
    <Build Include="dbo\Tables\FilerRoleHistory.sql" />
    <Build Include="dbo\Tables\FilerRole.sql" />
    <Build Include="dbo\Tables\ElectionRaceHistory.sql" />
    <Build Include="dbo\Tables\ElectionRace.sql" />
    <Build Include="dbo\Tables\FilingSummaryTypeHistory.sql" />
    <Build Include="dbo\Tables\FilingSummaryType.sql" />
    <Build Include="dbo\Tables\FilingSummaryHistory.sql" />
    <Build Include="dbo\Tables\FilingSummary.sql" />
    <Build Include="dbo\Tables\UserNotificationPreference.sql" />
    <Build Include="dbo\Tables\LegislativeSessionHistory.sql" />
    <Build Include="dbo\Tables\LegislativeSession.sql" />
    <Build Include="dbo\Tables\LlcDetailHistory.sql" />
    <Build Include="dbo\Tables\LlcDetail.sql" />
    <Build Include="dbo\Tables\CapitalContributionHistory.sql" />
    <Build Include="dbo\Tables\CapitalContribution.sql" />
    <Build Include="dbo\Tables\FilerLinkType.sql" />
    <Build Include="dbo\Tables\FilerLinkHistory.sql" />
    <Build Include="dbo\Tables\FilerLink.sql" />
    <Build Include="dbo\Tables\UserHistory.sql" />
    <Build Include="dbo\Tables\User.sql" />
    <None Include="Scripts\Post-Deployment\Script.NotificationTemplate.sql" />
    <None Include="Scripts\Post-Deployment\Script.NotificationTemplateTranslation.sql" />
    <Build Include="dbo\Tables\UnRegisteredCandidateHistory.sql" />
    <Build Include="dbo\Tables\UnRegisteredCandidate.sql" />
    <Build Include="dbo\Tables\UnRegisteredBallotMeasureHistory.sql" />
    <Build Include="dbo\Tables\UnRegisteredBallotMeasure.sql" />
    <Build Include="dbo\Tables\OfficialPosition.sql" />
    <Build Include="dbo\Tables\DisclosureFilingRelatedFiler.sql" />
    <Build Include="dbo\Tables\DisclosureFilingRelatedFilerHistory.sql" />
    <None Include="Scripts\Post-Deployment\Script.FilingSummaryStatus.sql" />
    <Build Include="dbo\Tables\FilingSummaryStatusHistory.sql" />
    <Build Include="dbo\Tables\FilingSummaryStatus.sql" />
    <Build Include="dbo\Tables\NatureAndInterestTypeHistory.sql" />
    <Build Include="dbo\Tables\NatureAndInterestType.sql" />
    <Build Include="dbo\Tables\IndustryGroupClassificationTypeHistory.sql" />
    <Build Include="dbo\Tables\IndustryGroupClassificationType.sql" />
    <None Include="Scripts\Post-Deployment\Script.NatureAndInterestType.sql" />
    <None Include="Scripts\Post-Deployment\Script.FilingSummaryType.sql" />
    <None Include="Scripts\Post-Deployment\Script.BillHouse.sql" />
    <Build Include="dbo\Tables\BillHouse.sql" />
    <Build Include="dbo\Tables\Bill.sql" />
    <None Include="Scripts\Post-Deployment\Script.PaymentCode.sql" />
    <Build Include="dbo\Tables\PaymentCode.sql" />
    <Build Include="dbo\Tables\ActionsLobbiedHistory.sql" />
    <Build Include="dbo\Tables\ActionsLobbied.sql" />
    <None Include="Scripts\Post-Deployment\Script.MonetaryType.sql" />
    <Build Include="dbo\Tables\BillHistory.sql" />
    <Build Include="dbo\Tables\CommitteeType.sql" />
    <Build Include="dbo\Tables\CommitteeTypeHistory.sql" />
    <Build Include="dbo\Tables\CountryHistory.sql" />
    <Build Include="dbo\Tables\Country.sql" />
    <Build Include="dbo\Tables\BannerMessageHistory.sql" />
    <Build Include="dbo\Tables\BannerMessageFilerTypeHistory.sql" />
    <Build Include="dbo\Tables\BannerMessageFilerType.sql" />
    <Build Include="dbo\Tables\BannerMessage.sql" />
    <Build Include="dbo\Tables\DisclosureStanceOnCandidateHistory.sql" />
    <Build Include="dbo\Tables\DisclosureStanceOnCandidate.sql" />
    <Build Include="dbo\Tables\DisclosureStanceOnBallotMeasureHistory.sql" />
    <Build Include="dbo\Tables\DisclosureStanceOnBallotMeasure.sql" />
    <Build Include="dbo\Tables\ProponentHistory.sql" />
    <Build Include="dbo\Tables\Proponent.sql" />
    <Build Include="dbo\Tables\BallotMeasureProponentHistory.sql" />
    <Build Include="dbo\Tables\BallotMeasureProponent.sql" />
    <Build Include="dbo\Tables\LobbyingDirectoryReportHistory.sql" />
    <Build Include="dbo\Tables\LobbyingDirectoryReport.sql" />
    <Build Include="dbo\Tables\LobbyingDirectoryImageHistory.sql" />
    <Build Include="dbo\Tables\LobbyingDirectoryImage.sql" />
    <Build Include="dbo\Tables\VendorCertificationFormSelectionHistory.sql" />
    <Build Include="dbo\Tables\VendorCertificationFormSelection.sql" />
    <Build Include="dbo\Tables\VendorCertificationApplicationHistory.sql" />
    <Build Include="dbo\Tables\VendorCertificationApplication.sql" />
    <Build Include="dbo\Tables\LegacyIdMappings.sql" />
    <Build Include="dbo\Tables\DisclosureWithoutPaymentReceived.sql" />
    <Build Include="dbo\Tables\DisclosureWithoutPaymentReceivedHistory.sql" />
    <Build Include="dbo\Tables\SystemParameterHistory.sql" />
    <Build Include="dbo\Tables\SystemParameter.sql" />
    <Build Include="dbo\Tables\LinkageStatusHistory.sql" />
    <Build Include="dbo\Tables\LinkageStatus.sql" />
    <Build Include="dbo\Tables\LinkageRequestHistory.sql" />
    <Build Include="dbo\Tables\LinkageRequest.sql" />
    <None Include="Scripts\Post-Deployment\Script.Country.sql" />
    <Build Include="dbo\Tables\FilingContactSummaryTypeHistory.sql" />
    <Build Include="dbo\Tables\FilingContactSummaryType.sql" />
    <Build Include="dbo\Tables\FilingContactSummaryHistory.sql" />
    <Build Include="dbo\Tables\FilingContactSummary.sql" />
    <Build Include="dbo\Tables\DisclosureFilingNonRegisteredLobbyist.sql" />
    <Build Include="dbo\Tables\DisclosureFilingNonRegisteredLobbyistHistory.sql" />
    <Build Include="dbo\Tables\ExpenditureCodeHistory.sql" />
    <Build Include="dbo\Tables\ExpenditureCode.sql" />
    <Build Include="dbo\Views\ElectionFullView_1.sql" />
    <Build Include="dbo\Tables\FilingRejectionReasonHistory.sql" />
    <Build Include="dbo\Tables\FilingRejectionReason.sql" />
    <Build Include="dbo\Tables\ExpenditureLimitHistory.sql" />
    <Build Include="dbo\Tables\ExpenditureLimit.sql" />
    <Build Include="dbo\Tables\DonorTypeHistory.sql" />
    <Build Include="dbo\Tables\DonorType.sql" />
    <Build Include="dbo\Tables\ContributionLimitHistory.sql" />
    <Build Include="dbo\Tables\ContributionLimitDonorHistory.sql" />
    <Build Include="dbo\Tables\ContributionLimitDonor.sql" />
    <Build Include="dbo\Tables\ContributionLimit.sql" />
    <Build Include="dbo\Tables\CandidateElectionResultHistory.sql" />
    <Build Include="dbo\Tables\CandidateElectionResult.sql" />
    <Build Include="dbo\Views\ElectionFullView_1.sql" />
    <Build Include="dbo\Tables\AdvertisementDistributionMethod.sql" />
    <Build Include="dbo\Tables\AdvertisementDistributionMethodHistory.sql" />
    <Build Include="dbo\Tables\UploadedFileStatusHistory.sql" />
    <Build Include="dbo\Tables\UploadedFileStatus.sql" />
    <Build Include="dbo\Tables\UploadedFileHistory.sql" />
    <Build Include="dbo\Tables\UploadedFile.sql" />
    <None Include="Scripts\DbSetup\DeveloperScript.sql" />
    <None Include="Scripts\DbSetup\ElectionRace.sql" />
    <None Include="Scripts\DbSetup\BeforePostDeploymentScript.sql" />
    <Build Include="dbo\Tables\FilingPeriodTypeHistory.sql" />
    <Build Include="dbo\Tables\FilingPeriodType.sql" />
    <None Include="Scripts\Post-Deployment\Script.BusinessSubcategory.sql" />
    <Build Include="dbo\Tables\BusinessSubcategory.sql" />
    <None Include="Scripts\Post-Deployment\Script.IndustryGroupClassificationType.sql" />
  </ItemGroup>
  <ItemGroup>
    <PostDeploy Include="Scripts\Post-Deployment\PostDeployment.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Scripts\Post-Deployment\Script.FilingStatus.sql" />
    <None Include="Scripts\Post-Deployment\Script.SeedConversionUserActor.sql" />
    <None Include="Scripts\Post-Deployment\Script.RegistrationContactType.sql" />
    <None Include="Scripts\Post-Deployment\Script.PoliticalParty.sql" />
    <None Include="Scripts\Post-Deployment\Script.ActivityExpenseType.sql" />
    <None Include="Scripts\Post-Deployment\Script.ContributionType.sql" />
    <None Include="Scripts\Post-Deployment\Script.ElectionType.sql" />
    <None Include="Scripts\Post-Deployment\Script.FilerContactType.sql" />
    <None Include="Scripts\Post-Deployment\Script.LegislativeSession.sql" />
    <None Include="Scripts\Post-Deployment\Script.Agency.sql" />
    <None Include="Scripts\Post-Deployment\Script.OfficialPosition.sql" />
    <None Include="Scripts\Post-Deployment\Script.FilerLinkType.sql" />
    <None Include="Scripts\Post-Deployment\Script.FilingSummaryType.sql" />
    <None Include="Scripts\Post-Deployment\Script.CommitteeType.sql" />
    <None Include="Scripts\Post-Deployment\Script.LinkageStatus.sql" />
    <None Include="Scripts\Post-Deployment\Script.ExpenditureCode.sql" />
    <None Include="Scripts\Post-Deployment\Script.DonorType.sql" />
    <None Include="Scripts\Post-Deployment\Script.AdvertisementDistributionMethod.sql" />
    <None Include="Scripts\Post-Deployment\Script.UploadedFileStatus.sql" />
    <None Include="Scripts\Post-Deployment\Script.FilingContactSummaryType.sql" />
    <None Include="Scripts\InstructionsForSetup" />
    <None Include="Scripts\Post-Deployment\Script.FilingPeriodType.sql" />
  </ItemGroup>
</Project>
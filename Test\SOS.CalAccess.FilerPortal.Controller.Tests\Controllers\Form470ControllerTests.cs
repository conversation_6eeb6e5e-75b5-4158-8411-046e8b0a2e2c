using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices.Form470;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations.Form470;
using SOS.CalAccess.FilerPortal.Models.Registrations.Form470Attestation;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
public class Form470ControllerTests
{
    private Form470Controller _controller;
    private IAuthorizationSvc _mockAuthorizationSvc;
    private IStringLocalizer<SharedResources> _mockLocalizer;
    private IForm470CtlSvc _mockForm470CtlSvc;
    private IForm470Svc _mockForm470Svc;
    private IToastService _mockToastService;
    private IDateTimeSvc _mockDateTimeSvc;

    [SetUp]
    public void Setup()
    {
        _mockAuthorizationSvc = Substitute.For<IAuthorizationSvc>();
        _mockLocalizer = Substitute.For<IStringLocalizer<SharedResources>>();
        _mockForm470CtlSvc = Substitute.For<IForm470CtlSvc>();
        _mockForm470Svc = Substitute.For<IForm470Svc>();
        _mockToastService = Substitute.For<IToastService>();
        _mockDateTimeSvc = Substitute.For<IDateTimeSvc>();

        _controller = new Form470Controller(
            _mockAuthorizationSvc,
            _mockLocalizer,
            _mockForm470CtlSvc,
            _mockForm470Svc,
            _mockDateTimeSvc,
            _mockToastService
        );

        _mockLocalizer[Arg.Any<string>()].Returns(p => new LocalizedString((string)p[0], (string)p[0]));
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    #region Core
    [Test]
    public async Task Index_Valid_ShouldRedirectToPage01()
    {
        // Arrange
        var user = new ClaimsPrincipal(new ClaimsIdentity());
        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = user }
        };

        // Act
        var result = await _controller.Index(1);

        // Assert
        await _mockAuthorizationSvc.Received(1).VerifyAuthorization(Arg.Any<AuthorizationRequest>());
        var redirectResult = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult, Is.Not.Null);
            Assert.That(redirectResult!.ActionName, Is.EqualTo("Page01"));
        });
    }
    [Test]
    public async Task Index_Invalid_ShouldReturnNotFound()
    {
        // Arrange
        var user = new ClaimsPrincipal(new ClaimsIdentity());
        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = user }
        };
        _controller.ModelState.AddModelError("KEY", "ERROR");

        // Act
        var result = await _controller.Index(null) as NotFoundResult;

        // Assert
        await _mockAuthorizationSvc.Received(1).VerifyAuthorization(Arg.Any<AuthorizationRequest>());
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task Cancel_InvalidShouldReturnNotFound()
    {
        // Arrange
        var id = 1L;
        _controller.ModelState.AddModelError("KEY", "ERROR");
        // Act
        var result = await _controller.Cancel(id, default) as NotFoundResult;
        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
        await _mockForm470CtlSvc.Received(0).Cancel(Arg.Any<long>());
    }
    [Test]
    public async Task Cancel_ValidShouldRedirectToDashboard()
    {
        // Arrange
        var id = 1L;
        // Act
        var result = await _controller.Cancel(id, default) as RedirectToActionResult;
        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ControllerName, Is.EqualTo("DisclosureTemporaryDashboard"));
        _mockToastService.Received(1).Success(Arg.Any<string>());
        await _mockForm470CtlSvc.Received(1).Cancel(Arg.Any<long>());
    }
    [Test]
    public async Task Edit_InvalidShouldReturnNotFound()
    {
        // Arrange
        var id = 1L;
        _controller.ModelState.AddModelError("KEY", "ERROR");
        // Act
        var result = await _controller.Edit(id) as NotFoundResult;
        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public void Close_ShouldRedirectToDashboard()
    {
        // Act
        var result = _controller.Close() as RedirectToActionResult;
        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ControllerName, Is.EqualTo("DisclosureTemporaryDashboard"));
    }
    [Test]
    public async Task New_WhenFilerFound_ShouldRedirectToPage01()
    {
        // Arrange
        _mockForm470Svc.GetFilerIdOfLatestAccepted501().Returns(1);
        // Act
        var result = await _controller.New() as RedirectToActionResult;
        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Page01"));
    }
    [Test]
    public async Task New_WhenNoFiler_ShouldReturnNotFound()
    {
        // Arrange
        _mockForm470Svc.GetFilerIdOfLatestAccepted501().Returns(0);
        // Act
        var result = await _controller.New() as NotFoundResult;
        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Edit_Valid_ShouldRedirectToPage01()
    {
        // Arrange
        var id = 1L;
        // Act
        var result = await _controller.Edit(id) as RedirectToActionResult;
        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Page01"));
    }
    [Test]
    public async Task Amend_ValidId_ShouldRedirectToPage01()
    {
        // Arrange
        _mockForm470CtlSvc.InitializeForm470Amendment(Arg.Any<long>()).Returns(1L);

        // Act
        var result = await _controller.Amend(1) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Page01"));
    }
    [Test]
    public async Task Amend_InValidId_ShouldRedirectToDashboard()
    {
        // Arrange
        _mockForm470CtlSvc.InitializeForm470Amendment(Arg.Any<long>()).ThrowsAsync(new ArgumentException("Error Message"));

        // Act
        var result = await _controller.Amend(1) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Index"));
    }
    #endregion

    #region Page01_Get
    [Test]
    public async Task Page01_Get_ReturnsView()
    {
        _controller.ModelState.AddModelError("KEY", "ERROR");

        var result = await _controller.Page01(null, null) as ViewResult;

        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    [Test]
    public async Task Page01_Get_HasId()
    {
        _mockForm470CtlSvc.Page01GetExisting(Arg.Any<long>()).Returns(new Form470Page01ViewModel());
        var result = await _controller.Page01(1, null) as ViewResult;

        Assert.That(result, Is.InstanceOf<ViewResult>());
        await _mockForm470CtlSvc.Received(1).Page01GetExisting(Arg.Any<long>());
    }
    [Test]
    public async Task Page01_Get_HasFilerId()
    {
        _mockForm470CtlSvc.Page01GetViewModelFromFilerId(Arg.Any<long>()).Returns(new Form470Page01ViewModel());
        var result = await _controller.Page01(null, 1) as ViewResult;

        Assert.That(result, Is.InstanceOf<ViewResult>());
        await _mockForm470CtlSvc.Received(1).Page01GetViewModelFromFilerId(Arg.Any<long>());
    }
    [Test]
    public async Task Page01_Get_HasId_NullResponse()
    {
        var result = await _controller.Page01(1, null) as NotFoundResult;

        Assert.That(result, Is.InstanceOf<NotFoundResult>());
        await _mockForm470CtlSvc.Received(1).Page01GetExisting(Arg.Any<long>());
        await _mockForm470CtlSvc.Received(0).Page01GetViewModelFromFilerId(Arg.Any<long>());
    }
    #endregion

    #region Page01_Post
    [Test]
    public async Task Page01_Post_Invalid_ReturnsView()
    {
        var model = new Form470Page01ViewModel();
        _controller.ModelState.AddModelError("KEY", "ERROR");

        var result = await _controller.Page01(model) as ViewResult;

        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    [Test]
    public async Task Page01_Post_UnhandledAction_ReturnsView()
    {
        var model = new Form470Page01ViewModel
        {
            Action = FormAction.Cancel,
        };

        var result = await _controller.Page01(model) as ViewResult;

        Assert.Multiple(() =>
        {
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(1));
            Assert.That(result, Is.InstanceOf<ViewResult>());
        });
    }
    [Test]
    public async Task Page01_Post_SaveAndClose_RedirectsToDashboard()
    {
        var model = new Form470Page01ViewModel
        {
            Action = FormAction.SaveAndClose,
        };

        var result = await _controller.Page01(model) as RedirectToActionResult;

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ControllerName, Is.EqualTo("DisclosureTemporaryDashboard"));
    }
    [Test]
    public async Task Page01_Post_SaveAndClose_Error_ReturnsView()
    {
        var model = new Form470Page01ViewModel
        {
            Action = FormAction.SaveAndClose,
        };
        _mockForm470CtlSvc
            .When(x => x.Page01Submit(Arg.Any<Form470Page01ViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>()))
            .Do((callInfo) =>
            {
                var state = callInfo.Arg<ModelStateDictionary>();
                state.AddModelError("KEY", "ERROR");
            });

        var result = await _controller.Page01(model) as ViewResult;

        Assert.That(result, Is.InstanceOf<ViewResult>());
        await _mockForm470CtlSvc.Received(1).Page01ViewModelSetReadOnlyData(Arg.Any<Form470Page01ViewModel>());
    }
    [Test]
    public async Task Page01_Post_Continue_RedirectsToNextPage()
    {
        // TD: Refactor this after implementing Continue
        var model = new Form470Page01ViewModel
        {
            Id = 1,
            Action = FormAction.Continue,
        };
        _mockForm470CtlSvc
            .Page01Submit(Arg.Any<Form470Page01ViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(1);

        var result = await _controller.Page01(model) as RedirectToActionResult;

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Page02"));
    }
    [Test]
    public async Task Page01_Post_Continue_Error_ReturnsView()
    {
        var model = new Form470Page01ViewModel
        {
            Action = FormAction.Continue,
        };
        _mockForm470CtlSvc
            .When(x => x.Page01Submit(Arg.Any<Form470Page01ViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>()))
            .Do((callInfo) =>
            {
                var state = callInfo.Arg<ModelStateDictionary>();
                state.AddModelError("KEY", "ERROR");
            });

        var result = await _controller.Page01(model) as ViewResult;

        Assert.That(result, Is.InstanceOf<ViewResult>());
        await _mockForm470CtlSvc.Received(1).Page01ViewModelSetReadOnlyData(Arg.Any<Form470Page01ViewModel>());
    }
    #endregion

    #region Page02
    [Test]
    public async Task Page02DeleteCommittee_ValidModelState_CallsServiceAndReturnsOk()
    {
        // Arrange
        var filingRelatedFilerId = 123L;
        _controller.ModelState.Clear();

        // Act
        var result = await _controller.Page02DeleteCommittee(filingRelatedFilerId);

        // Assert
        await _mockForm470Svc.Received(1).CancelFilingRelatedFiler(filingRelatedFilerId);
        Assert.That(result, Is.InstanceOf<OkResult>());
    }

    [Test]
    public async Task Page02DeleteCommittee_InvalidModelState_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error");

        // Act
        var result = await _controller.Page02DeleteCommittee(123);

        // Assert
        await _mockForm470Svc.DidNotReceive().CancelFilingRelatedFiler(Arg.Any<long>());
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Page02_WhenModelStateIsValid_ShouldReturnViewWithModel()
    {
        var id = 123L;
        var expectedModel = new Form470Page02ViewModel { Id = id };
        _mockForm470CtlSvc.Page02GetViewModel(id).Returns(expectedModel);

        var result = await _controller.Page02(id) as ViewResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(expectedModel));
    }

    [Test]
    public async Task Page02_WhenModelStateIsInvalid_ShouldReturnViewWithEmptyModel()
    {
        _controller.ModelState.AddModelError("key", "error");

        var result = await _controller.Page02(123L) as ViewResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<Form470Page02ViewModel>());
    }

    [Test]
    public void Page02_WhenActionIsPrevious_ShouldRedirectToPage01()
    {
        var model = new Form470Page02ViewModel
        {
            Id = 1,
            Action = FormAction.Previous
        };

        var result = _controller.Page02(model, CancellationToken.None) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo(nameof(Form470Controller.Page01)));
            Assert.That(result.RouteValues["Id"], Is.EqualTo(model.Id));
        });
    }

    [Test]
    public void Page02_WhenActionIsContinue_ShouldReturnUnderConstructionView()
    {
        var model = new Form470Page02ViewModel
        {
            Id = 1,
            Action = FormAction.Continue
        };

        var result = _controller.Page02(model, CancellationToken.None) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo(nameof(Form470Controller.Page04)));
            Assert.That(result.RouteValues["Id"], Is.EqualTo(1));
        });
    }

    [Test]
    public void Page02_WhenActionIsSaveAndClose_ShouldRedirectToDashboard()
    {
        var model = new Form470Page02ViewModel
        {
            Action = FormAction.SaveAndClose
        };

        var result = _controller.Page02(model, CancellationToken.None) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Index"));
    }
    #endregion

    #region Page03
    [Test]
    public void Page03_Get_WhenModelStateIsValid_ShouldReturnViewWithModel()
    {
        var id = 1L;

        var result = _controller.Page03(id) as ViewResult;

        var model = result?.Model as Form470Page03ViewModel;

        Assert.That(model, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(model.Id, Is.EqualTo(id));
        });
    }

    [Test]
    public void Page03_Get_WhenModelStateIsInvalid_ShouldReturnView()
    {
        _controller.ModelState.AddModelError("key", "error");

        var result = _controller.Page03(1L) as ViewResult;

        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Page03_Post_WhenModelStateIsInvalid_ShouldReturnViewWithModel()
    {
        var model = new Form470Page03ViewModel();
        _controller.ModelState.AddModelError("key", "error");

        var result = await _controller.Page03(model, CancellationToken.None) as ViewResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page03_Post_WhenActionIsPrevious_ShouldRedirectToPage02()
    {
        var model = new Form470Page03ViewModel
        {
            Id = 5,
            Action = FormAction.Previous
        };

        var result = await _controller.Page03(model, CancellationToken.None) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo(nameof(Form470Controller.Page02)));
            Assert.That(result.RouteValues["Id"], Is.EqualTo(model.Id));
        });
    }

    [Test]
    public async Task Page03_Post_ActionContinue_ShouldSaveAndRedirectToPage04()
    {
        // Arrange
        var model = new Form470Page03ViewModel
        {
            Id = 10,
            FilerId = 20,
            Action = FormAction.Continue
        };

        // Act
        var result = await _controller.Page03(model, CancellationToken.None) as RedirectToActionResult;

        // Assert
        await _mockForm470CtlSvc.Received(1).Page03Save(model.Id.Value, model.FilerId.Value, Arg.Any<ModelStateDictionary>());
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo(nameof(Form470Controller.Page02)));
    }

    [Test]
    public async Task Page03_Post_WhenActionIsContinueAndModelStateInvalid_ShouldReturnPage03View()
    {
        // Arrange
        var model = new Form470Page03ViewModel
        {
            Id = 10,
            FilerId = 0,
            Action = FormAction.Continue
        };

        _mockForm470CtlSvc
            .When(x => x.Page03Save(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<ModelStateDictionary>()))
            .Do(call =>
            {
                var modelState = call.Arg<ModelStateDictionary>();
                modelState.AddModelError("FilerId", "Unable to continue. Value is missing.");
            });

        // Act
        var result = await _controller.Page03(model, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("Page03"));
            Assert.That(result.Model, Is.TypeOf<Form470Page03ViewModel>());
            Assert.That(_controller.ModelState.IsValid, Is.False);
        });
    }

    [Test]
    public async Task Page03_Post_WhenActionIsSaveAndClose_ShouldSaveAndRedirectToDashboard()
    {
        // Arrange
        var model = new Form470Page03ViewModel
        {
            Id = 11,
            FilerId = 21,
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = await _controller.Page03(model, CancellationToken.None) as RedirectToActionResult;

        // Assert
        await _mockForm470CtlSvc.Received(1).Page03Save(model.Id.Value, model.FilerId.Value, Arg.Any<ModelStateDictionary>());
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task Page03_Post_WhenActionIsSaveAndClose_IsInvalid_ShouldReturnView()
    {
        // Arrange
        var model = new Form470Page03ViewModel
        {
            Id = 11,
            FilerId = 21,
            Action = FormAction.SaveAndClose
        };
        _mockForm470CtlSvc
            .When(x => x.Page03Save(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<ModelStateDictionary>()))
            .Do(callInfo =>
            {
                var arg = callInfo.Arg<ModelStateDictionary>();
                arg.AddModelError("KEY", "ERROR");
            });

        // Act
        var result = await _controller.Page03(model, CancellationToken.None) as ViewResult;

        // Assert
        await _mockForm470CtlSvc.Received(1).Page03Save(model.Id.Value, model.FilerId.Value, Arg.Any<ModelStateDictionary>());
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ViewName, Is.EqualTo("Page03"));
    }
    [Test]
    public async Task SearchCommitteeByNameOrId_ShouldReturnExpectedJsonResult()
    {
        var search = "Test";
        var cancellationToken = CancellationToken.None;

        var expectedData = new List<CommitteeSearchResult>
        {
            new() {
                Id = 1,
                CommitteeName= "test"
            },
            new() {
                Id = 2,
                CommitteeName= "test"
            }
        };
        _mockForm470CtlSvc.Page03FindCommitteesByIdOrName(search).Returns([.. expectedData]);

        var result = await _controller.SearchCommitteeByNameOrId(search, cancellationToken);

        var jsonResult = result;
        var data = jsonResult?.Value as CommitteeSearchResult[];

        Assert.That(data, Is.Not.Null.And.Not.Empty);
        Assert.That(jsonResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(jsonResult.Value, Is.Not.Null);
            var item = data[0];
            Assert.That(item.GetType().GetProperty("Id")?.GetValue(item), Is.EqualTo(1));
        });
    }

    #endregion

    #region Verification
    [Test]
    public async Task Page04_Get_ShouldReturnViewWithModel()
    {
        // Act
        var id = 1;
        _mockForm470CtlSvc.Page04GetViewModel(id).Returns(new Form470Page04ViewModel());
        _mockAuthorizationSvc.IsAuthorized(Arg.Any<AuthorizationRequest>()).Returns(true);
        var result = await _controller.Page04(1);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult!.Model, Is.InstanceOf<Form470Page04ViewModel>());
    }
    [Test]
    public async Task Page04_Get_InvalidModelState_ShouldReturnNotFound()
    {
        // Act
        _controller.ModelState.AddModelError("KEY", "ERROR");
        var result = await _controller.Page04(1);

        // Assert
        var viewResult = result;
        Assert.That(viewResult, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Page04_Get_NullModel_ShouldReturnNotFound()
    {
        // Act
        var result = await _controller.Page04(1);

        // Assert
        var viewResult = result;
        Assert.That(viewResult, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Page04_Post_InvalidModel_ShouldReturnViewWithModel()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model");

        var model = new Form470Page04ViewModel()
        {
            Name = "Candidate",
            ExecutedOn = new(),
            IsAgreedTerm = true,
            Id = 1
        };

        // Act
        var result = await _controller.Page04(model);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult!.Model, Is.EqualTo(model));
    }
    [Test]
    public async Task Page04_Post_NoId_ShouldReturnNotFound()
    {
        // Arrange
        var model = new Form470Page04ViewModel()
        {
            Name = "Candidate",
            ExecutedOn = new(),
            IsAgreedTerm = true,
            Id = null
        };

        // Act
        var result = await _controller.Page04(model);

        // Assert
        var viewResult = result as NotFoundResult;
        Assert.That(viewResult, Is.Not.Null);
    }

    [Test]
    public async Task Page04_Post_ValidModel_SubmitAction_ShouldRedirectToPage05()
    {
        // Arrange
        var model = new Form470Page04ViewModel
        {
            Action = FormAction.Submit,
            Name = "Candidate",
            ExecutedOn = new(),
            IsAgreedTerm = true,
            Id = 1,
            IsCandidate = true,
            RegistrationId = 1
        };

        // Act
        _mockForm470CtlSvc.UserIsCandidate(Arg.Any<long>()).Returns(true);
        var result = await _controller.Page04(model);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.That(redirectResult.ActionName, Is.EqualTo("Page05"));
    }
    [Test]
    public async Task Page04_Post_ValidModel_SubmitAction_ShouldRedirectToPage06Page()
    {
        // Arrange
        var model = new Form470Page04ViewModel
        {
            Action = FormAction.Submit,
            Name = "Non Candidate",
            ExecutedOn = new(),
            IsAgreedTerm = true,
            Id = 1,
            IsCandidate = false,
            RegistrationId = 1
        };

        // Act
        _mockForm470CtlSvc.UserIsCandidate(Arg.Any<long>()).Returns(false);
        var result = await _controller.Page04(model);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.That(redirectResult.ActionName, Is.EqualTo("Page06"));
    }
    [Test]
    public async Task Page04_Post_ValidModel_PreviousAction_ShouldRedirectToCommitteePage()
    {
        // Arrange
        var model = new Form470Page04ViewModel { Id = 1, Action = FormAction.Previous };

        // Act
        var result = await _controller.Page04(model);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult!.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["id"], Is.EqualTo(1));
        });
    }
    [Test]
    public async Task Page04_Post_ValidModel_SaveAndCloseAction_ShouldRedirectToDisclosureIndex()
    {
        // Arrange
        var model = new Form470Page04ViewModel {
            Id = 1,
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = _controller.Page04(model);

        // Assert
        var redirectResult = await result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult, Is.Not.Null);
            Assert.That(redirectResult!.ActionName, Is.EqualTo("Index"));
        });
    }

    [Test]
    public async Task Page04_Post_InvalidAction_ShouldReturnViewWithModelError()
    {
        // Arrange
        var model = new Form470Page04ViewModel {
            Id = 1,
            Action = (FormAction)999
        };

        // Act
        var viewResult = await _controller.Page04(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(viewResult, Is.Not.Null);
            Assert.That(_controller.ModelState.ContainsKey(string.Empty), Is.True);
            Assert.That(_controller.ModelState[string.Empty]?.Errors, Is.Not.Empty);
        });
    }
    #endregion

    #region Confirmation
    [Test]
    public async Task Page05_Get_ShouldReturnViewWithModel()
    {
        // Act
        var result = await _controller.Page05(1);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult!.Model, Is.InstanceOf<Form470Page05ViewModel>());
    }
    [Test]
    public async Task Page05_Get_InvalidModel_ShouldReturnView()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model");
        // Act
        var result = await _controller.Page05(1) as NotFoundResult;

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public void Page05_Post_ValidModel_ShouldRedirectToDisclosureIndex()
    {
        // Act
        var model = new Form470Page05ViewModel() { Id = 1, Action = FormAction.Close };
        var result = _controller.Page05(model);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult, Is.Not.Null);
            Assert.That(redirectResult!.ActionName, Is.EqualTo("Index"));
        });
    }

    [Test]
    public void Page05_Post_InvalidModel_ShouldReturnView()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model");
        // Act
        var result = _controller.Page05(new Form470Page05ViewModel());

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
    }
    [Test]
    public async Task Page06_Get_ShouldReturnViewWithModel()
    {
        // Act
        var result = await _controller.Page06(1);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult!.Model, Is.InstanceOf<Form470Page06ViewModel>());
    }
    [Test]
    public async Task Page06_Get_InvalidModel_ShouldReturnView()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model");
        // Act
        var result = await _controller.Page06(1) as NotFoundResult;

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public void Page06Page_Post_ValidModel_ShouldRedirectToDisclosureIndex()
    {
        // Act
        var result = _controller.Page06(new Form470Page06ViewModel() { Id = 1, Action = FormAction.Close });

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult, Is.Not.Null);
            Assert.That(redirectResult!.ActionName, Is.EqualTo("Index"));
        });
    }

    [Test]
    public void Page06Page_Post_InvalidModel_ShouldReturnView()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model");

        // Act
        var result = _controller.Page06(new Form470Page06ViewModel());

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
    }
    #endregion

    #region Shared
    [Test]
    public void OnActionExecuting_ShouldSetCommonViewData()
    {
        // Arrange
        var actionContext = new ActionExecutingContext(
            new ActionContext
            {
                HttpContext = new DefaultHttpContext(),
                RouteData = new Microsoft.AspNetCore.Routing.RouteData(),
                ActionDescriptor = new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()
            },
            new List<IFilterMetadata>(),
            new Dictionary<string, object>()!,
            _controller
        );

        // Act
        _controller.OnActionExecuting(actionContext);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(_controller.ViewData.ContainsKey(LayoutConstants.Title), Is.True);
            Assert.That(_controller.ViewData[LayoutConstants.Title], Is.EqualTo(ResourceConstants.Form470AttestationTitle));
        });
    }
    #endregion
}

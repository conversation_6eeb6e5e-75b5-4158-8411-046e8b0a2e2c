using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.ControllerServices.SharedServices;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.Registrations.AmendSmoRegistration;
using SOS.CalAccess.FilerPortal.Models.SharedModels;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Constants;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using FilerRole = SOS.CalAccess.Models.Authorization.FilerRole;
using NoticeOfTerminationRequest = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.NoticeOfTerminationRequest;
using PhoneNumberDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.PhoneNumberDto;
using SlateMailerOrganizationRequest = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SlateMailerOrganizationRequest;
using SmoRegistrationAttestationResponseDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoRegistrationAttestationResponseDto;
using SmoRegistrationSendForAttestationRequest = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoRegistrationSendForAttestationRequest;


namespace SOS.CalAccess.FilerPortal.ControllerServices;

public class AmendSmoRegistrationCtlSvc(
    ISmoRegistrationSvc smoRegistrationSvc,
    IDateTimeSvc dateTimeSvc,
    IStringLocalizer<SharedResources> localizer,
    IUsersApi usersApi,
    ISmoRegistrationCtlSvc smoRegistrationCtlSvc,
    ISharedSmoRegistrationCtlSvc sharedSmoRegistrationCtlSvc,
    IDecisionsValidationMapService decisionsValidationMapService
    ) : IAmendSmoRegistrationCtlSvc
{
    #region Page01
    /// <inheritdoc />
    public async Task<AmendSmoRegistrationStep01ViewModel?> GetPage01ViewModel(long registrationId, CancellationToken cancellationToken)
    {
        // Create a new amendment registration. If the amendment already existed, return it's ID.
        var amendmentRegistration = await smoRegistrationSvc.CreateSmoAmendmentRegistrationAsync(registrationId);

        if (amendmentRegistration is not { } data)
        {
            return null;
        }
        else
        {
            return new AmendSmoRegistrationStep01ViewModel(data);
        }
    }

    /// <inheritdoc />
    public async Task<long?> Page01Submit(AmendSmoRegistrationStep01ViewModel model, ModelStateDictionary modelState, bool isSubmission = true)
    {
        var request = sharedSmoRegistrationCtlSvc.MapSmoViewModelToRequest(model);
        request.CheckRequiredFieldsFlag = isSubmission;

        var response = await sharedSmoRegistrationCtlSvc.SubmitSmoRegistrationForm(model, modelState, request);

        return response.Id;
    }
    #endregion

    #region Page02
    public async Task<AmendSmoRegistrationDetailsStep01ViewModel?> GetPage02ViewModel(long registrationId, CancellationToken cancellationToken)
    {
        try
        {
            var response = await smoRegistrationSvc.GetRegistrationFilingById(registrationId) ?? throw new ArgumentException($"{InvalidRegistrationIdErrorMessage}");

            // Get linked Recipient Committee
            var committee = await smoRegistrationSvc.GetLinkedRecipientCommitteeAsync(response.FilerId.GetValueOrDefault());

            var newModel = new AmendSmoRegistrationDetailsStep01ViewModel
            {
                Id = response.Id > 0 ? response.Id : registrationId,
                OrganizationLevelOfActivity = response.ActivityLevel ?? "",
                IsOrganizationQualified = response.QualifiedCommittee ?? null,
                IsOrganizationCampaignCommittee = response.CampaignCommittee ?? null,
                DateQualifiedAsSMO = response.DateQualified,
                CommitteeId = committee?.Id,
                CommitteeName = committee?.Name,
                IsTerminating = response.IsTerminating
            };

            return newModel;
        }
        catch (Exception ex)
        {
            // Handle specific deserialization issues or any other error
            Console.WriteLine("Error deserializing the response: " + ex.Message);
            return null;
        }
    }

    public async Task<long?> Page02ContinueSubmit(
        AmendSmoRegistrationDetailsStep01ViewModel model,
        ModelStateDictionary modelState,
        bool isSubmission = true)
    {
        if (model.Id == 0)
        {
            throw new ArgumentException($"{InvalidRegistrationIdErrorMessage}");
        }

        try
        {
            var request = new SlateMailerOrganizationRequest
            {
                CheckRequiredFieldsFlag = isSubmission,
                ActivityLevel = model.OrganizationLevelOfActivity,
                IsQualifiedCommittee = model.IsOrganizationQualified.GetValueOrDefault(false),
                DateQualified = model.DateQualifiedAsSMO,
                IsCampaignCommittee = model.IsOrganizationCampaignCommittee.GetValueOrDefault(false),
                CommitteeId = model.CommitteeId
            };

            var updateResult = await smoRegistrationSvc.UpdateSmoRegistration(model.Id, request);
            if (updateResult.ValidationErrors != null)
            {
                decisionsValidationMapService.ApplyErrorsToModelState(sharedSmoRegistrationCtlSvc.GetSmoFieldValidationMap(model), updateResult.ValidationErrors, modelState);
            }
            return model.Id;
        }
        catch (Exception ex)
        {
            throw new ArgumentException("Invalid request:" + ex);
        }
    }
    #endregion

    #region Page05
    /// <inheritdoc />
    public async Task<AmendSmoRegistrationStep02ViewModel?> GetPage05ViewModel(long registrationId, CancellationToken cancellationToken)
    {
        var response = await smoRegistrationSvc.GetSmoTreasurerPage02(registrationId);

        if (response?.Id == null)
        {
            return null;
        }
        else
        {
            var viewModel = new AmendSmoRegistrationStep02ViewModel(registrationId, response)
            {
                IsTerminating = await smoRegistrationSvc.IsRegistrationTerminatingAsync(registrationId)
            };

            return viewModel;
        }
    }
    #endregion

    #region Page06
    /// <inheritdoc />
    public async Task<AmendSmoRegistrationStep02ViewModel> GetPage06ViewModel(long id, CancellationToken cancellationToken)
    {
        var registrationContacts = await smoRegistrationSvc.GetSmoOfficers(id);
        var officerContacts = registrationContacts.Where(x => x.Role != FilerRole.SlateMailerOrg_AccountManager.Name);

        if (!officerContacts.Any())
        {
            return new AmendSmoRegistrationStep02ViewModel
            {
                Id = id,
            };
        }

        string deleteMessage = $"{localizer[ResourceConstants.SmoRegistration08DeleteOfficerModalMessage].Value}";

        var model = new AmendSmoRegistrationStep02ViewModel
        {
            Id = id,
            IsTerminating = await smoRegistrationSvc.IsRegistrationTerminatingAsync(id),
            OfficerContactsGridModel = new SmallDataGridModel
            {
                GridId = "OfficerContactsGrid",
                GridType = nameof(SmoOfficerGridDto),
                DataSource = officerContacts,
                DeleteConfirmationMessage = deleteMessage,
                PrimaryKey = nameof(SmoOfficerGridDto.ContactId),
                EnableExport = false,
                AllowPaging = false,
                AllowTextWrap = false,
                AllowAdding = false,
                AllowDeleting = true,
                AllowSearching = false,
                ActionItems = new List<GridActionItem>
                {
                    new() { Label = CommonResourceConstants.Edit, Action = "editRow", ControllerName= $"AmendSmoRegistration/{id}", ActionName = "EditOfficer" },
                    new() { Label = ResourceConstants.AmendSmoRegistrationPage06MakeTreasurer, Action = "callAction", ControllerName = $"AmendSmoRegistration/{id}", ActionName = "MakeTreasurer" },
                    new() { Label = CommonResourceConstants.Delete, Action = "deleteRow", ControllerName=$"AmendSmoRegistration/{id}", ActionName = "DeleteOfficer"}
                },
                Columns = new List<DataGridColumn>
                {
                    new() { Field = nameof(SmoOfficerGridDto.OfficerName), HeaderText = ResourceConstants.SmoRegistration08OfficerName },
                    new() { Field = nameof(SmoOfficerGridDto.Title), HeaderText = ResourceConstants.SmoRegistration08OfficerTitle },
                    new() { Field = nameof(SmoOfficerGridDto.StartDate), HeaderText = ResourceConstants.SmoRegistration08OfficerStartDate, IsUtcDate = true },
                    new() { Field = $"{nameof(SmoOfficerGridDto.PhoneNumber)}.{nameof(SmoOfficerGridDto.PhoneNumber.Number)}", HeaderText = ResourceConstants.SmoRegistration08OfficerPhoneNumber }
                },
            },
        };

        return model!;
    }

    /// <summary>
    /// Transfers Treasurer role to a new officer.
    /// </summary>
    public async Task TransferTreasurer(long registrationId, long newTreasurerId, bool keepOldTreasurer, string? newTitle, ModelStateDictionary modelState)
    {
        var request = new Services.Business.FilerRegistration.Registrations.Models.SmoTreasurerTransferDto
        {
            NewTreasurerId = newTreasurerId,
            PreviousTreasurerKeep = keepOldTreasurer,
            PreviousTreasurerTitle = newTitle
        };

        var response = await smoRegistrationSvc.PostSmoRegistrationTransferTreasurer(registrationId, request);

        if (!response.Valid)
        {
            Page05ApplyErrorsToModelState(response.ValidationErrors, modelState);
        }
    }
    #endregion

    #region Page09
    public async Task<AmendSmoRegistrationStep03ViewModel?> GetPage09ViewModel(long id, CancellationToken cancellationToken)
    {
        var officerContacts = await smoRegistrationSvc.GetSmoOfficers(id);

        var model = new AmendSmoRegistrationStep03ViewModel
        {
            Id = id,
            IndividualAuthorizersGridModel = new SmallDataGridModel(),
            IsTerminating = await smoRegistrationSvc.IsRegistrationTerminatingAsync(id),
        };

        string deleteMessage = $"{localizer[ResourceConstants.SmoRegistration12DeleteAuthorizerModalMessage].Value}";

        if (officerContacts is null || !officerContacts.Any(x => x.CanAuthorize ?? false))
        {
            return model;
        }

        var individualAuthorizers = officerContacts.Where(x => x.CanAuthorize ?? false);

        model.IndividualAuthorizersGridModel = new SmallDataGridModel
        {
            PrimaryKey = nameof(SmoOfficerGridDto.Id),
            GridId = "IndividualAuthorizersGrid",
            GridType = nameof(SmoOfficerGridDto),
            DataSource = individualAuthorizers,
            DeleteConfirmationField = nameof(SmoOfficerGridDto.Id),
            DeleteConfirmationMessage = deleteMessage,
            EnableExport = false,
            AllowPaging = false,
            AllowTextWrap = false,
            AllowAdding = false,
            AllowDeleting = true,
            AllowSearching = false,
            ActionItems = new List<GridActionItem>
            {
                new() { Label = CommonResourceConstants.Edit, Action = "editRow", ControllerName= $"AmendSmoRegistration/{id}", ActionName = "EditIndividualAuthorizer" },
                new() { Label = CommonResourceConstants.Delete, Action = "deleteRow", ControllerName = $"AmendSmoRegistration/{id}", ActionName = "DeleteIndividualAuthorizer" }
            },
            Columns = new List<DataGridColumn>
            {
                new() { Field = nameof(SmoOfficerGridDto.OfficerName), HeaderText = ResourceConstants.SmoRegistration08OfficerName },
                new() { Field = $"{nameof(SmoOfficerGridDto.PhoneNumber)}.{nameof(SmoOfficerGridDto.PhoneNumber.Number)}", HeaderText = ResourceConstants.SmoRegistration08OfficerPhoneNumber }
            },
        };

        return model;
    }
    #endregion

    #region Termination
    public async Task<AmendSmoRegistrationStep04ViewModel> Termination02ContinueSubmit(AmendSmoRegistrationStep04ViewModel model, ModelStateDictionary modelState, CancellationToken cancellationToken, bool isSubmission = true)
    {
        if (model.Id == null)
        {
            modelState.AddModelError(string.Empty, "Missing registration ID.");
            return model;
        }

        var terminationPayload = new NoticeOfTerminationRequest
        {
            IsTerminating = true,
            TerminationDate = model.EffectiveDateOfTermination + dateTimeSvc.GetCurrentDateTime().TimeOfDay, // TD: Find alternative way to append time value
            CheckRequiredFields = isSubmission
        };

        var response = await smoRegistrationSvc.UpdateNoticeOfTerminationSmoRegistrationAsync((long)model.Id!, terminationPayload);

        if (!response.Valid)
        {
            decisionsValidationMapService.ApplyErrorsToModelState(_termination02FieldValidationMap, response.ValidationErrors, modelState);
        }

        return model;
    }
    #endregion

    #region Page11
    /// <inheritdoc />
    public async Task<AmendSmoRegistrationStep04ViewModel> GetPage11ViewModel(long? registrationId, bool isAuthorizedToCompleteTreasurerAck, CancellationToken cancellationToken)
    {
        if (registrationId == null)
        {
            throw new KeyNotFoundException($"No registration id exists for registration");
        }

        var existingRegistration = await smoRegistrationSvc.GetRegistrationFilingById(registrationId.GetValueOrDefault()) ?? throw new ArgumentException("Invalid Registration ID.");

        // Get all treasurer acknowledgement contacts
        var response = await smoRegistrationSvc.GetTreasurerAcknowledgementContactsAsync(registrationId.GetValueOrDefault());

        // Get info of current user
        // As the contact does not associate with the user, will work around by search by filer user
        var userResponse = await usersApi.GetSelf(cancellationToken);

        // Find the contact that match with current user
        var matchedContact = response.Contacts.FirstOrDefault(x => x.UserId == userResponse.Id);

        //Find incomplete treasurer acknowledgement contacts
        var incompleteContacts = response.Contacts.Where(x => !x.HasAcknowledged.GetValueOrDefault());

        return new AmendSmoRegistrationStep04ViewModel
        {
            Id = registrationId,
            ResponsibleOfficers = [.. incompleteContacts.Select(x => new ResponsibleOfficerSharedViewModel // Exclude who acknowledged
            {
                FirstName = x.FirstName,
                LastName = x.LastName,
                Title = x.Title,
            })],
            FirstName = matchedContact?.FirstName,
            LastName = matchedContact?.LastName,
            Title = matchedContact?.Title,
            IsTreasurerOrAssistantTreasurer = isAuthorizedToCompleteTreasurerAck,
            IsAgreementAccepted = matchedContact?.HasAcknowledged ?? false,
            ExecutedOn = matchedContact?.AcknowledgedOn ?? dateTimeSvc.GetCurrentDateTime(),
            IsRequiredOtherAcknowledgements = !isAuthorizedToCompleteTreasurerAck && incompleteContacts.Any(), // If it's treasurer/ast.treasurer, skip. Otherwise, check the remaining
        };
    }

    /// <inheritdoc />
    public async Task<AmendSmoRegistrationStep04ViewModel> Page11Submit(AmendSmoRegistrationStep04ViewModel model)
    {
        if (model.Id == null)
        {
            throw new KeyNotFoundException($"No registration id exists for registration");
        }

        // Complete the treasurer acknowledgement
        await smoRegistrationSvc.CompleteTreasurerAcknowledgementAsync(model.Id.Value);

        var response = await smoRegistrationSvc.GetTreasurerAcknowledgementContactsAsync(model.Id.Value);

        return new AmendSmoRegistrationStep04ViewModel
        {
            Id = model.Id.Value,
            ResponsibleOfficers = [.. response.Contacts.Where(x => !x.HasAcknowledged.GetValueOrDefault()).Select(x => new ResponsibleOfficerSharedViewModel
            {
                FirstName = x.FirstName,
                LastName = x.LastName,
                Title = x.Title,
            })],
        };
    }

    /// <inheritdoc />
    public async Task Page11SendForAcknowledgement(AmendSmoRegistrationStep04ViewModel model)
    {
        if (model.Id == null)
        {
            throw new KeyNotFoundException($"No registration id exists for registration");
        }

        // Send notification to officer that has not been acknowledgement
        await smoRegistrationSvc.SendAcknowledgementNotificationsAsync(model.Id.Value);
    }
    #endregion

    #region Page12
    /// <inheritdoc />
    public async Task<AmendSmoRegistrationStep04ViewModel> GetPage12ViewModel(long? registrationId, bool isAuthorizedToAttest)
    {
        if (registrationId == null)
        {
            throw new KeyNotFoundException($"No registration id exists for registration");
        }

        var existingRegistration = await smoRegistrationSvc.GetRegistrationFilingById(registrationId.GetValueOrDefault()) ?? throw new ArgumentException($"{InvalidRegistrationIdErrorMessage}");

        // Get current user & filer user
        var userResponse = await usersApi.GetSelf();

        // Get responsible officer contacts
        var contacts = await smoRegistrationSvc.GetResponsibleOfficerContactsAsync(registrationId.GetValueOrDefault());

        // Find the contact that match with current user
        var matchedContact = contacts.FirstOrDefault(x => x.UserId == userResponse.Id);

        // If current user is authorized to attest, check if the registration already attested or not
        var attestationResponse = new SmoRegistrationAttestationResponseDto { };
        if (isAuthorizedToAttest)
        {
            attestationResponse = await smoRegistrationSvc.GetRegistrationAttestationAsync(registrationId.GetValueOrDefault());
        }

        // Find incomplete treasurer acknowledgement contacts
        var incompleteContacts = contacts.Where(x => !x.HasAcknowledged.GetValueOrDefault() && (x.Role == FilerRole.SlateMailerOrg_Treasurer.Name || x.Role == FilerRole.SlateMailerOrg_AssistantTreasurer.Name));

        return new AmendSmoRegistrationStep04ViewModel
        {
            ResponsibleOfficers = [.. contacts.Select(x => new ResponsibleOfficerSharedViewModel()
            {
                ContactId = x.Id,
                Title = x.Title,
                FirstName = x.FirstName,
                LastName = x.LastName
            })],
            Id = existingRegistration.Id,
            FirstName = attestationResponse.FirstName ?? matchedContact?.FirstName,
            LastName = attestationResponse.LastName ?? matchedContact?.LastName,
            Title = attestationResponse.Title ?? matchedContact?.Title,
            ExecutedOn = attestationResponse.ExecutedAt ?? dateTimeSvc.GetCurrentDateTime(),
            IsVerificationCertified = attestationResponse.ExecutedAt is not null,
            IsUserAuthorizedToAttest = isAuthorizedToAttest,
            IsAgreementAccepted = matchedContact?.HasAcknowledged ?? false,
            IsRequiredOtherAcknowledgements = incompleteContacts.Any(),
        };
    }

    /// <inheritdoc />
    public async Task Page12AttestRegistration(AmendSmoRegistrationStep04ViewModel model, ModelStateDictionary modelState)
    {
        if (model.Id is null)
        {
            throw new KeyNotFoundException($"No registration id exists for registration");
        }

        // Attest the registration
        var response = await smoRegistrationSvc.AttestRegistrationAsync(model.Id.GetValueOrDefault());

        // Add error to model state
        if (!response.Valid)
        {
            Page12ApplyErrorsToModelState(response.ValidationErrors, modelState);
        }
    }

    /// <inheritdoc />
    public async Task Page12SendForAttestation(AmendSmoRegistrationStep04ViewModel model, ModelStateDictionary modelState)
    {
        if (model.Id is null)
        {
            throw new KeyNotFoundException($"No registration id exists for registration");
        }

        var request = new SmoRegistrationSendForAttestationRequest
        {
            RegistrationRegistrationContactIds = model.SelectedResponsibleOfficersContactIds ?? new List<long> { }
        };

        // Send notification for attestation
        var response = await smoRegistrationSvc.SendRegistrationForAttestationAsync(model.Id.GetValueOrDefault(), request);

        // Add error to model state
        if (!response.Valid)
        {
            Page12ApplyErrorsToModelState(response.ValidationErrors, modelState);
        }
    }

    private static void Page12ApplyErrorsToModelState(List<CalAccess.Models.Common.WorkFlowError> errors, ModelStateDictionary modelState)
    {
        foreach (var error in errors)
        {
            modelState.AddModelError("", ReplaceFieldName(error.Message, error.FieldName));
        }
    }
    #endregion

    #region Page13
    public async Task<ConfirmationViewModel> Page13GetViewModel(long? registrationId)
    {
        if (registrationId is null)
        {
            throw new KeyNotFoundException($"No registration id exists for registration");
        }

        var existingRegistration = await smoRegistrationSvc.GetRegistrationFilingById(registrationId.GetValueOrDefault()) ?? throw new ArgumentException($"{InvalidRegistrationIdErrorMessage}");

        // Get pending items
        var response = await smoRegistrationSvc.GetPendingItemsAsync(registrationId.GetValueOrDefault());

        return new ConfirmationViewModel
        {
            Id = registrationId.GetValueOrDefault(),
            ExecutedOn = existingRegistration.SubmittedAt ?? dateTimeSvc.GetCurrentDateTime(),
            IsSubmission = true,
            PendingItems = [.. response.Select(x => new PendingItemSharedViewModel
            {
                Item = x.Item,
                Status = x.Status,
            })],
        };
    }
    #endregion

    #region Shared
    /// <inheritdoc />
    public async Task<AmendSmoRegistrationStep02ViewModel?> PagePrefill(long registrationId, long? contactId, bool? isTreasurerOrOfficer, CancellationToken cancellationToken)
    {
        var isTerminating = await smoRegistrationSvc.IsRegistrationTerminatingAsync(registrationId);

        if (contactId != null)
        {
            var officer = await smoRegistrationSvc.GetSmoOfficer(registrationId, contactId.GetValueOrDefault());

            var result = smoRegistrationCtlSvc.MapOfficerToSmoRegistrationStepViewModel<AmendSmoRegistrationStep02ViewModel>(officer, registrationId);
            result.ContactId = contactId;
            result.IsTerminating = isTerminating;

            return result;
        }

        if (isTreasurerOrOfficer.GetValueOrDefault())
        {
            var prefillModel = await GetCurrentUserInformation(registrationId, cancellationToken);
            prefillModel.IsTerminating = isTerminating;

            return prefillModel;
        }

        return null;
    }

    /// <inheritdoc />
    // Update method name for officer Page
    public async Task<AmendSmoRegistrationStep02ViewModel> Page05GetEmptyViewModel(long registrationId)
    {
        var isTerminating = await smoRegistrationSvc.IsRegistrationTerminatingAsync(registrationId);
        return new()
        {
            Id = registrationId,
            // Addresses list must contain an empty address record so that the page renders inputs for it.
            Addresses = new()
            {
                new() { Purpose = CommonConstants.Address.PurposeOfficer }
            },
            IsTerminating = isTerminating,
        };
    }

    /// <inhertidoc />
    public async Task<long?> Page05Submit(AmendSmoRegistrationStep02ViewModel model, ModelStateDictionary modelState, CancellationToken cancellationToken, bool isSubmission = true)
    {
        long? userId = null;
        if (model.IsUserTreasurer.GetValueOrDefault() || model.IsOfficer.GetValueOrDefault())
        {
            var userResponse = await usersApi.GetSelf(cancellationToken);
            userId = userResponse?.Id;
        }

        // If title is not present then must be on Page05, therefore set title to treasurer.
        model.ContactTitle = string.IsNullOrWhiteSpace(model.ContactTitle)
            ? FilerRole.SlateMailerOrg_Treasurer.Name
            : model.ContactTitle;

        var request = smoRegistrationCtlSvc.MapSmoRegistrationSharedModelToSmoRegistrationContactDto(model, userId);
        request.Id = model.ContactId;
        request.CheckRequiredFieldsFlag = isSubmission;

        if (model.Id == null)
        {
            throw new KeyNotFoundException($"No id exists for registration");
        }

        var response = await smoRegistrationSvc.PostSmoRegistrationContactsPage05((long)model.Id, request, false); // Handles create and update

        if (!response.Valid)
        {
            Page05ApplyErrorsToModelState(response.ValidationErrors, modelState);
        }

        return response.Id;
    }

    /// <inhertidoc />
    public async Task<AmendSmoRegistrationStep02ViewModel> InitStep02EmptyViewModelAsync(long id)
    {
        return new AmendSmoRegistrationStep02ViewModel()
        {
            Id = id,
            IsTerminating = await smoRegistrationSvc.IsRegistrationTerminatingAsync(id)
        };
    }

    /// <inheritdoc />
    public async Task<AmendSmoRegistrationStep03ViewModel> InitStep03EmptyViewModelAsync(long id)
    {
        return new()
        {
            Id = id,
            // Addresses list must contain an empty address record so that the page renders inputs for it.
            Addresses = new()
            {
                new() { Purpose = CommonConstants.Address.PurposeOfficer }
            },
            IsTerminating = await smoRegistrationSvc.IsRegistrationTerminatingAsync(id),
        };
    }

    /// <inheritdoc />
    public async Task<bool> IsUserTreasurerAssistantTreasurerOfficer(long registrationId)
    {
        var currentUserRoleId = await smoRegistrationSvc.GetCurrentUserRoleByIdAsync(registrationId);

        var validRoles = new List<long>
        {
            FilerRole.SlateMailerOrg_Treasurer.Id,
            FilerRole.SlateMailerOrg_AssistantTreasurer.Id,
            FilerRole.SlateMailerOrg_Officer.Id,
        };

        return validRoles.Contains(currentUserRoleId);
    }
    #endregion

    #region Page08
    /// <inheritdoc />
    public async Task<AmendSmoRegistrationStep02ViewModel> Page08GetEmptyViewModel(long registrationId)
    {
        return new()
        {
            Id = registrationId,
            // Addresses list must contain an empty address record so that the page renders inputs for it.
            Addresses = new()
            {
                new() { Purpose = CommonConstants.Address.PurposeOfficer }
            },
            IsTerminating = await smoRegistrationSvc.IsRegistrationTerminatingAsync(registrationId),
        };
    }
    #endregion

    #region Private
    private async Task<AmendSmoRegistrationStep02ViewModel> GetCurrentUserInformation(long id, CancellationToken cancellationToken)
    {
        // Return my information for prefill
        var response = await usersApi.GetSelf(cancellationToken);
        var address = new AddressViewModel()
        {
            Purpose = CommonConstants.Address.PurposeOfficer,
            Country = "United States",
            Type = "Residential",
            City = "Los Angeles",
            Street = "316 Brannon Street",
            State = "CA",
            Zip = "90057"
        };
        var myInfo = new AmendSmoRegistrationStep02ViewModel
        {
            Id = id,
            FirstName = response.FirstName,
            LastName = response.LastName,
            Email = response.Email,
            IsUserTreasurer = true,
            // Hard coded mock data for telephone number and address
            TelephoneNumber = new PhoneNumberDto
            {
                Number = "1112223333",
                Type = CommonConstants.PhoneNumber.TypeHome,
            },
            //Address = address
            Addresses = new() { address }
        };
        return myInfo;
    }

    private static readonly Dictionary<string, FieldProperty> _termination02FieldValidationMap = new()
    {
        { "EffectiveDateOfTermination", new FieldProperty(FieldName:"EffectiveDateOfTermination", LabelKey:ResourceConstants.RegistrationNoticeOfTerminationEffectiveDate) },
    };

    private void Page05ApplyErrorsToModelState(List<CalAccess.Models.Common.WorkFlowError> errors, ModelStateDictionary modelState)
    {
        foreach (var error in errors)
        {
            if (_page05FieldValidationMap.TryGetValue(error.FieldName, out var data))
            {
                modelState.AddModelError(data.FieldName, ReplaceFieldName(error.Message, localizer[data.LabelKey].Value));
            }
            else
            {
                // Errors to show at the bottom using Html.ValidationSummary
                modelState.AddModelError("", error.Message);
            }
        }
    }

    private static readonly Dictionary<string, FieldProperty> _page05FieldValidationMap = new()
    {
        { "Address.Country", new FieldProperty(FieldName:"Addresses[0].Country", LabelKey:ResourceConstants.SmoRegistration07Country) },
        { "Address.Street", new FieldProperty(FieldName:"Addresses[0].Street", LabelKey:ResourceConstants.SmoRegistration07Address) },
        { "Address.Street2", new FieldProperty(FieldName:"Addresses[0].Street2", LabelKey:ResourceConstants.SmoRegistration07Address2) },
        { "Address.City", new FieldProperty(FieldName:"Addresses[0].City", LabelKey:ResourceConstants.SmoRegistration07City) },
        { "Address.State", new FieldProperty(FieldName:"Addresses[0].State", LabelKey:ResourceConstants.SmoRegistration07State) },
        { "Address.Zip", new FieldProperty(FieldName:"Addresses[0].Zip", LabelKey:ResourceConstants.SmoRegistration07ZipCode) },
        { "Address.Type", new FieldProperty(FieldName:"Addresses[0].Type", LabelKey:ResourceConstants.SmoRegistration07TypeOfAddress) },
        { "IsUserAuthorized", new FieldProperty(FieldName:"IsUserAuthorized", LabelKey:ResourceConstants.SmoRegistration07IsUserAuthorized) },
        { "Title", new FieldProperty(FieldName:"ContactTitle", LabelKey:ResourceConstants.SmoRegistration10OfficerTitle) },
        { "FirstName", new FieldProperty(FieldName:"FirstName", LabelKey:ResourceConstants.SmoRegistration07FirstName) },
        { "MiddleName", new FieldProperty(FieldName:"MiddleName", LabelKey:ResourceConstants.SmoRegistration07MiddleName) },
        { "LastName", new FieldProperty(FieldName:"LastName", LabelKey:ResourceConstants.SmoRegistration07LastName) },
        { "Email", new FieldProperty(FieldName:"Email", LabelKey:ResourceConstants.SmoRegistration07Email) },
        { "PhoneNumber", new FieldProperty(FieldName:"TelephoneNumber", LabelKey:ResourceConstants.SmoRegistration07TelephoneNumber) },
    };

    private static string ReplaceFieldName(string template, string replacement)
    {
        return template.Replace("{{Field Name}}", replacement, StringComparison.Ordinal);
    }

    private const string InvalidRegistrationIdErrorMessage = "Invalid Registration ID.";
    #endregion
}

using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.EntityFramework.Repositories.UploadFile;
using SOS.CalAccess.Models.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.UploadFile;
public sealed class UploadedFileRepositoryTests
{
    private DatabaseContext _dbContext;
    private UploadedFileRepository _repository;
    private const string OriginalFileName = "upload.txt";
    private const string RelationshipType = "DisclosureFiling";
    private const long RelationshipId = 1362025;
    private const long NewRelationshipId = 1362026;
    private readonly Guid _originalFileNameGuid = Guid.NewGuid();
    private List<string> _fileGuids;
    private UploadedFile _uploadedFileEntity;

    [SetUp]
    public async Task SetUp()
    {
        _uploadedFileEntity = new()
        {
            OriginalFileName = OriginalFileName,
            RelationshipType = RelationshipType,
            RelationshipId = RelationshipId,
            FileName = _originalFileNameGuid,
            Path = "myblobcontainer",
        };
        _fileGuids = new() { _originalFileNameGuid.ToString() };
        var options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase(databaseName: "CARS")
            .Options;

        _dbContext = new DatabaseContext(options);
        await _dbContext.Database.EnsureCreatedAsync();

        _repository = new UploadedFileRepository(_dbContext);
        await _repository.Create(_uploadedFileEntity);
    }

    [TearDown]
    public void TearDown()
    {
        _dbContext.Database.EnsureDeleted();
        _dbContext.Dispose();
    }

    [Test]
    public async Task FindUploadByFileNameGuid()
    {
        var entity = await _repository.FindUploadByFileNameGuid(_uploadedFileEntity.FileName.ToString());
        Assert.That(entity, Is.InstanceOf<UploadedFile>());
    }

    [Test]
    public async Task GetUploadsByRelationshipId()
    {
        var entity = await _repository.GetUploadsByRelationshipId(RelationshipId);
        Assert.That(entity, Is.InstanceOf<List<UploadedFile>>());
    }

    [Test]
    public async Task UpdateUploadedFile()
    {
        var beforeUpdate = _uploadedFileEntity.UploadedFileStatusId;
        _uploadedFileEntity.UploadedFileStatusId++;
        var entity = await _repository.UpdateUploadedFile(_uploadedFileEntity);
        Assert.That(entity.UploadedFileStatusId, Is.EqualTo(beforeUpdate + 1));
    }

    [Test]
    public async Task DeleteUploadedFile()
    {
        var before = _dbContext.UploadedFiles.Count();
        await _repository.DeleteUploadedFile(_uploadedFileEntity);
        var after = _dbContext.UploadedFiles.Count();
        _ = await _repository.Create(_uploadedFileEntity);

        Assert.That(after, Is.EqualTo(before - 1));
    }

    #region FindUploadFilesByFileNameGuids
    [Test]
    public async Task FindUploadFilesByFileNameGuids_Found_ShouldReturnResult()
    {
        // Arrange
        var fileNameGuids = new List<Guid>
        {
            _originalFileNameGuid
        };

        // Act
        var result = await _repository.FindUploadFilesByFileNameGuids(fileNameGuids);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Is.InstanceOf<List<UploadedFile>>());
        });
    }

    [Test]
    public async Task FindUploadFilesByFileNameGuids_NotFound_ShouldReturnResult()
    {
        // Arrange
        var fileNameGuids = new List<Guid>
        {
            Guid.NewGuid()
        };

        // Act
        var result = await _repository.FindUploadFilesByFileNameGuids(fileNameGuids);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Empty);
            Assert.That(result, Is.InstanceOf<List<UploadedFile>>());
        });
    }
    #endregion

    #region UpdateRelationships
    [Test]
    public async Task UpdateRelationships_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var relationshipId = 1L;
        _uploadedFileEntity.RelationshipId = relationshipId;
        var uploadedFiles = new List<UploadedFile>
        {
            _uploadedFileEntity
        };

        // Act
        await _repository.UpdateRelationships(uploadedFiles);

        // Assert
        Assert.That(uploadedFiles.All(x => x.RelationshipId == relationshipId), Is.True);
    }
    #endregion
}

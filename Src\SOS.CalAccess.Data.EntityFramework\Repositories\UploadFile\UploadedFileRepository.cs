using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.Contracts.Common;
using SOS.CalAccess.Models.Common;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.UploadFile;

/// <summary>
/// UploadedFile repository implementation
/// </summary>
/// <param name="dbContext"></param>
public class UploadedFileRepository(DatabaseContext dbContext)
    : Repository<UploadedFile, long>(dbContext), IUploadedFileRepository
{
    public async Task<IEnumerable<UploadedFile>> GetUploadsByRelationshipId(long relationshipId)
    {
        return await dbSet.Where(upload => upload.RelationshipId == relationshipId)
            .ToListAsync();
    }

    public async Task<UploadedFile> UpdateUploadedFile(UploadedFile uploadedFile)
    {
        dbSet.Update(uploadedFile);
        await dbContext.SaveChangesAsync();
        return uploadedFile;
    }

    public async Task DeleteUploadedFile(UploadedFile uploadedFile)
    {
        dbSet.Remove(uploadedFile);
        await dbContext.SaveChangesAsync();
    }

    public async Task<UploadedFile?> FindUploadByFileNameGuid(string fileNameGuid)
    {
        return await dbSet.FirstOrDefaultAsync(upload => upload.FileName == Guid.Parse(fileNameGuid));
    }

    /// <inheritdoc />
    public async Task<List<UploadedFile>> FindUploadFilesByFileNameGuids(List<Guid> fileNameGuids)
    {
        return await dbSet.Where(u => fileNameGuids.Contains(u.FileName)).ToListAsync();
    }

    /// <inheritdoc />
    public async Task UpdateRelationships(List<UploadedFile> uploadedFiles)
    {
        dbSet.UpdateRange(uploadedFiles);
        await dbContext.SaveChangesAsync();
    }
}

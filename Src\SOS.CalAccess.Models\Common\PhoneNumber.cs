using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SOS.CalAccess.Models.Common;

/// <summary>
/// Entity that represents phone numbers that can be linked to various models.
/// </summary>
[Table("PhoneNumber")]
[Documentation("", Context = "This table holds system phone numbers.")]
public class PhoneNumber : IIdentifiable<long>
{
    /// <summary>
    /// Gets or sets the primary id for the phone number.
    /// </summary>
    [Documentation("Phone number identifier.")]
    public long Id { get; set; }

    /// <summary>
    /// Gets or sets the international phone number setting
    /// </summary>
    [Documentation("Indicates if this phone number is international.")]
    public bool InternationalNumber { get; set; }

    /// <summary>
    /// Gets or sets the country code this record.
    /// </summary>
    [MaxLength(10)]
    [Documentation("The actual dialed Country code. Should include the preceding + character")]
    public string? CountryCode { get; set; }

    /// <summary>
    /// Gets or sets the selected country.
    /// </summary>
    [Documentation("Foreign key reference to the country table.")]
    public long? CountryId { get; set; }
    public Country? Country { get; set; }


    /// <summary>
    /// Gets or sets the phone number for this record.
    /// </summary>
    [MaxLength(20)]
    [Documentation("Phone number.")]
    public required string Number { get; set; }

    /// <summary>
    /// Gets or sets the extension code for this record.
    /// </summary>
    [MaxLength(20)]
    [Documentation("Extension code.")]
    public string? Extension { get; set; }

    /// <summary>
    /// Gets or sets the type (intended purpose, e.g. home or work phone) for this record.
    /// </summary>
    [MaxLength(40)]
    [Documentation("Intended purpose of the phone number when linked to other entities.")]
    public required string Type { get; set; }

    /// <summary>
    /// Gets or sets the id of the phone number list this record is linked to.
    /// </summary>
    [Documentation("List to which this phone number is linked.", Context = "Reference to a phone number list.")]
    public long PhoneNumberListId { get; set; }

    /// <summary>
    /// Gets or sets primary phone number option
    /// </summary>
    [Documentation("Is Primary PhoneNumber.")]
    public bool IsPrimaryPhoneNumber { get; set; }

    //TODO: Clean up models below this line

    /// <summary>
    /// Gets or sets the created by user identifier.
    /// </summary>
    [Documentation("The identifier of the user who created this record.")]
    public long CreatedBy { get; set; }

    /// <summary>
    /// Gets or sets the modified by user identifier.
    /// </summary>
    [Documentation("The identifier of the user who last modified this record.")]
    public long ModifiedBy { get; set; }

    public void UpdateFrom(PhoneNumber? phoneNumber)
    {
        if (phoneNumber != null)
        {
            InternationalNumber = phoneNumber.InternationalNumber;
            CountryCode = phoneNumber.CountryCode;
            Number = phoneNumber.Number;
            Type = phoneNumber.Type;
        }
    }
}

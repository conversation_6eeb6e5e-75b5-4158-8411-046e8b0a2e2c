using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.Contracts.UserAccountMaintenance;
using SOS.CalAccess.Models.UserAccountMaintenance;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.UserAccountMaintenance;
public class UserRepository(DatabaseContext dbContext) : Repository<User, long>(dbContext), IUserRepository
{
    /// <inheritdoc />
    public async Task<long?> FindUserIdByUsername(string username)
    {
        var result = await dbContext.Users
             .Where(user => user.UserName == username)
             .Select(user => user.Id)
             .FirstOrDefaultAsync();

        if (result != default)
        {
            return result;
        }
        else
        {
            return null;
        }
    }

    /// <inheritdoc />
    public Task<List<User>> SearchUsers(UserSearchCriteria criteria)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task<List<User>> FindUsersByUserNames(List<string> userNames)
    {
        return await dbContext.Users
            .AsNoTracking()
            .Where(x => userNames.Contains(x.UserName))
            .ToListAsync();
    }

    public async Task<User?> GetUserByEntraOId(string entraOid)
    {
        return await dbContext.Users.AsNoTracking().Where(x => x.EntraOid == entraOid).FirstOrDefaultAsync();
    }

    /// <summary>
    /// Get user account details by user id
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    public async Task<User> GetUserAccountDetailsByUserId(long userId)
    {
        var result = await dbSet.Include(x => x.AddressList)
            .ThenInclude(x => x!.Addresses)
            .Include(x => x.PhoneNumberList)
            .ThenInclude(x => x!.PhoneNumbers)
            .AsSplitQuery()
            .Where(u => u.Id == userId)
            .FirstOrDefaultAsync();

        return result!;
    }

    /// <summary>
    /// Save changes to database
    /// </summary>
    /// <returns></returns>
    public async Task SaveChangesAsync()
    {
        await dbContext.SaveChangesAsync();
    }
}

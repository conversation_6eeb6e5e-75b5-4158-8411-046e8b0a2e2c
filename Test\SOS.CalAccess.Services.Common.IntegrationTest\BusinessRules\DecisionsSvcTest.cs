using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using NUnit.Framework.Internal;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Common.IntegrationTest.BusinessRules;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(DecisionsSvc))]
public sealed class DecisionsSvcTest : IDisposable
{
    private DecisionsSvc _decisionsSvc;
    private HttpClient _httpClient;
    private readonly ILogger<DecisionsSvc> _logger = new NullLogger<DecisionsSvc>();
    private readonly string _decisionsHost = "http://devcaldcs01:8080/Primary/restapi/Flow/";
    private readonly string _sessionId = "NS-01JFJZH87PSBE9D2H8GZK4VH9X";
    private readonly Dictionary<string, string> _inputObject = new() { { "FirstName", "Test" } };
    private readonly TestCandidateInfo _candidateInfo = new("<PERSON>", "<PERSON>", "Doe", "invalidEmailAddress", "Not a phone number", "***********", new TestAddress("", false, "", "", "", "", "", ""), new TestAddress("", false, "", "", "", "", "", ""));

    [SetUp]
    public void Setup()
    {
        _httpClient = new HttpClient();
        DecisionsSvcOptions options = new(_decisionsHost, _sessionId);
        _decisionsSvc = new DecisionsSvc(_logger, _httpClient, options);
    }

    [Test]
    public void ThrowsExceptionIfHostNotConfigured()
    {
        // Arrange
        DecisionsSvcOptions options = new(null, "sessionId");
        //Act
        try
        {
            DecisionsSvc service = new(_logger, _httpClient, options);
            Assert.Fail("Expected exception not thrown");
        }
        catch (Exception) { }
    }

    [Test]
    public void ThrowsExceptionIfSessionIdNotConfigured()
    {
        // Arrange
        DecisionsSvcOptions options = new("Host", null);
        //Act
        try
        {
            DecisionsSvc service = new(_logger, _httpClient, options);
            Assert.Fail("Expected exception not thrown");
        }
        catch (Exception) { }
    }

    [Test]

    public async Task InitiateWorkflowWithGenericInputOutputTypes()
    {
        // Arrange

        //Act
        List<Dictionary<string, object>>? result = await _decisionsSvc.InitiateWorkflow<Dictionary<string, string>, List<Dictionary<string, object>>>(DecisionsWorkflow.CandidateInformationRuleset, _inputObject, false);

        //Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]

    public async Task InitiateWorkflowReturnsListWorkFlowErrors()
    {
        // Arrange

        //Act
        List<WorkFlowError>? result = await _decisionsSvc.InitiateWorkflow<TestCandidateInfo?, List<WorkFlowError>>(DecisionsWorkflow.CandidateInformationRuleset, _candidateInfo);

        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.Not.Empty);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].FieldName, Is.Not.Empty);
            Assert.That(result[0].Message, Is.Not.Empty);
            Assert.That(result[0].ErrorCode, Is.Not.Empty);
            Assert.That(result[0].ErrorType, Is.Not.Empty);
        });
    }

    [Test]
    public async Task InitiateWorkflowNullInput()
    {
        // Arrange

        //Act
        List<WorkFlowError>? result = await _decisionsSvc.InitiateWorkflow<TestCandidateInfo?, List<WorkFlowError>>(DecisionsWorkflow.CandidateInformationRuleset, null);

        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.Not.Empty);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].FieldName, Is.Not.Empty);
            Assert.That(result[0].Message, Is.Not.Empty);
            Assert.That(result[0].ErrorCode, Is.Not.Empty);
            Assert.That(result[0].ErrorType, Is.Not.Empty);
        });
    }

    [Test]

    public async Task InitiateWorkflowNullInputRequiredFieldsChecked()
    {
        // Arrange

        //Act
        List<WorkFlowError>? result = await _decisionsSvc.InitiateWorkflow<TestCandidateInfo?, List<WorkFlowError>>(DecisionsWorkflow.CandidateInformationRuleset, null, false);

        //Assert
        Assert.That(result?.Any(x => x.ErrorCode == "ErrGlobal0001"), Is.True);
    }

    public void Dispose()
    {
        _httpClient.Dispose();
    }

    private record TestAddress(string Type, bool IsMailingAddress, string Street, string Street2, string City, string State, string Zip, string Country);

    private record TestCandidateInfo(string FirstName, string MiddleName, string LastName, string Email, string PhoneNumber, string FaxNumber, TestAddress Address1, TestAddress Address2);
}

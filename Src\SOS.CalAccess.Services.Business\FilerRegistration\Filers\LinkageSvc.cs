using System.Collections.ObjectModel;
using System.Globalization;
using System.Security.Cryptography;
using SOS.CalAccess.Data.Contracts.Authorization;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.FilerRegistration.Filers;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Data.Notifications;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Common.BusinessRules.Models;
using SOS.CalAccess.Services.Common.Email;
using SOS.CalAccess.Services.Common.Email.Model;
using LinkageRequestDto = SOS.CalAccess.Models.FilerRegistration.Filers.LinkageRequestDto;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Filers;
#pragma warning disable S107 // Methods should not have too many parameters
public sealed class LinkageSvc(
    ILinkageRequestRepository linkageRequestRepository,
    IFilerRepository filerRepository,
    IFilerUserRepository filerUserRepository,
    IFilerRoleRepository filerRoleRepository,
    INotificationSvc notificationSvc,
    IUserMaintenanceSvc userMaintenanceSvc,
    IDecisionsSvc decisionsSvc,
    IDateTimeSvc dateTimeSvc,
    IRegistrationContactRepository registrationContactRepository,
    IEmailSvc emailSvc,
    INotificationTemplateRepository notificationTemplateRepository
    ) : ILinkageSvc
#pragma warning restore S107 // Methods should not have too many parameters
{
    private const string UserNamePlaceholder = "UserName";
    private const string FilerNamePlaceholder = "FilerName";
    private const string OfficerNamePlaceholder = "OfficerName";
    private const string RequesterNamePlaceholder = "RequesterName";
    private const string RequesterEmailPlaceholder = "RequesterEmail";
    private const string AssignedRolePlaceholder = "AssignedRole";
    private const string RequestDatePlaceholder = "RequestDate";
    private const string CreateAnAccountUrlPlaceholder = "CreateAnAccountUrl";
    private const string InvitationCodePlaceholder = "InvitationCode";
    private const string OrganizationNamePlaceholder = "OrganizationName";

    /// <inheritdoc />
    public async Task<LinkageResponseDto> GetLinkages()
    {
        var currentUser = await userMaintenanceSvc.GetCurrentUser();

        return new LinkageResponseDto
        {
            CurrentLinkages = await GetCurrentLinkagesByUserId(currentUser.Id),
            PendingLinkages = await GetPendingLinkageRequestsByUserId(currentUser.Id),
            RejectedLinkages = await GetRejectedLinkagesByUserId(currentUser.Id)
        };

    }

    /// <inheritdoc />
    public async Task<long> SendLinkageRequestToEmail(SendLinkageRequestToEmailDto data)
    {
        var baseData = await GetDataForSendLinkageRequest(data.FilerId, data.FilerRoleId);

        // Create LinkageRequest
        var linkageRequest = await linkageRequestRepository.Create(new()
        {
            FilerId = data.FilerId,
            FilerRoleId = data.FilerRoleId,
            Email = data.RecipientEmail,
            RequestedById = baseData.RequesterId,
            SendDate = dateTimeSvc.GetCurrentDateTime(),
            LinkageStatusId = LinkageStatus.Sent.Id,
            LinkageCode = GenerateLinkageCode(),
            IsInvitation = true,
        });

        baseData.TemplateData.Add(RequestDatePlaceholder, linkageRequest.SendDate.ToString("MMMM dd, yyyy hh:mm tt", CultureInfo.InvariantCulture));
        baseData.TemplateData.Add(InvitationCodePlaceholder, linkageRequest.LinkageCode!);

        var recipient = new CalAccessEmailRecipient
        {
            EmailAddress = data.RecipientEmail,
            Name = string.Empty,
            UserId = baseData.RequesterId,
        };

        await SendTemplatedEmailToNonUser(new()
        {
            NotificationTemplateId = NotificationTemplate.LinkageRequestForEmail.Id,
            Recipients = new() { recipient },
            TemplateData = baseData.TemplateData
        });

        return linkageRequest.Id;
    }

    /// <inheritdoc />
    public async Task<long> SendLinkageRequestToPerson(SendLinkageRequestToPersonDto data)
    {
        var baseData = await GetDataForSendLinkageRequest(data.FilerId, data.FilerRoleId);

        // Create LinkageRequest
        var linkageRequest = await linkageRequestRepository.Create(new()
        {
            FilerId = data.FilerId,
            FilerRoleId = data.FilerRoleId,
            Email = data.RecipientEmail,
            RequestedById = baseData.RequesterId,
            SendDate = dateTimeSvc.GetCurrentDateTime(),
            LinkageStatusId = LinkageStatus.Sent.Id,
            LinkageCode = GenerateLinkageCode(),
            IsInvitation = true,
            RegistrationContactId = data.RegistrationContactId,
        });

        baseData.TemplateData.Add(OfficerNamePlaceholder, data.RecipientName ?? "");
        baseData.TemplateData.Add(RequestDatePlaceholder, linkageRequest.SendDate.ToString("MMMM dd, yyyy hh:mm tt", CultureInfo.InvariantCulture));
        baseData.TemplateData.Add(InvitationCodePlaceholder, linkageRequest.LinkageCode!);

        var recipient = new CalAccessEmailRecipient
        {
            EmailAddress = data.RecipientEmail,
            Name = data.RecipientName ?? "",
            UserId = baseData.RequesterId,
        };

        await SendTemplatedEmailToNonUser(new()
        {
            NotificationTemplateId = NotificationTemplate.LinkageRequestForOfficer.Id,
            Recipients = new() { recipient },
            TemplateData = baseData.TemplateData
        });

        return linkageRequest.Id;
    }

    /// <inheritdoc />
    public async Task<long> SendLinkageRequestToFiler(SendLinkageRequestToFilerDto data)
    {
        var baseData = await GetDataForSendLinkageRequest(data.FilerId, data.FilerRoleId);

        // Create LinkageRequest
        var linkageRequest = await linkageRequestRepository.Create(new()
        {
            LinkageStatusId = LinkageStatus.Sent.Id,
            FilerId = data.FilerId,
            RequestedById = baseData.RequesterId,
            FilerRoleId = data.FilerRoleId,
            SendDate = dateTimeSvc.GetCurrentDateTime(),
            IsInvitation = false,
        });

        baseData.TemplateData.Add(RequestDatePlaceholder, linkageRequest.SendDate.ToString("MMMM dd, yyyy hh:mm tt", CultureInfo.InvariantCulture));

        var notificationRequest = new SendFilerNotificationRequest(NotificationTemplate.LinkageRequestForFiler.Id, linkageRequest.FilerId, null, baseData.TemplateData);

        await notificationSvc.SendFilerNotification(notificationRequest);

        return linkageRequest.Id;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<LinkageFilerSearchResponseDto>> GetFilersBySearchCriteria(FilerSearchRequestDto filerSearchRequestDto)
    {
        var filers = await filerRepository.SearchFilers(filerSearchRequestDto.Query, filerSearchRequestDto.FilerTypeId);

        var result = filers
            .Where(f => f.CurrentRegistration != null)
            .Select(f =>
            {
                var response = new LinkageFilerSearchResponseDto
                {
                    FilerId = f.Id,
                    FilerName = f.CurrentRegistration!.Name,
                };

                var rrc = f.CurrentRegistration!.RegistrationRegistrationContacts.FirstOrDefault();

                var r = f.CurrentRegistration!;
                if (r is not null)
                {
                    var addresses = r.AddressList?.Addresses ?? new();
                    var addressDtos = addresses.Select(x => new AddressDto(x)).ToList();
                    var phoneNumber = r.PhoneNumberList?.PhoneNumbers?.FirstOrDefault(x => x.Type != "Fax");
                    var email = r.Email ?? string.Empty;
                    response.Email = email;
                    response.Addresses = addressDtos;
                    response.TelephoneNumber = new PhoneNumberDto(phoneNumber).ToString();
                }

                if (rrc is not null)
                {
                    var officerName = string.Join(" ", new[]
                    {
                        rrc.RegistrationContact?.FirstName,
                        rrc.RegistrationContact?.MiddleName,
                        rrc.RegistrationContact?.LastName
                    }.Where(n => !string.IsNullOrWhiteSpace(n)));
                    response.OfficerName = officerName;
                }

                if (f.FilerType.Id == FilerType.Lobbyist.Id)
                {
                    response.LobbyistEmployerOrFirm = f.FilerLinks?
                                                .Where(f1 => f1.FilerLinkType.Id == FilerLinkType.LobbyingFirm.Id || f1.FilerLinkType.Id == FilerLinkType.LobbyistEmployer.Id)
                                                .Select(f1 => f1.LinkedEntity?.CurrentRegistration?.Name)
                                                .FirstOrDefault() ?? string.Empty;
                }

                return response;
            })
            .ToList();

        return result;
    }

    /// <inheritdoc />
    public async Task<LinkageReviewResponseDto> GetLinkageRequestForReview(LinkageReviewRequestDto request)
    {
        var result = await linkageRequestRepository.GetLinkageByCode(request.InvitationCode);

        if (result == null)
        {
            return new LinkageReviewResponseDto();
        }
        return new LinkageReviewResponseDto(result);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<OfficerResponseDto>> GetOfficers(long filerId)
    {
        var filer = await filerRepository.GetFilerById(filerId);

        var contacts = filer?.CurrentRegistration?.RegistrationRegistrationContacts;
        if (contacts == null || contacts.Count == 0)
        {
            return [];
        }

        var officers = contacts
            .Where(rrc => rrc.RegistrationContact != null)
            .Select(rrc => new OfficerResponseDto
            {
                FullName = string.Join(" ", new[]
                {
                rrc.RegistrationContact!.FirstName,
                rrc.RegistrationContact.MiddleName,
                rrc.RegistrationContact.LastName
                }.Where(name => !string.IsNullOrWhiteSpace(name))),
                Role = rrc.Role ?? string.Empty
            })
            .ToList();

        return officers;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<LinkageRequestDto>> GetPendingUserLinkages(long filerId)
    {
        var linkageResponse = await linkageRequestRepository.GetPendingUserLinkagesByFilerId(filerId);

        if (linkageResponse == null) { return []; }

        var linkageReviewResponses = linkageResponse.ToList();

        return linkageReviewResponses;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<LinkageDto>> GetCurrentAuthorizedUsers(long filerId)
    {
        var linkageResponse = await linkageRequestRepository.GetCurrentAuthorizedUsersByFilerId(filerId);

        if (linkageResponse == null) { return []; }

        var linkageReviewResponses = linkageResponse.ToList();

        return linkageReviewResponses;
    }

    /// <inheritdoc />
    public async Task TerminateLinkageForUser(long filerUserId)
    {
        var filerUser = await filerUserRepository.GetFilerUserById(filerUserId)
            ?? throw new InvalidOperationException($"No filer user found for FilerUserId {filerUserId}.");

        if (filerUser.Filer == null)
        {
            throw new InvalidOperationException($"Filer with ID {filerUser.FilerId} not found.");
        }

        // Call decisions to get notification templates
        DecisionResponse decisionResponse = await decisionsSvc.InitiateWorkflow<string, DecisionResponse>(DecisionsWorkflow.TerminateLinkageRuleset, string.Empty)
            ?? throw new InvalidOperationException($"Unable to get decisions notification templates");

        // Get notification information
        var userName = (filerUser.User?.FirstName ?? "") + " " + (filerUser.User?.LastName ?? "");
        var filerName = filerUser.Filer.CurrentRegistration?.Name ?? string.Empty;
        var notificationData = new Dictionary<string, string> { { FilerNamePlaceholder, filerName }, { UserNamePlaceholder, userName } };

        // Delete linkage
        var success = await filerUserRepository.Delete(filerUser);
        if (!success)
        {
            throw new IOException($"Unable to delete record.  Id={filerUserId}");
        }

        // Send Notifications
        if (decisionResponse.Notifications?.Count > 0)
        {
            await SendNotifications(
            decisionResponse.Notifications,
            filerUser.UserId,
            filerUser.FilerId,
            notificationData);
        }
    }


    /// <inheritdoc />
    public async Task TerminateFilerUser(long filerId, long filerUserId)
    {
        var filerUser = await filerUserRepository.FindById(filerUserId)
            ?? throw new KeyNotFoundException($"FilerUser not found. Id={filerUserId}");

        // This exists only for this check otherwise can use TerminateLinkageForUser
        if (filerUser.FilerId != filerId)
        {
            throw new ArgumentException($"FilerId {filerUser.FilerId} not assigned to this FilerUser");
        }

        await TerminateLinkageForUser(filerUserId);
    }

    /// <inheritdoc />
    public async Task AcceptLinkageRequest(long linkageId)
    {
        var (linkageRequest, filer) = await HandleLinkageRequest(linkageId, LinkageStatus.Accepted.Id);

        await ApplyAcceptanceUpdatesAsync(linkageRequest);

        await CallDecisions(linkageRequest, DecisionsWorkflow.AcceptLinkageRequestRuleset, filer);
    }

    /// <inheritdoc />
    public async Task RejectLinkageRequest(long linkageId)
    {
        var (linkageRequest, filer) = await HandleLinkageRequest(linkageId, LinkageStatus.Rejected.Id);

        await CallDecisions(linkageRequest, DecisionsWorkflow.RejectLinkageRequestRuleset, filer);
    }

    #region private methods

    private async Task<(LinkageRequest, Filer)> HandleLinkageRequest(long linkageId, long targetStatusId)
    {
        var linkageRequest = await linkageRequestRepository.FindById(linkageId)
            ?? throw new InvalidOperationException($"Linkage Request with ID {linkageId} not found.");

        if (linkageRequest.LinkageStatusId != LinkageStatus.Sent.Id)
        {
            throw new InvalidOperationException($"Cannot change status of a linkage request that is not in 'Sent' status. Id={linkageId}, CurrentStatus={linkageRequest.LinkageStatusId}");
        }

        var filer = await filerRepository.GetFilerById(linkageRequest.FilerId)
            ?? throw new InvalidOperationException($"Filer with ID {linkageRequest.FilerId} not found.");

        var currentUser = await userMaintenanceSvc.GetCurrentUser();

        // Update LinkageRequest status
        linkageRequest.LinkageStatusId = targetStatusId;
        linkageRequest.ResolvedById = currentUser.Id;
        await linkageRequestRepository.Update(linkageRequest);

        return (linkageRequest, filer);
    }

    private async Task ApplyAcceptanceUpdatesAsync(LinkageRequest linkageRequest)
    {
        // Create FilerUser
        var filerUserEntity = new FilerUser
        {
            FilerId = linkageRequest.FilerId,
            FilerRoleId = linkageRequest.FilerRoleId
        };

        // If the user is an invitation, the filer role is linked to the resolving user.
        // Otherwise it is linked to the requesting user.
        if (linkageRequest.IsInvitation.GetValueOrDefault())
        {
            filerUserEntity.UserId = linkageRequest.ResolvedById!.Value;
        }
        else
        {
            filerUserEntity.UserId = linkageRequest.RequestedById;
        }

        await filerUserRepository.Update(filerUserEntity);

        //This logic happens only in UAM flow.
        if (linkageRequest.RegistrationContactId.HasValue)
        {
            var registrationContact = await registrationContactRepository.FindById(linkageRequest.RegistrationContactId.Value);
            if (registrationContact != null)
            {
                await registrationContactRepository.UpdateProperty(registrationContact, static r => r.UserId, linkageRequest.ResolvedById!.Value);
            }
        }
    }

    private async Task SendNotifications(List<NotificationTrigger> notifications, long userId, long filerId, Dictionary<string, string> notificationData)
    {
        foreach (var notification in notifications)
        {
            if (!notification.SendNotification || notification.NotificationTemplateId is null)
            {
                continue;
            }

            switch (notification.NotificationRecipient)
            {
                case NotificationRecipient.InitiatingUser:
                    await notificationSvc.SendUserNotification(
                        new SendUserNotificationRequest(
                            notification.NotificationTemplateId.Value,
                            userId,
                            filerId,
                            null,
                            notificationData));
                    break;

                case NotificationRecipient.FilerUsers:
                    await notificationSvc.SendFilerNotification(
                        new SendFilerNotificationRequest(
                            notification.NotificationTemplateId.Value,
                            filerId,
                            null,
                            notificationData));
                    break;
                default:
                    break;
            }
        }
    }

    private async Task CallDecisions(LinkageRequest linkageRequest, DecisionsWorkflow decisionsWorkflow, Filer filer)
    {
        var filerName = filer.CurrentRegistration?.Name ?? string.Empty;

        // Call decisions service
        var decisionsResponse = await decisionsSvc.InitiateWorkflow<string, DecisionResponse>(
          decisionsWorkflow,
           string.Empty);

        var notificationData = new Dictionary<string, string> { { FilerNamePlaceholder, filerName } };

        if (decisionsResponse != null && decisionsResponse.Notifications.Count > 0)
        {
            await SendNotifications(
            decisionsResponse.Notifications,
            linkageRequest.ResolvedById!.Value,
            linkageRequest.FilerId,
            notificationData);
        }
    }

    private async Task<List<LinkageRequestDto>> GetPendingLinkageRequestsByUserId(long userId)
    {
        return await linkageRequestRepository.GetPendingLinkageRequestsByUserId(userId);
    }

    private async Task<List<LinkageRequestDto>> GetRejectedLinkagesByUserId(long userId)
    {
        return await linkageRequestRepository.GetRejectedLinkageRequestsByUserId(userId);
    }

    private async Task<List<LinkageDto>> GetCurrentLinkagesByUserId(long userId)
    {
        return await linkageRequestRepository.GetLinkagesByUserId(userId);
    }

    private static string GenerateLinkageCode(int length = 12)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        var code = new char[length];

        using var rng = RandomNumberGenerator.Create();
        var buffer = new byte[4]; // For random integers

        for (int i = 0; i < length; i++)
        {
            rng.GetBytes(buffer);
            int num = BitConverter.ToInt32(buffer, 0) & int.MaxValue;
            code[i] = chars[num % chars.Length];
        }

        return new string(code);
    }

    private sealed class SendTemplatedEmailToNonUserRequest
    {
        public long NotificationTemplateId { get; set; }

        public required Collection<CalAccessEmailRecipient> Recipients { get; set; }

        public required Dictionary<string, string> TemplateData { get; set; }
    }

    private async Task SendTemplatedEmailToNonUser(SendTemplatedEmailToNonUserRequest request)
    {
        NotificationTemplate template = await notificationTemplateRepository.FindById(request.NotificationTemplateId)
            ?? throw new KeyNotFoundException($"Unable to find NotificationTemplate {request.NotificationTemplateId}");

        var translation = template.EnglishTranslation();

        if (string.IsNullOrEmpty(translation.EmailTemplateId))
        {
            string subject = ReplaceVariables(translation.Subject, request.TemplateData);
            string message = ReplaceVariables(translation.Message, request.TemplateData);

            var emailMsgRequest = new EmailMessageRequest(request.Recipients, null, subject, message);

            await emailSvc.SendPlainEmail(emailMsgRequest);
        }
        else
        {
            var sendEmailRequest = new EmailTemplateRequest(request.Recipients, null, translation.EmailTemplateId, request.TemplateData);

            await emailSvc.SendTemplatedEmail(sendEmailRequest);
        }

    }

    private static string ReplaceVariables(string text, IDictionary<string, string>? notificationData)
    {
        if (notificationData != null)
        {
            foreach (var key in notificationData.Keys)
            {
                var pattern = "{{" + key + "}}";
                text = text.Replace(pattern, notificationData[key], StringComparison.CurrentCultureIgnoreCase);
            }
        }
        return text;
    }

    private sealed class SendLinkageRequestBaseData
    {
        public required string FilerName { get; set; }
        public required string FilerRoleName { get; set; }
        public long RequesterId { get; set; }
        public required string RequesterEmail { get; set; }
        public required string RequesterName { get; set; }
        public Dictionary<string, string> TemplateData { get; set; } = [];
    }

    private async Task<SendLinkageRequestBaseData> GetDataForSendLinkageRequest(long filerId, long filerRoleId)
    {
        var filer = await filerRepository.GetFilerById(filerId)
            ?? throw new InvalidOperationException($"Filer with ID {filerId} not found.");

        var filerRole = await filerRoleRepository.FindById(filerRoleId)
            ?? throw new InvalidOperationException($"No filer role found for FilerRoleId {filerRoleId}.");

        if (filerRole.FilerTypeId != filer.FilerTypeId)
        {
            throw new InvalidOperationException($"Filer role {filerRoleId} is not valid for filer type {filer.FilerTypeId}.");
        }
        var currentUser = await userMaintenanceSvc.GetCurrentUser();
        // Data for template
        var templateData = new Dictionary<string, string>
        {
            { RequesterNamePlaceholder, $"{currentUser.FirstName ?? ""} {currentUser.LastName ?? ""}".Trim() },
            { RequesterEmailPlaceholder, currentUser.Email },
            { AssignedRolePlaceholder, filerRole.Name },
            { FilerNamePlaceholder, filer.CurrentRegistration?.Name ?? "" },
            { CreateAnAccountUrlPlaceholder, "https://devfilerportal.azurewebsites.us/" },
            { "CalAccessUrl", "https://devfilerportal.azurewebsites.us/" },
            { OrganizationNamePlaceholder, "[Organization Name]" }
        };
        return new()
        {
            FilerName = filer.CurrentRegistration?.Name ?? string.Empty,
            FilerRoleName = filerRole.Name,
            RequesterId = currentUser.Id,
            RequesterEmail = currentUser.Email,
            RequesterName = $"{currentUser.FirstName ?? ""} {currentUser.LastName ?? ""}".Trim(),
            TemplateData = templateData,
        };
    }

    #endregion

}


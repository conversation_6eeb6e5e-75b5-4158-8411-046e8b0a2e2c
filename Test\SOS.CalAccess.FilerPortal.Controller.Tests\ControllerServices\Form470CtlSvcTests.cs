using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using NSubstitute;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.ControllerServices.Form470;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Registrations.Form470;
using SOS.CalAccess.FilerPortal.Models.Registrations.Form470Attestation;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.UI.Common;
using FilingModels = SOS.CalAccess.Models.FilerDisclosure.Filings.Models;
using Form470AmendResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.Form470AmendResponseDto;
using Form470AttestRequest = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.Form470AttestRequest;
using Form470Committee = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.Form470Committee;
using RegistrationModels = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.FilerPortal.Tests.ControllerServices;
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(nameof(CandidateRegistrationCtlSvc))]
internal sealed class Form470CtlSvcTests
{
    private IFilingsApi _filingsApi;
    private IForm470Svc _form470Svc;
    private IForm470AmendSvc _form470AmendSvc;
    private IStringLocalizer<SharedResources> _localizer;
    private IFilersApi _filersApi;
    private Form470CtlSvc _service;
    private IDateTimeSvc _mockDateTimeSvc;

    [SetUp]
    public void Setup()
    {
        _filingsApi = Substitute.For<IFilingsApi>();
        _form470Svc = Substitute.For<IForm470Svc>();
        _form470AmendSvc = Substitute.For<IForm470AmendSvc>();
        _localizer = Substitute.For<IStringLocalizer<SharedResources>>();
        _filersApi = Substitute.For<IFilersApi>();
        _mockDateTimeSvc = Substitute.For<IDateTimeSvc>();
        _service = new Form470CtlSvc(_filingsApi, _form470Svc, _form470AmendSvc, _mockDateTimeSvc, _localizer, _filersApi);

        _localizer[Arg.Any<string>()].Returns(p => new LocalizedString((string)p[0], (string)p[0]));
    }

    #region Core
    [Test]
    public async Task Cancel()
    {
        // Arrange
        long id = 1;
        // Act
        await _service.Cancel(id);
        // Assert
        await _filingsApi.Received(1).CancelFiling(Arg.Any<long>());
    }

    [Test]
    public async Task UserIsCandidate_NoFiling_ReturnFalse()
    {
        // Arrange
        long filingId = 1;
        var form470 = new FilingModels.Form470OverviewResponse();
        _form470Svc.GetForm470Overview(Arg.Any<long>()).Returns(form470);
        // Act
        var result = await _service.UserIsCandidate(filingId);
        // Assert
        Assert.That(result, Is.False);
    }

    [Test]
    public async Task UserIsCandidate_Valid_ReturnsResult()
    {
        // Arrange
        long filingId = 1;
        var form470 = new FilingModels.Form470OverviewResponse
        {
            Form470Filing = new()
            {
                FilerId = 2
            }
        };
        _form470Svc.GetForm470Overview(Arg.Any<long>()).Returns(form470);
        _filersApi.CurrentUserHasFilerRole(Arg.Any<long>(), Arg.Any<long>()).Returns(true);
        // Act
        var result = await _service.UserIsCandidate(filingId);
        // Assert
        Assert.That(result, Is.True);
    }

    [Test]
    public async Task Amend_Valid_ReturnId()
    {
        // Arrange
        long id = 1;
        Form470AmendResponseDto response = new(1, 1);

        _form470AmendSvc.Initialize470Amendment(Arg.Any<long>()).Returns(response);
        // Act
        var result = await _service.InitializeForm470Amendment(id);
        // Assert
        Assert.That(result, Is.EqualTo(1));
    }

    [Test]
    public void Amend_InValid_Exception()
    {
        // Arrange
        long id = 1;
        string errorMessage = "Error Message";
        Form470AmendResponseDto response = new(errorMessage);

        _ = _form470AmendSvc.Initialize470Amendment(Arg.Any<long>()).Returns(response);

        //Act and Assert
        var error = Assert.ThrowsAsync<ArgumentException>(async () => await _service.InitializeForm470Amendment(id));
        Assert.That(error.Message, Is.EqualTo(errorMessage));
    }
    #endregion

    #region Page01
    [Test]
    public async Task Page01GetExisting_EmptyResponse_ReturnsNull()
    {
        // Arrange
        long id = 1;
        var res = new FilingModels.Form470OverviewResponse();
        _form470Svc.GetForm470Overview(Arg.Any<long>()).Returns(res);
        // Act
        var result = await _service.Page01GetExisting(id);
        // Assert
        await _form470Svc.Received(1).GetForm470Overview(Arg.Any<long>());
        Assert.That(result, Is.Null);
    }
    [Test]
    public async Task Page01GetExisting_ValidResponse_ReturnsModel()
    {
        // Arrange
        long id = 1;
        var res = new FilingModels.Form470OverviewResponse
        {
            CandidateIntentionStatement470 = new(),
            Form470Filing = new(),
        };
        _form470Svc.GetForm470Overview(Arg.Any<long>()).Returns(res);
        var filings = new List<FilingPeriod>
        {
            new(100, "", DateTime.Now, new(0, "", DateTime.Now, 0, 0, "", DateTime.Now), null, DateTime.Now, null!, 3, default!, 1, 100, null!,  1, 1, "Name", DateTime.Now)
        };
        _filingsApi.GetAllFilingPeriods().Returns(filings);
        // Act
        var result = await _service.Page01GetExisting(id);
        // Assert
        await _form470Svc.Received(1).GetForm470Overview(Arg.Any<long>());
        await _filingsApi.Received(1).GetAllFilingPeriods();
        Assert.That(result, Is.Not.Null);
        Assert.That(result.FilingPeriodOptions, Is.Not.Null);
        Assert.That(result.FilingPeriodOptions, Has.Count.EqualTo(1));
    }
    [Test]
    public async Task Page01ViewModelSetReadOnlyData_NoFilerId_NoChanges()
    {
        // Arrange
        var model = new Form470Page01ViewModel();
        // Act
        var result = await _service.Page01ViewModelSetReadOnlyData(model);
        // Assert
        Assert.That(result.FilerId, Is.Null);
    }
    [Test]
    public async Task Page01ViewModelSetReadOnlyData_AppliesData()
    {
        // Arrange
        var model = new Form470Page01ViewModel
        {
            FilerId = 1,
            CandidateName = "Name from View Model"
        };
        var res = new RegistrationModels.CandidateIntentionStatement470
        {
            Name = "Name from API"
        };
        _form470Svc.GetCandidateIntentionStatementWithElectionByFilerId(Arg.Any<long>()).Returns(res);
        var filings = new List<FilingPeriod>
        {
            new(100, "", DateTime.Now, new(0, "", DateTime.Now, 0, 0, "", DateTime.Now), null, DateTime.Now, null!, 3, null!,  1, 100, null!, null, 0, "", DateTime.Now)
        };
        _filingsApi.GetAllFilingPeriods().Returns(filings);
        // Act
        var result = await _service.Page01ViewModelSetReadOnlyData(model);
        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.CandidateName, Is.EqualTo("Name from API"));
            Assert.That(result.FilingPeriodOptions, Is.Not.Null);
        });
        Assert.That(result.FilingPeriodOptions, Has.Count.EqualTo(1));
    }
    [Test]
    public async Task Page01Submit_NoId_ShouldCreate()
    {
        // Arrange
        var model = new Form470Page01ViewModel
        {
            FilingPeriodId = 1,
        };
        var res = 2L;
        var modelState = new Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary();
        _form470Svc.CreateOfficeHolderCandidateShortFormFiling(Arg.Any<long?>(), Arg.Any<long?>()).Returns(res);
        // Act
        await _service.Page01Submit(model, modelState, false);
        // Assert
        await _form470Svc.Received(1).CreateOfficeHolderCandidateShortFormFiling(Arg.Any<long?>(), Arg.Any<long?>());
    }
    [Test]
    public async Task Page01Submit_NoFilingPeriodId_ShouldReturnNull()
    {
        // Arrange
        var model = new Form470Page01ViewModel();
        var modelState = new Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary();
        // Act
        var result = await _service.Page01Submit(model, modelState, false);
        // Assert
        Assert.That(result, Is.Null);
    }
    [Test]
    public async Task Page01Submit_Valid_ShouldReturnId()
    {
        // Arrange
        var model = new Form470Page01ViewModel
        {
            Id = 1,
            FilingPeriodId = 1,
        };
        var modelState = new Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary();
        // Act
        var result = await _service.Page01Submit(model, modelState, false);
        // Assert
        Assert.That(result, Is.Not.Null);
    }
    #endregion

    #region Page02
    [Test]
    public async Task Page02GetViewModel_Should_ReturnExpectedModel()
    {
        // Arrange
        var id = 123L;
        var expectedCommittees = new List<Form470Committee> {
            new()
            {
                Id = id,
                CommitteeAddress = new()
                {
                    Street = "TEST STREET",
                    Street2 = "TEST STREET2",
                    City = "TEST CITY",
                    State = "CA",
                    Zip = "99999",
                    Type = "Residential"
                }
            }
        };

        _form470Svc.GetFilingRelatedFiler(id).Returns(expectedCommittees);

        // Act
        var result = await _service.Page02GetViewModel(id);

        // Assert
        Assert.That(result.CommitteesGridModel, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.CommitteesGridModel.PrimaryKey, Is.EqualTo("FilerRelatedFilerId"));
            Assert.That(result.CommitteesGridModel.GridId, Is.EqualTo("CommitteeGrid"));
            Assert.That(result.CommitteesGridModel.DataSource, Is.EquivalentTo(expectedCommittees));
            Assert.That(result.CommitteesGridModel.AllowPaging, Is.True);
            Assert.That(result.CommitteesGridModel.AllowTextWrap, Is.False);
            Assert.That(result.CommitteesGridModel.AllowAdding, Is.False);
            Assert.That(result.CommitteesGridModel.AllowDeleting, Is.True);
            Assert.That(result.CommitteesGridModel.EnableExport, Is.False);
            Assert.That(result.CommitteesGridModel.Columns, Has.Exactly(4).Items);
            Assert.That(result.CommitteesGridModel.ActionItems, Has.Exactly(1).Items);
            Assert.That(result.CommitteesGridModel.ActionItems.First().Action, Is.EqualTo("deleteRow"));
        });
    }
    #endregion

    #region Page03
    [Test]
    public async Task Page03FindCommitteesByIdOrName_ShouldReturnData()
    {
        var searchResult = new RegistrationModels.Form470SearchCommitteeResponseDto
        {
            Id = 1,
            FilerId = 1,
            CommitteeName = "Test",
            Address = new()
            {
                new()
                {
                    Street = "TEST STREET",
                    Street2 = "TEST STREET2",
                    City = "TEST CITY",
                    State = "CA",
                    Zip = "99999",
                    Type = "Residential"
                }
            },
            Treasurer = new()
            {
                EntraOid = "TestOid",
                FirstName = "first",
                LastName = "last",
                EmailAddress = "<EMAIL>",
                Id = 1,
            }
        };
        var searchResults = new List<RegistrationModels.Form470SearchCommitteeResponseDto>();
        searchResults.Add(searchResult);
        _form470Svc.FindPrimarilyFormedCommitteeByIdOrName(Arg.Any<string>()).Returns(searchResults);

        var result = await _service.Page03FindCommitteesByIdOrName("test");

        Assert.That(result, Has.Length.EqualTo(1));
    }
    [Test]
    public void Page03Save_Should_CompleteSuccessfully()
    {
        var filingId = 1L;
        var committeeId = 2L;
        var modelState = new ModelStateDictionary();

        Assert.DoesNotThrowAsync(async () =>
        {
            await _service.Page03Save(filingId, committeeId, modelState);
        });
    }
    [Test]
    public async Task Page03Save_WhenCommitteeIdIsZero_ShouldAddModelErrorAndNotCallService()
    {
        // Arrange
        var modelState = new ModelStateDictionary();

        // Act
        await _service.Page03Save(123, 0, modelState);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(modelState.IsValid, Is.False);
            Assert.That(modelState.ContainsKey("FilerId"), Is.True);
            Assert.That(modelState["FilerId"]?.Errors.Any(e => e.ErrorMessage == "Committee name or ID is required."), Is.True);
        });
        await _form470Svc.DidNotReceive().CreateFilingRelatedFiler(Arg.Any<long>(), Arg.Any<long>());
    }

    #endregion

    #region Page04
    [Test]
    public async Task Page04GetViewModel_Valid()
    {
        var res = new FilingModels.Form470OverviewResponse
        {
            CandidateIntentionStatement470 = new() { RegistrationId = 1, Name = "Name" },
            Form470Filing = new(),
        };
        _form470Svc.GetForm470Overview(1).Returns(res);

        var result = await _service.Page04GetViewModel(1);

        Assert.That(result, Is.InstanceOf<Form470Page04ViewModel>());
    }
    [Test]
    public async Task Page04Submit_Candidate_Valid()
    {
        var model = new Form470Page04ViewModel()
        {
            Id = 1,
            IsCandidate = true,
            IsAgreedTerm = true
        };
        var res = new FilingModels.Form470OverviewResponse
        {
            CandidateIntentionStatement470 = new() { RegistrationId = 1, Name = "Name" },
            Form470Filing = new(),
        };

        var modelState = new Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary();

        var result = await _service.Page04Submit(model, modelState, true);

        await _form470Svc.Received(1).AttestOfficeholderAndCandidateCampaignStatement(Arg.Any<long>(), Arg.Any<Form470AttestRequest>());
    }
    [Test]
    public async Task Page04Submit_NonCandidate_Valid()
    {
        var model = new Form470Page04ViewModel()
        {
            Id = 1,
            IsCandidate = false,
        };
        var res = new FilingModels.Form470OverviewResponse
        {
            CandidateIntentionStatement470 = new() { RegistrationId = 1, Name = "Name" },
            Form470Filing = new(),
        };

        var modelState = new Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary();

        var result = await _service.Page04Submit(model, modelState, true);

        await _form470Svc.Received(1).AttestOfficeholderAndNonCandidateCampaignStatement(Arg.Any<long>(), Arg.Any<Form470AttestRequest>());
    }
    [Test]
    public async Task Page04Submit_InValid_NullId()
    {
        var model = new Form470Page04ViewModel()
        {
            Id = null,
        };
        var res = new FilingModels.Form470OverviewResponse
        {
            CandidateIntentionStatement470 = new() { RegistrationId = 1, Name = "Name" },
            Form470Filing = new(),
        };

        var modelState = new Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary();

        var result = await _service.Page04Submit(model, modelState, true);
        Assert.That(modelState, Is.Not.Empty);

    }
    #endregion
}

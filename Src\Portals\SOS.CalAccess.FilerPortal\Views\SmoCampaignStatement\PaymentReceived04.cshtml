@using SOS.CalAccess.UI.Common
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.FilerPortal.Models.Disclosure.SmoCampaignStatement
@using SOS.CalAccess.UI.Common.Enums
@using SOS.CalAccess.UI.Common.Localization;
@using SOS.CalAccess.UI.Common.Constants;
@inject IHtmlLocalizer<SharedResources> Localizer

@model SmoCampaignStatementTransactionEntryViewModel
@{
    // FR-CF-PaymentReceived-4
    var buttonConfig = new ButtonBarModel
    {
        RightButtons = new List<ButtonConfig>
        {
            new ()
            {
                Type = ButtonType.Custom,
                HtmlContent = await Html.PartialAsync("_CancelPaymentEntryButton"),
            },
        },
        LeftButtons = new List<ButtonConfig>
        {
            ButtonBarModel.DefaultPrevious,
            new ()
            {
                Type = ButtonType.Button,
                Action = FormAction.SaveAndClose,
                CssClass = "btn btn-primary me-2",
                InnerTextKey = CommonResourceConstants.Save
            },
        },
    };

    var isDisplayCumulativeAmount = Model.IsIncludePreviouslyUnitemizedAmount ? "block" : "none";
}

<div class="p-5">
    @Html.StepHeader(SharedLocalizer, ResourceConstants.SmoCampaignStatementEnterTransaction)
    <div class="p-5">
        <div class="mb-5">
            @Html.StepHeader(SharedLocalizer, ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentTitle)

            @Html.TextBlock(SharedLocalizer, ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentBody)
        </div>

        @using (Html.BeginForm("PaymentReceived04", "SmoCampaignStatement", FormMethod.Post))
        {
            @Html.AntiForgeryToken()
            @Html.HiddenFor(m => m.ContactId)
            @Html.HiddenFor(m => m.TransactionId)
            @Html.HiddenFor(m => m.TransactionDate)
            @Html.HiddenFor(m => m.Notes)
            @Html.HiddenFor(m => m.AttachedFileGuidsJson)

            <partial name="_SmoDisclosureStanceOnCandidateOrMeasure" model="@Model" />

            <div class="my-3">
                <div class="col-sm-6">
                    @Html.CurrencyInputFor(SharedLocalizer, m => m.TransactionAmount, ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentAmountReceived, true, true, disabled: true, widthInEmUnits: null)
                </div>
            </div>

            <div class="my-3">
                @Html.CheckBoxFor(SharedLocalizer, m => m.IsIncludePreviouslyUnitemizedAmount, ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentIsCumulativeAmount)
            </div>

            <div id="cumulativeSection" style="display:@isDisplayCumulativeAmount;">
                <div class="mb-3 col-sm-6">
                    @Html.CurrencyInputFor(SharedLocalizer, m => m.UnitemizedAmount, ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentPreviouslyUnitemizedAmount, true, true, widthInEmUnits: null)
                </div>

                <div class="col-sm-6 mb-6">
                    @Html.CurrencyInputFor(SharedLocalizer, m => m.CumulativeAmountToDate, ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentCumulativeAmountToDate, true, true, widthInEmUnits: null, disabled: true)
                </div>
            </div>
            
            <div class="mt-5">
                <partial name="_ButtonBar" model="buttonConfig" />
            </div>
        }
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function() {
        const transactionAmountField = document.getElementById('@Html.IdFor(m => m.TransactionAmount)');
        const unitemizedAmountField = document.getElementById('@Html.IdFor(m => m.UnitemizedAmount)');

        function disableListener(e) {
            e.stopImmediatePropagation();
        }

        // Disable mousewheel to changing value
        transactionAmountField.addEventListener("mousewheel", disableListener);
        unitemizedAmountField.addEventListener("mousewheel", disableListener);

        // Toggle the cumulative section when the checkbox changes
        const cumulativeCheckbox = document.getElementById('@Html.IdFor(m => m.IsIncludePreviouslyUnitemizedAmount)');
        const cumulativeSection = document.getElementById("cumulativeSection");

        cumulativeCheckbox.addEventListener("change", function () {
            if (this.checked) {
                cumulativeSection.style.display = "block";
            } else {
                cumulativeSection.style.display = "none";
            }
        });

        // “UnitemizedAmount + base Cumulative = new Cumulative”
        const unElem = document.getElementById('@Html.IdFor(m => m.UnitemizedAmount)');
        const cumElem = document.getElementById('@Html.IdFor(m => m.CumulativeAmountToDate)');

        if (unElem && cumElem) {
            const unWidget = unElem.ej2_instances[0];
            const cumWidget = cumElem.ej2_instances[0];
            const baseValue = (cumWidget.value || 0);


            function recalcCumulative() {
                const uVal = (unWidget.value || 0);
                cumWidget.value = baseValue + uVal;
            }

            unElem.addEventListener("keyup", recalcCumulative);
            unElem.addEventListener("paste", function() {
                setTimeout(recalcCumulative, 50);
            });
            unElem.addEventListener("blur", recalcCumulative);

            if (typeof unWidget.change === "function") {
                unWidget.change = function(args) {
                    recalcCumulative();
                };
            }
        }
    })
</script>

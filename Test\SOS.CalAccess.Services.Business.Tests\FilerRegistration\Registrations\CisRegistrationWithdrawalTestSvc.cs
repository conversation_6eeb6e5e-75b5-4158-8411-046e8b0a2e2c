using NSubstitute;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using NSubstitute.ReceivedExtensions;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Data.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Common.BusinessRules.Models;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using FluentAssertions;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Foundation.Utils;

namespace SOS.CalAccess.Services.Business.Tests.FilerRegistration.Registrations;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(CisRegistrationWithdrawalSvc))]
public sealed class CisRegistrationWithdrawalTest
{
    private IAttestationRepository _attestationRepository;
    private IRegistrationRepository _registrationRepository;
    private INotificationWrapperSvc _notificationWrapperSvc;
    private IFilerRepository _filerRepository;
    private IDecisionsSvc _decisionsSvc;
    private IAuthorizationSvc _authorizationSvc;

    private IDateTimeSvc _dateTimeSvcMock;
    private CisRegistrationWithdrawalSvc _service;
    /// <summary>
    /// Sets up the unit tests for this fixture.
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _registrationRepository = Substitute.For<IRegistrationRepository>();
        _attestationRepository = Substitute.For<IAttestationRepository>();
        _filerRepository = Substitute.For<IFilerRepository>();
        _notificationWrapperSvc = Substitute.For<INotificationWrapperSvc>();
        _decisionsSvc = Substitute.For<IDecisionsSvc>();
        _authorizationSvc = Substitute.For<IAuthorizationSvc>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();

        _service = new CisRegistrationWithdrawalSvc(
            _dateTimeSvcMock,
            _registrationRepository,
            _attestationRepository,
            _filerRepository,
            _notificationWrapperSvc,
            _decisionsSvc,
            _authorizationSvc);
    }

    #region CreateCisWithdrawal
    [Test]
    public async Task CreateCisWithdrawal_Success()
    {
        // Arrange
        var id = 1;
        var registration = new CandidateIntentionStatement { Id = id, Name = "Name", StatusId = RegistrationStatus.Accepted.Id, FilerId = 123 };

        _registrationRepository.FindById(Arg.Any<long>()).Returns(registration);
        _registrationRepository.GetLatestAcceptedCisRegistrationByFilerId(Arg.Any<long>()).Returns(registration);

        // Act & Assert
        await _service.CreateCisWithdrawal(id);
        await _registrationRepository.Received(1).Create(Arg.Any<CisWithdrawal>());
    }

    [Test]
    public void CreateCisWithdrawal_RegistrationIdNotFound_ThrowException()
    {
        // Arrange
        var id = 1;

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _service.CreateCisWithdrawal(id));
    }

    [Test]
    public void CreateCisWithdrawal_LatestAcceptedRegistrationIdNotFound_ThrowException()
    {
        // Arrange
        var id = 1;
        var registration = new CandidateIntentionStatement { Id = id, Name = "Name", StatusId = RegistrationStatus.Accepted.Id, FilerId = 123 };

        _registrationRepository.FindById(Arg.Any<long>()).Returns(registration);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await _service.CreateCisWithdrawal(id));
    }
    #endregion

    #region GetCisWithdrawal
    [Test]
    public async Task GetCisWithdrawal_Success()
    {
        // Arrange
        var id = 1;
        var registration = new CandidateIntentionStatement
        {
            Name = "Name",
            StatusId = RegistrationStatus.Accepted.Id,
            FilerId = 123,
            Candidate = new()
            {
                Registrations = new List<CandidateIntentionStatement>() { },
                User = new User
                {
                    FirstName = "First",
                    LastName = "Last",
                    EmailAddress = "Email",
                    EntraOid = "1"
                }
            },
            AddressList = new AddressList
            {
                Addresses = new List<Address>
                {
                    new()
                    {
                        City = "dummy city",
                        Country = "dummy country",
                        Purpose = "Home",
                        State = "CA",
                        Street = "123 honolulu",
                        Street2 = "321",
                        Type = "Home",
                        Zip = "89532"
                    }
                }
            },
            SubmittedAt = DateTime.Now,
        };

        var cisWithdrawal = new CisWithdrawal
        {
            Id = id,
            FilerId = 1,
            Name = "Name",
            StatusId = RegistrationStatus.Pending.Id,
        };

        _registrationRepository.GetCisRegistrationWithdrawalById(Arg.Any<long>()).Returns(cisWithdrawal);
        _registrationRepository.GetLatestAcceptedCisRegistrationByFilerId(Arg.Any<long>()).Returns(registration);

        // Act & Assert
        var result = await _service.GetCisWithdrawal(id);
        Assert.That(result.Id, Is.EqualTo(id));

    }
    [Test]
    public async Task GetCisWithdrawal_NullRegistration_ReturnEmptyObject()
    {
        // Arrange
        var id = 1;

        // Act & Assert
        var result = await _service.GetCisWithdrawal(id);
        Assert.That(result.Id, Is.Null);
    }

    [Test]
    public async Task GetCisWithdrawal_NullRegistrationFilerId_ReturnEmptyObject()
    {
        // Arrange
        var id = 1;
        var cisWithdrawal = new CisWithdrawal
        {
            Id = id,
            Name = "Name",
            StatusId = RegistrationStatus.Pending.Id,
        };

        _registrationRepository.GetCisRegistrationWithdrawalById(Arg.Any<long>()).Returns(cisWithdrawal);

        // Act & Assert
        var result = await _service.GetCisWithdrawal(id);
        Assert.That(result.Id, Is.Null);
    }


    [Test]
    public async Task GetCisWithdrawal_NullLatestAcceptedRegistration_ThrowException()
    {
        // Arrange
        var id = 1;
        var cisWithdrawal = new CisWithdrawal
        {
            Id = id,
            FilerId = 1,
            Name = "Name",
            StatusId = RegistrationStatus.Pending.Id,
        };

        _registrationRepository.GetCisRegistrationWithdrawalById(Arg.Any<long>()).Returns(cisWithdrawal);

        // Act & Assert
        var result = await _service.GetCisWithdrawal(id);
        Assert.That(result.Id, Is.Null);
    }
    #endregion

    #region CancelCisWithdrawal
    [Test]
    public async Task CancelCisWithdrawal_Success()
    {
        // Arrange
        var id = 1;
        _registrationRepository.FindById(Arg.Any<long>()).Returns(new CisWithdrawal { Name = "Name", StatusId = RegistrationStatus.Draft.Id, FilerId = 123 });

        // Act & Assert
        await _service.CancelCisWithdrawal(id);
        await _registrationRepository.Received(1).FindById(Arg.Any<long>());
        await _registrationRepository.Received(1).Update(Arg.Any<CisWithdrawal>());
    }
    [Test]
    public void CancelCisWithdrawal_RegistrationIdNotFound_ThrowException()
    {
        // Arrange
        var id = 1;

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _service.CancelCisWithdrawal(id));
    }

    [Test]
    public void CancelCisWithdrawal_RegistrationNotCisWithdrawal_ThrowException()
    {
        // Arrange
        var id = 1;
        _registrationRepository.FindById(Arg.Any<long>()).Returns(new CandidateIntentionStatement { Name = "Name", StatusId = RegistrationStatus.Draft.Id });

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await _service.CancelCisWithdrawal(id));
    }

    [Test]
    public void CancelCisWithdrawal_InvalidRegistrationStatus_ThrowException()
    {
        // Arrange
        var id = 1;
        _registrationRepository.FindById(Arg.Any<long>()).Returns(new CisWithdrawal { Name = "Name", StatusId = RegistrationStatus.Accepted.Id });

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await _service.CancelCisWithdrawal(id));
    }
    #endregion

    #region AttestationCisWithdrawal - Candidate

    [Test]
    public async Task AttestationCisWithdrawal_SuccessfulFlow_ShouldUpdateStatusAndSendNotifications()
    {
        // Arrange
        var registration = new CisWithdrawal { Id = 1, Name = "Test", FilerId = 123, StatusId = 2 };
        var filer = new Filer { Id = 123 };

        _registrationRepository.FindById(1).Returns(registration);
        _filerRepository.FindById(123).Returns(filer);
        _attestationRepository.Create(Arg.Any<Attestation>()).Returns(new Attestation());

        var decisionsResponse = new StandardDecisionsSubmissionResponse
        {
            Status = "Accepted",
            Errors = new List<WorkFlowError>(),
            Notifications = new List<NotificationTrigger> { new() }
        };
        _decisionsSvc.InitiateWorkflow<bool, StandardDecisionsSubmissionResponse>(
            DecisionsWorkflow.CISWithdrawalAttestationSubmitRuleset, Arg.Any<bool>()).Returns(decisionsResponse);

        _authorizationSvc.GetInitiatingUserId().Returns(999);

        var dto = new CisRegistrationAttestationWithdrawalDto { RegistrationId = 1, IsCertified = true };

        // Act
        var result = await _service.AttestationCisWithdrawal(dto);

        // Assert
        result.Should().NotBeNull();
        result.Valid.Should().BeTrue();
        result.StatusId.Should().Be(RegistrationStatus.Accepted.Id);
        result.ValidationErrors.Should().BeEmpty();
        result.Notifications.Should().HaveCount(1);

        await _attestationRepository.Received(1).Create(Arg.Any<Attestation>());
        await _registrationRepository.Received(1).Update(registration);
        await _filerRepository.Received(1).Update(filer);
        await _notificationWrapperSvc.Received(1).SendNotifications(Arg.Any<NotificationRequest>());
    }

    [Test]
    public void AttestationCisWithdrawal_RegistrationNotFound_ShouldThrow()
    {
        // Arrange
        _registrationRepository.FindById(Arg.Any<long>()).Returns((CisWithdrawal?)null);
        var dto = new CisRegistrationAttestationWithdrawalDto { RegistrationId = 99 };

        // Act & Assert
        var act = () => _service.AttestationCisWithdrawal(dto);
        act.Should().ThrowAsync<KeyNotFoundException>().WithMessage("*Registration with ID 99*");
    }

    [Test]
    public void AttestationCisWithdrawal_InvalidRegistrationType_ShouldThrow()
    {
        // Arrange
        _registrationRepository.FindById(Arg.Any<long>()).Returns(new CandidateIntentionStatement { Id = 1, Name = "IntentionStatement", StatusId = RegistrationStatus.Accepted.Id }); // Not CisWithdrawal
        var dto = new CisRegistrationAttestationWithdrawalDto { RegistrationId = 1 };

        // Act & Assert
        var act = () => _service.AttestationCisWithdrawal(dto);
        act.Should().ThrowAsync<InvalidOperationException>().WithMessage("*not a CIS Withdrawal*");
    }

    [Test]
    public void AttestationCisWithdrawal_FilerNotFound_ShouldThrow()
    {
        var reg = new CisWithdrawal { Id = 1, Name = "X", FilerId = 123, StatusId = 2 };
        _registrationRepository.FindById(Arg.Any<long>()).Returns(reg);
        _filerRepository.FindById(Arg.Any<long>()).Returns((Filer?)null);

        var dto = new CisRegistrationAttestationWithdrawalDto { RegistrationId = 1 };

        // Act & Assert
        var act = () => _service.AttestationCisWithdrawal(dto);
        act.Should().ThrowAsync<KeyNotFoundException>().WithMessage("*Filer with ID 123*");
    }

    [Test]
    public async Task AttestationCisWithdrawal_DecisionsReturnsErrors_ShouldNotUpdateOrNotify()
    {
        var reg = new CisWithdrawal { Id = 1, Name = "X", FilerId = 123, StatusId = 2 };
        var filer = new Filer { Id = 123 };
        var decisionsResponse = new StandardDecisionsSubmissionResponse
        {
            Status = "Accepted",
            Errors = new List<WorkFlowError>
                {
                    new("field1", "code1", "error", "Error message 1")
                },
            Notifications = null
        };

        _registrationRepository.FindById(Arg.Any<long>()).Returns(reg);
        _filerRepository.FindById(Arg.Any<long>()).Returns(filer);
        _decisionsSvc.InitiateWorkflow<bool, StandardDecisionsSubmissionResponse>(DecisionsWorkflow.CISWithdrawalAttestationSubmitRuleset, Arg.Any<bool>())
            .Returns(decisionsResponse);

        var dto = new CisRegistrationAttestationWithdrawalDto { RegistrationId = 1 };

        // Act
        var result = await _service.AttestationCisWithdrawal(dto);

        // Assert
        result.Valid.Should().BeFalse();
        await _registrationRepository.DidNotReceive().Update(Arg.Any<CisWithdrawal>());
        await _notificationWrapperSvc.DidNotReceive().SendNotifications(Arg.Any<NotificationRequest>());
    }

    #endregion

    #region SendForAttestationCisWithdrawal
    [Test]
    public async Task SendForAttestationCisWithdrawal_SuccessfulFlow_ShouldUpdateStatusAndSendNotifications()
    {
        // Arrange
        var id = 1;
        var registration = new CisWithdrawal { Id = 1, Name = "Test", FilerId = 123, StatusId = 2 };
        var filer = new Filer { Id = 123 };

        _registrationRepository.FindById(Arg.Any<long>()).Returns(registration);
        _filerRepository.FindById(Arg.Any<long>()).Returns(filer);

        var decisionsResponse = new StandardDecisionsSubmissionResponse
        {
            Status = "Pending",
            Errors = new List<WorkFlowError>(),
            Notifications = new List<NotificationTrigger> { new(true, 89, null) }
        };
        _decisionsSvc.InitiateWorkflow<string, StandardDecisionsSubmissionResponse>(
            DecisionsWorkflow.CISWithdrawalSendForAttestationRuleset, Arg.Any<string>(), Arg.Any<bool>()).Returns(decisionsResponse);

        _authorizationSvc.GetInitiatingUserId().Returns(999);

        // Act
        var result = await _service.SendForAttestationCisWithdrawal(id);

        // Assert
        result.Should().NotBeNull();
        result.Valid.Should().BeTrue();
        result.StatusId.Should().Be(RegistrationStatus.Pending.Id);
        result.ValidationErrors.Should().BeEmpty();
        result.Notifications.Should().HaveCount(1);

        await _registrationRepository.Received(1).Update(registration);
        await _notificationWrapperSvc.Received(1).SendNotifications(Arg.Any<NotificationRequest>());
    }

    [Test]
    public async Task SendForAttestationCisWithdrawal_DecisionsReturnsErrors_ShouldNotUpdateOrNotify()
    {
        var id = 1;
        var reg = new CisWithdrawal { Id = 1, Name = "X", FilerId = 123, StatusId = 2 };
        var filer = new Filer { Id = 123 };
        var decisionsResponse = new StandardDecisionsSubmissionResponse
        {
            Status = "Accepted",
            Errors = new List<WorkFlowError>
                {
                    new("field1", "code1", "error", "Error message 1")
                },
            Notifications = null,

        };

        _registrationRepository.FindById(Arg.Any<long>()).Returns(reg);
        _filerRepository.FindById(Arg.Any<long>()).Returns(filer);
        _decisionsSvc.InitiateWorkflow<string, StandardDecisionsSubmissionResponse>(DecisionsWorkflow.CISWithdrawalSendForAttestationRuleset, Arg.Any<string>(), Arg.Any<bool>())
            .Returns(decisionsResponse);

        // Act
        var result = await _service.SendForAttestationCisWithdrawal(id);

        // Assert
        result.Valid.Should().BeFalse();
        await _registrationRepository.DidNotReceive().Update(Arg.Any<CisWithdrawal>());
        await _notificationWrapperSvc.DidNotReceive().SendNotifications(Arg.Any<NotificationRequest>());
    }

    #endregion

    #region GetPendingItems
    [Test]
    public async Task GetPendingItems_SuccessfulFlow_ShouldRetrievePendingItems()
    {
        // Arrange
        var id = 1;
        var registration = new CisWithdrawal { Id = id, Name = "Test", FilerId = 123, StatusId = 2 };
        var attestation = new Attestation { Id = 1, RegistrationId = id, Name = "Name" };
        _registrationRepository.FindById(Arg.Any<long>()).Returns(registration);

        // Act
        var result = await _service.GetPendingItems(id);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<List<PendingItemDto>>();
        result.Should().HaveCount(1);

    }
    #endregion
}


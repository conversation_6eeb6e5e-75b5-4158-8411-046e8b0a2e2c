using SOS.CalAccess.FilerPortal.Models.Transactions;

namespace SOS.CalAccess.FilerPortal.Tests.Models;

[TestFixture]
public class LobbyistSearchViewModelTests
{
    [Test]
    public void LobbyistSearchViewModel_ShouldSetAndGetPropertiesCorrectly()
    {
        // Arrange
        var viewModel = new LobbyistSearchViewModel
        {
            FilerId = 123,
            ContactId = 456,
            RegistrationFilingId = 789,
            ContactName = "Jane Lobbyist",
            LobbyistId = 999,
            Required = true,
            SearchActionName = "CustomSearchAction",
            SearchDivId = "customSearchDiv",
            SearchLabelKey = "CustomLabelKey",
            SearchLabelText = "Custom Label Text",
            SearchInputKey = "CustomInputKey",
            SearchInputLabel = "Custom Input Label"
        };

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(viewModel.FilerId, Is.EqualTo(123));
            Assert.That(viewModel.ContactId, Is.EqualTo(456));
            Assert.That(viewModel.RegistrationFilingId, Is.EqualTo(789));
            Assert.That(viewModel.ContactName, Is.EqualTo("Jane Lobbyist"));
            Assert.That(viewModel.LobbyistId, Is.EqualTo(999));
            Assert.That(viewModel.Required, Is.True);
            Assert.That(viewModel.SearchActionName, Is.EqualTo("CustomSearchAction"));
            Assert.That(viewModel.SearchDivId, Is.EqualTo("customSearchDiv"));
            Assert.That(viewModel.SearchLabelKey, Is.EqualTo("CustomLabelKey"));
            Assert.That(viewModel.SearchLabelText, Is.EqualTo("Custom Label Text"));
            Assert.That(viewModel.SearchInputKey, Is.EqualTo("CustomInputKey"));
            Assert.That(viewModel.SearchInputLabel, Is.EqualTo("Custom Input Label"));
        });
    }

    [Test]
    public void LobbyistSearchViewModel_ShouldHaveCorrectDefaultValues()
    {
        // Arrange
        var viewModel = new LobbyistSearchViewModel();

        // Assert default values
        Assert.Multiple(() =>
        {
            Assert.That(viewModel.SearchActionName, Is.EqualTo("SearchLobbyistByIdOrName"));
            Assert.That(viewModel.SearchDivId, Is.EqualTo("lobbyistSearch"));
            Assert.That(viewModel.SearchLabelKey, Is.EqualTo("LobbyistSearch"));
            Assert.That(viewModel.SearchLabelText, Is.EqualTo("Look up lobbyist by ID# or name"));
            Assert.That(viewModel.SearchInputKey, Is.EqualTo("LobbyistSearch"));
            Assert.That(viewModel.SearchInputLabel, Is.EqualTo("Lobbyist Search"));
        });
    }
}

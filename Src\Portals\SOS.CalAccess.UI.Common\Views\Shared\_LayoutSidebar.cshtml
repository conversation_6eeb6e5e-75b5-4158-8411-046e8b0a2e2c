@using Microsoft.AspNetCore.Mvc.Localization
@using SOS.CalAccess.UI.Common
@using SOS.CalAccess.UI.Common.Models
@using Syncfusion.EJ2
@inject IHtmlLocalizer<SharedResources> SharedLocalizer

<div id="left-sidebar">
    <nav aria-label="Sidebar Navigation">
        <h2><a href="#">Page Title</a></h2>
        <ul>
            <li><a href="/Home/UnderConstruction">Registration</a></li>
            <li><a class="child-link" href="/Dashboard">Dashboard</a></li>
            <li><a class="child-link" href="/CandidateRegistration">Candidate</a></li>
            <li><a class="child-link" href="/Home/UnderConstruction">Committee</a></li>
            <li><a class="child-link" href="/SmoRegistration">Slate Mailer</a></li>
            <li><a class="child-link" href="/LobbyistRegistration">Lobbyist</a></li>
            <li><a class="child-link" href="/LobbyistEmployerRegistration">Lobbyist Employer</a></li>
            <li>
                <a>Disclosure</a>
                <ul >
                    <li><a class="child-link" href="/Filing/TemporaryDashboard">Dashboard</a></li>
                    <li><a class="child-link" href="/Form470/New">Candidate Statement</a></li>
                    <li><a class="child-link" href="/Form470S/New">Candidate Statement Supplement</a></li>
                    <li><a class="child-link" href="/Filing/SmoCampaignStatement">Slate Mailer Campaign Statement</a></li>
                    <li><a class="child-link" href="/Filing/Lobbyist">Lobbyist</a></li>
                    <li><a class="child-link" href="/Filing/LobbyistEmployer">Lobbyist Employer</a></li>
                    <li><a class="child-link" href="/Filing/Report72H">72h Report</a></li>
                    <li><a class="child-link" href="/Filing/Report48H">48h Report</a></li>
                </ul>
            </li>
            <li>
                <a>AccountManagement</a>
                <ul>
                    <li><a class="child-link" href="/UserMaintenance/UserDetails">User details</a></li>
                    <li><a class="child-link" href="/UserMaintenance/UnderConstruction">Password</a></li>
                    <li><a class="child-link" href="/UserMaintenance/UnderConstruction">Notification preferences</a></li>
                </ul>
            </li>
            <li><a href="/MyLinkages">Linkages</a></li>
            <li><a href="/Home/UnderConstruction">Correspondence</a></li>
            <li><a href="/Home/UnderConstruction">Financial Transactions</a></li>
            <li><a href="/Home/ReferencePage">Reference Page</a></li>
        </ul>
    </nav>
</div>

@using Microsoft.AspNetCore.Mvc.Localization
@using SOS.CalAccess.UI.Common

@model SOS.CalAccess.UI.Common.Models.ConfirmDialogModal

@inject IHtmlLocalizer<SharedResources> SharedLocalizer

<!-- Modal -->
<div class="modal fade" id="@Model.Id" tabindex="-1" aria-labelledby="@Model.Id-Label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content border-0">
            <div class="modal-header border-0">
                <h1 class="modal-title fs-5" id="@Model.Id-Label">@SharedLocalizer[Model.Title]</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body border-0">
                @SharedLocalizer[Model.Body]
            </div>
            <div class="modal-footer border-0 justify-content-end">
                <button type="button" class="btn btn-flat-primary me-2" data-bs-dismiss="modal">@SharedLocalizer[Model.CloseButtonText]</button>
                <form method="get" id="<EMAIL>" action="@Model.ActionUrl">
                    <button type="submit" class="btn btn-primary me-2">
                        @SharedLocalizer[Model.SubmitButtonText]
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.getElementById('@Model.Id')?.addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget;
        const actionUrl = button?.getAttribute('data-action');
        const form = document.getElementById('<EMAIL>');
        if (form && actionUrl) {
            form.setAttribute('action', actionUrl);
        }
    });
</script>

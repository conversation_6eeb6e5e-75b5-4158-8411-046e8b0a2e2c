using System.Globalization;
using System.Net;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Refit;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Disclosure;
using SOS.CalAccess.FilerPortal.Models.Filings;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Transactions;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using FilingSummaryTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;
using FilingType = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;

namespace SOS.CalAccess.FilerPortal.ControllerServices;

#pragma warning disable S107 // Methods should not have too many parameters
public class DisclosureCtlSvc(
    IFilingsApi filingsApi,
    IActivityExpenseApi activityExpenseApi,
    IFilingSummaryApi filingSummaryApi,
    ILobbyistApi lobbyistApi,
    ILobbyistEmployerApi lobbyistEmployerApi,
    ILobbyistEmployerCoalitionApi lobbyistEmployerCoalitionApi,
    ILobbyingAdvertisementApi lobbyingAdvertisementApi,
    ITransactionsApi transactionsApi,
    IReferenceDataApi referenceDataApi,
    IStringLocalizer<SharedResources> localizer) : IDisclosureCtlSvc
#pragma warning restore S107 // Methods should not have too many parameters
{
    private const string DeleteRow = "deleteRow";
    private const string CallActionRow = "callAction";
    private const string EditRow = "editRow";
    private const string Edit = "Edit";
    private const string Delete = "Delete";
    private const string EditCoalitionTransaction = "EditPaymentReceivedByLobbyingCoalition";
    private const string EditOtherPaymentToInfluenceTransaction = "EditOtherPaymentToInfluenceTransaction";
    private const string DisclosureSummaryViewModelTempDataKey = "DisclosureSummaryViewModel";
    private const string DashboardViewName = "Dashboard";
    private const string DisclosureControllerName = "Disclosure";
    private const string TransactionControlerName = "Transaction";
    private const string AdministrativeActionTempDataKey = "AdministrativeAction";
    private const string LegislationAssyBillTempDataKey = "AssemblyBill";
    private const string LegislationSenateBillTempDataKey = "SenateBill";

    public async Task<FilingItemResponse> GetFilingById(long filingId, CancellationToken cancellationToken = default)
    {
        return await filingsApi.GetFiling(filingId, cancellationToken);
    }

    public async Task<List<DisclosureReport>> GetDisclosureFilingReportsById(long filingId, CancellationToken cancellationToken = default)
    {
        var filingSummariesResponse = await filingsApi.GetFilingSummaries(filingId, cancellationToken);
        return [.. filingSummariesResponse.OrderBy(x => x.FilingSummaryType.Order).Select(x => {
            var type = x.FilingSummaryType != null ? x.FilingSummaryType.Name : string.Empty;
            var status = x.FilingSummaryStatus != null ? x.FilingSummaryStatus.Name : string.Empty;
            var reportName = localizer[$"{ResourceConstants.DisclosureSummaryBase}.{type}"].Value;
            return new DisclosureReport(x.Id, reportName, type, status, x.FilingSummaryTypeId);
        })];
    }

    public async Task UpdateFilingSummaryById(long filingSummaryId, string filingSummaryStatusName, bool nothingToReport, CancellationToken cancellationToken = default)
    {
        var request = new FilingSummaryRequest(filingSummaryStatusName, nothingToReport);
        await filingSummaryApi.UpdateFilingSummaryStatusById(filingSummaryId, request, cancellationToken);
    }

    public async Task<DisclosureLobbyingGeneralInfoViewModel> GetGeneralInfoViewForLobbyist(long filerId, long filingId, CancellationToken cancellationToken = default)
    {
        var lobbyist =
                    await lobbyistApi.GetLobbyistByFilerId(filerId,
                        cancellationToken);
        var report =
            await filingsApi.GetLobbyistReportFiling(filingId,
                cancellationToken);

        return new DisclosureLobbyingGeneralInfoViewModel(lobbyist, report)
        {
            ReportType = "Lobbyist report",
            FilingTypeId = FilingType.LobbyistReport.Id
        };
    }

    public async Task<DisclosureLobbyingGeneralInfoViewModel> GetGeneralInfoViewForLobbyistEmployer(long filerId, long filingId, CancellationToken cancellationToken = default)
    {
        var lobbyistEmployer = await lobbyistEmployerApi.GetLobbyistEmployerByFilerId(filerId, cancellationToken);
        var report = await filingsApi.GetLobbyistEmployerReportFiling(filingId, cancellationToken);
        var cumulativePeriodStart = await filingsApi.GetCumulativePeriodStartByFilingId(filingId, cancellationToken);

        return new DisclosureLobbyingGeneralInfoViewModel(lobbyistEmployer, report)
        {
            ReportType = "Report of Lobbyist Employer or Report of Lobbying Coalition",
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            CumulativePeriodStartDate = cumulativePeriodStart.Date,
        };
    }

    public async Task<IEnumerable<FilingReportGridDto>?> GetDisclosureFilingReports(CancellationToken cancellationToken = default)
    {
        var reports = await filingsApi.GetDisclosureFilingReports(cancellationToken);
        return reports;
    }

    public async Task<DisclosureSummaryViewModel> BuildSummaryViewModel(
        string? viewName,
        string? filerId,
        string? filingId,
        string? filingSummaryId,
        string? filingStatus,
        string? reportType,
        HttpContext httpContext,
        ITempDataDictionary tempData,
        CancellationToken cancellationToken)
    {
        var model = new DisclosureSummaryViewModel();

        if (tempData[DisclosureSummaryViewModelTempDataKey] != null)
        {
            model = JsonConvert.DeserializeObject<DisclosureSummaryViewModel>(
                tempData[DisclosureSummaryViewModelTempDataKey]?.ToString()!) ?? new DisclosureSummaryViewModel();
            tempData.Remove(DisclosureSummaryViewModelTempDataKey);
        }

        model.FilingSummaryId = long.Parse(filingSummaryId ?? "0", CultureInfo.InvariantCulture);

        if (string.IsNullOrEmpty(viewName) || viewName == DashboardViewName)
        {
            await ProcessDashboardView(model, filingId, cancellationToken);
            model.ViewName = DashboardViewName;
        }
        else
        {
            model.ViewName = viewName;
        }

        if (!string.IsNullOrEmpty(filerId) && !string.IsNullOrEmpty(filingId))
        {
            model.Id = long.Parse(filingId, CultureInfo.InvariantCulture);
            model.FilerId = long.Parse(filerId, CultureInfo.InvariantCulture);

            switch (viewName)
            {
                case nameof(FilingSummaryTypeModel.ActivityExpenseSummary):
                    ClearActivityExpenseData(httpContext);
                    await ProcessActivityExpenseView(model, filingId, reportType ?? "", cancellationToken);
                    break;

                case nameof(FilingSummaryTypeModel.CampaignContributionSummary):
                    await ProcessCampaignContributionView(model, filingId, reportType, cancellationToken);
                    break;

                case nameof(FilingSummaryTypeModel.ToLobbyingCoalitionSummary):
                    await ProcessCoalitionPaymentsView(model, filingId, reportType ?? "", cancellationToken);
                    break;

                case nameof(FilingSummaryTypeModel.OtherPaymentsToInfluenceSummary):
                    await ProcessOtherPaymentsView(model, filingId, filerId, cancellationToken);
                    break;

                case nameof(FilingSummaryTypeModel.RecieveLobbyingCoalitionSummary):
                    await ProcessReceivedCoalitionPaymentsView(model, filingId, reportType ?? "", filerId, cancellationToken);
                    break;

                case nameof(FilingSummaryTypeModel.MadeToLobbyingFirmsSummary):
                    await ProcessFirmPaymentsView(model, filingId, cancellationToken);
                    break;

                case nameof(FilingSummaryTypeModel.PucActivitySummary):
                    await ProcessPucActivityView(model, filingId, cancellationToken);
                    break;

                case nameof(FilingSummaryTypeModel.ActionsLobbiedSummary):
                    await ProcessActionsLobbiedView(model, filingId, reportType, tempData, cancellationToken);
                    break;

                case nameof(FilingSummaryTypeModel.LobbyistReportsSummary):
                    await ProcessLobbyistReportsView(model, filingId, cancellationToken);
                    break;

                case nameof(FilingSummaryTypeModel.EndOfSessionLobbyingSummary):
                    await ProcessEndOfSessionLobbyingView(model, filingId, cancellationToken);
                    break;

                default:
                    break;
            }
        }

        if (!string.IsNullOrEmpty(filingStatus))
        {
            model.Status = long.Parse(filingStatus, CultureInfo.InvariantCulture);
        }

        return model;
    }

    private async Task ProcessDashboardView(DisclosureSummaryViewModel model, string? filingId, CancellationToken cancellationToken)
    {
        if (filingId == null || !long.TryParse(filingId, CultureInfo.InvariantCulture, out var parsedFilingId) || parsedFilingId == 0)
        {
            return;
        }

        var reports = await GetDisclosureFilingReportsById(parsedFilingId, cancellationToken);
        model.TransactionEntries = reports;

        var filing = await filingsApi.GetFiling(parsedFilingId, cancellationToken);
        var filingPeriods = await GetAllFilingPeriodsByFiler(filing.FilerId, filing.FilingTypeId, filing.Id);

        model.StartDate = filing.StartDate == DateTime.MinValue ? null : filing.StartDate;
        model.EndDate = filing.EndDate == DateTime.MinValue ? null : filing.EndDate;
        model.AmendmentExplanation = filing.AmendmentExplanation;
        model.FilingPeriods = filingPeriods;
        model.FilingPeriodId = filing.FilingPeriodId;
    }

    #region ProcessSummaryView

    private async Task ProcessActivityExpenseView(
        DisclosureSummaryViewModel model,
        string filingId,
        string reportType,
        CancellationToken cancellationToken)
    {
        var summary = await activityExpenseApi.GetAllActivityExpenseTransactionsForFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture), cancellationToken);

        model.ActivityExpensesGridModel = CreateActivityExpenseGridModel(summary.ActivityExpenseItems, reportType);
        model.Subtotal = summary.Subtotal;
    }
    private async Task ProcessEndOfSessionLobbyingView(
        DisclosureSummaryViewModel model,
        string filingId,
        CancellationToken cancellationToken
        )
    {
        var transactions = await transactionsApi.GetAllEndOfSessionLobbyingTransactionsForFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture), cancellationToken);

        model.EndOfSessionGridModel = CreateEndOfSessionGridModel(transactions);
        model.Subtotal = transactions.Sum(c => c.Amount);
    }

    public async Task ProcessCampaignContributionView(
        DisclosureSummaryViewModel model,
        string filingId,
        string? reportType,
        CancellationToken cancellationToken)
    {
        if (reportType == FilingType.LobbyistEmployerReport.Name)
        {
            var report = await filingsApi.GetLobbyistEmployerReportFiling(long.Parse(filingId, CultureInfo.InvariantCulture), cancellationToken);
            model.ContributionsInExistingStatements = report.ContributionsInExistingStatements.ToString();
        }

        var transactions = await lobbyistEmployerCoalitionApi.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture), cancellationToken);

        model.CampaignContributionsGridModel = CreateCampaignContributionGridModel(transactions);
        model.Subtotal = transactions.Sum(c => c.Amount);

        var relatedFilers = await filingsApi.GetRelatedFilersByFilingId(long.Parse(filingId, CultureInfo.InvariantCulture), cancellationToken);
        model.RelatedFilers = relatedFilers?.Select(x => new Services.Business.FilerDisclosure.Filings.Models.FilerSearchDto
        {
            Id = x.Id,
            Name = x.Name,
            FilerId = x.FilerId,
            FilerType = x.FilerType,
            FilerTypeId = x.FilerTypeId
        }).ToList();
    }

    private async Task ProcessCoalitionPaymentsView(
        DisclosureSummaryViewModel model,
        string filingId,
        string reportType,
        CancellationToken cancellationToken)
    {
        var report = await filingsApi.GetLobbyistEmployerReportFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture), cancellationToken);
        var transactions = await lobbyistEmployerCoalitionApi.GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture), cancellationToken);

        model.IsMemberOfLobbyingCoalition = report.IsMemberOfLobbyingCoalition.ToString();
        model.CoalitionPaymentsGridModel = CreateCoalitionPaymentsGridModel(transactions, model.FilingSummaryId, reportType, filingId);
        model.Subtotal = transactions.Sum(p => p.AmountThisPeriod);
    }

    private async Task ProcessOtherPaymentsView(
        DisclosureSummaryViewModel model,
        string filingId,
        string filerId,
        CancellationToken cancellationToken)
    {
        var report = await filingsApi.GetLobbyistEmployerReportFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture), cancellationToken);
        var transactions = await lobbyistEmployerCoalitionApi.GetAllOtherPaymentsToInfluenceTransactionsForFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture), cancellationToken);

        model.TotalOverheadExpense = (decimal)report.TotalOverheadExpense;
        model.TotalUnderThresholdPayments = (decimal)report.TotalUnderThresholdPayments;
        model.OtherPaymentsGridModel = CreateOtherPaymentsGridModel(transactions, filingId, filerId);
        model.Subtotal = transactions.Sum(p => p.Amount);
    }

    private async Task ProcessReceivedCoalitionPaymentsView(
        DisclosureSummaryViewModel model,
        string filingId,
        string reportType,
        string filerId,
        CancellationToken cancellationToken)
    {
        var payments = await lobbyistEmployerCoalitionApi.GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture), cancellationToken);

        model.CoalitionReceivedGridModel = CreateReceivedCoalitionPaymentsGridModel(payments, filingId, reportType, filerId);
        model.Subtotal = payments.Sum(p => p.AmountThisPeriod);
    }

    private async Task ProcessPucActivityView(
        DisclosureSummaryViewModel model,
        string filingId,
        CancellationToken cancellationToken)
    {
        var filing = await filingsApi.GetFiling(long.Parse(filingId, CultureInfo.InvariantCulture), cancellationToken);
        model.TotalPaymentsPucActivity = filing.TotalPaymentsPucActivity;
    }

    private async Task ProcessLobbyistReportsView(
        DisclosureSummaryViewModel model,
        string filingId,
        CancellationToken cancellationToken)
    {
        if (!model.FilingSummaryId.HasValue)
        {
            return;
        }
        var nonRegisteredLobbyists = await filingSummaryApi.GetNonRegisteredLobbyistsByFilingSummaryId(model.FilingSummaryId.Value, cancellationToken);
        model.RegisteredLobbyistsGridModel = CreateRegisteredLobbyistTableModel();
        model.NonRegisteredLobbyistsGridModel = CreateNonRegisteredLobbyistTableModel(nonRegisteredLobbyists);
        model.Id = long.Parse(filingId, CultureInfo.InvariantCulture);
    }

    private async Task ProcessFirmPaymentsView(
        DisclosureSummaryViewModel model,
        string filingId,
        CancellationToken cancellationToken)
    {
        var transactions = await lobbyistEmployerCoalitionApi.GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture), cancellationToken);

        model.FirmPaymentsGridModel = CreateFirmPaymentsGridModel(transactions);
        model.Subtotal = transactions.Sum(p => p.AmountThisPeriod);
    }

    private async Task ProcessActionsLobbiedView(DisclosureSummaryViewModel model, string filingId,
        string? reportType, ITempDataDictionary tempData, CancellationToken cancellationToken)
    {
        bool shouldInitializeFromApi = model.Messages.Validations.Count == 0;

        if (reportType == FilingType.Report72h.Name)
        {
            var officialPositions = await referenceDataApi.GetAllOfficialPositions(cancellationToken);
            model.OfficialPositions = officialPositions?.ToDictionary(x => x.Id, x => x.Name);
        }

        // If Actions Lobbied directed from Index page, initialize from API
        // If Actions Lobbied directed from Submit, initialize from TempData
        if (shouldInitializeFromApi)
        {
            await InitializeActionsLobbiedFromApi(model, filingId, reportType, tempData, cancellationToken);
        }
        else
        {
            InitializeActionsLobbiedFromTempData(model, reportType, tempData);
        }
    }

    #endregion

    #region Private

    private async Task InitializeActionsLobbiedFromApi(DisclosureSummaryViewModel model, string filingId,
        string? reportType, ITempDataDictionary tempData, CancellationToken cancellationToken)
    {
        var actionsLobbied = await filingsApi.GetActionsLobbiedSummaryForFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture), cancellationToken);

        var adminActions = actionsLobbied.AdministrativeActions.Select(a => new AdministrativeActionViewModel
        {
            Id = a.Id,
            AgencyId = a.AgencyId ?? 0,
            AdministrativeAction = a.AdministrativeAction,
            AgencyDescription = a.AgencyName,
            Name = a.AgencyName,
            OfficialPosition = a.OfficialPositionName,
            OfficialPositionId = a.OfficialPositionId
        }).ToList();
        var assemblyBills = actionsLobbied.AssemblyBillActions.Select(a => new LegislativeBillViewModel(a)).ToList();
        var senateBills = actionsLobbied.SenateBillActions.Select(a => new LegislativeBillViewModel(a)).ToList();

        StoreActionsLobbiedInTempData(tempData, adminActions, assemblyBills, senateBills);

        InitializeGridModels(model, reportType, adminActions, assemblyBills, senateBills);

        model.AdvertAdminActions = adminActions.Count > 0;
        model.AdvertLegislation = senateBills.Count > 0 || assemblyBills.Count > 0;
        model.AdvertOther = !string.IsNullOrEmpty(actionsLobbied.OtherActionsLobbied);
        model.AdvertOtherActionsLobbied = actionsLobbied.OtherActionsLobbied;
    }

    private void InitializeActionsLobbiedFromTempData(DisclosureSummaryViewModel model, string? reportType, ITempDataDictionary tempData)
    {
        // Retrieve data from TempData
        var adminActions = DeserializeFromTempData<List<AdministrativeActionViewModel>>(
            tempData, AdministrativeActionTempDataKey) ?? [];

        var assemblyBills = DeserializeFromTempData<List<LegislativeBillViewModel>>(
            tempData, LegislationAssyBillTempDataKey) ?? [];

        var senateBills = DeserializeFromTempData<List<LegislativeBillViewModel>>(
            tempData, LegislationSenateBillTempDataKey) ?? [];

        // Initialize grid models
        InitializeGridModels(model, reportType, adminActions, assemblyBills, senateBills);
    }

    private static void StoreActionsLobbiedInTempData(ITempDataDictionary tempData,
        List<AdministrativeActionViewModel> adminActions,
        List<LegislativeBillViewModel> assemblyBills,
        List<LegislativeBillViewModel> senateBills)
    {
        tempData[AdministrativeActionTempDataKey] = JsonConvert.SerializeObject(adminActions);
        tempData[LegislationAssyBillTempDataKey] = JsonConvert.SerializeObject(assemblyBills);
        tempData[LegislationSenateBillTempDataKey] = JsonConvert.SerializeObject(senateBills);
    }

    private void InitializeGridModels(DisclosureSummaryViewModel model,
        string? reportType,
        List<AdministrativeActionViewModel> adminActions,
        List<LegislativeBillViewModel> assemblyBills,
        List<LegislativeBillViewModel> senateBills)
    {
        model.AdministrativeActionGridModel = CreateAdministrativeActionTableModel(adminActions, reportType);

        model.LegislationAssemblyBillGridModel = CreateLegislationBillTableModel(
            LegislationAssyBillTempDataKey,
            assemblyBills,
            "DeleteAssemblyBill",
            reportType
        );

        model.LegislationSenateBillGridModel = CreateLegislationBillTableModel(
            LegislationSenateBillTempDataKey,
            senateBills,
            "DeleteSenateBill",
            reportType
        );
    }

    private async Task<List<FilingPeriodSelectionModel>> GetAllFilingPeriodsByFiler(long filerId, long filingType, long filingId)
    {
        var allPredefinedPeriods = await filingsApi.GetAllFilingPeriodsForFiler(filerId, filingType, filingId);
        var displayedPeriods = allPredefinedPeriods.Select(p => new FilingPeriodSelectionModel(p)).ToList();
        displayedPeriods.Add(new FilingPeriodSelectionModel()); // Add Other selection item
        return displayedPeriods;
    }

    #endregion

    #region CreateDataGridModel

    private SmallDataGridModel CreateActivityExpenseGridModel(IEnumerable<ActivityExpenseItemResponse> items, string reportType)
    {
        return new SmallDataGridModel
        {
            GridId = "ActivityExpensesGrid",
            GridType = nameof(ActivityExpenseItemResponse),
            DataSource = items,
            AllowPaging = true,
            AllowTextWrap = false,
            PageSize = 10,
            PageSizes = new List<int> { 10, 20, 50 },
            AllowAdding = false,
            AllowDeleting = false,
            EnableExport = false,
            Columns = new List<DataGridColumn>{
                new() { Field = nameof(ActivityExpenseItemResponse.TransactionDate), HeaderText = localizer[CommonResourceConstants.Date], IsUtcDate = true },
                new() { Field = nameof(ActivityExpenseItemResponse.PayeeName), HeaderText = localizer[ResourceConstants.ActivityExpensesPayee] },
                new() { Field = nameof(ActivityExpenseItemResponse.PayeeType), HeaderText =localizer[ResourceConstants.ActivityExpensesPayeeType] },
                new() { Field = nameof(ActivityExpenseItemResponse.PersonBenefiting), HeaderText = localizer[ResourceConstants.ActivityExpensesPersonBenefiting] },
                new() { Field = nameof(ActivityExpenseItemResponse.AmountBenefiting), HeaderText = localizer[ResourceConstants.ActivityExpensesAmountBenefiting], IsCurrency = true }
            },
            ActionItems = new List<GridActionItem>
            {
                new() {Label = CommonResourceConstants.Edit, Action = EditRow, ControllerName = $"{TransactionControlerName}/{reportType}", ActionName = "PreEditActivityExpense"},
                new() {Label = CommonResourceConstants.Delete, Action = DeleteRow, ControllerName = DisclosureControllerName, ActionName = Delete}
            }
        };
    }

    public SmallDataGridModel CreateEndOfSessionGridModel(IEnumerable<EndOfSessionLobbyingDto> items)
    {
        return new SmallDataGridModel
        {
            GridId = "EndOfSessionLobbying",
            GridType = nameof(EndOfSessionLobbyingDto),
            DataSource = items,
            AllowPaging = true,
            AllowTextWrap = false,
            PageSize = 10,
            PageSizes = new List<int> { 10, 20, 50 },
            AllowAdding = false,
            AllowDeleting = false,
            EnableExport = false,
            Columns = new List<DataGridColumn>
            {
                new() { Field = "FilerId", HeaderText = localizer[ResourceConstants.LobbyingFirmId] },
                new() { Field = "FirmName", HeaderText = localizer[ResourceConstants.LobbyingFirmName] },
                new() { Field = "DateLobbyingFirmHired", HeaderText = localizer[ResourceConstants.LobbyingFirmHiringDate], IsUtcDate = true },
                new() { Field = "Amount", HeaderText = localizer[ResourceConstants.AmountIncurred], IsCurrency = true },
            },
            ActionItems = new List<GridActionItem>
            {
                new() { Label = CommonResourceConstants.Edit, Action = EditRow, ControllerName = DisclosureControllerName, ActionName = Edit },
                new() { Label = CommonResourceConstants.Delete, Action = DeleteRow, ControllerName = DisclosureControllerName, ActionName = Delete }
            },
        };
    }
    private SmallDataGridModel CreateCampaignContributionGridModel(IEnumerable<LobbyistEmployerCampaignContributionItemResponse> items)
    {
        var gridDataSource = items.Select(x => new LobbyistEmployerCampaignContributionGridModel(x)).ToList();

        return new SmallDataGridModel
        {
            GridId = "CampaignContribution",
            GridType = nameof(LobbyistEmployerCampaignContributionGridModel),
            DataSource = gridDataSource,
            AllowPaging = true,
            AllowTextWrap = false,
            PageSize = 10,
            PageSizes = new List<int> { 10, 20, 50 },
            AllowAdding = false,
            AllowDeleting = false,
            EnableExport = false,
            Columns = new List<DataGridColumn>
            {
                new()
                {
                    Field = nameof(LobbyistEmployerCampaignContributionGridModel.TransactionDate),
                    HeaderText = "Date",
                    IsUtcDate = true
                },
                new()
                {
                    Field = nameof(LobbyistEmployerCampaignContributionGridModel.RecipientType),
                    HeaderText = "Type of recipient"
                },
                new()
                {
                    Field = nameof(LobbyistEmployerCampaignContributionGridModel.RecipientName),
                    HeaderText = "Name"
                },
                new()
                {
                    Field = nameof(LobbyistEmployerCampaignContributionGridModel.RecipientFilerId),
                    HeaderText = "ID#"
                },
                new()
                {
                    Field = nameof(LobbyistEmployerCampaignContributionGridModel.Amount),
                    HeaderText = "Amount",
                    IsCurrency = true
                }
            },
            ActionItems = new List<GridActionItem>
            {
                new() { Label = CommonResourceConstants.Edit, Action = EditRow, ControllerName= TransactionControlerName, ActionName = "EditLobbyistCampaignContribution"},
                new() { Label = CommonResourceConstants.Delete, Action = DeleteRow, ControllerName = DisclosureControllerName, ActionName = Delete }
            }
        };
    }

    private SmallDataGridModel CreateCoalitionPaymentsGridModel(
        IEnumerable<PaymentMadeToLobbyingCoalitionResponse> items,
        long? filingSummaryId, string reportType, string filingId)
    {
        return new SmallDataGridModel
        {
            GridId = "CoalitionPaymentsGrid",
            GridType = nameof(PaymentMadeToLobbyingCoalitionResponse),
            DataSource = items,
            AllowPaging = true,
            AllowTextWrap = false,
            PageSize = 10,
            PageSizes = new List<int> { 10, 20, 50 },
            AllowAdding = false,
            AllowDeleting = false,
            EnableExport = false,
            Columns = new List<DataGridColumn>
            {
                new()
                {
                    Field = nameof(PaymentMadeToLobbyingCoalitionResponse.CoalitionName),
                    HeaderText = "Lobbying Coalition"
                },
                new()
                {
                    Field = nameof(PaymentMadeToLobbyingCoalitionResponse.FilerId),
                    HeaderText = "ID#"
                },
                new()
                {
                    Field = nameof(PaymentMadeToLobbyingCoalitionResponse.AmountThisPeriod),
                    HeaderText = "Amount this Period",
                    IsCurrency = true
                },
                new()
                {
                    Field = nameof(PaymentMadeToLobbyingCoalitionResponse.CumulativeAmount),
                    HeaderText = "Cumulative Amount",
                    IsCurrency = true
                }
            },
            ActionItems = new List<GridActionItem>
            {
                new() { Label = CommonResourceConstants.Edit, Action = EditRow, ControllerName = $"{TransactionControlerName}/{filingSummaryId}/{reportType}/{filingId}", ActionName = "InitializePaymentToLobbyingCoalition"},
                new() { Label = CommonResourceConstants.Delete, Action = DeleteRow, ControllerName = DisclosureControllerName, ActionName = Delete }
            }
        };
    }

    private SmallDataGridModel CreateOtherPaymentsGridModel(
        IEnumerable<OtherPaymentsToInfluenceResponse> items, string filingId, string filerId)
    {
        return new SmallDataGridModel
        {
            GridId = "OtherPaymentsGrid",
            GridType = nameof(OtherPaymentsToInfluenceResponse),
            DataSource = items,
            AllowPaging = true,
            AllowTextWrap = false,
            PageSize = 10,
            PageSizes = new List<int> { 10, 20, 50 },
            AllowAdding = false,
            AllowDeleting = false,
            EnableExport = false,
            Columns = new List<DataGridColumn>
            {
                new()
                {
                    Field = nameof(OtherPaymentsToInfluenceResponse.PaymentCodeName),
                    HeaderText = "Code"
                },
                new()
                {
                    Field = nameof(OtherPaymentsToInfluenceResponse.PayeeName),
                    HeaderText = "Payee"
                },
                new()
                {
                    Field = nameof(OtherPaymentsToInfluenceResponse.Amount),
                    HeaderText = "Amount",
                    IsCurrency = true
                },
                new()
                {
                    Field = nameof(OtherPaymentsToInfluenceResponse.CumulativeAmount),
                    HeaderText = "Cumulative Amount",
                    IsCurrency = true
                }
            },
            ActionItems = new List<GridActionItem>
            {
                new() { Label = CommonResourceConstants.Edit, Action = EditRow, ControllerName = TransactionControlerName, ActionName = $"{EditOtherPaymentToInfluenceTransaction}/{filingId}/{filerId}" },
                new() { Label = CommonResourceConstants.Delete, Action = DeleteRow, ControllerName = DisclosureControllerName, ActionName = Delete }
            }
        };
    }

    private SmallDataGridModel CreateReceivedCoalitionPaymentsGridModel(
        IEnumerable<PaymentReceiveLobbyingCoalitionResponse> items, string filingId, string reportType, string filerId)
    {
        return new SmallDataGridModel
        {
            GridId = "CoalitionReceivedGrid",
            GridType = nameof(PaymentReceiveLobbyingCoalitionResponse),
            DataSource = items,
            AllowPaging = true,
            AllowTextWrap = false,
            PageSize = 10,
            PageSizes = new List<int> { 10, 20, 50 },
            AllowAdding = false,
            AllowDeleting = false,
            EnableExport = false,
            Columns = new List<DataGridColumn>
            {
                new()
                {
                    Field = nameof(PaymentReceiveLobbyingCoalitionResponse.CoalitionName),
                    HeaderText = "Coalition Member"
                },
                new()
                {
                    Field = nameof(PaymentReceiveLobbyingCoalitionResponse.AmountThisPeriod),
                    HeaderText = "Amount Received",
                    IsCurrency = true
                },
                new()
                {
                    Field = nameof(PaymentReceiveLobbyingCoalitionResponse.CumulativeAmount),
                    HeaderText = "Cumulative Amount",
                    IsCurrency = true
                }
            },
            ActionItems = new List<GridActionItem>
            {
                new() { Label = CommonResourceConstants.Edit, Action = EditRow, ControllerName = TransactionControlerName, ActionName = $"{reportType}/{EditCoalitionTransaction}/{filingId}/{filerId}" },
                new() { Label = CommonResourceConstants.Delete, Action = DeleteRow, ControllerName = DisclosureControllerName, ActionName = Delete }
            }
        };
    }

    private SmallDataGridModel CreateFirmPaymentsGridModel(
        IEnumerable<PaymentMadeToLobbyingFirmsResponse> items)
    {
        return new SmallDataGridModel
        {
            GridId = "FirmPaymentsGrid",
            GridType = nameof(PaymentMadeToLobbyingFirmsResponse),
            DataSource = items,
            AllowPaging = true,
            AllowTextWrap = false,
            PageSize = 10,
            PageSizes = new List<int> { 10, 20, 50 },
            AllowAdding = false,
            AllowDeleting = false,
            EnableExport = false,
            Columns = new List<DataGridColumn>
            {
                new()
                {
                    Field = nameof(PaymentMadeToLobbyingFirmsResponse.FilerId),
                    HeaderText = "Lobbying firm ID"
                },
                new()
                {
                    Field = nameof(PaymentMadeToLobbyingFirmsResponse.FirmName),
                    HeaderText = "Name"
                },
                new()
                {
                    Field = nameof(PaymentMadeToLobbyingFirmsResponse.AmountThisPeriod),
                    HeaderText = "Total this period",
                    IsCurrency = true
                },
                new()
                {
                    Field = nameof(PaymentMadeToLobbyingFirmsResponse.CumulativeAmount),
                    HeaderText = "Cumulative total to date",
                    IsCurrency = true
                }
            },
            ActionItems = new List<GridActionItem>
            {
                new() { Label = CommonResourceConstants.Edit, Action = EditRow, ControllerName = DisclosureControllerName, ActionName = Edit },
                new() { Label = CommonResourceConstants.Delete, Action = DeleteRow, ControllerName = DisclosureControllerName, ActionName = Delete }
            }
        };
    }

    private SmallDataGridModel CreateAdministrativeActionTableModel(List<AdministrativeActionViewModel> dataSource, string? reportType)
    {
        var columns = new List<DataGridColumn>
        {
            new()
            {
                Field = nameof(AdministrativeActionViewModel.Name),
                HeaderText = "Agency/Office",
            },
            new()
            {
                Field = nameof(AdministrativeActionViewModel.AdministrativeAction),
                HeaderText = "Administrative Actions"
            }
        };

        if (reportType == FilingType.Report72h.Name)
        {
            columns.Add(new DataGridColumn
            {
                Field = nameof(AdministrativeActionViewModel.OfficialPosition),
                HeaderText = "Position"
            });
        }

        return new SmallDataGridModel
        {
            PrimaryKey = nameof(AdministrativeActionViewModel.Id),
            GridId = "AdministrativeAction",
            GridType = nameof(AdministrativeActionViewModel),
            DataSource = dataSource,
            AllowPaging = true,
            AllowTextWrap = false,
            PageSize = 10,
            PageSizes = [10, 20, 50],
            AllowAdding = false,
            AllowDeleting = true,
            EnableExport = false,
            Columns = columns,
            ActionItems = new List<GridActionItem>
            {
                new() {Label = CommonResourceConstants.Delete, Action = "deleteRow", ControllerName = $"{TransactionControlerName}", ActionName = "DeleteAdministrativeAction"}
            },
            DeleteConfirmationMessage = ResourceConstants.LobbyistEmployerActionsLobbiedAgencyDeleteConfirmation,
            DeleteConfirmationField = nameof(AdministrativeActionViewModel.Name)
        };
    }

    /// <summary>
    /// Helper function to help initialize the legislation bill grids (assembly bills and senate bills)
    /// <param name="gridId">To set the gridId of the grid model</param>
    /// <param name="dataSource">To point the grid to the appropriate list of bills</param>
    /// <param name="deleteAction">To point the grid to the appropriate delete action</param>
    /// </summary>
    private SmallDataGridModel CreateLegislationBillTableModel(string gridId, List<LegislativeBillViewModel> dataSource, string deleteAction, string? reportType)
    {
        var columns = new List<DataGridColumn>
        {
            new()
            {
                Field = nameof(LegislativeBillViewModel.Number),
                HeaderText = "Bill Number"
            },
            new()
            {
                Field = nameof(LegislativeBillViewModel.Title),
                HeaderText = "Bill Title"
            },
        };

        if (reportType == FilingType.Report72h.Name)
        {
            columns.Add(new DataGridColumn
            {
                Field = nameof(LegislativeBillViewModel.OfficialPosition),
                HeaderText = "Position"
            });
        }

        return new SmallDataGridModel
        {
            PrimaryKey = nameof(LegislativeBillViewModel.Id),
            GridId = gridId,
            GridType = nameof(LegislativeBillViewModel),
            DataSource = dataSource,
            AllowPaging = true,
            AllowTextWrap = false,
            PageSize = 10,
            PageSizes = new List<int> { 10, 20, 50 },
            AllowAdding = false,
            AllowDeleting = true,
            EnableExport = false,
            Columns = columns,
            ActionItems = new List<GridActionItem>
            {
                new() {Label = CommonResourceConstants.Delete, Action = "deleteRow", ControllerName = $"{TransactionControlerName}", ActionName = deleteAction}
            },
            DeleteConfirmationMessage = ResourceConstants.LobbyistEmployerActionsLobbiedBillDeleteConfirmation,
            DeleteConfirmationField = nameof(LegislativeBillViewModel.Title),
        };
    }

    private SmallDataGridModel CreateRegisteredLobbyistTableModel()
    {
        return new SmallDataGridModel
        {
            GridId = "RegisteredLobbyistsGrid",
            GridType = nameof(DisclosureFilingNonRegisteredLobbyistResponseDto),
            DataSource = [],
            AllowPaging = true,
            AllowTextWrap = false,
            PageSize = 10,
            PageSizes = new List<int> { 10, 20, 50 },
            AllowAdding = false,
            AllowDeleting = false,
            EnableExport = false,
            Columns = new List<DataGridColumn>{
                new() { Field = nameof(DisclosureFilingNonRegisteredLobbyistResponseDto.LobbyistName), HeaderText = localizer[ResourceConstants.Disclosure.LobbyistReports.LobbyistName] },
                new() { Field = nameof(DisclosureFilingNonRegisteredLobbyistResponseDto.Id), HeaderText = localizer[ResourceConstants.IdNumberHeader] },
            },
            ActionItems = new List<GridActionItem>
            {
                new() {Label = ResourceConstants.ViewReport, Action = "callAction", ControllerName = $"{DisclosureControllerName}", ActionName = "ViewReportRegisteredLobbyist" },
            }
        };
    }

    private SmallDataGridModel CreateNonRegisteredLobbyistTableModel(IReadOnlyList<DisclosureFilingNonRegisteredLobbyistResponseDto> dataSource)
    {
        return new SmallDataGridModel
        {
            GridId = "NonRegisteredLobbyistsGrid",
            GridType = nameof(DisclosureFilingNonRegisteredLobbyistResponseDto),
            DataSource = dataSource,
            AllowPaging = true,
            AllowTextWrap = false,
            PageSize = 10,
            PageSizes = new List<int> { 10, 20, 50 },
            AllowAdding = false,
            AllowDeleting = true,
            EnableExport = false,
            Columns = new List<DataGridColumn>{
                new() { Field = nameof(DisclosureFilingNonRegisteredLobbyistResponseDto.LobbyistName), HeaderText = localizer[ResourceConstants.Disclosure.LobbyistReports.LobbyistName] },
            },
            ActionItems = new List<GridActionItem>
            {
                new() { Label = CommonResourceConstants.Edit, Action = CallActionRow },
                new() { Label = CommonResourceConstants.Delete, Action = DeleteRow, ControllerName = $"{DisclosureControllerName}", ActionName = "DeleteNonRegisteredLobbyist" }
            },
            DeleteConfirmationMessage = ResourceConstants.Disclosure.LobbyistReports.DeleteConfirmation,
        };
    }
    #endregion

    public async Task<LobbyistEmployerReportViewModel> BuildPaymentsToInHouseLobbyistsModel(
    string? filerId,
    string? filingId,
    CancellationToken cancellationToken)
    {
        var model = new LobbyistEmployerReportViewModel();

        if (!string.IsNullOrEmpty(filerId) && !string.IsNullOrEmpty(filingId))
        {
            model.Id = long.Parse(filingId, CultureInfo.InvariantCulture);
            model.FilerId = long.Parse(filerId, CultureInfo.InvariantCulture);

            var report = await filingsApi.GetLobbyistEmployerReportFiling(
                long.Parse(filingId, CultureInfo.InvariantCulture),
                cancellationToken);

            model.TotalPaymentsToInHouseLobbyists = (decimal)report.TotalPaymentsToInHouseLobbyists;
        }

        return model;
    }

    public async Task<FilingViewModel> BuildAmendmentExplanationModel(
    string? filerId,
    string? filingId,
    CancellationToken cancellationToken)
    {
        var model = new FilingViewModel();
        if (!string.IsNullOrEmpty(filerId) && !string.IsNullOrEmpty(filingId))
        {
            model.Id = long.Parse(filingId, CultureInfo.InvariantCulture);
            model.FilerId = long.Parse(filerId, CultureInfo.InvariantCulture);
            var filing = await filingsApi.GetFiling(model!.Id.Value, cancellationToken);
            model = new FilingViewModel(filing);
        }

        return model;
    }

    public async Task<LobbyingAdvertisementViewModel> BuildPaymentToLobbyingAdvertisementModel(
    string? filerId,
    string? filingId,
    CancellationToken cancellationToken)
    {
        var model = new LobbyingAdvertisementViewModel();
        if (!string.IsNullOrEmpty(filerId) && !string.IsNullOrEmpty(filingId))
        {
            model.FilingId = long.Parse(filingId, CultureInfo.InvariantCulture);
            model.FilerId = long.Parse(filerId, CultureInfo.InvariantCulture);

            // Initialize the payment codes
            model.AdvertisementDistributionMethods = await GetAdvertisementDistributionMethods();

            try
            {
                var transaction = await lobbyingAdvertisementApi.GetLobbyingAdvertisementTransactionByFilingId(
                long.Parse(filingId, CultureInfo.InvariantCulture),
                cancellationToken);
                if (transaction is not null)
                {
                    model.PublicationDate = transaction.PublicationDate;
                    model.AdvertisementDistributionMethodId = transaction.DistributionMethodId;
                    model.DistributionMethodDescription = transaction.DistributionMethodDescription;
                    model.LegislatorId = transaction.LegislatorId;
                    model.AdditionalInformation = transaction.AdditionalInformation;
                    model.Amount = (decimal?)transaction.Amount;
                    model.Action = "UpsertLobbyingAdvertisementTransaction";
                    model.Id = transaction.Id;
                }

                return model;
            }
            catch (ApiException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
            {
                // Return 0 for 404 Not Found errors
                return model;
            }
        }

        return model;
    }

    public async Task<DisclosureFilingNonRegisteredLobbyistResponseDto> UpsertNonRegisteredLobbyist(
        DisclosureFilingNonRegisteredLobbyistRequest request,
        CancellationToken cancellationToken)
    {
        return request.Id.HasValue
            ? await filingSummaryApi.UpdateNonRegisteredLobbyist(request.Id.Value, request, cancellationToken)
            : await filingSummaryApi.CreateNewNonRegisteredLobbyist(request, cancellationToken);
    }

    public async Task DeleteRegisteredLobbyist(long id, CancellationToken cancellationToken)
    {
        await filingSummaryApi.DeleteNonRegisteredLobbyist(id, cancellationToken);
    }

    public async Task<TransactionResponseDto> HandleLobbyingAdvertisementTransactionSubmit(long filingId, LobbyingAdvertisementRequestDto requestDto, ModelStateDictionary modelState, CancellationToken cancellationToken = default)
    {
        TransactionResponseDto response;
        if (requestDto.TransactionId is not null)
        {
            response = await lobbyingAdvertisementApi.EditLobbyingAdvertisementTransaction(filingId, requestDto, cancellationToken);
        }
        else
        {
            response = await lobbyingAdvertisementApi.CreateLobbyingAdvertisementTransaction(filingId, requestDto, cancellationToken);
        }

        if (!response.Valid)
        {
            ApplyErrorsToModelState([.. response.ValidationErrors], modelState, _lobbyingAdvertisementFieldValidationMap);
        }
        return response;
    }

    public async Task<UpdateAmendmentExplanationResponse> HandleUpdateAmendmentExplanation(
        long filingId,
        FilingViewModel model,
        ModelStateDictionary modelState,
        CancellationToken cancellationToken = default)
    {
        UpdateAmendmentExplanationRequest request = new(model.AmendmentExplanation ?? "");
        var response = await filingsApi.UpdateAmendmentExplanation(filingId, request, cancellationToken);

        if (!response.Valid)
        {
            ApplyErrorsToModelState([.. response.ValidationErrors], modelState, _amendmentExplanationFieldValidationMap);
        }

        return response;
    }

    public async Task<List<SelectListItem>> GetAdvertisementDistributionMethods()
    {
        var advertisementDistributionMethodRefResponses = await referenceDataApi.GetAllAdvertisementDistributionMethods();
        var advertisementDistributionMethods = advertisementDistributionMethodRefResponses.Select(x =>
        {
            var name = x.DisplayName;
            var id = x.Id.ToString(CultureInfo.InvariantCulture);
            return new SelectListItem
            {
                Text = $"{name}",
                Value = $"{id}"
            };
        }).ToList();

        return advertisementDistributionMethods;
    }

    private static void ClearActivityExpenseData(HttpContext httpContext)
    {
        if (httpContext.Session?.Keys.Contains(TransactionController.ActivityExpenseData) == true)
        {
            httpContext.Session.Remove(TransactionController.ActivityExpenseData);
        }
    }

    private void ApplyErrorsToModelState(List<WorkFlowError> errors, ModelStateDictionary modelState, Dictionary<string, FieldProperty> dictionary)
    {
        foreach (var error in errors)
        {
            if (dictionary.TryGetValue(error.FieldName, out var data))
            {
                var errorMessage = error.Message.Replace("{{Field Name}}", localizer[data.LabelKey].Value, StringComparison.OrdinalIgnoreCase);
                modelState.AddModelError(data.FieldName, errorMessage);
            }
            else
            {
                // Errors to show at the bottom using Html.ValidationSummary
                modelState.AddModelError("", error.Message);
            }
        }
    }

    private static readonly Dictionary<string, FieldProperty> _amendmentExplanationFieldValidationMap = new()
    {
        { "FD_LOB_LobbyistEmployer5000Firm_AmendReport02_AmendmentExplanation.AmendmentExplanation", new FieldProperty(FieldName:"AmendmentExplanation", LabelKey:ResourceConstants.AmendmentExplanationTitle) },
    };

    private static readonly Dictionary<string, FieldProperty> _lobbyingAdvertisementFieldValidationMap = new()
    {
        { "DistributionMethod", new FieldProperty(FieldName:"DistributionMethod", LabelKey:ResourceConstants.LobbyingAdvertisementDistributionMethod) },
        { "Description", new FieldProperty(FieldName:"Description", LabelKey:ResourceConstants.Description) },
        { "PublicationDate", new FieldProperty(FieldName:"PublicationDate", LabelKey:ResourceConstants.LobbyingAdvertisementPublicationDate) },
        { "Amount", new FieldProperty(FieldName:"Amount", LabelKey:ResourceConstants.LobbyingAdvertisementAmount) }
    };

    #region Shared
    private static T? DeserializeFromTempData<T>(ITempDataDictionary tempData, string key) where T : class
    {
        var data = tempData.Peek(key) as string;
        return string.IsNullOrEmpty(data) ? null : JsonConvert.DeserializeObject<T>(data);
    }

    #endregion
}

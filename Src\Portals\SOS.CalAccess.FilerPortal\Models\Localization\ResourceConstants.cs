namespace SOS.CalAccess.FilerPortal.Models.Localization;

public static class ResourceConstants
{
    //Common
    public const string StartRegistration = "Common.StartRegistration";
    public const string Continue = "Common.Continue";
    public const string Previous = "Common.Previous";
    public const string Cancel = "Common.Cancel";
    public const string Close = "Common.Close";
    public const string Submit = "Common.Submit";
    public const string SaveAndClose = "Common.SaveAndClose";
    public const string Create = "Common.Create";
    public const string Edit = "Common.Edit";
    public const string View = "Common.View";
    public const string Start = "Common.Start";
    public const string AddNew = "Common.AddNew";
    public const string UploadTransactions = "Common.UploadTransactions";
    public const string Export = "Common.Export";
    public const string Jurisdiction = "Common.Jurisdiction";
    public const string State = "Common.State";
    public const string County = "Common.County";
    public const string City = "Common.City";
    public const string Party = "Common.Party";
    public const string Candidate = "Common.Candidate";
    public const string Election = "Common.Election";
    public const string Verification = "Common.Verification";
    public const string Step = "Common.Step";
    public const string FieldIsRequired = "Common.FieldIsRequired";
    public const string Residential = "Common.Residential";
    public const string Business = "Common.Business";
    public const string Yes = "Common.Yes";
    public const string No = "Common.No";
    public const string Myself = "Common.Myself";
    public const string SomeoneElse = "Common.SomeoneElse";
    public const string Attest = "Common.Attest";
    public const string Terminate = "Common.Terminate";
    public const string SendForAttestation = "Common.SendForAttestation";
    public const string PreviewPdf = "Common.PreviewPDF";
    public const string SelectCountry = "Common.SelectCountry";
    public const string SelectState = "Common.SelectState";
    public const string SelectFilingPeriod = "Common.SelectFilingPeriod";
    public const string AmendRegistration = "Common.AmendRegistration";
    public const string AmendTerminateRegistration = "Common.AmendTerminiateRegistration";
    public const string WithdrawRegistration = "Common.WithdrawRegistration";
    public const string Termination = "Common.Termination";
    public const string TerminationRegistration = "Common.TerminationRegistration";
    public const string WithdrawalRegistration = "Common.WithdrawalRegistration";
    public const string RenewalRegistration = "Common.RenewalRegistration";
    public const string Address = "Common.Address";
    public const string PhoneNumber = "Common.PhoneNumber";
    public const string EmailAddress = "Common.EmailAddress";
    public const string FaxNumber = "Common.FaxNumber";
    public const string District = "Common.District";
    public const string DistrictNumber = "Common.DistrictNumber";
    public const string Date = "Common.Date";
    public const string CommonSelectOption = "Common.SelectOption";
    public const string Save = "Common.Save";
    public const string APIRequestError = "Common.APIRequestError";
    public const string IdNumberHeader = "Common.IdNumberHeader";
    public const string ViewReport = "Common.ViewReport";
    public const string FirstName = "Common.FirstName";
    public const string LastName = "Common.LastName";
    public const string Amount = "Common.Amount";
    public const string PaymentCode = "Common.PaymentCode";
    public const string Description = "Common.Description";
    public const string SelectCode = "Common.SelectCode";
    public const string SelectOfficer = "Common.SelectOfficer";
    public const string FilerName = "Common.FilerName";
    public const string FilerId = "Common.FilerId";
    public const string DocumentId = "Common.DocumentId";
    public const string Form = "Common.Form";
    public const string Submitted = "Common.Submitted";
    public const string Status = "Common.Status";
    public const string TelephoneNumber = "Common.TelephoneNumber";
    public const string StreetAddress = "Common.StreetAddress";
    public const string Street2 = "Common.Street2";
    public const string ZipCode = "Common.ZipCode";
    public const string TypeOfAddress = "Common.TypeOfAddress";
    public const string Name = "Common.Name";
    public const string Title = "Common.Title";
    public const string StartDate = "Common.StartDate";
    public const string ExecutedOn = "Common.ExecutedOn";
    public const string CandidateName = "Common.CandidateName";
    public const string CandidateEmail = "Common.CandidateEmail";
    public const string CandidateAddress = "Common.CandidateAddress";
    public const string OfficeSought = "Common.OfficeSought";
    public const string ElectionYear = "Common.ElectionYear";
    public const string NotApplicable = "Common.NotApplicable";

    //FilerPortal
    public const string FilerPortalTitle = "FilerPortal.Common.Title";

    //FilerPortal.NotificationPreferences Constants
    public const string NotificationsPrefsTitle = "FilerPortal.NotificationPrefs.Edit.Title";
    public const string SystemNotificationHeading = "FilerPortal.NotificationPrefs.Edit.SystemNotificationHeading";
    public const string FilingNotificationHeading = "FilerPortal.NotificationPrefs.Edit.FilingNotificationHeading";
    public const string AllFilersHeading = "FilerPortal.NotificationPrefs.Edit.AllFilersHeading";
    public const string SystemNotifications = "FilerPortal.NotificationPrefs.Edit.SystemNotifications";
    public const string ReminderNotifications = "FilerPortal.NotificationPrefs.Edit.ReminderNotifications";
    public const string PriorityNotifications = "FilerPortal.NotificationPrefs.Edit.PriorityNotifications";
    public const string OtherNotifications = "FilerPortal.NotificationPrefs.Edit.OtherNotifications";
    public const string Email = "FilerPortal.NotificationPrefs.Edit.Email";
    public const string CancelDialog = "FilerPortal.NotificationPrefs.Edit.CancelDialog";
    public const string SaveSuccess = "FilerPortal.NotificationPrefs.Edit.SaveSuccess";

    //FilerPortal.NotificationMessage.Messages Constants
    public const string MessageDate = "FilerPortal.NotificationMessage.Messages.MessageDate";
    public const string MessageSubject = "FilerPortal.NotificationMessage.Messages.MessageSubject";
    public const string MessageType = "FilerPortal.NotificationMessage.Messages.MessageType";
    public const string MessageDueDate = "FilerPortal.NotificationMessage.Messages.MessageDueDate";
    public const string MessagePriority = "FilerPortal.NotificationMessage.Messages.MessagePriority";
    public const string MessageActionRequired = "FilerPortal.NotificationMessage.Messages.MessageActionRequired";
    public const string MessageCenterTitle = "FilerPortal.NotificationMessage.Messages.MessageCenterTitle";
    public const string MessageFilerName = "FilerPortal.NotificationMessage.Messages.MessageFilerName";
    public const string MessageDeleteFailureMessage = "FilerPortal.NotificationMessage.Messages.DeleteFailureMessage";
    public const string MessageMarkAsRead = "FilerPortal.NotificationMessage.Messages.MarkAsRead";
    public const string MessageMarkUnRead = "FilerPortal.NotificationMessage.Messages.MarkUnread";

    //FilerPortal.NotificationMessage.Details Constants
    public const string NotificationDetailsDeleteConfirmation = "FilerPortal.NotificationMessage.Details.DeleteConfirmation";
    public const string NotificationDetailsDateReceived = "FilerPortal.NotificationMessage.Details.DateReceived";
    public const string NotificationDetailsDueDate = "FilerPortal.NotificationMessage.Details.DueDate";
    public const string NotificationDetailsFilerType = "FilerPortal.NotificationMessage.Details.FilerType";
    public const string NotificationDetailsIsActionRequired = "FilerPortal.NotificationMessage.Details.ActionRequired";
    public const string NotificationDetailsIsPriorityMessage = "FilerPortal.NotificationMessage.Details.PriorityNotification";
    public const string NotificationDetailsMessage = "FilerPortal.NotificationMessage.Details.Message";
    public const string NotificationDetailsNotificationType = "FilerPortal.NotificationMessage.Details.NotificationType";
    public const string NotificationDetailsSubject = "FilerPortal.NotificationMessage.Details.Subject";
    public const string NotificationDetailsMarkAsRead = "FilerPortal.NotificationMessage.Details.MarkAsRead";
    public const string NotificationDetailsMarkUnRead = "FilerPortal.NotificationMessage.Details.MarkUnread";
    public const string NotificationDetailsFilerName = "FilerPortal.NotificationMessage.Details.FilerName";

    //FilerPortal.ReferencePage Constants
    public const string RegistrationDate = "FilerPortal.Home.ReferencePage.RegistrationDate";
    public const string FilingDate = "FilerPortal.Home.ReferencePage.FilingDate";

    //CandidateIntentionStatement
    public const string CisRegistrationTitle = "CandidateIntentionStatement.Common.Title";
    public const string CisRegistrationBreadcrumb = "CandidateIntentionStatement.Common.Breadcrumb";
    public const string CisRegistrationCreatedSuccess = "CandidateIntentionStatement.Common.CreatedSuccess";
    public const string CisRegistrationNewTitle = "CandidateIntentionStatement.New.Title";
    public const string CisRegistrationNewBody = "CandidateIntentionStatement.New.Body";
    public const string CisRegistration01Title = "CandidateIntentionStatement.New.Title";
    public const string CisRegistration01Body = "CandidateIntentionStatement.New.Body";

    public const string CisRegistration02Title = "CandidateIntentionStatement.Page02.Title";
    public const string CisRegistration02Body = "CandidateIntentionStatement.Page02.Body";

    public const string CisRegistration03Title = "CandidateIntentionStatement.Page03.Title";
    public const string CisRegistration03Body = "CandidateIntentionStatement.Page03.Body";
    public const string CisRegistration03WhoIsCandidate = "CandidateIntentionStatement.RegisteringYourselfOrSomeoneElse";
    public const string CisRegistration03AlreadyRegistered = "CandidateIntentionStatement.HasCandidateRegisteredBefore";

    public const string CisRegistrationFormTitle = "CandidateIntentionStatement.Page04.Title";
    public const string CisRegistrationFormBody = "CandidateIntentionStatement.Page04.Body";
    public const string CisRegistrationFormFirstName = "CandidateIntentionStatement.FirstName";
    public const string CisRegistrationFormMiddleName = "CandidateIntentionStatement.MiddleName";
    public const string CisRegistrationFormLastName = "CandidateIntentionStatement.LastName";
    public const string CisRegistrationFormEmail = "CandidateIntentionStatement.Email";
    public const string CisRegistrationFormPhoneNumber = "CandidateIntentionStatement.PhoneNumber";
    public const string CisRegistrationFormFaxNumber = "CandidateIntentionStatement.FaxNumber";
    public const string CisRegistrationFormCandidateCountry = "CandidateIntentionStatement.CandidateAddress.Country";
    public const string CisRegistrationFormCandidateStreet = "CandidateIntentionStatement.CandidateAddress.Street";
    public const string CisRegistrationFormCandidateStreet2 = "CandidateIntentionStatement.CandidateAddress.Street2";
    public const string CisRegistrationFormCandidateCity = "CandidateIntentionStatement.CandidateAddress.City";
    public const string CisRegistrationFormCandidateState = "CandidateIntentionStatement.CandidateAddress.State";
    public const string CisRegistrationFormCandidateZip = "CandidateIntentionStatement.CandidateAddress.Zip";
    public const string CisRegistrationFormCandidateType = "CandidateIntentionStatement.CandidateAddress.Type";
    public const string CisRegistrationFormMailingCountry = "CandidateIntentionStatement.MailingAddress.Country";
    public const string CisRegistrationFormMailingStreet = "CandidateIntentionStatement.MailingAddress.Street";
    public const string CisRegistrationFormMailingStreet2 = "CandidateIntentionStatement.MailingAddress.Street2";
    public const string CisRegistrationFormMailingCity = "CandidateIntentionStatement.MailingAddress.City";
    public const string CisRegistrationFormMailingState = "CandidateIntentionStatement.MailingAddress.State";
    public const string CisRegistrationFormMailingZip = "CandidateIntentionStatement.MailingAddress.Zip";
    public const string CisRegistrationFormMailingType = "CandidateIntentionStatement.MailingAddress.Type";
    public const string CisRegistrationFormSameAs = "CandidateIntentionStatement.SameAsCandidateAddress";

    public const string CisRegistration05Title = "CandidateIntentionStatement.Page05.Title";
    public const string CisRegistration05Body = "CandidateIntentionStatement.Page05.Body";

    public const string CisRegistrationElectionFormTitle = "CandidateIntentionStatement.Page06.Title";
    public const string CisRegistrationElectionFormBody = "CandidateIntentionStatement.Page06.Body";

    public const string CisRegistration07Title = "CandidateIntentionStatement.Page07.Title";
    public const string CisRegistration07Body = "CandidateIntentionStatement.Page07.Body";
    public const string CisRegistration07ExpendiureLimitYearText = "CandidateIntentionStatement.ElectionInformation.ExpenditureLimit.YearText";
    public const string CisRegistration07ExpenditureLimitRadioLabel = "CandidateIntentionStatement.ElectionInformation.ExpenditureLimit.RadioLabel";
    public const string CisRegistration07ExpenditureLimitAccept = "CandidateIntentionStatement.ElectionInformation.ExpenditureLimit.Accept";
    public const string CisRegistration07ExpenditureLimitDecline = "CandidateIntentionStatement.ElectionInformation.ExpenditureLimit.Decline";

    public const string CisRegistration08DiligenceAgreement = "CandidateIntentionStatement.DiligenceAgreement";
    public const string CisRegistration08PreviewPdf = "CandidateIntentionStatement.PreviewPDF";
    public const string CisRegistration08VerifyHeading = "CandidateIntentionStatement.VerifyHeading";
    public const string CisRegistration08VerifyInstructions = "CandidateIntentionStatement.VerifyInstructions";
    public const string CisRegistration08VerifyNotice = "CandidateIntentionStatement.VerifyNotice";
    public const string CisRegistration08VerifySendforAttestation = "CandidateIntentionStatement.VerifySendforAttestation";

    public const string CisRegistration09SubHeader = "CandidateIntentionStatement.Page09.SubHeader";
    public const string CisRegistration09Body = "CandidateIntentionStatement.Page09.Body";
    public const string CisRegistration09Label = "CandidateIntentionStatement.Page09.Label";
    public const string CisRegistration09Action = "CandidateIntentionStatement.Page09.Action";

    public const string CisRegistrationConfirmationFormTitle = "CandidateIntentionStatement.Page08.Title";
    public const string CisRegistrationConfirmationFormVerificationTitle = "CandidateIntentionStatement.VerifyTitle";

    public const string CisElection02Title = "CandidateIntentionStatement.Election02.Title";
    public const string CisElection02Body01 = "CandidateIntentionStatement.Election02.Body01";
    public const string CisElection02Body02 = "CandidateIntentionStatement.Election02.Body02";
    public const string CisElectionElectionYear = "CandidateIntentionStatement.ElectionInformation.ElectionYear";
    public const string CisElectionOfficeSought = "CandidateIntentionStatement.ElectionInformation.OfficeSought";
    public const string CisElectionDistrictNumber = "CandidateIntentionStatement.ElectionInformation.DistrictNumber";
    public const string CisElectionDistrictOrCounty = "CandidateIntentionStatement.ElectionInformation.DistrictOrCounty";
    public const string CisElectionAgencyName = "CandidateIntentionStatement.ElectionInformation.AgencyName";

    //FilerPortal.AmendCandidateRegistration Constants

    public const string AmendCis03AmendmentBody = "FilerPortal.AmendCandidateRegistration.Page03.Amendment.Body";
    public const string AmendCis03AmendmentCheckbox = "FilerPortal.AmendCandidateRegistration.Page03.Amendment.Checkbox";
    public const string AmendCis03PersonalFundsHeader = "FilerPortal.AmendCandidateRegistration.Page03.PersonalFunds.Header";
    public const string AmendCis03PerosnalFundsCheckbox = "FilerPortal.AmendCandidateRegistration.Page03.PersonalFunds.Checkbox";

    //FilerPortal.WithdrawalCIS Constants
    public const string WithdrawalCisTitle = "FilerPortal.WithdrawalCis.Title";
    public const string WithdrawalCisBreadcrumb = "FilerPortal.WithdrawalCis.Breadcrumb";
    public const string WithdrawalCisStep01 = "FilerPortal.WithdrawalCis.Step01";
    public const string WithdrawalCisStep02 = "FilerPortal.WithdrawalCis.Step02";

    public const string WithdrawalCisPage01Header = "FilerPortal.WithdrawalCis.Page01.Header";
    public const string WithdrawalCisPage01Body = "FilerPortal.WithdrawalCis.Page01.Body";
    public const string WithdrawalCisPage02Header = "FilerPortal.WithdrawalCis.Page02.Header";
    public const string WithdrawalCisPage02Checkbox = "FilerPortal.WithdrawalCis.Page02.Checkbox";
    public const string ******************************** = "FilerPortal.WithdrawalCis.Page02.NonCand.Body01";
    public const string WithdrawalCisPage02NonCandBody02 = "FilerPortal.WithdrawalCis.Page02.NonCand.Body02";
    public const string WithdrawalCisPage02NonCandBody03 = "FilerPortal.WithdrawalCis.Page02.NonCand.Body03";
    public const string WithdrawalCisPage03CandHeader = "FilerPortal.WithdrawalCis.Page03.CandHeader";
    public const string WithdrawalCisPage03CandBody = "FilerPortal.WithdrawalCis.Page03.CandBody";
    public const string WithdrawalCisPage03NonCandHeader = "FilerPortal.WithdrawalCis.Page03.NonCandHeader";
    public const string WithdrawalCisPage03NonCandBody = "FilerPortal.WithdrawalCis.Page03.NonCandBody";

    // FilerPortal.Form470 Constants
    public const string Form470Title = "FilerPortal.Form470.Title";
    public const string Form470Subtitle = "FilerPortal.Form470.Subtitle";
    public const string Form470Page01Title = "FilerPortal.Form470.Page01.Title";
    public const string Form470Page01Body = "FilerPortal.Form470.Page01.Body";
    public const string Form470Page01CalendarYearCovered = "FilerPortal.Form470.Page01.CalendarYearCovered";
    public const string Form470Page01CandidateHeader = "FilerPortal.Form470.Page01.CandidateHeader";
    public const string Form470Page01CandidateName = "FilerPortal.Form470.Page01.CandidateName";
    public const string Form470Page01OfficeSoughtHeader = "FilerPortal.Form470.Page01.OfficeSoughtHeader";
    public const string Form470Page01OfficeSoughtName = "FilerPortal.Form470.Page01.OfficeSoughtName";
    public const string Form470Page01DateOfElection = "FilerPortal.Form470.Page01.DateOfElection";

    public const string Form470Page02Title = "FilerPortal.Form470.Page02.Title";
    public const string Form470Page02Description = "FilerPortal.Form470.Page02.Description";
    public const string Form470Page02TableCommitteeName = "FilerPortal.Form470.Page02.TableCommitteeName";
    public const string Form470Page02TableId = "FilerPortal.Form470.Page02.TableId";
    public const string Form470Page02TableAddress = "FilerPortal.Form470.Page02.TableAddress";
    public const string Form470Page02TreasurerName = "FilerPortal.Form470.Page02.TableTreasurerName";
    public const string Form470Page02AddButton = "FilerPortal.Form470.Page02.AddButton";
    public const string Form470Page02DeleteMessage = "FilerPortal.Form470.Page02.DeleteMessage";

    public const string Form470Page03Title = "FilerPortal.Form470.Page03.Title";
    public const string Form470Page03Description1 = "FilerPortal.Form470.Page03.Description1";
    public const string Form470Page03Description2 = "FilerPortal.Form470.Page03.Description2";

    public const string Form470AttestationTitle = "FilerPortal.Form470Attestation.Title";
    public const string Form470AttestationDescription = "FilerPortal.Form470Attestation.Description";
    public const string Form470AttestationVerificationTerm = "FilerPortal.Form470Attestation.Verification.Term";
    public const string Form470AttestationVerificationName = "FilerPortal.Form470Attestation.Verification.Name";
    public const string Form470AttestationVerificationNameNonCandidateHeader = "FilerPortal.Form470Attestation.Verification.NonCandidate.Header";
    public const string Form470AttestationVerificationNameNonCandidateBody1 = "FilerPortal.Form470Attestation.Verification.NonCandidate.Body1";
    public const string Form470AttestationVerificationNameNonCandidateBody2 = "FilerPortal.Form470Attestation.Verification.NonCandidate.Body2";
    public const string Form470AttestationAttestationRequestSent = "FilerPortal.Form470Attestation.Confirmation.AttestationRequestSent";
    public const string Form470AttestationAttestationRequestSentMessage = "FilerPortal.Form470Attestation.Confirmation.AttestationRequestSent.Message";

    // FilerPortal.Form470 Constants
    public const string Form470SPage01Title = "FilerPortal.Form470S.Page01.Title";
    public const string Form470SPage01Body = "FilerPortal.Form470S.Page01.Body";
    public const string Form470SPage01DateContributionsTitle = "FilerPortal.Form470S.Page01.DateContributions.Title";
    public const string Form470SPage01DateContributionsBody = "FilerPortal.Form470S.Page01.DateContributions.Body";
    public const string Form470SPage02NextStepsBody = "FilerPortal.Form470S.Page02.NextSteps.Body";

    // FilerPortal.LinkCommittee Constants

    public const string LinkCommitteeTitle = "FilerPortal.LinkCommittee.Title";
    public const string LinkCommitteeCommitteeName = "FilerPortal.LinkCommittee.CommitteeName";
    public const string LinkCommitteeId = "FilerPortal.LinkCommittee.Id";
    public const string LinkCommitteeEmail = "FilerPortal.LinkCommittee.Email";
    public const string LinkCommitteePhone = "FilerPortal.LinkCommittee.Phone";
    public const string LinkCommitteeAddress = "FilerPortal.LinkCommittee.Address";
    public const string LinkCommitteeMailAddress = "FilerPortal.LinkCommittee.MailAddress";

    public const string LinkCommittee01SubTitle = "FilerPortal.LinkCommittee01.SubTitle";
    public const string LinkCommittee01Description = "FilerPortal.LinkCommittee01.Description";
    public const string LinkCommittee01Label = "FilerPortal.LinkCommittee01.Label";
    public const string LinkCommittee01Yes = "FilerPortal.LinkCommittee01.Yes";
    public const string LinkCommittee01No = "FilerPortal.LinkCommittee01.No";
    public const string LinkCommittee01SubSectionTitle = "FilerPortal.LinkCommittee01.SubSectionTitle";
    public const string LinkCommittee01Body = "FilerPortal.LinkCommittee01.Body";
    public const string LinkCommittee01Body2 = "FilerPortal.LinkCommittee01.Body2";

    public const string LinkCommittee02SubTitle = "FilerPortal.LinkCommittee02.SubTitle";
    public const string LinkCommittee02Description = "FilerPortal.LinkCommittee02.Description";

    //FilerPortal.SmoRegistration Constants

    public const string SmoRegistrationTitle = "FilerPortal.SmoRegistration.Common.Title";
    public const string SmoRegistrationOrganization = "FilerPortal.SmoRegistration.Common.Organization";
    public const string SmoRegistrationOfficers = "FilerPortal.SmoRegistration.Common.Officers";
    public const string SmoRegistrationIndividualAuthorizers = "FilerPortal.SmoRegistration.Common.IndividualAuthorizers";
    public const string SmoRegistrationSubmit = "FilerPortal.SmoRegistration.Common.Submit";
    public const string SmoRegistration01Body = "FilerPortal.SmoRegistration.Page01.Body";
    public const string SmoRegistration02Title = "FilerPortal.SmoRegistration.Page02.Title";
    public const string SmoRegistration02Body = "FilerPortal.SmoRegistration.Page02.Body";
    public const string SmoRegistration02Body2 = "FilerPortal.SmoRegistration.Page02.Body2";

    public const string SmoRegistration03Title = "FilerPortal.SmoRegistration.Page03.Title";
    public const string SmoRegistration03Body = "FilerPortal.SmoRegistration.Page03.Body";

    public const string SmoRegistration03OrganizationName = "FilerPortal.SmoRegistration.Page03.OrganizationName";
    public const string SmoRegistration03Email = "FilerPortal.SmoRegistration.Page03.Email";
    public const string SmoRegistration03PhoneNumber = "FilerPortal.SmoRegistration.Page03.TelephoneNumber";
    public const string SmoRegistration03FaxNumber = "FilerPortal.SmoRegistration.Page03.FaxNumber";
    public const string SmoRegistration03OrganizationAddress = "FilerPortal.SmoRegistration.Page03.OrganizationAddress";
    public const string SmoRegistration03MailingAddress = "FilerPortal.SmoRegistration.Page03.MailingAddress";
    public const string SmoRegistration03Country = "FilerPortal.SmoRegistration.Page03.Country";
    public const string SmoRegistration03Street1 = "FilerPortal.SmoRegistration.Page03.Street1";
    public const string SmoRegistration03Street1Caption = "FilerPortal.SmoRegistration.Page03.Street1Caption";
    public const string SmoRegistration03Street2 = "FilerPortal.SmoRegistration.Page03.Street2";
    public const string SmoRegistration03City = "FilerPortal.SmoRegistration.Page03.City";
    public const string SmoRegistration03State = "FilerPortal.SmoRegistration.Page03.State";
    public const string SmoRegistration03ZipCode = "FilerPortal.SmoRegistration.Page03.ZipCode";
    public const string SmoRegistration03County = "FilerPortal.SmoRegistration.Page03.County";
    public const string SmoRegistration03TypeOfAddress = "FilerPortal.SmoRegistration.Page03.TypeOfAddress";
    public const string SmoRegistration03SameAddress = "FilerPortal.SmoRegistration.Page03.SameAddress";

    public const string SmoRegistration04Title = "FilerPortal.SmoRegistration.Page04.Title";
    public const string SmoRegistration04Body = "FilerPortal.SmoRegistration.Page04.Body";
    public const string SmoRegistration04OrganizationLevelOfActivity = "FilerPortal.SmoRegistration.Page04.OrganizationLevelOfActivity";
    public const string SmoRegistration04IsOrganizationQualified = "FilerPortal.SmoRegistration.Page04.IsOrganizationQualified";
    public const string SmoRegistration04IsOrganizationQualifiedTooltip = "FilerPortal.SmoRegistration.Page04.IsOrganizationQualifiedTooltip";
    public const string SmoRegistration04DateQualifiedAsSMO = "FilerPortal.SmoRegistration.Page04.DateQualifiedAsSMO";
    public const string SmoRegistration04IsOrganizationCampaignCommittee = "FilerPortal.SmoRegistration.Page04.IsOrganizationCampaignCommittee";
    public const string SmoRegistration04IsOrganizationCampaignCommitteeTooltip = "FilerPortal.SmoRegistration.Page04.IsOrganizationCampaignCommitteeTooltip";
    public const string SmoRegistration04IsOrganizationCampaignCommitteeCaption = "FilerPortal.SmoRegistration.Page04.IsOrganizationCampaignCommitteeCaption";
    public const string SmoRegistration04CommitteeIDOrName = "FilerPortal.SmoRegistration.Page04.CommitteeIDOrName";
    public const string SmoRegistration04CommitteeIDOrNameMessage = "FilerPortal.SmoRegistration.Page04.CommitteeIDOrNameMessage";
    public const string SmoRegistration05Title = "FilerPortal.SmoRegistration.Page05.Title";
    public const string SmoRegistration05Body = "FilerPortal.SmoRegistration.Page05.Body";
    public const string SmoRegistration06Title = "FilerPortal.SmoRegistration.Page06.Title";
    public const string SmoRegistration06Body = "FilerPortal.SmoRegistration.Page06.Body";
    public const string SmoRegistration06IsTreasurer = "FilerPortal.SmoRegistration.Page06.IsTreasurer";
    public const string SmoRegistration07TitleAdd = "FilerPortal.SmoRegistration.Page07.TitleAdd";
    public const string SmoRegistration07TitleEdit = "FilerPortal.SmoRegistration.Page07.TitleEdit";
    public const string SmoRegistration07TitleConfirmTreasurer = "FilerPortal.SmoRegistration.Page07.TitleConfirmTreasurer";
    public const string SmoRegistration07Body = "FilerPortal.SmoRegistration.Page07.Body";
    public const string SmoRegistration07FirstName = "FilerPortal.SmoRegistration.Page07.FirstName";
    public const string SmoRegistration07MiddleName = "FilerPortal.SmoRegistration.Page07.MiddleName";
    public const string SmoRegistration07LastName = "FilerPortal.SmoRegistration.Page07.LastName";
    public const string SmoRegistration07Email = "FilerPortal.SmoRegistration.Page07.Email";
    public const string SmoRegistration07TelephoneNumber = "FilerPortal.SmoRegistration.Page07.TelephoneNumber";
    public const string SmoRegistration07Country = "FilerPortal.SmoRegistration.Page07.Country";
    public const string SmoRegistration07Address = "FilerPortal.SmoRegistration.Page07.Address";
    public const string SmoRegistration07Address2 = "FilerPortal.SmoRegistration.Page07.Address2";
    public const string SmoRegistration07City = "FilerPortal.SmoRegistration.Page07.City";
    public const string SmoRegistration07State = "FilerPortal.SmoRegistration.Page07.State";
    public const string SmoRegistration07ZipCode = "FilerPortal.SmoRegistration.Page07.ZipCode";
    public const string SmoRegistration07TypeOfAddress = "FilerPortal.SmoRegistration.Page07.TypeOfAddress";
    public const string SmoRegistration07IsUserAuthorized = "FilerPortal.SmoRegistration.Page07.IsUserAuthorized";
    public const string SmoRegistration08Title = "FilerPortal.SmoRegistration.Page08.Title";
    public const string SmoRegistration08Body = "FilerPortal.SmoRegistration.Page08.Body";
    public const string SmoRegistration08AddOfficerMessage = "FilerPortal.SmoRegistration.Page08.AddOfficerMessage";
    public const string SmoRegistration08AddOfficer = "FilerPortal.SmoRegistration.Page08.AddOfficer";
    public const string SmoRegistration08AddAnotherOfficer = "FilerPortal.SmoRegistration.Page08.AddAnotherOfficer";
    public const string SmoRegistration08OfficerName = "FilerPortal.SmoRegistration.Page08.OfficerName";
    public const string SmoRegistration08OfficerTitle = "FilerPortal.SmoRegistration.Page08.OfficerTitle";
    public const string SmoRegistration08OfficerStartDate = "FilerPortal.SmoRegistration.Page08.OfficerStartDate";
    public const string SmoRegistration08OfficerPhoneNumber = "FilerPortal.SmoRegistration.Page08.OfficerPhoneNumber";
    public const string SmoRegistration08DeleteOfficerModalTitle = "FilerPortal.SmoRegistration.Page08.DeleteOfficerModalTitle";
    public const string SmoRegistration08DeleteOfficerModalMessage = "FilerPortal.SmoRegistration.Page08.DeleteOfficerModalMessage";
    public const string SmoRegistration09Title = "FilerPortal.SmoRegistration.Page09.Title";
    public const string SmoRegistration09Body = "FilerPortal.SmoRegistration.Page09.Body";
    public const string SmoRegistration09IsOfficer = "FilerPortal.SmoRegistration.Page09.IsOfficer";
    public const string SmoRegistration10TitleAdd = "FilerPortal.SmoRegistration.Page10.TitleAdd";
    public const string SmoRegistration10TitleEdit = "FilerPortal.SmoRegistration.Page10.TitleEdit";
    public const string SmoRegistration10Body = "FilerPortal.SmoRegistration.Page10.Body";
    public const string SmoRegistration10OfficerTitle = "FilerPortal.SmoRegistration.Page10.OfficerTitle";
    public const string SmoRegistration10FirstName = "FilerPortal.SmoRegistration.Page10.FirstName";
    public const string SmoRegistration10MiddleName = "FilerPortal.SmoRegistration.Page10.MiddleName";
    public const string SmoRegistration10LastName = "FilerPortal.SmoRegistration.Page10.LastName";
    public const string SmoRegistration10Email = "FilerPortal.SmoRegistration.Page10.Email";
    public const string SmoRegistration10TelephoneNumber = "FilerPortal.SmoRegistration.Page10.TelephoneNumber";
    public const string SmoRegistration10Country = "FilerPortal.SmoRegistration.Page10.Country";
    public const string SmoRegistration10Address = "FilerPortal.SmoRegistration.Page10.Address";
    public const string SmoRegistration10Address2 = "FilerPortal.SmoRegistration.Page10.Address2";
    public const string SmoRegistration10City = "FilerPortal.SmoRegistration.Page10.City";
    public const string SmoRegistration10State = "FilerPortal.SmoRegistration.Page10.State";
    public const string SmoRegistration10ZipCode = "FilerPortal.SmoRegistration.Page10.ZipCode";
    public const string SmoRegistration10TypeOfAddress = "FilerPortal.SmoRegistration.Page10.TypeOfAddress";
    public const string SmoRegistration10IsUserAuthorized = "FilerPortal.SmoRegistration.Page10.IsUserAuthorized";
    public const string SmoRegistration11Title = "FilerPortal.SmoRegistration.Page11.Title";
    public const string SmoRegistration11Body = "FilerPortal.SmoRegistration.Page11.Body";
    public const string SmoRegistration12Title = "FilerPortal.SmoRegistration.Page12.Title";
    public const string SmoRegistration12Body = "FilerPortal.SmoRegistration.Page12.Body";
    public const string SmoRegistration12AddAuthorizerMessage = "FilerPortal.SmoRegistration.Page12.AddAuthorizerMessage";
    public const string SmoRegistration12AddAuthorizerButton = "FilerPortal.SmoRegistration.Page12.AddAuthorizerButton";
    public const string SmoRegistration12DeleteAuthorizerModalTitle = "FilerPortal.SmoRegistration.Page12.DeleteAuthorizerModalTitle";
    public const string SmoRegistration12DeleteAuthorizerModalMessage = "FilerPortal.SmoRegistration.Page12.DeleteAuthorizerModalMessage";
    public const string SmoRegistration13AuthorizerAddress = "FilerPortal.SmoRegistration.Page13.AuthorizerAddress";
    public const string SmoRegistration14IsTreasurerTitle = "FilerPortal.SmoRegistration.Page14.IsTreasurerTitle";
    public const string SmoRegistration14IsNotTreasurerTitle = "FilerPortal.SmoRegistration.Page14.IsNotTreasurerTitle";
    public const string SmoRegistration14SendForAcknowledgement = "FilerPortal.SmoRegistration.Page14.SendForAcknowledgement";
    public const string SmoRegistration14TreasurerAcknowledgement = "FilerPortal.SmoRegistration.Page14.TreasurerAcknowledgement";
    public const string SmoRegistration14And15Body = "FilerPortal.SmoRegistration.Page14And15.Body";
    public const string SmoRegistration14And15Body2 = "FilerPortal.SmoRegistration.Page14And15.Body2";
    public const string SmoRegistration14And15BodyResponsibleOfficers = "FilerPortal.SmoRegistration.Page14And15.ResponsibleOfficers";

    public const string SmoRegistration15Title = "FilerPortal.SmoRegistration.Page15.Title";
    public const string SmoRegistration15VerificationCertification = "FilerPortal.SmoRegistration.Page15.VerificationCertification";
    public const string SmoRegistration15OfficerSelectRequirement = "FilerPortal.SmoRegistration.Page15.OfficerSelectRequirement";
    public const string SmoRegistration15Attest = "FilerPortal.SmoRegistration.Page15.Attest";
    public const string SmoRegistration15VerificationName = "FilerPortal.Filing.Verification.Name";
    public const string SmoRegistration15VerificationTitleRow = "FilerPortal.Filing.Verification.TitleRow";
    public const string SmoRegistration15VerificationExecutedOn = "FilerPortal.Filing.Verification.ExecutedOn";
    public const string SmoRegistration15OfficerDropdownHeader = "FilerPortal.SmoRegistration.Page15.OfficerDropdownHeader";
    public const string SmoRegistration15OfficerDropdown = "FilerPortal.SmoRegistration.Page15.OfficerDropdown";
    public const string SmoRegistration15SendForAttestation = "FilerPortal.SmoRegistration.Page15.SendForAttestation";
    public const string SmoRegistration16SubmissionReceived = "FilerPortal.SmoRegistration.SubmissionReceived";
    public const string SmoRegistration16SubmissionBody = "FilerPortal.SmoRegistration.SubmissionBody";
    public const string SmoRegistration16PendingItems = "FilerPortal.SmoRegistration.PendingItems";

    public const string SmoRegistrationSummaryHeader = "FilerPortal.SmoRegistration.Summary.Header";
    public const string SmoRegistrationSummaryFilingHeader = "FilerPortal.SmoRegistration.Summary.FilingHeader";
    public const string SmoRegistrationSummaryRegistrationInfoHeader = "FilerPortal.SmoRegistration.Summary.RegistrationInfo.Header";
    public const string SmoRegistrationSummaryRegistrationInfoBody = "FilerPortal.SmoRegistration.Summary.RegistrationInfo.Body";
    public const string SmoRegistrationSummaryRegistrationInfoSmoName = "FilerPortal.SmoRegistration.Summary.RegistrationInfo.SmoName";
    public const string SmoRegistrationSummaryRegistrationInfoHeaderOrgAddress = "FilerPortal.SmoRegistration.Summary.RegistrationInfo.HeaderOrganizationAddress";
    public const string SmoRegistrationSummaryRegistrationInfoHeaderMailAddress = "FilerPortal.SmoRegistration.Summary.RegistrationInfo.HeaderMailingAddress";
    public const string SmoRegistrationSummaryRegistrationInfoHeaderOrgDetails = "FilerPortal.SmoRegistration.Summary.RegistrationInfo.HeaderOrganizationDetails";
    public const string SmoRegistrationSummaryRegistrationInfoLevelOfActivity = "FilerPortal.SmoRegistration.Summary.RegistrationInfo.LevelOfActivity";
    public const string SmoRegistrationSummaryRegistrationInfoIsQualified = "FilerPortal.SmoRegistration.Summary.RegistrationInfo.IsQualified";
    public const string SmoRegistrationSummaryRegistrationInfoDateQualified = "FilerPortal.SmoRegistration.Summary.RegistrationInfo.DateQualified";
    public const string SmoRegistrationSummaryRegistrationInfoIsCampaignCommittee = "FilerPortal.SmoRegistration.Summary.RegistrationInfo.IsCampaignCommittee";
    public const string SmoRegistrationSummaryRegistrationInfoCommitteeIdentifier = "FilerPortal.SmoRegistration.Summary.RegistrationInfo.CommitteeIdentifier";
    public const string SmoRegistrationSummaryOfficersHeader = "FilerPortal.SmoRegistration.Summary.Officers.Header";
    public const string SmoRegistrationSummaryIndividualAuthorizersHeader = "FilerPortal.SmoRegistration.Summary.IndividualAuthorizers.Header";
    public const string SmoRegistrationSummaryVerificaitonHeader = "FilerPortal.SmoRegistration.Summary.Verificaiton.Header";
    public const string SmoRegistrationSummaryVerificaitonPendingItems = "FilerPortal.SmoRegistration.Summary.Verificaiton.PendingItems";
    public const string SmoRegistrationSummaryVerificaitonTreasurerAcknowledgement = "FilerPortal.SmoRegistration.Summary.Verificaiton.TreasurerAcknowledgement";
    public const string SmoRegistrationSummaryVerificaitonTreasurerAcknowledgementCheckbox = "FilerPortal.SmoRegistration.Summary.Verificaiton.TreasurerAcknowledgementCheckbox";
    public const string SmoRegistrationSummaryVerificaitonVerify = "FilerPortal.SmoRegistration.Summary.Verificaiton.Verify";
    public const string SmoRegistrationSummaryVerificaitonVerifyCheckbox = "FilerPortal.SmoRegistration.Summary.Verificaiton.VerifyCheckbox";
    public const string SmoRegistrationSummaryVerificaitonHelperText = "FilerPortal.SmoRegistration.Summary.Verificaiton.HelperText";
    public const string SmoRegistrationSummaryEditModelTitle = "FilerPortal.SmoRegistration.Summary.EditModel.Title";
    public const string SmoRegistrationSummaryEditModelBody = "FilerPortal.SmoRegistration.Summary.EditModel.Body";

    //FilerPortal.AmendSmoRegistration Constants
    public const string TerminateRegistrationDisplayText = "FilerPortal.Registration.TerminateRegistrationDisplayText";
    public const string AmendSmoRegistrationPage03Title = "FilerPortal.AmendSmoRegistration.Page03.Title";
    public const string AmendSmoRegistrationPage03Body = "FilerPortal.AmendSmoRegistration.Page03.Body";
    public const string AmendSmoRegistrationPage03AreYouAmending = "FilerPortal.AmendSmoRegistration.Page03.AreYouAmending";
    public const string AmendSmoRegistrationPage03Option1 = "FilerPortal.AmendSmoRegistration.Page03.Option1";
    public const string AmendSmoRegistrationPage03Option2 = "FilerPortal.AmendSmoRegistration.Page03.Option2";
    public const string AmendSmoRegistrationPage03Option3 = "FilerPortal.AmendSmoRegistration.Page03.Option3";
    public const string AmendSmoRegistrationPage03Option4 = "FilerPortal.AmendSmoRegistration.Page03.Option4";
    public const string AmendSmoRegistrationPage05Title = "FilerPortal.AmendSmoRegistration.Page05.Title";
    public const string AmendSmoRegistrationPage05Body = "FilerPortal.AmendSmoRegistration.Page05.Body";
    public const string AmendSmoRegistrationPage06MakeTreasurer = "FilerPortal.AmendSmoRegistration.Page06.MakeTreasurer";
    public const string AmendSmoRegistrationPage06MakeOfficerTreasurer = "FilerPortal.AmendSmoRegistration.Page06.MakeOfficerTreasurer";
    public const string AmendSmoRegistrationPage06RemoveCurrentTreasurer = "FilerPortal.AmendSmoRegistration.Page06.RemoveCurrentTreasurer";
    public const string AmendSmoRegistrationPage06ReplaceCurrentTreasurer = "FilerPortal.AmendSmoRegistration.Page06.ReplaceCurrentTreasurer";
    public const string AmendSmoRegistrationPage06Cancel = "FilerPortal.AmendSmoRegistration.Page06.Cancel";
    public const string RegistrationTerminateRegistration = "FilerPortal.Registration.TerminateRegistration";
    public const string RegistrationYesTerminate = "FilerPortal.Registration.YesTerminate";
    public const string RegistrationNoContinueAmendment = "FilerPortal.Registration.NoContinueAmendment";
    public const string RegistrationNoticeOfTerminationTitle = "FilerPortal.Registration.NoticeOfTermination.Title";
    public const string RegistrationNoticeOfTerminationBody = "FilerPortal.Registration.NoticeOfTermination.Body";
    public const string RegistrationNoticeOfTerminationEffectiveDate = "FilerPortal.Registration.NoticeOfTermination.EffectiveDate";
    public const string RegistrationAmendmentVerificationTitle = "FilerPortal.Registration.AmendmentVerification.Title";
    public const string RegistrationTerminationVerificationTitle = "FilerPortal.Registration.TerminationVerification.Title";
    public const string RegistrationTerminationVerificationCertification = "FilerPortal.Registration.TerminationVerification.Certification";

    //FilerPortal.LobbyistRegistration Constants

    public const string LobbyistRegistrationTitle = "FilerPortal.LobbyistRegistration.Common.Title";
    public const string LobbyistRegistrationRenewTitle = "FilerPortal.LobbyistRegistration.Common.Title.Renew";
    public const string LobbyistRegistrationGeneralInformation = "FilerPortal.LobbyistRegistration.Common.GeneralInformation";
    public const string LobbyistRegistrationPayFees = "FilerPortal.LobbyistRegistration.Common.PayFees";
    public const string LobbyistRegistrationSubmit = "FilerPortal.LobbyistRegistration.Common.Submit";
    public const string LobbyistRegistration01Body = "FilerPortal.LobbyistRegistration.Page01.Body";
    public const string LobbyistRegistration02Title = "FilerPortal.LobbyistRegistration.Page02.Title";
    public const string LobbyistRegistration02Body = "FilerPortal.LobbyistRegistration.Page02.Body";
    public const string LobbyistRegistration02Lobbyist = "FilerPortal.LobbyistRegistration.Page02.Lobbyist";
    public const string LobbyistRegistration02Question = "FilerPortal.LobbyistRegistration.Page02.Question";
    public const string LobbyistRegistration03Title = "FilerPortal.LobbyistRegistration.Page03.Title";
    public const string LobbyistRegistration03Body = "FilerPortal.LobbyistRegistration.Page03.Body";
    public const string LobbyistRegistration03EffectiveDateOfChanges = "FilerPortal.LobbyistRegistration.Page03.EffectiveDateOfChanges";
    public const string LobbyistRegistration03FirstName = "FilerPortal.LobbyistRegistration.Page03.FirstName";
    public const string LobbyistRegistration03MiddleName = "FilerPortal.LobbyistRegistration.Page03.MiddleName";
    public const string LobbyistRegistration03LastName = "FilerPortal.LobbyistRegistration.Page03.LastName";
    public const string LobbyistRegistration03Email = "FilerPortal.LobbyistRegistration.Page03.Email";
    public const string LobbyistRegistration03PhoneNumber = "FilerPortal.LobbyistRegistration.Page03.PhoneNumber";
    public const string LobbyistRegistration03FaxNumber = "FilerPortal.LobbyistRegistration.Page03.FaxNumber";
    public const string LobbyistRegistration03LobbyistEmployerOrLobbyingFirm = "FilerPortal.LobbyistRegistration.Page03.LobbyistEmployerOrLobbyingFirm";
    public const string LobbyistRegistration03BusinessAddress = "FilerPortal.LobbyistRegistration.Page03.BusinessAddress";
    public const string LobbyistRegistration03MailingAddress = "FilerPortal.LobbyistRegistration.Page03.MailingAddress";
    public const string LobbyistRegistration03Country = "FilerPortal.LobbyistRegistration.Page03.Country";
    public const string LobbyistRegistration03Street = "FilerPortal.LobbyistRegistration.Page03.Street";
    public const string LobbyistRegistration03Street2 = "FilerPortal.LobbyistRegistration.Page03.Street2";
    public const string LobbyistRegistration03City = "FilerPortal.LobbyistRegistration.Page03.City";
    public const string LobbyistRegistration03State = "FilerPortal.LobbyistRegistration.Page03.State";
    public const string LobbyistRegistration03Zip = "FilerPortal.LobbyistRegistration.Page03.Zip";
    public const string LobbyistRegistration03SameAs = "FilerPortal.LobbyistRegistration.Page03.SameAs";
    public const string LobbyistRegistration03LegislativeSession = "FilerPortal.LobbyistRegistration.Page03.LegislativeSession";
    public const string LobbyistRegistration03QualificationDate = "FilerPortal.LobbyistRegistration.Page03.QualificationDate";
    public const string LobbyistRegistration03PlacementAgent = "FilerPortal.LobbyistRegistration.Page03.PlacementAgent";
    public const string LobbyistRegistration03EthicsCourse = "FilerPortal.LobbyistRegistration.Page03.EthicsCourse";
    public const string LobbyistRegistration03EthicsNotTaken = "FilerPortal.LobbyistRegistration.Page03.EthicsNotTaken";
    public const string LobbyistRegistration03EthicsNew = "FilerPortal.LobbyistRegistration.Page03.EthicsNew";
    public const string LobbyistRegistration03EthicsRenewal = "FilerPortal.LobbyistRegistration.Page03.EthicsRenewal";
    public const string LobbyistRegistration03EthicsCompleted = "FilerPortal.LobbyistRegistration.Page03.EthicsCompleted";
    public const string LobbyistRegistration03AgenciesLobbied = "FilerPortal.LobbyistRegistration.Page03.AgenciesLobbied";
    public const string LobbyistRegistration03ChooseOption = "FilerPortal.LobbyistRegistration.Page03.ChooseOption";
    public const string LobbyistRegistration03AgenciesLobbied1 = "FilerPortal.LobbyistRegistration.Page03.AgenciesLobbied1";
    public const string LobbyistRegistration03AgenciesLobbied2 = "FilerPortal.LobbyistRegistration.Page03.AgenciesLobbied2";
    public const string LobbyistRegistration03StateAgenciesLobbied = "FilerPortal.LobbyistRegistration.Page03.StateAgenciesLobbied";
    public const string LobbyistRegistration03FullList = "FilerPortal.LobbyistRegistration.Page03.FullList";
    public const string LobbyistRegistration03WillYouLobby = "FilerPortal.LobbyistRegistration.Page03.WillYouLobby";
    public const string LobbyistRegistration03Upload = "FilerPortal.LobbyistRegistration.Page03.Upload";
    public const string LobbyistRegistration03UploadRules = "FilerPortal.LobbyistRegistration.Page03.UploadRules";
    public const string LobbyistRegistration04Title = "FilerPortal.LobbyistRegistration.Page04.Title";
    public const string LobbyistRegistration04Body = "FilerPortal.LobbyistRegistration.Page04.Body";
    public const string LobbyistRegistration05Title = "FilerPortal.LobbyistRegistration.Page05.Title";
    public const string LobbyistRegistration05Body = "FilerPortal.LobbyistRegistration.Page05.Body";
    public const string LobbyistRegistration06Title = "FilerPortal.LobbyistRegistration.Page06.Title";
    public const string LobbyistRegistration06Body = "FilerPortal.LobbyistRegistration.Page06.Body";
    public const string LobbyistRegistration07Title = "FilerPortal.LobbyistRegistration.Page07.Title";
    public const string LobbyistRegistration07Body = "FilerPortal.LobbyistRegistration.Page07.Body";
    public const string LobbyistRegistration07Subtitle1 = "FilerPortal.LobbyistRegistration.Page07.Subtitle1";
    public const string LobbyistRegistration07Verification = "FilerPortal.LobbyistRegistration.Page07.Verification";
    public const string LobbyistRegistration07Name = "FilerPortal.LobbyistRegistration.Page07.Name";
    public const string LobbyistRegistration07ExecutedOn = "FilerPortal.LobbyistRegistration.Page07.ExecutedOn";
    public const string LobbyistRegistration08SubmissionReceived = "FilerPortal.LobbyistRegistration.Page08.SubmissionReceived";
    public const string LobbyistRegistration08Body = "FilerPortal.LobbyistRegistration.Page08.Body";
    public const string LobbyistRegistration08PendingItems = "FilerPortal.LobbyistRegistration.Page08.PendingItems";
    public const string LobbyistSentForAttestationSuccess = "FilerPortal.LobbyistRegistration.SendForAttestation.Success";

    public const string LobbyistRegistrationTerminationStep01Title = "FilerPortal.TerminationLobbyistRegistration.NoticeOfTermination";
    public const string LobbyistRegistrationTerminationStep02Title = "FilerPortal.TerminationLobbyistRegistration.Verification";
    public const string LobbyistRegistrationTerminationVerificationTitle = "FilerPortal.LobbyistRegistration.TerminationVerification.Title";
    public const string LobbyistRegistrationTerminationVerificationBody01 = "FilerPortal.LobbyistRegistration.TerminationVerification.Body01";
    public const string LobbyistRegistrationTerminationVerificationBody02 = "FilerPortal.LobbyistRegistration.TerminationVerification.Body02";
    public const string LobbyistRegistrationTerminationVerificationBody03 = "FilerPortal.LobbyistRegistration.TerminationVerification.Body03";
    public const string LobbyistRegistrationWithdrawalVerificationBody01 = "FilerPortal.LobbyistRegistration.WithdrawalVerification.Body01";
    public const string LobbyistRegistrationTerminationVerificationName = "FilerPortal.LobbyistRegistration.TerminationVerification.Name";
    public const string LobbyistRegistrationTerminationVerificationExecutedOn = "FilerPortal.LobbyistRegistration.TerminationVerification.ExecutedOn";
    public const string LobbyistRegistrationTerminationVerificationEmail = "FilerPortal.LobbyistRegistration.TerminationVerification.LobbyistEmail";
    public const string LobbyistRegistrationTerminationVerificationDescription = "FilerPortal.LobbyistRegistration.TerminationVerification.UIText";
    public const string LobbyistRegistrationTerminationConfirmationTitle = "FilerPortal.LobbyistRegistration.Termination.Confirmation.Title";
    public const string LobbyistRegistrationWithdrawConfirmationTitle = "FilerPortal.LobbyistRegistration.Withdraw.Confirmation.Title";
    public const string LobbyistRegistrationAmendConfirmationTitle = "FilerPortal.LobbyistRegistration.Amend.Confirmation.Title";
    public const string LobbyistRegistrationRenewalConfirmationTitle = "FilerPortal.LobbyistRegistration.Renewal.Confirmation.Title";
    public const string LobbyistRegistrationConfirmationBody = "FilerPortal.LobbyistRegistration.Confirmation.Body";

    //FilerPortal.WithdrawLobbyistRegistration
    public const string WithdrawLobbyistRegistrationStep01Title = "FilerPortal.WithdrawLobbyistRegistration.NoticeOfWithdrawal";
    public const string WithdrawLobbyistRegistrationStep02Title = "FilerPortal.WithdrawLobbyistRegistration.Verification";
    public const string WithdrawLobbyistRegistrationEffectiveDate = "FilerPortal.WithdrawLobbyistRegistration.EffectiveDateOfWithdrawal";
    public const string WithdrawLobbyistRegistrationFirmOrEmployerName = "FilerPortal.WithdrawLobbyistRegistration.FirmOrEmployerName";
    public const string WithdrawLobbyistRegistrationLobbyistName = "FilerPortal.WithdrawLobbyistRegistration.LobbyistName";

    //FilerPortal.LobbyistEmployerRegistration Constants
    public const string LobbyistEmployerRegistrationTitle = "FilerPortal.LobbyistEmployerRegistration.Common.Title";
    public const string LobbyistEmployerRegistrationTitle2 = "FilerPortal.LobbyistEmployerRegistration.Common.Title2";
    public const string LobbyistEmployerRegistrationGeneralInformation = "FilerPortal.LobbyistEmployerRegistration.Common.GeneralInformation";
    public const string LobbyistEmployerRegistrationInHouseLobbyists = "FilerPortal.LobbyistEmployerRegistration.Common.InHouseLobbyists";
    public const string LobbyistEmployerRegistrationLobbyingFirms = "FilerPortal.LobbyistEmployerRegistration.Common.LobbyingFirms";
    public const string LobbyistEmployerRegistrationResponsibleOfficers = "FilerPortal.LobbyistEmployerRegistration.Common.ResponsibleOfficers";
    public const string LobbyistEmployerRegistrationSubmit = "FilerPortal.LobbyistEmployerRegistration.Common.Submit";
    public const string LobbyistEmployerRegistration01Body = "FilerPortal.LobbyistEmployerRegistration.Page01.Body";
    public const string LobbyistEmployerRegistration02Title = "FilerPortal.LobbyistEmployerRegistration.Page02.Title";
    public const string LobbyistEmployerRegistration02Body = "FilerPortal.LobbyistEmployerRegistration.Page02.Body";
    public const string LobbyistEmployerRegistration03Title = "FilerPortal.LobbyistEmployerRegistration.Page03.Title";
    public const string LobbyistEmployerRegistration03Body = "FilerPortal.LobbyistEmployerRegistration.Page03.Body";

    public const string LobbyistEmployerRegistration03Name = "FilerPortal.LobbyistEmployerRegistration.Page03.Name";
    public const string LobbyistEmployerRegistration03Email = "Common.EmailAddress";
    public const string LobbyistEmployerRegistration03PhoneNumber = "Common.PhoneNumber";
    public const string LobbyistEmployerRegistration03FaxNumber = "Common.FaxNumber";
    public const string LobbyistEmployerRegistration03BusinessAddress = "Common.BusinessAddress";
    public const string LobbyistEmployerRegistration03Country = "Common.Country";
    public const string LobbyistEmployerRegistration03Street = "Common.Street";
    public const string LobbyistEmployerRegistration03Street2 = "Common.Street2";
    public const string LobbyistEmployerRegistration03City = "Common.City";
    public const string LobbyistEmployerRegistration03State = "Common.State";
    public const string LobbyistEmployerRegistration03Zip = "Common.ZipCode";
    public const string LobbyistEmployerRegistration03MailingAddress = "Common.MailingAddress";
    public const string LobbyistEmployerRegistration03SameAs = "FilerPortal.LobbyistEmployerRegistration.Page03.SameAs";
    public const string LobbyistEmployerRegistration03LegislativeSession = "FilerPortal.LobbyistEmployerRegistration.Page03.LegislativeSession";
    public const string LobbyistEmployerRegistration03QualificationDate = "FilerPortal.LobbyistEmployerRegistration.Page03.QualificationDate";
    public const string LobbyistEmployerRegistrationPage05Title = "FilerPortal.LobbyistEmployerRegistration.Page05.Title";
    public const string LobbyistEmployerRegistrationPage05Body = "FilerPortal.LobbyistEmployerRegistration.Page05.Body";
    public const string LobbyistEmployerRegistrationPage05Certification = "FilerPortal.LobbyistEmployerRegistration.Page05.Certification";
    public const string LobbyistEmployerRegistrationPage05Add = "FilerPortal.LobbyistEmployerRegistration.Page05.Add";
    public const string LobbyistEmployerRegistrationStep02LobbyistListTitle = "FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.Title";
    public const string LobbyistEmployerRegistrationStep02LobbyistListName = "FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.Name";
    public const string LobbyistEmployerRegistrationStep02LobbyistListCertified = "FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.Certified";
    public const string LobbyistEmployerRegistrationStep02LobbyistListNotCertified = "FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.NotCertified";
    public const string LobbyistEmployerRegistrationStep02LobbyistListId = "FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.Id";
    public const string LobbyistEmployerRegistrationStep02LobbyistListDelete = "FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.Delete";
    public const string LobbyistEmployerRegistration03IsLobbyingCoalition = "FilerPortal.LobbyistEmployerRegistration.Page03.IsLobbyingCoalition";

    public const string LobbyistEmployerRegistration04Title = "FilerPortal.LobbyistEmployerRegistration.Page04.Title";
    public const string LobbyistEmployerRegistration04Subtitle = "FilerPortal.LobbyistEmployerRegistration.Page04.Subtitle";
    public const string LobbyistEmployerRegistration04Body = "FilerPortal.LobbyistEmployerRegistration.Page04.Body";
    public const string LobbyistEmployerRegistration04StateAgenciesLobbied = "FilerPortal.LobbyistRegistration.Page04.StateAgenciesLobbied";
    public const string LobbyistEmployerRegistration04FullList = "FilerPortal.LobbyistRegistration.Page04.FullList";
    public const string LobbyistEmployerRegistration04StateLegislatureLobbied = "FilerPortal.LobbyistRegistration.Page04.StateLegislatureLobbied";

    public const string LobbyistEmployerRegistration05Title = "FilerPortal.LobbyistEmployerRegistration.Page05.Title";
    public const string LobbyistEmployerRegistration05Subtitle = "FilerPortal.LobbyistEmployerRegistration.Page05.Subtitle";
    public const string LobbyistEmployerRegistration05Example1 = "FilerPortal.LobbyistEmployerRegistration.Page05.Example1";
    public const string LobbyistEmployerRegistration05Example1Body = "FilerPortal.LobbyistEmployerRegistration.Page05.Example1Body";
    public const string LobbyistEmployerRegistration05Example2 = "FilerPortal.LobbyistEmployerRegistration.Page05.Example2";
    public const string LobbyistEmployerRegistration05Example2Body = "FilerPortal.LobbyistEmployerRegistration.Page05.Example2Body";
    public const string LobbyistEmployerRegistration05DescribeInterests = "FilerPortal.LobbyistEmployerRegistration.Page05.DescribeInterests";
    public const string LobbyistEmployerRegistrationInitiateCertifyLobbyistTitle = "FilerPortal.LobbyistEmployerRegistration.InitiateCertifyLobbyist.Title";
    public const string LobbyistEmployerRegistrationInitiateCertifyLobbyistSummaryText = "FilerPortal.LobbyistEmployerRegistration.InitiateCertifyLobbyist.SummaryText";

    public const string LobbyistEmployerRegistrationStep03LobbyingFirmsTitle = "FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Title";
    public const string LobbyistEmployerRegistrationStep03LobbyingFirmsDescription = "FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Description";
    public const string LobbyistEmployerRegistrationStep03LobbyingFirmsAdd = "FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Add";
    public const string LobbyistEmployerRegistrationStep03LobbyingFirmsTableID = "FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Table.Id";
    public const string LobbyistEmployerRegistrationStep03LobbyingFirmsTableName = "FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Table.Name";
    public const string LobbyistEmployerRegistrationStep03LobbyingFirmsTableEmail = "FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Table.Email";

    // FilerPortal.TerminateLobbyistRegistration
    public const string LobbyistRegistrationName = "FilerPortal.LobbyistRegistration.LobbyistName";
    public const string TerminateLobbistRegistrationTerminatedAt = "FilerPortal.TerminateLobbyistRegistration.TerminatedAt";
    public const string TerminateLobbistRegistrationRequiredTerminatedAt = "FilerPortal.TerminateLobbyistRegistration.RequiredTerminatedAt";
    public const string TerminateLobbistRegistrationSendForAttestationSuccessfully = "FilerPortal.TerminateLobbyistRegistration.SendForAttestation.Successfully";

    public const string LobbyistEmployerRegistrationDetailTitle = "FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.Detail";
    public const string LobbyistEmployerRegistrationCreationBody = "FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.Body";
    public const string LobbyistEmployerRegistrationCreationFirstName = "FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.FirstName";
    public const string LobbyistEmployerRegistrationCreationLastName = "FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.LastName";
    public const string LobbyistEmployerRegistrationCreationMiddleName = "FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.MiddleName";
    public const string LobbyistEmployerRegistrationCreationEmail = "FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.Email";

    public const string LobbyistEmployerRegistrationPage03Title = "FilerPortal.Disclosure.GeneralInfo.Lobbyist.FilerDetails";
    public const string LobbyistEmployerRegistrationPage03Body = "FilerPortal.LobbyistEmployerRegistration.Page03.Body";
    public const string LobbyistEmployerRegistrationPage03Search = "FilerPortal.LobbyistEmployerRegistration.Page03.Search";
    public const string LobbyistEmployerRegistrationPage03EnterInformation = "FilerPortal.LobbyistEmployerRegistration.Page03.EnterInformation";
    public const string LobbyistEmployerRegistrationPage03Add = "FilerPortal.LobbyistEmployerRegistration.Page03.Add";
    public const string LobbyistEmployerRegistrationPage04Title = "FilerPortal.LobbyistEmployerRegistration.Page04.Title";
    public const string LobbyistEmployerRegistrationPage04Body = "FilerPortal.LobbyistEmployerRegistration.Page04.Body";
    public const string LobbyistEmployerRegistrationPage04FirstName = "FilerPortal.LobbyistEmployerRegistration.Page04.FirstName";
    public const string LobbyistEmployerRegistrationPage04MiddleName = "FilerPortal.LobbyistEmployerRegistration.Page04.MiddleName";
    public const string LobbyistEmployerRegistrationPage04LastName = "FilerPortal.LobbyistEmployerRegistration.Page04.LastName";
    public const string LobbyistEmployerRegistrationPage04Email = "FilerPortal.LobbyistEmployerRegistration.Page04.Email";

    // FilerPortal.Disclosure.Index Constants
    public const string DisclosureIndexTitle = "FilerPortal.Disclosure.Index.Title";

    //FilerPortal.Disclosure.Dashboard Constants
    public const string UpdateFailMessage = "FilerPortal.Disclosure.Dashboard.UpdateFailMessage";
    public const string CreateTransactionSuccessMessage = "FilerPortal.Disclosure.Dashboard.CreateTransactionSuccessMessage";
    public const string UpdateTransactionSuccessMessag = "FilerPortal.Disclosure.Dashboard.UpdateTransactionSuccessMessage";
    public const string CancelTransactionSuccessMessage = "FilerPortal.Disclosure.Dashboard.CancelTransactionSuccessMessage";
    public const string DisclosureSummaryBase = "FilerPortal.Disclosure.Dashboard.Summary";
    public const string CampaignContributionSummary = "FilerPortal.Disclosure.Dashboard.Summary.CampaignContributionSummary";
    public const string ActivityExpenseSummary = "FilerPortal.Disclosure.Dashboard.Summary.ActivityExpenseSummary";
    public const string ToLobbyingCoalitionSummary = "FilerPortal.Disclosure.Dashboard.Summary.ToLobbyingCoalitionSummary";
    public const string RecieveLobbyingCoalitionSummary = "FilerPortal.Disclosure.Dashboard.Summary.RecieveLobbyingCoalitionSummary";
    public const string MadeToLobbyingFirmsSummary = "FilerPortal.Disclosure.Dashboard.Summary.MadeToLobbyingFirmsSummary";
    public const string LobbyistReportsSummary = "FilerPortal.Disclosure.Dashboard.Summary.LobbyistReportsSummary";
    public const string OtherPaymentsToInfluenceSummary = "FilerPortal.Disclosure.Dashboard.Summary.OtherPaymentsToInfluenceSummary";
    public const string PaymentsToInHouseLobbyists = "FilerPortal.Disclosure.Dashboard.Summary.PaymentsToInHouseLobbyists";
    public const string AdHocFilingSummary = "FilerPortal.Disclosure.Dashboard.Summary.AdHocFilingSummary";
    public const string PucActivitySummary = "FilerPortal.Disclosure.Dashboard.Summary.PucActivitySummary";
    public const string DisclosureTemporaryDashboardTitle = "FilerPortal.Disclosure.TemporaryDashboard.Title";
    public const string DisclosureTemporaryDashboardReportDocumentId = "FilerPortal.Disclosure.TemporaryDashboard.DocumentId";
    public const string DisclosureTemporaryDashboardReportName = "FilerPortal.Disclosure.TemporaryDashboard.ReportName";
    public const string DisclosureTemporaryDashboardReportCreatedDate = "FilerPortal.Disclosure.TemporaryDashboard.CreatedDate";
    public const string DisclosureTemporaryDashboardReportPeriod = "FilerPortal.Disclosure.TemporaryDashboard.ReportPeriod";
    public const string DisclosureTemporaryDashboardReportStatus = "FilerPortal.Disclosure.TemporaryDashboard.Status";
    public const string SendForAttestationFailMessage = "FilerPortal.Disclosure.Dashboard.SendForAttestationFailMessage";
    public const string SendForAttestationSuccessMessage = "FilerPortal.Disclosure.Dashboard.SendForAttestationSuccessMessage";

    // FilerPortal.Disclosure.FilingSummary Constants
    public const string FilingSummaryTitle = "FilerPortal.Disclosure.FilingSummary.Title";
    public const string FilingSummaryBody02 = "FilerPortal.Disclosure.FilingSummary.Body02";
    public const string FilingSummaryPreviewPDF = "FilerPortal.Disclosure.FilingSummary.PreviewPDF";
    public const string FilingSummaryOverall = "FilerPortal.Disclosure.FilingSummary.Overall";
    public const string FilingSummaryThisReportingPeriod = "FilerPortal.Disclosure.FilingSummary.ThisReportingPeriod";
    public const string FilingSummaryCumulativeToDate = "FilerPortal.Disclosure.FilingSummary.CumulativeToDate";
    public const string FilingSummarySummaryGrandTotal = "FilerPortal.Disclosure.FilingSummary.SummaryGrandTotal";
    public const string FilingSummaryPaymentsPUCActivities = "FilerPortal.Disclosure.FilingSummary.PaymentsPUCActivities";
    public const string FilingSummaryTotals = "FilerPortal.Disclosure.FilingSummary.Totals";
    public const string FilingSummaryPaymentsToLobbyingFirms = "FilerPortal.Disclosure.FilingSummary.PaymentsToLobbyingFirms";
    public const string FilingSummaryTotalsAllFirms = "FilerPortal.Disclosure.FilingSummary.TotalsAllFirms";
    public const string FilingSummaryPaymentsToLobbyingCoalitions = "FilerPortal.Disclosure.FilingSummary.PaymentsToLobbyingCoalitions";
    public const string FilingSummaryTotalsAllLobbyingCoalitions = "FilerPortal.Disclosure.FilingSummary.TotalsAllLobbyingCoalitions";
    public const string FilingSummaryPaymentsReceivedByCoalitionMember = "FilerPortal.Disclosure.FilingSummary.PaymentsReceivedByCoalitionMember";
    public const string FilingSummaryTotalsAllCoalitionMembers = "FilerPortal.Disclosure.FilingSummary.TotalsAllCoalitionMembers";

    //FilerPortal.Disclosure.CampaignContributions Contants
    public const string CampaignContributionsTitle = "FilerPortal.Disclosure.CampaignContributions.Title";
    public const string CampaignContributionsBodyLobbyist = "FilerPortal.Disclosure.CampaignContributions.BodyLobbyist";
    public const string CampaignContributionsBodyLobbyistEmployer = "FilerPortal.Disclosure.CampaignContributions.BodyLobbyistEmployer";

    //FilerPortal.Disclosure.PUCActivity Contants
    public const string PUCActivityTitle = "FilerPortal.Disclosure.PUCActivity.Title";
    public const string PUCActivityBody = "FilerPortal.Disclosure.PUCActivity.Body";
    public const string PUCActivityTotalPaymentsLabel = "FilerPortal.Disclosure.PUCActivity.TotalPaymentsLabel";

    //FilerPortal.Disclosure.CampaignContributions.NewTransaction Constants
    public const string CcNewIsRecipientCommittee = "FilerPortal.Disclosure.CampaignContributions.NewTransaction.IsRecipientCommittee";
    public const string CcNewNonCommitteeRecipentName = "FilerPortal.Disclosure.CampaignContributions.NewTransaction.NonCommitteeRecipentName";
    public const string CcNewRecipientCommitteeFilerId = "FilerPortal.Dislosure.CampaignContributions.NewTransaction.RecipientCommitteeFilerId";
    public const string CcNewTransactionDate = "FilerPortal.Disclosure.CampaignContributions.NewTransaction.TransactionDate";
    public const string CcNewTransactionAmount = "FilerPortal.Disclosure.CampaignContributions.NewTransaction.Amount";
    public const string CcNewTransactionNonFilerContributorName = "FilerPortal.Disclosure.CampaignContributions.NewTransaction.NonFilerContributorName";
    public const string CcNewTransactionSeparateAccountName = "FilerPortal.Disclosure.CampaignContributions.NewTransaction.SeparateAccountName";

    //FilerPortal.Disclosure.CoalitionPayments Constants
    public const string CoalitionPaymentsAmount = "FilerPortal.Disclosure.CoalitionPayments.Amount";

    //FilerPortal.Disclosure.ActivityExpenses Constants
    public const string ActivityExpensesPayee = "FilerPortal.Disclosure.ActivityExpenses.Payee";
    public const string ActivityExpensesPayeeType = "FilerPortal.Disclosure.ActivityExpenses.PayeeType";
    public const string ActivityExpensesAmountBenefiting = "FilerPortal.Disclosure.ActivityExpenses.AmountBenefiting";
    public const string ActivityExpensesPersonBenefiting = "FilerPortal.Disclosure.ActivityExpenses.PersonBenefiting";

    //FilerPortal.Disclosure.EndOfSessionLobbying Constants
    public const string EndOfSessionLobbyingTitle = "FilerPortal.Disclosure.EndOfSessionLobbying.Title";
    public const string EndOfSessionLobbyingBody = "FilerPortal.Disclosure.EndOfSessionLobbying.Body";
    public const string EndOfSessionLobbyingReviewInstruction = "FilerPortal.Disclosure.EndOfSessionLobbying.ReviewInstruction";
    public const string EndOfSessionLobbyingAddNew = "FilerPortal.Disclosure.EndOfSessionLobbying.AddNew";

    public const string LobbyingFirmId = "FilerPortal.Disclosure.EndOfSessionLobbying.Id";
    public const string LobbyingFirmName = "FilerPortal.Disclosure.EndOfSessionLobbying.LobbyingFirmName";
    public const string LobbyingFirmHiringDate = "FilerPortal.Disclosure.EndOfSessionLobbying.LobbyingFirmHiringDate";
    public const string AmountIncurred = "FilerPortal.Disclosure.EndOfSessionLobbying.AmountIncurred";

    //FilerPortal.Disclosure.SmoCampaignStatement Constants
    public const string SmoCampaignStatementTitle = "FilerPortal.Disclosure.SmoCampaignStatement.Title";
    public const string SmoCampaignStatementAmendTitle = "FilerPortal.Disclosure.SmoCampaignStatement.AmendTitle";
    public const string SmoCampaignStatementSubtitle = "FilerPortal.Disclosure.SmoCampaignStatement.Subtitle";
    public const string SmoCampaignStatementSelectionTitle = "FilerPortal.Disclosure.SmoCampaignStatement.Selection.Title";
    public const string SmoCampaignStatementSelectionSearchHeader = "FilerPortal.Disclosure.SmoCampaignStatement.Selection.SearchHeader";
    public const string SmoCampaignStatementSelectionSearchPlaceholder = "FilerPortal.Disclosure.SmoCampaignStatement.Selection.SearchPlaceholder";
    public const string SmoCampaignStatementSelectionSearchErrorMessage = "FilerPortal.Disclosure.SmoCampaignStatement.Selection.ErrorMessage";
    public const string SmoCampaignStatementReviewTitle = "FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.Title";
    public const string SmoCampaignStatementReviewBody = "FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.Body";
    public const string SmoCampaignStatementReviewFilingPeriod = "FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.FilingPeriod";
    public const string SmoCampaignStatementReviewGeneralInformation = "FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.GeneralInformation";
    public const string SmoCampaignStatementReviewFilingSummaryHeader = "FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.FilingSummaryHeader";
    public const string SmoCampaignStatementReviewFilingSummary = "FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.FilingSummary";
    public const string SmoCampaignStatementReviewPaymentsReceived = "FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.PaymentsReceived";
    public const string SmoCampaignStatementReviewPaymentsMade = "FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.PaymentsMade";
    public const string SmoCampaignStatementReviewPaymentsMadeByAgentOrContractor = "FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.PaymentsMadeByAgentOrContractor";
    public const string SmoCampaignStatementReviewPersonsReceiving1000OrMore = "FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.PersonsReceiving1000OrMore";
    public const string SmoCampaignStatementReviewPaymentsCandidatesMeasuresNotListed = "FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.CandidatesMeasuresNotListed";
    public const string SmoCampaignStatementGeneralInformationTitle = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Title";
    public const string SmoCampaignStatementGeneralInformationBody = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Body";
    public const string SmoCampaignStatementGeneralInformationSmo = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Smo";
    public const string SmoCampaignStatementGeneralInformationSmoName = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.SmoName";
    public const string SmoCampaignStatementGeneralInformationSmoId = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.SmoId";
    public const string SmoCampaignStatementGeneralInformationStreetAddress = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.StreetAddress";
    public const string SmoCampaignStatementGeneralInformationMailingAddress = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.MailingAddress";
    public const string SmoCampaignStatementGeneralInformationPhone = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Phone";
    public const string SmoCampaignStatementGeneralInformationEmail = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Email";
    public const string SmoCampaignStatementGeneralInformationFax = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Fax";
    public const string SmoCampaignStatementGeneralInformationTreasurer = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Treasurer";
    public const string SmoCampaignStatementGeneralInformationTreasurerName = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.TreasurerName";
    public const string SmoCampaignStatementGeneralInformationRecipientCommittee = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.RecipientCommittee";
    public const string SmoCampaignStatementGeneralInformationCommitteeName = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.CommitteeName";
    public const string SmoCampaignStatementGeneralInformationCommitteeId = "FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.CommitteeId";
    public const string SmoCampaignStatementFilingSummaryTitle = "FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.Title";
    public const string SmoCampaignStatementFilingSummaryBody = "FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.Body";
    public const string SmoCampaignStatementFilingSummaryThisStatement = "FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.ThisStatement";
    public const string SmoCampaignStatementFilingSummaryTotalYTD = "FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.TotalYTD";
    public const string SmoCampaignStatementFilingSummaryTotalPaymentsReceived = "FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.TotalPaymentsReceived";
    public const string SmoCampaignStatementFilingSummaryTotalPaymentsMade = "FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.TotalPaymentsMade";
    public const string SmoCampaignStatementPaymentsReceivedTitle = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsReceivedTitle";
    public const string SmoCampaignStatementPaymentsMadeTitle = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsMadeTitle";
    public const string SmoCampaignStatementPaymentsMadeByAgentOrIndependentContractorTitle = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsMadeByAgentOrIndependentContractorTitle";
    public const string SmoCampaignStatementPersonsReceiving1000OrMoreTitle = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PersonsReceiving1000OrMore";
    public const string SmoCampaignStatementCandidatesAndMeasuresNotListedTitle = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.CandidatesAndMeasuresNotListedTitle";
    public const string SmoCampaignStatementCandidatesAndMeasuresNotListedBody = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.CandidatesAndMeasuresNotListedBody";
    public const string SmoCampaignStatementTransactionSummaryBody = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.Body";
    public const string SmoCampaignStatementTransactionSummaryReviewInstructions = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.ReviewInstructions";
    public const string SmoCampaignStatementTransactionSummaryPaymentsReceived100OrMoreGridTitle = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsReceived100OrMoreGridTitle";
    public const string SmoCampaignStatementTransactionSummaryPaymentsMade100OrMoreGridTitle = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsMade100OrMoreGridTitle";
    public const string SmoCampaignStatementTransactionSummaryPaymentsMadeByAgentOrIndependentContractorGridTitle = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsMadeByAgentOrIndependentContractorGridTitle";
    public const string SmoCampaignStatementTransactionSummaryPersonsReceiving1000OrMoreGridTitle = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PersonsReceiving1000OrMoreGridTitle";
    public const string SmoCampaignStatementTransactionSummaryCandidatesAndMeasuresNotListedGridTitle = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.CandidatesAndMeasuresNotListedGridTitle";
    public const string SmoCampaignStatementTransactionSummaryGridDateHeader = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridDateHeader";
    public const string SmoCampaignStatementTransactionSummaryGridNameHeader = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridNameHeader";
    public const string SmoCampaignStatementTransactionSummaryGridCandidateOrMeasureHeader = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridCandidateOrMeasureHeader";
    public const string SmoCampaignStatementTransactionSummaryGridSupportOpposeHeader = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridSupportOpposeHeader";
    public const string SmoCampaignStatementTransactionSummaryGridAmountHeader = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridAmountHeader";
    public const string SmoCampaignStatementTransactionSummaryGridAgentOrContractorHeader = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridAgentOrContractorHeader";
    public const string SmoCampaignStatementTransactionSummaryGridDescriptionHeader = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridDescriptionHeader";
    public const string SmoCampaignStatementTransactionSummaryGridCumulativeAmountHeader = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridCumulativeAmountHeader";
    public const string SmoCampaignStatementTransactionSummaryGridOfficeOrMeasureHeader = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridOfficeOrMeasureHeader";
    public const string SmoCampaignStatementTransactionSummaryGridJurisdictionHeader = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridJurisdictionHeader";
    public const string SmoCampaignStatementTransactionSummaryUnitemizedPaymentReceivedTitle = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.UnitemizedPaymentsReceivedTitle";
    public const string SmoCampaignStatementTransactionSummaryUnitemizedPaymentMadeTitle = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.UnitemizedPaymentsMadeTitle";
    public const string SmoCampaignStatementTransactionSummaryDeletePaymentConfirmation = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.DeletePaymentConfirmation";
    public const string SmoCampaignStatementTransactionSummarySubtotal = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.Subtotal";
    public const string SmoCampaignStatementTransactionSummaryUnitemizedPaymentsLessThan100 = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.UnitemizedPaymentsLessThan100";
    public const string SmoCampaignStatementTransactionSummaryUnitemizedPaymentsLessThan100Placeholder = "FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.UnitemizedPaymentsLessThan100Placeholder";
    public const string SmoCampaignStatementEnterTransaction = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.EnterTransaction";
    public const string SmoCampaignStatementPaymentReceived01Title = "FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived01.Title";
    public const string SmoCampaignStatementPaymentReceived01Body = "FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived01.Body";
    public const string SmoCampaignStatementTransactorsChooseContact = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.ChooseContact";
    public const string SmoCampaignStatementTransactorsEnterNameOrId = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.EnterNameOrId";
    public const string SmoCampaignStatementTransactorsAddNewContact = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AddNewContact";
    public const string SmoCampaignStatementPaymentReceived01EnterPayorInfo = "FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived01.EnterPayorInfo";
    public const string SmoCampaignStatementPaymentReceived02Title = "FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived02.Title";
    public const string SmoCampaignStatementPaymentReceived02Body = "FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived02.Body";
    public const string SmoCampaignStatementCandidatesMeasuresNotListedSubTitle = "FilerPortal.Disclosure.SmoCampaignStatement.CandidatesMeasuresNotListed.Subtitle";
    public const string SmoCampaignStatementCandidatesMeasuresNotListedDescription = "FilerPortal.Disclosure.SmoCampaignStatement.CandidatesMeasuresNotListed.Description";
    public const string SmoCampaignStatementCandidatesMeasuresNotListedTitle = "FilerPortal.Disclosure.SmoCampaignStatement.CandidatesMeasuresNotListed.Title";
    public const string SmoCampaignStatementPaymentMade01EnterPayeeInfo = "FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade01.EnterPayeeInfo";
    public const string SmoCampaignStatementPaymentMade01Title = "FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade01.Title";
    public const string SmoCampaignStatementPaymentMade01Body = "FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade01.Body";
    public const string SmoCampaignStatementPaymentMade02Title = "FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade02.Title";
    public const string SmoCampaignStatementPaymentMade02Body = "FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade02.Body";
    public const string SmoCampaignStatementPaymentMade03Checkbox = "FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade03.Checkbox";
    public const string SmoCampaignStatementPaymentMade03TextboxNameOf = "FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade03.TextboxNameOf";
    public const string SmoCampaignStatementTransactorsIndividual = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Individual";
    public const string SmoCampaignStatementTransactorsCommittee = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Committee";
    public const string SmoCampaignStatementTransactorsCandidate = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Candidate";
    public const string SmoCampaignStatementTransactorsOther = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Other";
    public const string SmoCampaignStatementTransactorsFirstName = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.FirstName";
    public const string SmoCampaignStatementTransactorsMiddleName = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.MiddleName";
    public const string SmoCampaignStatementTransactorsLastName = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.LastName";
    public const string SmoCampaignStatementTransactorsAddressOfPayor = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AddressOfPayor";
    public const string SmoCampaignStatementTransactorsEmployer = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Employer";
    public const string SmoCampaignStatementTransactorsEmployerDescription = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.EmployerDescription";
    public const string SmoCampaignStatementTransactorsSearchForCommittee = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.SearchForCommittee";
    public const string SmoCampaignStatementTransactorsSelectAnOption = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.SelectAnOption";
    public const string SmoCampaignStatementTransactorsEnterCommitteeNameOrId = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.EnterCommitteeNameOrId";
    public const string CandidateBallotMeasureCandidateFirstName = "FilerPortal.CandidateBallotMeasure.CandidateFirstName";
    public const string CandidateBallotMeasureCandidateMiddleName = "FilerPortal.CandidateBallotMeasure.CandidateMiddleName";
    public const string CandidateBallotMeasureCandidateLastName = "FilerPortal.CandidateBallotMeasure.CandidateLastName";
    public const string CandidateBallotMeasureOfficeSought = "FilerPortal.CandidateBallotMeasure.OfficeSought";
    public const string CandidateBallotMeasureJurisdictionName = "FilerPortal.CandidateBallotMeasure.JurisdictionName";
    public const string CandidateBallotMeasureDistrict = "FilerPortal.CandidateBallotMeasure.District";
    public const string SmoCampaignStatementTransactorsOrganizationName = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.OrganizationName";
    public const string SmoCampaignStatementTransactorsAboutThePaymentTitle = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.Title";
    public const string SmoCampaignStatementTransactorsAboutThePaymentBody = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.Body";
    public const string SmoCampaignStatementTransactorsAboutThePaymentPaymentPertain = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.PaymentPertain";
    public const string CandidateBallotMeasureCandidate = "FilerPortal.CandidateBallotMeasure.Candidate";
    public const string CandidateBallotMeasureBallotMeasure = "FilerPortal.CandidateBallotMeasure.BallotMeasure";
    public const string CandidateBallotMeasureJurisdiction = "FilerPortal.CandidateBallotMeasure.Jurisdiction";
    public const string CandidateBallotMeasureState = "FilerPortal.CandidateBallotMeasure.State";
    public const string CandidateBallotMeasureLocal = "FilerPortal.CandidateBallotMeasure.Local";
    public const string CandidateBallotMeasurePosition = "FilerPortal.CandidateBallotMeasure.Position";
    public const string CandidateBallotMeasureSupport = "FilerPortal.CandidateBallotMeasure.Support";
    public const string CandidateBallotMeasureOppose = "FilerPortal.CandidateBallotMeasure.Oppose";
    public const string SmoCampaignStatementTransactorsAboutThePaymentAmountReceived = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.AmountReceived";
    public const string SmoCampaignStatementTransactorsAboutThePaymentDateReceived = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.DateReceived";
    public const string SmoCampaignStatementTransactorsAboutThePaymentDatePaid = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.DatePaid";
    public const string SmoCampaignStatementTransactorsAboutThePaymentAttachFiles = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.AttachFiles";
    public const string SmoCampaignStatementTransactorsAboutThePaymentNotes = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.Notes";
    public const string SmoCampaignStatementTransactorsAboutThePaymentIsCumulativeAmount = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.IsCumulativeAmount";
    public const string SmoCampaignStatementTransactorsAboutThePaymentPreviouslyUnitemizedAmount = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.PreviouslyUnitemizedAmount";
    public const string SmoCampaignStatementTransactorsAboutThePaymentCumulativeAmountToDate = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.CumulativeAmountToDate";
    public const string CandidateBallotMeasureAddCandidate = "FilerPortal.CandidateBallotMeasure.AddCandidate";
    public const string CandidateBallotMeasureAddMeasure = "FilerPortal.CandidateBallotMeasure.AddMeasure";
    public const string CandidateBallotMeasureEnterIdOrBallotOrTitle = "FilerPortal.CandidateBallotMeasure.EnterIdOrBallotOrTitle";
    public const string CandidateBallotMeasureBallotLetter = "FilerPortal.CandidateBallotMeasure.BallotLetter";
    public const string CandidateBallotMeasureFullTitleBallotMeasure = "FilerPortal.CandidateBallotMeasure.FullTitleBallotMeasure";
    public const string CandidateBallotMeasureEnterIdOrName = "FilerPortal.CandidateBallotMeasure.EnterIdOrName";
    public const string SmoCampaignStatementVerificationTitle = "FilerPortal.Disclosure.SmoCampaignStatement.Verification.Title";
    public const string SmoCampaignStatementVerificationBody = "FilerPortal.Disclosure.SmoCampaignStatement.Verification.Body";
    public const string SmoCampaignStatementAmendmentExplanationTitle = "FilerPortal.Disclosure.SmoCampaignStatement.AmendmentExplanation.Title";
    public const string SmoCampaignStatementAmendmentExplanationBody = "FilerPortal.Disclosure.SmoCampaignStatement.AmendmentExplanation.Body";
    public const string SmoCampaignStatementAmendmentExplanationAmendmentExplanationText = "FilerPortal.Disclosure.SmoCampaignStatement.AmendmentExplanation.AmendmentExplanationText";
    public const string SmoCampaignStatementTransactorsAboutThePayeeTitle = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayee.Title";
    public const string SmoCampaignStatementTransactorsAboutThePayeeBody = "FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayee.Body";


    public const string TransactionAboutThePayee = "FilerPortal.Transaction.NewOtherPaymentPayee.AboutThePayee";
    public const string TransactionAboutThePayeeBody = "FilerPortal.Transaction.NewOtherPaymentPayee.Description";
    public const string ChooseOfficer = "FilerPortal.ChooseOfficer";
    public const string EnterName = "FilerPortal.EnterName";
    public const string ContactFormTitle = "FilerPortal.Contact.ContactForm.Title";
    public const string ContactFormBody = "FilerPortal.Contact.ContactForm.Body";
    public const string ContactFormPayeeType = "FilerPortal.Contact.ContactForm.PayeeType";
    public const string ContactFormPayorType = "FilerPortal.Contact.ContactForm.PayorType";
    public const string ContactFormIndividual = "FilerPortal.Contact.ContactForm.Individual";
    public const string ContactCommonFieldsAddressOfPayee = "FilerPortal.Contact.CommonFields.Title";
    public const string TransactionAboutThePaymentAmountPaid = "FilerPortal.Transaction.AboutThePayment.AmountPaid";

    //FilerPortal.Transaction.LobbyingCoalitionTransaction Constants
    public const string LobbyingCoalitionTransactionTitle = "FilerPortal.Transaction.LobbyingCoalitionTransaction.Title";
    public const string LobbyingCoalitionTransactionSubTitle = "FilerPortal.Transaction.LobbyingCoalitionTransaction.SubTitle";
    public const string LobbyingCoalitionTransactionNameOfLobbyingCoalition = "FilerPortal.Transaction.LobbyingCoalitionTransaction.NameOfLobbyingCoalition";
    public const string LobbyingCoalitionTransactionPage02Title = "FilerPortal.Transaction.LobbyingCoalitionTransaction.Page02.Title";
    public const string LobbyingCoalitionTransactionPage02Body = "FilerPortal.Transaction.LobbyingCoalitionTransaction.Page02.Body";
    public const string LobbyingCoalitionTransactionPage03Title = "FilerPortal.Transaction.LobbyingCoalitionTransaction.Page03.Title";
    public const string LobbyingCoalitionTransactionPage03Body = "FilerPortal.Transaction.LobbyingCoalitionTransaction.Page03.Body";

    //FilerPortal.CoalitionReceivedTransaction Constants
    public const string CoalitionReceivedTransactionTitle = "FilerPortal.CoalitionReceivedTransaction.Title";
    public const string CoalitionReceivedTransactionSubTitle = "FilerPortal.CoalitionReceivedTransaction.SubTitle";
    public const string CoalitionReceivedTransaction01Title = "FilerPortal.CoalitionReceivedTransaction.01_SelectContact.Title";
    public const string CoalitionReceivedTransaction01SubTitle = "FilerPortal.CoalitionReceivedTransaction.01_SelectContact.SubTitle";
    public const string CoalitionReceivedTransaction01OrAddContact = "FilerPortal.CoalitionReceivedTransaction.01_SelectContact.OrAddContact";
    public const string CoalitionReceivedTransaction01EnterContactInfo = "FilerPortal.CoalitionReceivedTransaction.01_SelectContact.EnterContactInfo";
    public const string CoalitionReceivedTransaction01ContinueValidation = "FilerPortal.CoalitionReceivedTransaction.01_SelectContact.ContinueValidation";
    public const string CoalitionReceivedTransaction02Title = "FilerPortal.CoalitionReceivedTransaction.02_EnterContact.Title";
    public const string CoalitionReceivedTransaction02SubTitle = "FilerPortal.CoalitionReceivedTransaction.02_EnterContact.SubTitle";
    public const string CoalitionReceivedTransaction03Title = "FilerPortal.CoalitionReceivedTransaction.03_EnterAmount.Title";
    public const string CoalitionReceivedTransaction03SubTitle = "FilerPortal.CoalitionReceivedTransaction.03_EnterAmount.SubTitle";
    public const string CoalitionReceivedTransaction03AmountOfPayment = "FilerPortal.CoalitionReceivedTransaction.03_EnterAmount.AmountOfPayment";

    //FilerPortal.FirmPaymentTransaction Constants
    public const string FirmPaymentTransactionTitle = "FilerPortal.FirmPaymentTransaction.Title";
    public const string FirmPaymentTransactionSubTitle = "FilerPortal.FirmPaymentTransaction.SubTitle";
    public const string FirmPaymentTransaction01Title = "FilerPortal.FirmPaymentTransaction.01_SelectContact.Title";
    public const string FirmPaymentTransaction01SubTitle = "FilerPortal.FirmPaymentTransaction.01_SelectContact.SubTitle";
    public const string FirmPaymentTransaction01OrAddContact = "FilerPortal.FirmPaymentTransaction.01_SelectContact.OrAddContact";
    public const string FirmPaymentTransaction01OrAddContactButton = "FilerPortal.FirmPaymentTransaction.01_SelectContact.OrAddContactButton";
    public const string FirmPaymentTransaction01SearchContact = "FilerPortal.FirmPaymentTransaction.01_SelectContact.SearchContact";
    public const string FirmPaymentTransaction01ContinueValidation = "FilerPortal.FirmPaymentTransaction.01_SelectContact.ContinueValidation";
    public const string FirmPaymentTransaction02Title = "FilerPortal.FirmPaymentTransaction.02_EnterContact.Title";
    public const string FirmPaymentTransaction02SubTitle = "FilerPortal.FirmPaymentTransaction.02_EnterContact.SubTitle";
    public const string FirmPaymentTransaction02NameLabel = "FilerPortal.FirmPaymentTransaction.02_EnterContact.NameLabel";
    public const string FirmPaymentTransaction02AddressTitle = "FilerPortal.FirmPaymentTransaction.02_EnterContact.AddressTitle";
    public const string FirmPaymentTransaction03Title = "FilerPortal.FirmPaymentTransaction.03_EnterAmount.Title";
    public const string FirmPaymentTransaction03SubTitle = "FilerPortal.FirmPaymentTransaction.03_EnterAmount.SubTitle";
    public const string FirmPaymentTransaction03FeesAndRetainersAmount = "FilerPortal.FirmPaymentTransaction.03_EnterAmount.FeesAndRetainersAmount";
    public const string FirmPaymentTransaction03ReimbursementOfExpensesAmount = "FilerPortal.FirmPaymentTransaction.03_EnterAmount.ReimbursementOfExpensesAmount";
    public const string FirmPaymentTransaction03AdvancesOrOtherPaymentsAmount = "FilerPortal.FirmPaymentTransaction.03_EnterAmount.AdvancesOrOtherPaymentsAmount";
    public const string FirmPaymentTransaction03AdvancesOrOtherPaymentsExplanation = "FilerPortal.FirmPaymentTransaction.03_EnterAmount.AdvancesOrOtherpaymentsExplanation";
    public const string FirmPaymentTransaction03TotalAmount = "FilerPortal.FirmPaymentTransaction.03_EnterAmount.TotalAmount";

    //FilerPortal.AccountManagement Constants
    public const string UserAccountManagementTitle = "FilerPortal.AccountManagement.Title";
    public const string AccountManagementUserDetails = "FilerPortal.AccountManagement.UserDetails";
    public const string AccountManagementUserName = "FilerPortal.AccountManagement.UserName";
    public const string AccountManagementEmailAddress = "FilerPortal.AccountManagement.EmailAddress";
    public const string AccountManagementFullName = "FilerPortal.AccountManagement.FullName";
    public const string AccountManagementAddressType = "FilerPortal.AccountManagement.AddressType";
    public const string AccountManagementCountry = "FilerPortal.AccountManagement.Country";
    public const string AccountManagementStreetAddress = "FilerPortal.AccountManagement.StreetAddress";
    public const string AccountManagementStreetAddress2 = "FilerPortal.AccountManagement.StreetAddress2";
    public const string AccountManagementCity = "FilerPortal.AccountManagement.City";
    public const string AccountManagementZipCode = "FilerPortal.AccountManagement.ZipCode";
    public const string AccountManagementState = "FilerPortal.AccountManagement.State";
    public const string AccountManagementTypeOfPhone = "FilerPortal.AccountManagement.TypeOfPhone";
    public const string AccountManagementTelephoneNumber = "FilerPortal.AccountManagement.TelephoneNumber";
    public const string AccountManagementResidential = "FilerPortal.AccountManagement.Residential";
    public const string AccountManagementBusiness = "FilerPortal.AccountManagement.Business";
    public const string AccountManagementSelectCountry = "FilerPortal.AccountManagement.SelectCountry";
    public const string AccountManagementSelectState = "FilerPortal.AccountManagement.SelectState";
    public const string AccountManagementCell = "FilerPortal.AccountManagement.Cell";
    public const string AccountManagementOffice = "FilerPortal.AccountManagement.Office";
    public const string AccountManagementHome = "FilerPortal.AccountManagement.Home";
    public const string AccountManagementName = "FilerPortal.AccountManagement.Name";
    public const string AccountManagementAddress = "FilerPortal.AccountManagement.Address";
    public const string AccountManagementMailingAddress = "FilerPortal.AccountManagement.MailingAddress";
    public const string AccountManagementPhoneNumber = "FilerPortal.AccountManagement.PhoneNumber";
    public const string AccountManagementSameAsAbove = "FilerPortal.AccountManagement.SameAsAbove";
    public const string AccountManagementSetAsPrimaryPhoneNumber = "FilerPortal.AccountManagement.SetAsPrimaryPhoneNumber";
    public const string AccountManagementAddAnotherPhoneButton = "FilerPortal.AccountManagement.AddAnotherPhoneButton";

    public static class Disclosure
    {
        public static class LobbyistReports
        {
            public const string Title = "FilerPortal.Disclosure.LobbyistReports.Title";
            public const string Body = "FilerPortal.Disclosure.LobbyistReports.Body";
            public const string Instruction = "FilerPortal.Disclosure.LobbyistReports.Instruction";
            public const string RegisteredLobbyistsTitle = "FilerPortal.Disclosure.LobbyistReports.RegisteredLobbyistsTitle";
            public const string RegisteredLobbyistsBody = "FilerPortal.Disclosure.LobbyistReports.RegisteredLobbyistsBody";
            public const string NonRegisteredLobbyistsTitle = "FilerPortal.Disclosure.LobbyistReports.NonRegisteredLobbyistsTitle";
            public const string LobbyistName = "FilerPortal.Disclosure.LobbyistReports.LobbyistName";
            public const string DeleteConfirmation = "FilerPortal.Disclosure.LobbyistReports.DeleteConfirmation";
        }
    }

    //FilerPortal.Filing.Report48H Constants
    public const string Report48HTransactionSubTitle = "FilerPortal.Filing.Report48H.Title";

    //FilerPortal.Filing.LobbyistEmployer Constant
    public const string LobbyistEmployerTitle = "FilerPortal.Filing.LobbyistEmployer.Title";

    //FilerPortal.Filing.LobbyistEmployer ActionsLobbied Constant
    public const string LobbyistEmployerActionsLobbiedBillDeleteConfirmation = "FilerPortal.Filing.LobbyistEmployer.ActionsLobbied.Bill.DeleteConfirmation";
    public const string LobbyistEmployerActionsLobbiedAgencyDeleteConfirmation = "FilerPortal.Filing.LobbyistEmployer.ActionsLobbied.Agency.DeleteConfirmation";

    // FilerPortal.MyLinkages Constants
    public const string AccountManagementTitle = "FilerPortal.AccountManagement.Title";
    public const string LinkagesTitle = "FilerPortal.Linkages.Title";
    public const string LinkagesBody = "FilerPortal.Linkages.Body";
    public static class MyLinkages
    {
        public static class MyLinkagesTable
        {
            public const string Title = "FilerPortal.MyLinkages.MyLinkagesTable.Title";
            public const string TerminateLinkage = "FilerPortal.MyLinkages.MyLinkagesTable.TerminateLinkage";
            public const string TerminateLinkageMessage = "FilerPortal.MyLinkages.MyLinkagesTable.TerminateLinkageMessage";
        }

        public static class PendingLinkageRequests
        {
            public const string Title = "FilerPortal.MyLinkages.PendingLinkageRequests.Title";
        }

        public static class RequestNewLinkage
        {
            public const string Title = "FilerPortal.MyLinkages.RequestNewLinkage.Title";
            public const string Description = "FilerPortal.MyLinkages.RequestNewLinkage.Description";
            public const string Btn = "FilerPortal.MyLinkages.RequestNewLinkage.RequestLinkageBtn";
        }

        public static class ReviewRequest
        {
            public const string Header = "FilerPortal.MyLinkages.ReviewRequest.Header";
            public const string Description = "FilerPortal.MyLinkages.ReviewRequest.Description";
            public const string InputLabel = "FilerPortal.MyLinkages.ReviewRequest.InputLabel";
            public const string Btn = "FilerPortal.MyLinkages.ReviewRequest.Btn";
            public const string ModalTitle = "FilerPortal.MyLinkages.ReviewRequest.Modal.Title";
            public const string ModalRequestedBy = "FilerPortal.MyLinkages.ReviewRequest.Modal.RequestedBy";
            public const string ModalEmail = "FilerPortal.MyLinkages.ReviewRequest.Modal.Email";
            public const string ModalAssignedRole = "FilerPortal.MyLinkages.ReviewRequest.Modal.AssignedRole";
            public const string ModalDateOfRequest = "FilerPortal.MyLinkages.ReviewRequest.Modal.DateOfRequest";
            public const string ModalFiler = "FilerPortal.MyLinkages.ReviewRequest.Modal.Filer";
            public const string InvitationCodeRequiredError = "FilerPortal.MyLinkages.ReviewRequest.Modal.InvitationCodeRequiredError";
            public const string NotFound = "FilerPortal.MyLinkages.ReviewRequest.Modal.NotFound";
            public const string LookupError = "FilerPortal.MyLinkages.ReviewRequest.Modal.LookupError";
        }

        public static class SendLinkageRequest
        {
            public const string Title = "FilerPortal.MyLinkages.SendLinkageRequest.Title";
            public const string Body = "FilerPortal.MyLinkages.SendLinkageRequest.Body";
            public const string LinkageRequest = "FilerPortal.MyLinkages.SendLinkageRequest.LinkageRequest";
            public const string FilerType = "FilerPortal.MyLinkages.SendLinkageRequest.FilerType";
            public const string FilerTypeRequiredError = "FilerPortal.MyLinkages.SendLinkageRequest.FilerTypeRequiredError";
            public const string SearchFilerLabel = "FilerPortal.MyLinkages.SendLinkageRequest.SearchFilerLabel";
            public const string SearchFilerPlaceholder = "FilerPortal.MyLinkages.SendLinkageRequest.SearchFilerPlaceholder";
            public const string SearchFilerRequiredError = "FilerPortal.MyLinkages.SendLinkageRequest.SearchFilerRequiredError";
            public const string LobbyistEmployerFirmLabel = "FilerPortal.MyLinkages.SendLinkageRequest.LobbyistEmployerFirmLabel";
            public const string FilerRole = "FilerPortal.MyLinkages.SendLinkageRequest.FilerRole";
            public const string FilerRoleRequiredError = "FilerPortal.MyLinkages.SendLinkageRequest.FilerRoleRequiredError";
            public const string SendSuccessToast = "FilerPortal.MyLinkages.SendLinkageRequest.SendSuccessToast";
            public const string SendFailureToast = "FilerPortal.MyLinkages.SendLinkageRequest.SendFailureToast";
            public const string FilerInformation = "FilerPortal.MyLinkages.SendLinkageRequest.FilerInformation";
        }
    }

    //FilerPortal.Disclosure.LobbyingAdvertisement Constants
    public const string LobbyingAdvertisementTitle = "FilerPortal.Disclosure.LobbyingAdvertisement.Title";
    public const string LobbyingAdvertisementBody = "FilerPortal.Disclosure.LobbyingAdvertisement.Body";
    public const string LobbyingAdvertisementPublicationDate = "FilerPortal.Disclosure.LobbyingAdvertisement.PublicationDate";
    public const string LobbyingAdvertisementDistributionMethod = "FilerPortal.Disclosure.LobbyingAdvertisement.DistributionMethod";
    public const string LobbyingAdvertisementOtherDescription = "FilerPortal.Disclosure.LobbyingAdvertisement.OtherDescription";
    public const string LobbyingAdvertisementLegislator = "FilerPortal.Disclosure.LobbyingAdvertisement.Legislator";
    public const string LobbyingAdvertisementAdditionalInformation = "FilerPortal.Disclosure.LobbyingAdvertisement.AdditionalInformation";
    public const string LobbyingAdvertisementAmount = "FilerPortal.Disclosure.LobbyingAdvertisement.Amount";
    public const string LobbyingAdvertisementSave = "FilerPortal.Disclosure.LobbyingAdvertisement.Save";

    //FilerPortal.Disclosure.AmendmentExplanation Constants
    public const string AmendmentExplanationTitle = "FilerPortal.Disclosure.AmendmentExplanation.Title";
    public const string AmendmentExplanationSubtitle = "FilerPortal.Disclosure.AmendmentExplanation.Subtitle";
    public const string AmendmentExplanationBody = "FilerPortal.Disclosure.AmendmentExplanation.Body";

    public static class FilerAuthorizedUsers
    {
        public const string SectionTitle = "FilerPortal.FilerAuthorizedUsers.SectionTitle";
        public const string SectionBody = "FilerPortal.FilerAuthorizedUsers.SectionBody";

        public static class CurrentOfficers
        {
            public const string Title = "FilerPortal.FilerAuthorizedUsers.CurrentOfficers.Title";
        }

        public static class CurrentAuthorizedUsers
        {
            public const string Title = "FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.Title";
            public const string TerminateTitle = "FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.TerminateTitle";
            public const string TerminateBody = "FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.TerminateBody";
            public const string TerminateSuccessToast = "FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.TerminateSuccessToast";
            public const string TerminateFailureToast = "FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.TerminateFailureToast";
        }
        public static class PendingUserLinkages
        {
            public const string Title = "FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.Title";
            public const string RequestType = "FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.RequestType";
            public const string AcceptTitle = "FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.AcceptTitle";
            public const string AcceptBody = "FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.AcceptBody";
            public const string AcceptSuccessToast = "FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.AcceptSuccessToast";
            public const string AcceptFailureToast = "FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.AcceptFailureToast";
            public const string RejectTitle = "FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.RejectTitle";
            public const string RejectBody = "FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.RejectBody";
            public const string RejectSuccessToast = "FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.RejectSuccessToast";
            public const string RejectFailureToast = "FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.RejectFailureToast";
        }
        public static class RequestNewLinkage
        {
            public const string Title = "FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.Title";
            public const string Body = "FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.Body";
            public const string RequestLinkage = "FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.RequestLinkage";
            public const string FilerRoleRequiredError = "FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.FilerRoleRequiredError";
            public const string SendSuccessToast = "FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.SendSuccessToast";
            public const string SendFailureToast = "FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.SendFailureToast";
            public const string ConfirmTitle = "FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.ConfirmTitle";
            public const string ConfirmBody = "FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.ConfirmBody";
        }
    }
}

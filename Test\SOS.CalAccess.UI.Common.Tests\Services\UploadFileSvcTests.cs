using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using SOS.CalAccess.Data.Contracts.Common;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Services.Common.FileSystem;
using SOS.CalAccess.Services.Common.FileSystem.Models;

namespace SOS.CalAccess.UI.Common.Tests.Services;
public class UploadFileSvcTests
{
    private UploadFileSvc _uploadFileSvc;
    private IUploadedFileRepository _repository;
    private UploadedFile _uploadedFile;
    private const string OriginalFileName = "file.txt";
    private const string RelationshipType = "RegistrationFiling";
    private const long RelationshipId = 99;

    [SetUp]
    public void Setup()
    {
        _repository = Substitute.For<IUploadedFileRepository>();
        _uploadFileSvc = new(_repository);
        _uploadedFile = new() { Path = "my-blob-container", OriginalFileName = OriginalFileName, RelationshipId = RelationshipId, RelationshipType = RelationshipType };
    }

    [Test]
    public async Task CreateUploadedFile()
    {
        _ = await _uploadFileSvc.CreateUploadedFile(_uploadedFile);
        await _repository.Received(1).Create(_uploadedFile);
    }

    [Test]
    public async Task FindUploadByFileNameGuid()
    {
        await _uploadFileSvc.FindUploadByFileNameGuid(_uploadedFile.FileName.ToString());
        _ = await _repository.Received(1).FindUploadByFileNameGuid(_uploadedFile.FileName.ToString());
    }

    [Test]
    public async Task DeleteUploadedFileByFileNameGuid()
    {
        await _uploadFileSvc.DeleteUploadedFile(_uploadedFile);
        await _repository.Received(1).DeleteUploadedFile(_uploadedFile);
    }

    [Test]
    public async Task GetUploadsByRelationshipId()
    {
        _ = await _uploadFileSvc.GetUploadsByRelationshipId(RelationshipId);
        await _repository.Received(1).GetUploadsByRelationshipId(RelationshipId);
    }

    [Test]
    public async Task UpdateUploadedFile()
    {
        _ = await _uploadFileSvc.UpdateUploadedFile(_uploadedFile);
        await _repository.Received(1).UpdateUploadedFile(_uploadedFile);
    }

    #region UpdateUploadedFileRelationshipsAsync
    [Test]
    public async Task UpdateUploadedFileRelationshipsAsync_EmptyFileName_ShouldDoNothingAndReturn()
    {
        // Arrange
        var request = new UpdateUploadedFileRelationshipsRequest
        {
            AttachedFileGuidsJson = "",
            RelationshipId = 1,
        };

        // Act
        await _uploadFileSvc.UpdateUploadedFileRelationshipsAsync(request);

        // Assert
        _ = _repository.Received(0).FindUploadFilesByFileNameGuids(Arg.Any<List<Guid>>());
        _ = _repository.Received(0).UpdateRelationships(Arg.Any<List<UploadedFile>>());
    }

    [Test]
    public async Task UpdateUploadedFileRelationshipsAsync_ValidRequest_ShouldUpdateRelationshipSuccessfully()
    {
        // Arrange
        var request = new UpdateUploadedFileRelationshipsRequest
        {
            AttachedFileGuidsJson = JsonConvert.SerializeObject(new List<Guid> { Guid.NewGuid() }),
            RelationshipId = 1,
        };
        var uploadedFiles = new List<UploadedFile>
        {
            _uploadedFile
        };
        _repository.FindUploadFilesByFileNameGuids(Arg.Any<List<Guid>>()).Returns(Task.FromResult(uploadedFiles));

        // Act
        await _uploadFileSvc.UpdateUploadedFileRelationshipsAsync(request);

        // Assert
        _ = _repository.Received(1).FindUploadFilesByFileNameGuids(Arg.Any<List<Guid>>());
        _ = _repository.Received(1).UpdateRelationships(Arg.Any<List<UploadedFile>>());
    }
    #endregion
}

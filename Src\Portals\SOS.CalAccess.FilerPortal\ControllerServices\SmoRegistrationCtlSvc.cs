using System.Globalization;
using System.Reflection;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.ControllerServices.SharedServices;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.Registrations.SmoRegistration;
using SOS.CalAccess.FilerPortal.Models.SharedModels;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Mapping;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Constants;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using FilerRole = SOS.CalAccess.Models.Authorization.FilerRole;
using PhoneNumberDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.PhoneNumberDto;
using SlateMailerOrganizationRequest = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SlateMailerOrganizationRequest;
using SmoOfficerGridDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoOfficerGridDto;
using SmoRegistrationAttestationResponseDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoRegistrationAttestationResponseDto;
using SmoRegistrationContactDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoRegistrationContactDto;
using SmoRegistrationSendForAttestationRequest = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoRegistrationSendForAttestationRequest;

namespace SOS.CalAccess.FilerPortal.ControllerServices;

#pragma warning disable S107 // Methods should not have too many parameters
public class SmoRegistrationCtlSvc(
    ISmoRegistrationSvc smoRegistrationSvc,
    IDateTimeSvc dateTimeSvc,
    IStringLocalizer<SharedResources> localizer,
    IUsersApi usersApi,
    IFilersApi filersApi,
    IAccuMailValidatorService accuMailValidatorSvc,
    ISharedSmoRegistrationCtlSvc sharedSmoRegistrationCtlSvc,
    IDecisionsValidationMapService decisionsValidationMapService
    ) : ISmoRegistrationCtlSvc
#pragma warning restore S107 // Methods should not have too many parameters
{
    public async Task<SmoRegistrationStep01ViewModel?> GetPage03ViewModel(long id, CancellationToken cancellationToken)
    {
        var response = await smoRegistrationSvc.GetRegistrationFilingById(id);

        if (response is not { } data)
        {
            return null;
        }
        else
        {
            return new SmoRegistrationStep01ViewModel(data);
        }
    }

    public async Task<SmoRegistrationDetailsStep01ViewModel?> GetPage04ViewModel(long id, CancellationToken cancellationToken)
    {
        try
        {
            var response = await smoRegistrationSvc.GetRegistrationFilingById(id) ?? throw new ArgumentException($"{InvalidIdErrorMessage}");

            // Get linked Recipient Committee
            var committee = await smoRegistrationSvc.GetLinkedRecipientCommitteeAsync(response.FilerId.GetValueOrDefault());

            var newModel = new SmoRegistrationDetailsStep01ViewModel
            {
                Id = response.Id > 0 ? response.Id : id,
                OrganizationLevelOfActivity = response.ActivityLevel ?? "",
                IsOrganizationQualified = response.QualifiedCommittee ?? null,
                IsOrganizationCampaignCommittee = response.CampaignCommittee ?? null,
                DateQualifiedAsSMO = response.DateQualified,
                CommitteeId = committee?.Id,
                CommitteeName = committee?.Name,
            };

            return newModel;
        }
        catch (Exception ex)
        {
            // Handle specific deserialization issues or any other error
            Console.WriteLine("Error deserializing the response: " + ex.Message);
            return null;
        }
    }

    /// <inheritdoc />
    public async Task<long?> Page03Submit(SmoRegistrationStep01ViewModel model, ModelStateDictionary modelState, bool isSubmission = true)
    {
        var request = sharedSmoRegistrationCtlSvc.MapSmoViewModelToRequest(model);
        request.CheckRequiredFieldsFlag = isSubmission;

        var response = await sharedSmoRegistrationCtlSvc.SubmitSmoRegistrationForm(model, modelState, request);

        return response.Id;
    }

    public async Task<SmoRegistrationStep02ViewModel?> GetPage06AndPage07ViewModel(long id, CancellationToken cancellationToken)
    {
        var response = await smoRegistrationSvc.GetSmoTreasurerPage02(id);

        if (response?.Id == null)
        {
            return null;
        }
        else
        {
            response.IsOfficer = await IsUserAssistantTreasurerOrOfficer(id);
            return new SmoRegistrationStep02ViewModel(id, response);
        }
    }

    /// <inheritdoc />
    public async Task<SmoRegistrationStep02ViewModel?> PagePrefill(long registrationId, long? contactId, bool? isTreasurerOrOfficer, CancellationToken cancellationToken)
    {
        if (contactId != null)
        {
            var officer = await smoRegistrationSvc.GetSmoOfficer(registrationId, contactId.GetValueOrDefault());

            var result = MapOfficerToSmoRegistrationStepViewModel<SmoRegistrationStep02ViewModel>(officer, registrationId);
            result.ContactId = contactId;

            return result;
        }

        if (isTreasurerOrOfficer is true)
        {
            return await GetCurrentUserInformation(registrationId, cancellationToken);
        }

        return null;
    }

    /// <inheritdoc />
    public async Task<bool?> IsTreasurer(long registrationId, long contactId, CancellationToken cancellationToken)
    {
        var officer = await smoRegistrationSvc.GetSmoOfficer(registrationId, contactId);

        return officer.Role == FilerRole.SlateMailerOrg_Treasurer.Name;
    }

    /// <inheritdoc />
    public async Task<SmoRegistrationStep03ViewModel?> GetPage13ViewModel(long registrationId, long? contactId, CancellationToken cancellationToken)
    {
        var officer = await smoRegistrationSvc.GetSmoOfficer(registrationId, contactId.GetValueOrDefault());

        if (officer is null)
        {
            return null;
        }

        var viewModel = MapOfficerToSmoRegistrationStepViewModel<SmoRegistrationStep03ViewModel>(officer, registrationId);

        // Mapping contactId if it's editing
        viewModel.ContactId = contactId;

        return viewModel;
    }

    /// <inheritdoc />
    public SmoRegistrationStep02ViewModel Page07Or10GetEmptyViewModel(long registrationId)
    {
        return new()
        {
            Id = registrationId,
            // Addresses list must contain an empty address record so that the page renders inputs for it.
            Addresses = new()
            {
                new() { Purpose = CommonConstants.Address.PurposeOfficer }
            }
        };
    }

    /// <inheritdoc />
    public async Task<long?> Page07Or10Submit(SmoRegistrationStep02ViewModel model, ModelStateDictionary modelState, CancellationToken cancellationToken, bool isSubmission = true)
    {
        long? userId = null;
        // Get the current login user who files the registration
        if (model.IsUserTreasurer.GetValueOrDefault() || model.IsOfficer.GetValueOrDefault())
        {
            var userResponse = await usersApi.GetSelf(cancellationToken);
            userId = userResponse?.Id;
        }

        var request = MapSmoRegistrationSharedModelToSmoRegistrationContactDto(model, userId);
        request.Id = model.ContactId;
        request.CheckRequiredFieldsFlag = isSubmission;
        request.RoleId = ResolveRoleId(request.RoleId);

        if (model.Id == null)
        {
            throw new KeyNotFoundException($"No id exists for registration");
        }

        var response = await smoRegistrationSvc.PostSmoRegistrationContactsPage05((long)model.Id, request); // Handles create and update

        if (!response.Valid)
        {
            Page07And10And13ApplyErrorsToModelState(response.ValidationErrors, modelState);
        }

        return response.Id;
    }

    /// <inheritdoc />
    public async Task<SmoRegistrationStep02ViewModel?> GetPage08ViewModel(long id, CancellationToken cancellationToken)
    {
        var existingRegistration = await smoRegistrationSvc.GetRegistrationFilingById(id) ?? throw new ArgumentException($"{InvalidIdErrorMessage}");

        // Get the current user role
        var userResponse = await usersApi.GetSelf(cancellationToken);
        var filerUser = await filersApi.GetFilerUserByUserIdAsync(existingRegistration.FilerId.GetValueOrDefault(), userResponse.Id, cancellationToken);

        // Check if the current user is Treasurer or Officer
        var isUserTreasurerOrOfficer = filerUser?.FilerRoleId != FilerRole.SlateMailerOrg_AccountManager.Id;

        var registrationContacts = await smoRegistrationSvc.GetSmoOfficers(id);
        var officerContacts = registrationContacts.Where(x => x.Role != FilerRole.SlateMailerOrg_AccountManager.Name);

        if (!officerContacts.Any())
        {
            return new SmoRegistrationStep02ViewModel
            {
                Id = id,
                IsUserTreasurerOrOfficer = isUserTreasurerOrOfficer,
            };
        }

        string deleteMessage = $"{localizer[ResourceConstants.SmoRegistration08DeleteOfficerModalMessage].Value}";

        var model = new SmoRegistrationStep02ViewModel
        {
            Id = id,
            IsUserTreasurerOrOfficer = isUserTreasurerOrOfficer,
            OfficerContactsGridModel = new SmallDataGridModel
            {
                GridId = "OfficerContactsGrid",
                GridType = nameof(SmoOfficerGridDto),
                DataSource = officerContacts,
                AllowPaging = false,
                AllowTextWrap = false,
                AllowAdding = false,
                AllowDeleting = true,
                AllowSearching = false,
                DeleteConfirmationMessage = deleteMessage,
                PrimaryKey = nameof(SmoOfficerGridDto.ContactId),
                EnableExport = false,
                Columns = new List<DataGridColumn>
                {
                    new() { Field = nameof(SmoOfficerGridDto.OfficerName), HeaderText = ResourceConstants.SmoRegistration08OfficerName },
                    new() { Field = nameof(SmoOfficerGridDto.Title), HeaderText = ResourceConstants.SmoRegistration08OfficerTitle },
                    new() { Field = nameof(SmoOfficerGridDto.StartDate), HeaderText = ResourceConstants.SmoRegistration08OfficerStartDate, IsDateTime = true },
                    new() { Field = $"{nameof(SmoOfficerGridDto.PhoneNumber)}.{nameof(SmoOfficerGridDto.PhoneNumber.Number)}", HeaderText = ResourceConstants.SmoRegistration08OfficerPhoneNumber }
                },
                ActionItems = new List<GridActionItem>
                {
                    new() { Label = CommonResourceConstants.Edit, Action = "editRow", ControllerName= $"SmoRegistration/{id}", ActionName = "EditOfficer" },
                    new() { Label = CommonResourceConstants.Delete, Action = "deleteRow", ControllerName=$"SmoRegistration/{id}", ActionName = "DeleteOfficer"}
                }
            }
        };

        return model;
    }

    public async Task<SmoRegistrationStep03ViewModel?> GetPage12ViewModel(long id, CancellationToken cancellationToken)
    {
        var officerContacts = await smoRegistrationSvc.GetSmoOfficers(id);

        var model = new SmoRegistrationStep03ViewModel
        {
            Id = id,
            IndividualAuthorizersGridModel = new SmallDataGridModel()
        };

        if (officerContacts is null || !officerContacts.Any(x => x.CanAuthorize ?? false))
        {
            return model;
        }

        var individualAuthorizers = officerContacts.Where(x => x.CanAuthorize ?? false);

        string deleteMessage = $"{localizer[ResourceConstants.SmoRegistration12DeleteAuthorizerModalMessage].Value}";

        model.IndividualAuthorizersGridModel = new SmallDataGridModel
        {
            PrimaryKey = nameof(SmoOfficerGridDto.Id),
            GridId = "IndividualAuthorizersGrid",
            GridType = nameof(SmoOfficerGridDto),
            DataSource = individualAuthorizers,
            AllowPaging = false,
            AllowTextWrap = false,
            AllowAdding = false,
            AllowDeleting = true,
            AllowSearching = false,
            DeleteConfirmationField = nameof(SmoOfficerGridDto.Id),
            DeleteConfirmationMessage = deleteMessage,
            EnableExport = false,
            Columns = new List<DataGridColumn>
            {
                new() { Field = nameof(SmoOfficerGridDto.OfficerName), HeaderText = ResourceConstants.SmoRegistration08OfficerName },
                new() { Field = $"{nameof(SmoOfficerGridDto.PhoneNumber)}.{nameof(SmoOfficerGridDto.PhoneNumber.Number)}", HeaderText = ResourceConstants.SmoRegistration08OfficerPhoneNumber }
            },
            ActionItems = new List<GridActionItem>
            {
                new() { Label = CommonResourceConstants.Edit, Action = "editRow", ControllerName= $"SmoRegistration/{id}", ActionName = "EditIndividualAuthorizer" },
                new() { Label = CommonResourceConstants.Delete, Action = "deleteRow", ControllerName = $"SmoRegistration/{id}", ActionName = "DeleteIndividualAuthorizer" }
            },
        };

        return model;
    }

    /// <inheritdoc />
    public async Task<SmoRegistrationStep03ViewModel?> Page12DeleteIndividualAuthorizer(long registrationId, long contactId)
    {
        await smoRegistrationSvc.DeleteIndividualAuthorizer(registrationId, contactId);

        var model = await GetPage12ViewModel(registrationId, default);

        return model;
    }

    /// <inheritdoc />
    public SmoRegistrationStep03ViewModel Page13GetEmptyViewModel(long id)
    {
        return new()
        {
            Id = id,
            // Addresses list must contain an empty address record so that the page renders inputs for it.
            Addresses = new()
            {
                new() { Purpose = CommonConstants.Address.PurposeOfficer }
            }
        };
    }

    /// <inheritdoc />
    public async Task<long?> Page13Save(SmoRegistrationStep03ViewModel model, ModelStateDictionary modelState, CancellationToken cancellationToken, bool isSubmission = true)
    {
        // Mark contact as authorizer
        model.CanAuthorize = true;

        var request = MapSmoRegistrationSharedModelToSmoRegistrationContactDto(model, null);
        request.CheckRequiredFieldsFlag = isSubmission;
        request.Id = model.ContactId;
        request.RoleId = request.RoleId == 0 ? FilerRole.SlateMailerOrg_AccountManager.Id : request.RoleId;

        if (model.Id == null)
        {
            throw new KeyNotFoundException($"No id exists for registration");
        }

        var response = await smoRegistrationSvc.PostSmoRegistrationContactsPage05((long)model.Id, request, true); // Handles create and update

        if (!response.Valid)
        {
            Page07And10And13ApplyErrorsToModelState(response.ValidationErrors, modelState);
        }

        return response.Id;
    }

    /// <inheritdoc />
    public async Task<SmoRegistrationStep04ViewModel> GetPage14ViewModel(long? id, bool isAuthorizedToCompleteTreasurerAck, CancellationToken cancellationToken)
    {
        if (id == null)
        {
            throw new KeyNotFoundException($"No id exists for registration");
        }

        var existingRegistration = await smoRegistrationSvc.GetRegistrationFilingById(id.GetValueOrDefault()) ?? throw new ArgumentException($"{InvalidIdErrorMessage}");

        // Get all treasurer acknowledgement contacts
        var response = await smoRegistrationSvc.GetTreasurerAcknowledgementContactsAsync(id.GetValueOrDefault());

        // Get info of current user
        // As the contact does not associate with the user, will work around by search by filer user
        var userResponse = await usersApi.GetSelf(cancellationToken);

        // Find the contact that match with current user
        var matchedContact = response.Contacts.FirstOrDefault(x => x.UserId == userResponse.Id);

        // Find incomplete treasurer acknowledgement contacts
        var incompleteContacts = response.Contacts.Where(x => !x.HasAcknowledged.GetValueOrDefault());

        return new SmoRegistrationStep04ViewModel
        {
            Id = id,
            ResponsibleOfficers = [.. incompleteContacts.Select(x => new ResponsibleOfficerSharedViewModel // Exclude who acknowledged
            {
                FirstName = x.FirstName,
                LastName = x.LastName,
                Title = x.Title,
            })],
            FirstName = matchedContact?.FirstName,
            LastName = matchedContact?.LastName,
            Title = matchedContact?.Title,
            IsTreasurerOrAssistantTreasurer = isAuthorizedToCompleteTreasurerAck,
            IsAgreementAccepted = matchedContact?.HasAcknowledged ?? false,
            ExecutedOn = matchedContact?.AcknowledgedOn ?? dateTimeSvc.GetCurrentDateTime(),
            IsRequiredOtherAcknowledgements = !isAuthorizedToCompleteTreasurerAck && incompleteContacts.Any(), // If it's treasurer/ast.treasurer, skip. Otherwise, check the remaining
        };
    }

    /// <inheritdoc />
    public async Task<SmoRegistrationStep04ViewModel> Page14Submit(SmoRegistrationStep04ViewModel model)
    {
        if (model.Id is null)
        {
            throw new KeyNotFoundException($"No id exists for registration");
        }

        // Complete the treasurer acknowledgement
        await smoRegistrationSvc.CompleteTreasurerAcknowledgementAsync(model.Id.Value);

        var response = await smoRegistrationSvc.GetTreasurerAcknowledgementContactsAsync(model.Id.Value);

        return new SmoRegistrationStep04ViewModel
        {
            Id = model.Id.Value,
            ResponsibleOfficers = [.. response.Contacts.Where(x => !x.HasAcknowledged.GetValueOrDefault()).Select(x => new ResponsibleOfficerSharedViewModel
            {
                FirstName = x.FirstName,
                LastName = x.LastName,
                Title = x.Title,
            })],
        };
    }

    /// <inheritdoc />
    public async Task Page14SendForAcknowledgement(SmoRegistrationStep04ViewModel model)
    {
        if (model.Id is null)
        {
            throw new KeyNotFoundException($"No id exists for registration");
        }

        // Send notification to officer that has not been acknowledgement
        await smoRegistrationSvc.SendAcknowledgementNotificationsAsync(model.Id.Value);
    }

    public static string ReplaceFieldName(string template, string replacement)
    {
        return template.Replace("{{Field Name}}", replacement);
    }

    public void AddValidationErrorsToModelStateDynamically(
        List<CalAccess.Models.Common.WorkFlowError> errors,
        ModelStateDictionary modelState,
        object model)
    {
        foreach (var error in errors)
        {
            // Map the error field name to a model property
            var propertyInfo = model.GetType().GetProperty(error.FieldName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

            if (propertyInfo != null)
            {
                // If property found, add the error to ModelState
                string errorMessage = ReplaceFieldName(error.Message, error.FieldName);
                modelState.AddModelError(propertyInfo.Name, errorMessage);
            }
            else
            {
                // If property not found, add the error to the summary (for general errors)
                modelState.AddModelError("", error.Message);
            }
        }
    }

    [HttpPost]
    public async Task<long?> Page04ContinueSubmit(
       SmoRegistrationDetailsStep01ViewModel model,
       ModelStateDictionary modelState,
       bool isSubmission = true)
    {
        if (model.Id == 0)
        {
            throw new ArgumentException($"{InvalidIdErrorMessage}");
        }

        try
        {
            var request = new SlateMailerOrganizationRequest
            {
                ActivityLevel = model.OrganizationLevelOfActivity,
                IsQualifiedCommittee = model.IsOrganizationQualified,
                DateQualified = model.DateQualifiedAsSMO,
                IsCampaignCommittee = model.IsOrganizationCampaignCommittee,
                CommitteeId = model.CommitteeId,
                CheckRequiredFieldsFlag = isSubmission
            };

            var updateResult = await smoRegistrationSvc.UpdateSmoRegistration(model.Id, request);
            if (updateResult.ValidationErrors != null)
            {
                decisionsValidationMapService.ApplyErrorsToModelState(sharedSmoRegistrationCtlSvc.GetSmoFieldValidationMap(model), updateResult.ValidationErrors, modelState);
            }
            return model.Id;
        }
        catch (Exception ex)
        {
            throw new ArgumentException("Invalid request:" + ex);
        }
    }

    /// <inheritdoc />
    public async Task<SmoRegistrationStep04ViewModel> GetPage15ViewModel(long? id, bool isAuthorizedToAttest)
    {
        if (id == null)
        {
            throw new KeyNotFoundException($"No id exists for registration");
        }

        var existingRegistration = await smoRegistrationSvc.GetRegistrationFilingById(id.GetValueOrDefault()) ?? throw new ArgumentException($"{InvalidIdErrorMessage}");

        // Get current user & filer user
        var userResponse = await usersApi.GetSelf();

        // Get responsible officer contacts
        var contacts = await smoRegistrationSvc.GetResponsibleOfficerContactsAsync(id.GetValueOrDefault());

        // Find the contact that match with current user
        var matchedContact = contacts.FirstOrDefault(x => x.UserId == userResponse.Id);

        // If current user is authorized to attest, check if the registration already attested or not
        var attestationResponse = new SmoRegistrationAttestationResponseDto();
        if (isAuthorizedToAttest)
        {
            attestationResponse = await smoRegistrationSvc.GetRegistrationAttestationAsync(id.GetValueOrDefault());
        }

        return new SmoRegistrationStep04ViewModel
        {
            Id = existingRegistration.Id,
            FirstName = attestationResponse.FirstName ?? matchedContact?.FirstName,
            LastName = attestationResponse.LastName ?? matchedContact?.LastName,
            Title = attestationResponse.Title ?? matchedContact?.Title,
            ExecutedOn = attestationResponse.ExecutedAt ?? dateTimeSvc.GetCurrentDateTime(),
            IsVerificationCertified = attestationResponse.ExecutedAt is not null,
            IsUserAuthorizedToAttest = isAuthorizedToAttest,
            ResponsibleOfficers = [.. contacts.Select(x => new ResponsibleOfficerSharedViewModel()
            {
                ContactId = x.Id,
                Title = x.Title,
                FirstName = x.FirstName,
                LastName = x.LastName
            })],
        };
    }

    /// <inheritdoc />
    public async Task Page15AttestRegistration(SmoRegistrationStep04ViewModel model, ModelStateDictionary modelState)
    {
        if (model.Id is null)
        {
            throw new KeyNotFoundException($"No id exists for registration");
        }

        // Attest the registration
        var response = await smoRegistrationSvc.AttestRegistrationAsync(model.Id.GetValueOrDefault());

        // Add error to model state
        if (!response.Valid)
        {
            Page15ApplyErrorsToModelState(response.ValidationErrors, modelState);
        }
    }

    /// <inheritdoc />
    public async Task Page15SendForAttestation(SmoRegistrationStep04ViewModel model, ModelStateDictionary modelState)
    {
        if (model.Id is null)
        {
            throw new KeyNotFoundException($"No id exists for registration");
        }

        var request = new SmoRegistrationSendForAttestationRequest
        {
            RegistrationRegistrationContactIds = model.SelectedResponsibleOfficersContactIds ?? new List<long> { }
        };

        // Send notification for attestation
        var response = await smoRegistrationSvc.SendRegistrationForAttestationAsync(model.Id.GetValueOrDefault(), request);

        // Add error to model state
        if (!response.Valid)
        {
            Page15ApplyErrorsToModelState(response.ValidationErrors, modelState);
        }
    }

    /// <inheritdoc />
    public async Task<ConfirmationViewModel> Page16GetViewModel(long? id)
    {
        if (id is null)
        {
            throw new KeyNotFoundException($"No id exists for registration");
        }

        var existingRegistration = await smoRegistrationSvc.GetRegistrationFilingById(id.GetValueOrDefault()) ?? throw new ArgumentException($"{InvalidIdErrorMessage}");

        // Get pending items
        var response = await smoRegistrationSvc.GetPendingItemsAsync(id.GetValueOrDefault());

        return new ConfirmationViewModel
        {
            Id = id.GetValueOrDefault(),
            ExecutedOn = existingRegistration.SubmittedAt ?? dateTimeSvc.GetCurrentDateTime(),
            IsSubmission = true,
            PendingItems = [.. response.Select(x => new PendingItemSharedViewModel
            {
                Item = x.Item,
                Status = x.Status,
            })],
        };
    }

    /// <inheritdoc />
    public async Task<SmoRegistrationSummaryViewModel> SummaryGetViewModel(long id)
    {
        var smoSummary = await smoRegistrationSvc.GetSmoRegistrationFilingSummary(id);

        var officerGridModel = new SmallDataGridModel
        {
            GridId = "FilingReportGrid",
            GridType = "smoOfficerGrid",
            DataSource = smoSummary.Officers,
            AllowPaging = false,
            AllowTextWrap = false,
            AllowAdding = false,
            AllowDeleting = false,
            EnableExport = false,
            Columns = new List<DataGridColumn>
                        {
                            new() { Field = "OfficerName", HeaderText = ResourceConstants.Name },
                            new() { Field = "Title", HeaderText = ResourceConstants.Title },
                            new() { Field = "StartDate", HeaderText = ResourceConstants.StartDate, IsDateTime = true },
                            new() { Field = "Email", HeaderText = ResourceConstants.Email },
                            new() { Field = "PhoneNumber.Number", HeaderText = ResourceConstants.PhoneNumber },
                            new() { Field = "Address", HeaderText = ResourceConstants.Address }
                        },

        };

        var individualAuthorizersGridModel = new SmallDataGridModel
        {
            GridId = "FilingReportGrid1",
            GridType = "smoIndividualAuthorizersGrid",
            DataSource = smoSummary.IndividualAuthorizers,
            AllowPaging = false,
            AllowTextWrap = false,
            AllowAdding = false,
            AllowDeleting = false,
            EnableExport = false,
            Columns = new List<DataGridColumn>
                        {
                            new() { Field = "OfficerName", HeaderText = ResourceConstants.Name },
                            new() { Field = "PhoneNumber.Number", HeaderText = ResourceConstants.PhoneNumber }
                        },
        };

        var model = new SmoRegistrationSummaryViewModel
        {
            Id = smoSummary.Registration!.Id,
            IsAmendment = smoSummary.Registration.IsAmendment,
            FilerName = smoSummary.FilerName,
            FilerId = smoSummary.Registration?.FilerId ?? 0,
            FormName = "400 - Slate Mailer Organization",
            SubmittedAt = smoSummary.Registration?.SubmittedAt ?? null,
            Status = smoSummary.FilingStatus,
            SmoName = smoSummary.Registration?.Name,
            Email = smoSummary.Registration?.Email,
            PhoneNumber = smoSummary.Registration!.PhoneNumbers.FirstOrDefault(p => p.Type == "Phone")!,
            FaxNumber = smoSummary.Registration!.PhoneNumbers.FirstOrDefault(p => p.Type == "Fax")!,
            OrganizationAddress = new AddressViewModel(smoSummary.Registration.Address.FirstOrDefault(a => a.Purpose == "Organization")!),
            MailingAddress = new AddressViewModel(smoSummary.Registration!.Address.FirstOrDefault(a => a.Purpose == "Mailing")!),
            LevelOfActivity = smoSummary.Registration!.ActivityLevel,
            IsQualified = smoSummary.Registration!.QualifiedCommittee,
            SmoDateQualified = smoSummary.Registration?.DateQualified?.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture),
            IsCampaignCommittee = smoSummary.Registration?.CampaignCommittee,
            CommitteeIdentifier = $"{smoSummary.Registration?.CommitteeId} - {smoSummary.Registration?.CommitteeName}",
            OfficersGrid = officerGridModel,
            IndividualAuthorizersGrid = individualAuthorizersGridModel,
            PendingItems = smoSummary.PendingItems,
            ShowTreasurerAcknowledgement = smoSummary.IsTreasurerAcknowledgement,
            AcceptTreasurerAcknowledgement = smoSummary.IsTreasurerAcknowledgement ?? false,
            TreasurerAcknowledgementName = smoSummary.FilerName,
            TreasurerAcknowledgementTitle = smoSummary.Attestation?.Title,
            TreasurerAcknowledgementExecutedOn = smoSummary.Attestation?.ExecutedAt,
            ShowVerify = smoSummary.Attestation is not null,
            AcceptVerify = true,
            VerifyName = smoSummary.FilerName,
            VerifyTitle = smoSummary.Attestation?.Title,
            VerifyExecutedOn = smoSummary.Attestation?.ExecutedAt,
        };

        return model;
    }
    public async Task<MethodResult> HandleRegistrationEditAsync(long id)
    {
        try
        {
            await smoRegistrationSvc.HandleRegistrationEditAsync(id);
            return new();
        }
        catch (Exception ex)
        {
            return new(ex);
        }
    }

    #region Private
    private const string InvalidIdErrorMessage = "Invalid ID.";

    /// <inheritdoc />
    public SmoRegistrationContactDto MapSmoRegistrationSharedModelToSmoRegistrationContactDto<TModel>(TModel model, long? userId) where TModel : SmoRegistrationContactSharedModel
    {
        var address = model.Addresses.Find(x => x.Purpose == CommonConstants.Address.PurposeOfficer) ?? new AddressViewModel();

        var roleId = GetRoleIdFromTitle(model.ContactTitle);

        var request = new SmoRegistrationContactDto
        {
            RoleId = roleId.GetValueOrDefault(),
            UserId = userId,
            Title = model.ContactTitle ?? "",
            FirstName = model.FirstName ?? "",
            MiddleName = model.MiddleName ?? "",
            LastName = model.LastName ?? "",
            Email = model.Email ?? "",
            CanAuthorize = model.CanAuthorize,
            Address = address.ToAddressDto(),
            PhoneNumber = model.TelephoneNumber,
        };

        return request;
    }

    /// <inheritdoc />
    public T MapOfficerToSmoRegistrationStepViewModel<T>(SmoRegistrationContactDto officer, long registrationId) where T : SmoRegistrationContactSharedModel, new()
    {
        var request = new T
        {
            Id = registrationId,
            FirstName = officer.FirstName,
            MiddleName = officer.MiddleName,
            LastName = officer.LastName,
            Email = officer.Email,
            TelephoneNumber = officer.PhoneNumber ?? new PhoneNumberDto(),
            CanAuthorize = officer.CanAuthorize ?? false,
            ContactTitle = officer.Title,
            Addresses = new List<AddressViewModel>()
        };
        if (officer.Address is not null)
        {
            request.Addresses.Add(new(officer.Address));
        }
        return request;
    }

    private void Page07And10And13ApplyErrorsToModelState(List<CalAccess.Models.Common.WorkFlowError> errors, ModelStateDictionary modelState)
    {
        foreach (var error in errors)
        {
            if (_page07and10and13FieldValidationMap.TryGetValue(error.FieldName, out var data))
            {
                modelState.AddModelError(data.FieldName, ReplaceFieldName(error.Message, localizer[data.LabelKey].Value));
            }
            else
            {
                // Errors to show at the bottom using Html.ValidationSummary
                modelState.AddModelError("", error.Message);
            }
        }
    }

    private static readonly Dictionary<string, FieldProperty> _page07and10and13FieldValidationMap = new()
    {
        { "Title", new FieldProperty(FieldName:"ContactTitle", LabelKey:ResourceConstants.SmoRegistration10OfficerTitle) },
        { "FirstName", new FieldProperty(FieldName:"FirstName", LabelKey:ResourceConstants.SmoRegistration07FirstName) },
        { "MiddleName", new FieldProperty(FieldName:"MiddleName", LabelKey:ResourceConstants.SmoRegistration07MiddleName) },
        { "LastName", new FieldProperty(FieldName:"LastName", LabelKey:ResourceConstants.SmoRegistration07LastName) },
        { "Email", new FieldProperty(FieldName:"Email", LabelKey:ResourceConstants.SmoRegistration07Email) },
        { "TelephoneNumber", new FieldProperty(FieldName:"TelephoneNumber", LabelKey:ResourceConstants.SmoRegistration07TelephoneNumber) },
        { "PhoneNumber", new FieldProperty(FieldName:"TelephoneNumber", LabelKey:ResourceConstants.SmoRegistration07TelephoneNumber) },
        { "Address.Country", new FieldProperty(FieldName:"Addresses[0].Country", LabelKey:ResourceConstants.SmoRegistration07Country) },
        { "Address.Street", new FieldProperty(FieldName:"Addresses[0].Street", LabelKey:ResourceConstants.SmoRegistration07Address) },
        { "Address.Street2", new FieldProperty(FieldName:"Addresses[0].Street2", LabelKey:ResourceConstants.SmoRegistration07Address2) },
        { "Address.City", new FieldProperty(FieldName:"Addresses[0].City", LabelKey:ResourceConstants.SmoRegistration07City) },
        { "Address.State", new FieldProperty(FieldName:"Addresses[0].State", LabelKey:ResourceConstants.SmoRegistration07State) },
        { "Address.Zip", new FieldProperty(FieldName:"Addresses[0].Zip", LabelKey:ResourceConstants.SmoRegistration07ZipCode) },
        { "Address.Type", new FieldProperty(FieldName:"Addresses[0].Type", LabelKey:ResourceConstants.SmoRegistration07TypeOfAddress) },
        { "IsUserAuthorized", new FieldProperty(FieldName:"IsUserAuthorized", LabelKey:ResourceConstants.SmoRegistration07IsUserAuthorized) },
    };

    private async Task<SmoRegistrationStep02ViewModel> GetCurrentUserInformation(long id, CancellationToken cancellationToken)
    {
        // Return my information for prefill
        var response = await usersApi.GetSelf(cancellationToken);
        var address = new AddressViewModel()
        {
            Purpose = CommonConstants.Address.PurposeOfficer,
            Country = "United States",
            Type = "Residential",
            City = "Los Angeles",
            Street = "316 Brannon Street",
            State = "CA",
            Zip = "90057"
        };
        var myInfo = new SmoRegistrationStep02ViewModel
        {
            Id = id,
            FirstName = response.FirstName,
            LastName = response.LastName,
            Email = response.Email,
            IsUserTreasurer = true,
            // Hard coded mock data for telephone number and address
            TelephoneNumber = new PhoneNumberDto
            {
                Number = "1112223333",
                Type = CommonConstants.PhoneNumber.TypeHome,
            },
            Addresses = new() { address }
        };
        return myInfo;
    }

    private static long? GetRoleIdFromTitle(string? title)
    {
        if (string.IsNullOrWhiteSpace(title))
        {
            return null;
        }

        return ContactTitleRoleMapping.TitleOrRoleNameToRoleId.TryGetValue(title, out var roleId) ? roleId : null;
    }

    private static void Page15ApplyErrorsToModelState(List<CalAccess.Models.Common.WorkFlowError> errors, ModelStateDictionary modelState)
    {
        foreach (var error in errors)
        {
            // Errors to show at the bottom using Html.ValidationSummary
            string fieldName = "{{Field Name}}";
            if (error.Message.Contains(fieldName, StringComparison.Ordinal))
            {
                modelState.AddModelError("", error.Message.Replace(fieldName, error.FieldName, StringComparison.Ordinal));
            }
            else
            {
                modelState.AddModelError("", error.FieldName + ": " + error.Message);

            }
        }
    }

    private static long ResolveRoleId(long requestRoleId)
    {
        if (requestRoleId == 0)
        {
            return FilerRole.SlateMailerOrg_Officer.Id;
        }

        return requestRoleId;
    }

    private async Task<bool> IsUserAssistantTreasurerOrOfficer(long id)
    {
        var currentUserRoleId = await smoRegistrationSvc.GetCurrentUserRoleByIdAsync(id);

        var validRoles = new List<long>
        {
            FilerRole.SlateMailerOrg_AssistantTreasurer.Id,
            FilerRole.SlateMailerOrg_Officer.Id,
        };

        return validRoles.Contains(currentUserRoleId);
    }
    #endregion
}

using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SOS.CalAccess.Data.EntityFramework.Migrations
{
    /// <inheritdoc />
    public partial class InitBusinessSubcategory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "BusinessSubcategory",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Documentation:Description", "BusinessSubcategory Identifier.")
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                        .Annotation("Documentation:Description", "BusinessSubcategory Name."),
                    CreatedBy = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Documentation:Description", "The identifier of the user who created this record."),
                    ModifiedBy = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Documentation:Description", "The identifier of the user who last modified this record."),
                    AuditableResourceTag = table.Column<string>(type: "nvarchar(450)", nullable: true)
                        .Annotation("Documentation:Context", "A string-serialized unique identifier used to link audit logs to arbitrary resource types.")
                        .Annotation("Documentation:Description", "Unique resource tag.")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BusinessSubcategory", x => x.Id);
                })
                .Annotation("Documentation:Context", "This table holds BusinessSubcategorys.")
                .Annotation("Documentation:Description", "");

            migrationBuilder.CreateIndex(
                name: "IX_BusinessSubcategory_AuditableResourceTag",
                table: "BusinessSubcategory",
                column: "AuditableResourceTag",
                unique: true,
                filter: "[AuditableResourceTag] IS NOT NULL");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BusinessSubcategory")
                .Annotation("Documentation:Context", "This table holds BusinessSubcategorys.")
                .Annotation("Documentation:Description", "");
        }
    }
}

 @using SOS.CalAccess.FilerPortal.Models.Localization
 @inject IHtmlLocalizer<SharedResources> Localizer
 @model SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration.LobbyistEmployerRegistrationStep03LobbyingFirmsViewModel


@{
    var hasFirms = Model.HasFirms;

    var progressItem1Name = ViewData["ProgressItem1Name"]?.ToString() ?? "";
    var progressItem2Name = ViewData["ProgressItem2Name"]?.ToString() ?? "";
    var progressItem3Name = ViewData["ProgressItem3Name"]?.ToString() ?? "";
    var progressItem4Name = ViewData["ProgressItem4Name"]?.ToString() ?? "";
    var progressItem5Name = ViewData["ProgressItem5Name"]?.ToString() ?? "";

    var progressBar = new ProgressBar(new List<ProgressItem>
    {
        new(progressItem1Name, true, false),
        new(progressItem2Name, true, false),
        new(progressItem3Name, false, true),
        new(progressItem4Name, false, false),
        new(progressItem5Name, false, false),
    });

    var buttonBar = new ButtonBarModel
    {
        LeftButtons = new List<ButtonConfig>
        {
            ButtonBarModel.DefaultPrevious,
            ButtonBarModel.DefaultContinue,
        },
        RightButtons = new List<ButtonConfig>
        {
            new ()
            {
                Type = ButtonType.Custom,
                HtmlContent = await Html.PartialAsync("_CancelDraftButton", (long?)null),
            },
            ButtonBarModel.DefaultSaveAndClose,
        }
    };
}
@Html.StepHeader(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationTitle2)
<h3>@SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationTitle]</h3>
<partial name="_LayoutProgressbar" model="progressBar" />

@using (Html.BeginForm("Step03LobbyingFirms", "LobbyistEmployerRegistration", FormMethod.Post))
{
    <div class="d-flex flex-column gap-4 main-form">
        <b class="text-uppercase">
            @Localizer[ResourceConstants.Step].Value 3
        </b>

        @if (hasFirms)
        {
            <h3>@SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationStep03LobbyingFirmsTitle]</h3>
            @Html.TextBlock(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationStep03LobbyingFirmsDescription)
            <div class="mb-4">
                @await Html.PartialAsync("_SmallGrid", Model.LobbyingFirmsGridModel)
            </div>
        }
        else
        {
            <h3>@SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationStep03LobbyingFirmsTitle]</h3>
            @Html.TextBlock(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationStep03LobbyingFirmsDescription)
        }

        <div class="mb-4">
            <a asp-controller="Home" asp-action="UnderConstruction"
               class="btn btn-outline-primary btn">
                <i class="bi bi-plus-circle-fill"></i>
                @SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationStep03LobbyingFirmsAdd]
            </a>
        </div>

    </div>

    <div class="main-button">
        <partial name="_ButtonBar" model="buttonBar" />
    </div>
}

<style>
    .main-form {
        border-top: 4px solid #397187;
        margin-top: 40px;
        padding: 40px;
    }

    .main-button {
        padding-left: 40px;
    }
</style>

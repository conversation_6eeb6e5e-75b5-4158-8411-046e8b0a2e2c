using System.Globalization;
using System.Net;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Refit;
using SOS.CalAccess.FilerPortal.Constants;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistRegistration;
using SOS.CalAccess.FilerPortal.Models.SharedModels;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using Syncfusion.EJ2.Linq;
using LobbyistEmployerOrLobbyistFirmSearchResultDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.LobbyistEmployerOrLobbyistFirmSearchResultDto;
using WorkFlowError = SOS.CalAccess.Models.Common.WorkFlowError;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

[TestFixture]
public class LobbyistRegistrationControllerTests
{
    private Mock<IStringLocalizer<SharedResources>> _localizerMock;
    private Mock<ILobbyistRegistrationCtlSvc> _lobbyistRegistrationCtlSvcMock;
    private Mock<ILobbyistRegistrationSvc> _lobbyistRegistrationSvcMock;
    private Mock<IAccuMailValidatorService> _accuMailValidatorSvcMock;
    private Mock<IToastService> _toastServiceMock;
    private Mock<IReferenceDataApi> _referenceDataApiMock;
    private Mock<IFilingsApi> _filingsApiMock;
    private Mock<ILobbyistApi> _lobbyistApiMock;
    private Mock<ILogger<LobbyistRegistrationController>> _loggerMock;
    private Mock<IAuthorizationSvc> _authorizationSvcMock;
    private LobbyistRegistrationController _controller;

    [SetUp]
    public void Setup()
    {
        _localizerMock = new Mock<IStringLocalizer<SharedResources>>();
        _lobbyistRegistrationCtlSvcMock = new Mock<ILobbyistRegistrationCtlSvc>();
        _lobbyistRegistrationSvcMock = new Mock<ILobbyistRegistrationSvc>();
        _accuMailValidatorSvcMock = new Mock<IAccuMailValidatorService>();
        _toastServiceMock = new Mock<IToastService>();
        _referenceDataApiMock = new Mock<IReferenceDataApi>();
        _filingsApiMock = new Mock<IFilingsApi>();
        _lobbyistApiMock = new Mock<ILobbyistApi>();
        _authorizationSvcMock = new Mock<IAuthorizationSvc>();
        _loggerMock = new Mock<ILogger<LobbyistRegistrationController>>();

        _localizerMock.Setup(x => x[It.IsAny<string>()])
            .Returns((string key) => new LocalizedString(key, key));

        _controller = new LobbyistRegistrationController(
            _lobbyistRegistrationSvcMock.Object,
            _lobbyistRegistrationCtlSvcMock.Object,
            _accuMailValidatorSvcMock.Object,
            _localizerMock.Object,
            _toastServiceMock.Object,
            _authorizationSvcMock.Object,
            _loggerMock.Object
        );
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    #region Other

    [Test]
    public void Index_ShouldRedirectToPage01()
    {
        var result = _controller.Index() as RedirectToActionResult;
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page01"));
    }

    [Test]
    public void Close_WithCancelAction_ShouldShowToastAndRedirect()
    {
        var result = _controller.Close(FormAction.Cancel) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Cancel_WithValidModelState_ShouldCallServiceAndRedirect()
    {
        long id = 1;
        var result = await _controller.Cancel(id) as RedirectToActionResult;

        _lobbyistRegistrationSvcMock.Verify(s => s.CancelLobbyistRegistration(id), Times.Once);
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Cancel_WithInvalidModelState_ShouldReturnNotFound()
    {
        _controller.ModelState.AddModelError("Error", "Invalid state");
        var result = await _controller.Cancel(1);
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void Page01_ShouldReturnView()
    {
        var result = _controller.Page01();
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public void OnActionExecuting_ShouldSetCommonViewData()
    {
        var httpContext = new DefaultHttpContext();
        var routeData = new RouteData();
        var actionDescriptor = new ActionDescriptor();
        var actionContext = new ActionContext(httpContext, routeData, actionDescriptor);

        var context = new ActionExecutingContext(
            actionContext,
            new List<IFilterMetadata>(),
            new Dictionary<string, object?>(),
            _controller
        );

        _controller.OnActionExecuting(context);

        Assert.Multiple(() =>
        {
            Assert.That(_controller.ViewData.ContainsKey(LayoutConstants.Title), Is.True);
            Assert.That(_controller.ViewData[LayoutConstants.Title], Is.Not.Null);
        });
    }

    [Test]
    public void Close_WithInvalidModelState_ShouldReturnBadRequest()
    {
        _controller.ModelState.AddModelError("error", "some error");

        var result = _controller.Close(FormAction.Cancel);

        Assert.That(result, Is.InstanceOf<BadRequestResult>());
    }

    [Test]
    public void LobbyistRegistrationStep01_Constructor_InitializesDefaults()
    {
        var model = new LobbyistRegistrationStep01();

        Assert.Multiple(() =>
        {
            Assert.That(model, Is.Not.Null);
            Assert.That(model.BusinessAddress, Is.Not.Null);
            Assert.That(model.MailingAddress, Is.Not.Null);
            Assert.That(model.Addresses, Is.Not.Null.And.Empty);
            Assert.That(model.Suggestions, Is.Not.Null.And.Empty);
        });
    }

    [Test]
    public void LobbyistRegistrationStep01_Properties_AssignAndRetrieveValues()
    {
        var model = new LobbyistRegistrationStep01
        {
            Id = 202,
            Action = FormAction.SaveAndClose,
            BusinessAddress = new AddressViewModel { City = "Fake Org City" },
            MailingAddress = new AddressViewModel { City = "Fake Mail City" },
            Addresses = new List<AddressViewModel> { new() { City = "Fake Addr City" } },
            LegislativeNextSessionId = 1,
        };

        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(202));
            Assert.That(model.Action, Is.EqualTo(FormAction.SaveAndClose));
            Assert.That(model.BusinessAddress.City, Is.EqualTo("Fake Org City"));
            Assert.That(model.MailingAddress.City, Is.EqualTo("Fake Mail City"));
            Assert.That(model.Addresses[0].City, Is.EqualTo("Fake Addr City"));
        });
    }

    [Test]
    public void Edit_WhenModelStateIsInvalid_ShouldReturnNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Id", "Required");

        // Act
        var result = _controller.Edit(123);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void Edit_WhenModelStateIsValid_ShouldRedirectToPage03()
    {
        // Act
        var result = _controller.Edit(456) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page03"));
            Assert.That(result.RouteValues["Id"], Is.EqualTo(456));
        });
    }

    #endregion

    #region Page 02

    [Test]
    public void Page02_Post_WithContinueActionAndValidId_ShouldRedirectToPage03()
    {
        // Arrange
        var model = new LobbyistRegistrationDetailsStep01ViewModel
        {
            Id = 789,
            SelfRegister = true,
            Action = FormAction.Continue
        };

        // Act
        var result = _controller.Page02(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page03"));
            Assert.That(result.RouteValues["id"], Is.EqualTo(789));
            Assert.That(result.RouteValues["selfRegister"], Is.EqualTo(true));
        });
    }

    [Test]
    public async Task Page02_Get_WithValidModelState_ShouldReturnViewWithModel()
    {
        var result = await _controller.Page02(null, null, _lobbyistApiMock.Object, CancellationToken.None) as ViewResult;
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<LobbyistRegistrationDetailsStep01ViewModel>());

        var lobbyist = new LobbyistResponseDto(
            addresses: new List<AddressDtoModel>
            {
                new (
                    city: "Test City",
                    country: "USA",
                    purpose: "Mailing",
                    state: "CA",
                    street: "123 Test St",
                    street2: "",
                    type: "Home",
                    zip: "12345"
                )
            },
            addressListId: 789,
            dateQualified: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 456,
            id: 123,
            name: "Test Lobbyist",
            phoneNumberListId: 101,
            phoneNumbers:
            new List<PhoneNumberDto>
            {
                new("+1", "987", 1, false, "1011231234", 1, false, "Work"),
            },
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 1,
            agencies: [],
            completedCourseDate: new(),
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: new(),
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Walter",
            middleName: string.Empty,
            lastName: "White",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyOnlySpecifiedAgencies: false,
            photo: "sample.jpg",
            selfRegister: true,
            isSameAsCandidateAddress: true,
            lobbyistEmployerOrLobbyingFirmName: "test",
            withdrawnAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            terminatedAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            effectiveDateOfChanges: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            type: "Lobbyist"

        );
        _lobbyistApiMock
            .Setup(api => api.GetLobbyistRegistration(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(lobbyist);
        result = await _controller.Page02(123, null, _lobbyistApiMock.Object, CancellationToken.None) as ViewResult;
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<LobbyistRegistrationDetailsStep01ViewModel>());
    }

    [Test]
    public async Task Page02_Get_WithInvalidModelState_ShouldRedirectToPage01()
    {
        _controller.ModelState.AddModelError("Error", "Invalid state");
        var result = await _controller.Page02(123, null, _lobbyistApiMock.Object, CancellationToken.None) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page01"));
    }

    [Test]
    public async Task Page02_Get_LobbyistNotExisting_ShouldReturnNotFound()
    {
        // Arrange
        var httpResponse = new HttpResponseMessage(HttpStatusCode.BadRequest)
        {
            Content = new StringContent("error")
        };

        var apiEx = await ApiException.Create(
            new HttpRequestMessage(HttpMethod.Post, "http://test"),
            HttpMethod.Post,
            httpResponse,
            new RefitSettings());

        _lobbyistApiMock
            .Setup(api => api.GetLobbyistRegistration(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(apiEx);

        var result = await _controller.Page02(123, null, _lobbyistApiMock.Object, CancellationToken.None) as NotFoundResult;

        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public void Page02_Post_PreviousAction_ShouldRedirectToPage01()
    {
        var model = new LobbyistRegistrationDetailsStep01ViewModel
        {
            Id = 123,
            Action = FormAction.Previous
        };

        var result = _controller.Page02(model) as RedirectToActionResult;
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page01"));
    }

    [Test]
    public void Page02_Post_InvalidAction_ShouldReturnViewWithError()
    {
        var model = new LobbyistRegistrationDetailsStep01ViewModel
        {
            Id = 123,
            Action = (FormAction)999
        };

        var result = _controller.Page02(model) as ViewResult;
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.IsValid, Is.False);
        });
    }

    #endregion

    #region Page 03

    [Test]
    public async Task Page03_Get_WithInvalidModelState_ShouldReturnBadRequest()
    {
        // Arrange
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = await _controller.Page03(1, true, false, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
    }

    [Test]
    public async Task Page03_Get_NotExistingLobbyist_ShouldReturnNotFound()
    {
        // Arrange
        var httpResponse = new HttpResponseMessage(HttpStatusCode.BadRequest)
        {
            Content = new StringContent("error")
        };

        var apiEx = await ApiException.Create(
            new HttpRequestMessage(HttpMethod.Post, "http://test"),
            HttpMethod.Post,
            httpResponse,
            new RefitSettings());

        _lobbyistApiMock
            .Setup(api => api.GetLobbyistRegistration(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(apiEx);

        // Act
        var result = await _controller.Page03(1, true, false, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    [TestCase(true)]
    [TestCase(false)]
    [TestCase(null)]
    public async Task Page03_Get_WithValidModel_ShouldReturnViewWithModel(bool? isRenewal)
    {
        // Arrange
        var model = new LobbyistRegistrationStep01ViewModel();
        var lobbyist = new LobbyistResponseDto(
            addresses: new List<AddressDtoModel>
            {
                new (
                    city: "Test City",
                    country: "USA",
                    purpose: "Mailing",
                    state: "CA",
                    street: "123 Test St",
                    street2: "",
                    type: "Home",
                    zip: "12345"
                )
            },
            addressListId: 789,
            dateQualified: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 456,
            id: 789,
            name: "Test Lobbyist",
            phoneNumberListId: 101,
            phoneNumbers:
            new List<PhoneNumberDto>
            {
                new("+1", "987", 1, false, "1011231234", 1, false, "Work"),
            },
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 1,
            agencies: [],
            completedCourseDate: new(),
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: new(),
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Walter",
            middleName: string.Empty,
            lastName: "White",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyOnlySpecifiedAgencies: false,
            photo: "sample.jpg",
            selfRegister: true,
            isSameAsCandidateAddress: true,
            lobbyistEmployerOrLobbyingFirmName: "test",
            withdrawnAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            terminatedAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            effectiveDateOfChanges: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            type: "Lobbyist"
        );

        _lobbyistApiMock
            .Setup(api => api.GetLobbyistRegistration(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(lobbyist);

        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.GetPage03ViewModel(It.IsAny<long?>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(model);

        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>
            {
               new(createdBy: 1, id: 1, modifiedBy: 1, name: "Fake Agency", registrationAgencies: new List<RegistrationAgency>())
            });

        _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
            {
               new
               (
                   id: 1,
                   name: "Test Session",
                   startDate : DateTime.Now,
                   endDate: DateTime.Now.AddYears(1)
               )
            }));

        // Act
        var result = await _controller.Page03(1, true, isRenewal, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page03_Get_WithValidModel_SelectedLobbyistEmployerOrFirm_ShouldReturnViewWithModel()
    {
        // Arrange
        var model = new LobbyistRegistrationStep01ViewModel()
        {
            LobbyistEmployerOrLobbyingFirmId = 1
        };
        var lobbyist = new LobbyistResponseDto(
            addresses: new List<AddressDtoModel>
            {
                new (
                    city: "Test City",
                    country: "USA",
                    purpose: "Mailing",
                    state: "CA",
                    street: "123 Test St",
                    street2: "",
                    type: "Home",
                    zip: "12345"
                )
            },
            addressListId: 789,
            dateQualified: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 456,
            id: 789,
            name: "Test Lobbyist",
            phoneNumberListId: 101,
            phoneNumbers:
            new List<PhoneNumberDto>
            {
                new("+1", "987", 1, false, "1011231234", 1, false, "Work"),
            },
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 1,
            agencies: [],
            completedCourseDate: new(),
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: new(),
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Walter",
            middleName: string.Empty,
            lastName: "White",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyOnlySpecifiedAgencies: false,
            photo: "sample.jpg",
            selfRegister: true,
            isSameAsCandidateAddress: true,
            lobbyistEmployerOrLobbyingFirmName: "test",
            withdrawnAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            terminatedAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            effectiveDateOfChanges: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            type: "Lobbyist"
        );

        var expectedLobbyistEmployerOrLobbyistFirm = new List<Generated.LobbyistEmployerOrLobbyistFirmSearchResultDto>()
        {
            new(
                id: 1,
                name: "Test Firm"
            )
        };

        _lobbyistApiMock
            .Setup(api => api.GetLobbyistRegistration(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(lobbyist);

        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.GetPage03ViewModel(It.IsAny<long?>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(model);

        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>
            {
               new(createdBy: 1, id: 1, modifiedBy: 1, name: "Fake Agency", registrationAgencies: new List<RegistrationAgency>())
            });

        _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
            {
               new
               (
                   id: 1,
                   name: "Test Session",
                   startDate : DateTime.Now,
                   endDate: DateTime.Now.AddYears(1)
               )
            }));

        _lobbyistApiMock.Setup(api => api.SearchLobbyistEmployerOrLobbyingFirmByIdOrName(It.IsAny<string>(), It.IsAny<CancellationToken>())).ReturnsAsync(expectedLobbyistEmployerOrLobbyistFirm);

        // Act
        var result = await _controller.Page03(1, true, false, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page03_Get_WithNullModel_ShouldReturnViewWithDefaultEthicsType()
    {
        // Arrange
        var lobbyist = new LobbyistResponseDto(
            addresses: new List<AddressDtoModel>
            {
                new (
                    city: "Test City",
                    country: "USA",
                    purpose: "Mailing",
                    state: "CA",
                    street: "123 Test St",
                    street2: "",
                    type: "Home",
                    zip: "12345"
                )
            },
            addressListId: 789,
            dateQualified: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 456,
            id: 789,
            name: "Test Lobbyist",
            phoneNumberListId: 101,
            phoneNumbers:
            new List<PhoneNumberDto>
            {
                new("+1", "987", 1, false, "1011231234", 1, false, "Work"),
            },
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 1,
            agencies: [],
            completedCourseDate: new(),
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: new(),
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Walter",
            middleName: string.Empty,
            lastName: "White",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyOnlySpecifiedAgencies: false,
            photo: "sample.jpg",
            selfRegister: true,
            isSameAsCandidateAddress: true,
            lobbyistEmployerOrLobbyingFirmName: "test",
            withdrawnAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            terminatedAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            effectiveDateOfChanges: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            type: "Lobbyist"
        );

        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.GetPage03ViewModel(It.IsAny<long?>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((LobbyistRegistrationStep01ViewModel)null!);

        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>
            {
            new(createdBy: 1, id: 1, modifiedBy: 1, name: "Test Agency", registrationAgencies: new List<RegistrationAgency>())
            });

        _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
            {
               new
               (
                   id: 1,
                   name: "Test Session",
                   startDate : DateTime.Now,
                   endDate: DateTime.Now.AddYears(1)
               )
            }));

        _lobbyistApiMock
            .Setup(api => api.GetLobbyistRegistration(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(lobbyist);

        // Act
        var result = await _controller.Page03(1, false, false, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<LobbyistRegistrationStep01ViewModel>());

        var viewModel = result.Model as LobbyistRegistrationStep01ViewModel;
        Assert.Multiple(() =>
        {
            Assert.That(viewModel?.IsNewCertification, Is.EqualTo(true));
            Assert.That(viewModel?.Agencies, Is.Not.Null.And.ContainKey(1));
        });
    }

    [Test]
    [TestCase(false, null)]
    [TestCase(true, 1)]
    public async Task Page03_Post_WithInvalidModelState_ShouldReturnViewWithModel(bool? isRenewal, int? legislativeNextSessionId)
    {
        // Arrange
        var model = new LobbyistRegistrationStep01ViewModel
        {
            IsRenewal = isRenewal,
            LegislativeNextSessionId = legislativeNextSessionId
        };
        _controller.ModelState.AddModelError("Test", "Invalid");

        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>());

        _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
            {
               new
               (
                   id: 1,
                   name: "Test Session",
                   startDate : DateTime.Now,
                   endDate: DateTime.Now.AddYears(1)
               )
            }));

        // Act
        var result = await _controller.Page03(model, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page03_Post_WithAccuMailValidationFailure_ShouldReturnView()
    {
        // Arrange
        var model = new LobbyistRegistrationStep01ViewModel();

        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>());

        _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
            {
               new
               (
                   id: 1,
                   name: "Test Session",
                   startDate : DateTime.Now,
                   endDate: DateTime.Now.AddYears(1)
               )
            }));

        _accuMailValidatorSvcMock
            .Setup(svc => svc.AccuMailValidationHandler(model, It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.Page03(model, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page03_Post_WithDefaultAction_ShouldReturnViewWithModelError()
    {
        // Arrange
        var model = new LobbyistRegistrationStep01ViewModel
        {
            Action = null
        };

        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>());

        _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
            {
               new
               (
                   id: 1,
                   name: "Test Session",
                   startDate : DateTime.Now,
                   endDate: DateTime.Now.AddYears(1)
               )
            }));

        _accuMailValidatorSvcMock
            .Setup(svc => svc.AccuMailValidationHandler(model, It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.Page03(model, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.ContainsKey(string.Empty), Is.True);
        });
    }

    [Test]
    public async Task Page03_Post_WithSaveAndCloseAction_ShouldRedirectToDashboard()
    {
        // Arrange
        var model = new LobbyistRegistrationStep01ViewModel
        {
            Action = FormAction.SaveAndClose
        };

        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>());

        _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
            {
               new
               (
                   id: 1,
                   name: "Test Session",
                   startDate : DateTime.Now,
                   endDate: DateTime.Now.AddYears(1)
               )
            }));

        _accuMailValidatorSvcMock
            .Setup(svc => svc.AccuMailValidationHandler(model, It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
            .ReturnsAsync(false);

        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.Page03Submit(model, It.IsAny<ModelStateDictionary>(), true))
            .ReturnsAsync(123);

        // Act
        var result = await _controller.Page03(model, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Page03_Post_WithSaveAndCloseAction_ButInvalidModelState_ShouldReturnPage03ViewWithModel()
    {
        // Arrange
        var model = new LobbyistRegistrationStep01ViewModel
        {
            Action = FormAction.SaveAndClose
        };

        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>());

        _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
            {
               new
               (
                   id: 1,
                   name: "Test Session",
                   startDate : DateTime.Now,
                   endDate: DateTime.Now.AddYears(1)
               )
            }));

        _accuMailValidatorSvcMock
            .Setup(svc => svc.AccuMailValidationHandler(model, It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
            .ReturnsAsync(false);

        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.Page03Submit(model, It.IsAny<ModelStateDictionary>(), true))
            .ReturnsAsync(123)
            .Callback<LobbyistRegistrationStep01ViewModel, ModelStateDictionary, bool>(
                (m, ms, b) => ms.AddModelError("SubmitError", "Error during submission"));

        // Act
        var result = await _controller.Page03(model, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("Page03"));
            Assert.That(result.Model, Is.EqualTo(model));
        });

        // Verify that Page03Submit was called
        _lobbyistRegistrationCtlSvcMock.Verify(
            x => x.Page03Submit(model, It.IsAny<ModelStateDictionary>(), true),
            Times.Once);

        // Verify that the ModelState contains the error added during submission
        Assert.That(_controller.ModelState.ContainsKey("SubmitError"), Is.True);
    }

    [Test]
    public async Task Page03_Post_WithPreviousAction_ShouldRedirectToPage02WhenModelStateValid()
    {
        // Arrange
        var model = new LobbyistRegistrationStep01ViewModel
        {
            Action = FormAction.Previous
        };

        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>());

        _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
            {
               new
               (
                   id: 1,
                   name: "Test Session",
                   startDate : DateTime.Now,
                   endDate: DateTime.Now.AddYears(1)
               )
            }));

        _accuMailValidatorSvcMock
            .Setup(svc => svc.AccuMailValidationHandler(model, It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
            .ReturnsAsync(false);

        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.Page03Submit(model, It.IsAny<ModelStateDictionary>(), true))
            .ReturnsAsync(999);

        // Act
        var result = await _controller.Page03(model, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("Page03"));
        });
    }

    [Test]
    public async Task Page03_Post_WithPreviousAction_WhenModelStateIsInvalid_ShouldReturnPage03ViewWithModel()
    {
        // Arrange
        var model = new LobbyistRegistrationStep01ViewModel
        {
            Action = FormAction.Previous
        };

        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>());

        _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
            {
               new
               (
                   id: 1,
                   name: "Test Session",
                   startDate : DateTime.Now,
                   endDate: DateTime.Now.AddYears(1)
               )
            }));

        _accuMailValidatorSvcMock
            .Setup(svc => svc.AccuMailValidationHandler(model, It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
            .ReturnsAsync(false);

        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.Page03Submit(model, It.IsAny<ModelStateDictionary>(), true))
            .ReturnsAsync(123)
            .Callback<LobbyistRegistrationStep01ViewModel, ModelStateDictionary, bool>(
                (m, ms, b) => ms.AddModelError("Error", "Simulated error"));

        // Act
        var result = await _controller.Page03(model, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object) as ActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ActionResult>());
    }

    [Test]
    public async Task Page03_Post_WithValidationFailure_ShouldReturnView()
    {
        var model = new LobbyistRegistrationStep01ViewModel { Action = FormAction.Continue };

        _accuMailValidatorSvcMock
            .Setup(svc => svc.AccuMailValidationHandler(model, It.IsAny<ModelStateDictionary>(), false))
            .ReturnsAsync(true);

        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>
            {
                new (
                    createdBy: 1,
                    id: 1,
                    modifiedBy: 1,
                    name: "Fake Agency",
                    registrationAgencies: new List<RegistrationAgency>())
            });

        _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
            {
               new
               (
                   id: 1,
                   name: "Test Session",
                   startDate : DateTime.Now,
                   endDate: DateTime.Now.AddYears(1)
               )
            }));

        var result = await _controller.Page03(model, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object) as ViewResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page03Previous_WhenModelStateIsInvalid_ShouldReturnPage03ViewWithModel()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid state");
        var model = new LobbyistRegistrationStep01ViewModel
        {
            Id = 1,
            Action = FormAction.Previous
        };
        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>());
        _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>()));
        _accuMailValidatorSvcMock
            .Setup(svc => svc.AccuMailValidationHandler(model, It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
            .ReturnsAsync(false);
        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.Page03Submit(model, It.IsAny<ModelStateDictionary>(), true))
            .ReturnsAsync(123)
            .Callback<LobbyistRegistrationStep01ViewModel, ModelStateDictionary, bool>(
                (m, ms, b) => ms.AddModelError("Error", "Simulated error"));
        // Act
        var result = await _controller.Page03(model, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object) as ActionResult;
        // Assert
        Assert.That(result, Is.InstanceOf<ActionResult>());
    }

    [Test]
    public async Task Page03Previous_WhenModelStateIsValid_ShouldRedirectToPage02()
    {
        // Arrange
        var model = new LobbyistRegistrationStep01ViewModel
        {
            Id = 1,
            Action = FormAction.Previous
        };
        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>());
        _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>()));
        _accuMailValidatorSvcMock
            .Setup(svc => svc.AccuMailValidationHandler(model, It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
            .ReturnsAsync(false);
        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.Page03Submit(It.IsAny<LobbyistRegistrationStep01ViewModel>(), It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
            .ReturnsAsync(123);
        // Act
        var result = await _controller.Page03(model, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object) as RedirectToActionResult;
        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page02"));
        });
    }

    [Test]
    public async Task Page03_Post_WithContinueActionAndValidModel_ShouldRedirectToPage04()
    {
        var model = new LobbyistRegistrationStep01ViewModel
        {
            Action = FormAction.Continue
        };
        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.Page03Submit(model, It.IsAny<ModelStateDictionary>(), true))
            .ReturnsAsync(42); // Mock registrationId
        _accuMailValidatorSvcMock
            .Setup(svc => svc.AccuMailValidationHandler(model, It.IsAny<ModelStateDictionary>(), false))
            .ReturnsAsync(false); // Simulate pass
        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>());
        _filingsApiMock
        .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
        .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
        {
            new
            (
                id: 1,
                name: "Test Session",
                startDate : DateTime.Now,
                endDate: DateTime.Now.AddYears(1)
            )
        }));
        var result = await _controller.Page03(model, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object) as RedirectToActionResult;
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page04"));
            Assert.That(result.RouteValues["Id"], Is.EqualTo(42));
        });
    }

    [Test]
    public async Task SearchLobbyistEmployerOrLobbyingFirmByIdOrName_WithInvalidModelState_ReturnsEmptyJsonResult()
    {
        // Arrange
        var search = "test";
        _controller.ModelState.AddModelError("Error", "Invalid state");

        // Act
        var result = await _controller.SearchLobbyistEmployerOrLobbyingFirmByIdOrName(_lobbyistRegistrationSvcMock.Object, search);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());

        _lobbyistRegistrationSvcMock.Verify(
            svc => svc.SearchLobbyistEmployerOrLobbyingFirmByIdOrName(It.IsAny<string>()),
            Times.Never);
    }

    [Test]
    public async Task SearchLobbyistEmployerOrLobbyingFirmByIdOrName_WithEmptySearchString_CallsServiceWithEmptyString()
    {
        // Arrange
        var search = "test";
        var expectedData = new List<LobbyistEmployerOrLobbyistFirmSearchResultDto>()
        {
            new LobbyistEmployerOrLobbyistFirmSearchResultDto
            {
                Id = 1,
                Name = "Test Firm"
            },
        };

        _lobbyistRegistrationSvcMock
            .Setup(svc => svc.SearchLobbyistEmployerOrLobbyingFirmByIdOrName(It.IsAny<string>()))
            .ReturnsAsync(expectedData);

        // Act
        var result = await _controller.SearchLobbyistEmployerOrLobbyingFirmByIdOrName(_lobbyistRegistrationSvcMock.Object, search);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Value, Is.EqualTo(expectedData));
    }

    [Test]
    public void LobbyistRegistrationStep01_EffectiveDateOfChanges_SetAndGet_ShouldWork()
    {
        // Arrange
        var model = new LobbyistRegistrationStep01();
        var expectedDate = new DateTime(2024, 1, 15);

        // Act
        model.EffectiveDateOfChanges = expectedDate;

        // Assert
        Assert.That(model.EffectiveDateOfChanges, Is.EqualTo(expectedDate));
    }

    #endregion

    #region Page 04

    [Test]
    public void Page04_Get_WithValidModelState_ShouldReturnViewWithModel()
    {
        var mockModel = new LobbyistRegistrationStep02ViewModel
        {
            Id = 88,
        };

        var result = _controller.Page04(mockModel) as ViewResult;
        Assert.That(result, Is.Not.Null);
        var model = result.Model as LobbyistRegistrationStep02ViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.That(model.Id, Is.EqualTo(88));
    }

    [Test]
    public void Page04_Get_WithInvalidModelState_ShouldReturnView()
    {
        var mockModel = new LobbyistRegistrationStep02ViewModel
        {
            Id = 88
        };

        _controller.ModelState.AddModelError("Error", "Invalid");

        var result = _controller.Page04(mockModel);

        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public void Page04_Post_WithContinueAction_ShouldRedirectToPage05()
    {
        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = 111,
            Action = FormAction.Continue
        };
        var result = _controller.Page04(model) as RedirectToActionResult;
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page05"));
            Assert.That(result.RouteValues["Id"], Is.EqualTo(111));
        });
    }

    [Test]
    public void Page04_Post_WithSaveAndCloseAction_ShouldRedirectToDashboard()
    {
        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = 222,
            Action = FormAction.SaveAndClose
        };
        var result = _controller.Page04(model) as RedirectToActionResult;
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public void Page04_Post_WithPreviousAction_ShouldRedirectToPage03()
    {
        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = 222,
            Action = FormAction.Previous
        };
        var result = _controller.Page04(model) as RedirectToActionResult;
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page03"));
            Assert.That(result.RouteValues["id"], Is.EqualTo(222));
        });
    }

    [Test]
    public void Page04_Post_WithInvalidAction_ShouldReturnViewWithModelError()
    {
        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = 333,
            Action = (FormAction)999
        };
        var result = _controller.Page04(model) as ViewResult;
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.ContainsKey(string.Empty), Is.True);
        });
    }

    [Test]
    public async Task Page04_Get_WithValidId_ShouldReturnViewWithModel()
    {
        var mockLobbyist = new LobbyistResponseDto(
            addresses: new List<AddressDtoModel>(),
            addressListId: 0,
            dateQualified: DateTime.UtcNow,
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 42,
            id: 42,
            name: "Test Lobbyist",
            phoneNumberListId: 0,
            phoneNumbers: new List<PhoneNumberDto>(),
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 2,
            agencies: [],
            completedCourseDate: DateTime.UtcNow,
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: DateTime.UtcNow,
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Test",
            middleName: "M",
            lastName: "User",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyistEmployerOrLobbyingFirmName: "Test Firm",
            lobbyOnlySpecifiedAgencies: false,
            photo: "photo.jpg",
            selfRegister: false,
            isSameAsCandidateAddress: false,
            withdrawnAt: null,
            terminatedAt: null,
            effectiveDateOfChanges: DateTime.UtcNow,
            type: "Lobbyist"
        );

        _lobbyistApiMock
            .Setup(api => api.GetLobbyistRegistration(42, It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockLobbyist);

        var result = await _controller.Page04(42, _lobbyistApiMock.Object) as ViewResult;

        Assert.That(result, Is.Not.Null);
        var model = result!.Model as LobbyistRegistrationStep02ViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(model!.Id, Is.EqualTo(42));
            Assert.That(model.Version, Is.EqualTo(mockLobbyist.Version));
        });
    }

    [Test]
    public async Task Page04_Get_WithNullId_ShouldReturnBadRequest()
    {
        var result = await _controller.Page04(null, _lobbyistApiMock.Object);
        Assert.That(result, Is.InstanceOf<BadRequestResult>());
    }

    [Test]
    public async Task Page04_Get_WithInvalidModelState_ShouldReturnNotFound()
    {
        _controller.ModelState.AddModelError("Error", "Invalid");

        var mockLobbyist = new LobbyistResponseDto(
            addresses: new List<AddressDtoModel>(),
            addressListId: 0,
            dateQualified: DateTime.UtcNow,
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 1,
            id: 1,
            name: "Test Lobbyist",
            phoneNumberListId: 0,
            phoneNumbers: new List<PhoneNumberDto>(),
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 1,
            agencies: [],
            completedCourseDate: DateTime.UtcNow,
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: DateTime.UtcNow,
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Test",
            middleName: "M",
            lastName: "User",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyistEmployerOrLobbyingFirmName: "Test Firm",
            lobbyOnlySpecifiedAgencies: false,
            photo: "photo.jpg",
            selfRegister: false,
            isSameAsCandidateAddress: false,
            withdrawnAt: null,
            terminatedAt: null,
            effectiveDateOfChanges: DateTime.UtcNow,
            type: "Lobbyist"
        );

        _lobbyistApiMock
            .Setup(api => api.GetLobbyistRegistration(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockLobbyist);

        var result = await _controller.Page04(1, _lobbyistApiMock.Object);
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    #endregion

    #region Page 05

    [Test]
    public void Page05_Get_WithValidModelState_ShouldReturnViewWithModel()
    {
        var mockModel = new LobbyistRegistrationStep02ViewModel
        {
            Id = 101
        };

        var result = _controller.Page05(mockModel) as ViewResult;

        Assert.That(result, Is.Not.Null);
        var model = result.Model as LobbyistRegistrationStep02ViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.That(model.Id, Is.EqualTo(101));
    }

    [Test]
    public void Page05_Get_WithInvalidModelState_ShouldReturnView()
    {
        _controller.ModelState.AddModelError("Error", "Invalid");

        var mockModel = new LobbyistRegistrationStep02ViewModel
        {
            Id = 101
        };

        var result = _controller.Page05(mockModel);

        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public void Page05_Post_WithPreviousAction_ShouldRedirectToPage04()
    {
        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = 303,
            Action = FormAction.Previous
        };
        var result = _controller.Page05(model) as RedirectToActionResult;
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page04"));
            Assert.That(result.RouteValues["id"], Is.EqualTo(303));
        });
    }

    [Test]
    public void Page05_Post_WithSaveAndCloseAction_ShouldRedirectToDashboard()
    {
        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = 222,
            Action = FormAction.SaveAndClose
        };
        var result = _controller.Page05(model) as RedirectToActionResult;
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public void Page05_Post_WithContinueAction_ShouldRedirectToPage04()
    {
        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = 303,
            Action = FormAction.Continue
        };
        var result = _controller.Page05(model) as RedirectToActionResult;
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page06"));
            Assert.That(result.RouteValues["id"], Is.EqualTo(303));
        });
    }

    [Test]
    public void Page05_Post_WithInvalidAction_ShouldReturnViewWithModelError()
    {
        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = 404,
            Action = (FormAction)999
        };
        var result = _controller.Page05(model) as ViewResult;
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.ContainsKey(string.Empty), Is.True);
        });
    }

    [Test]
    public async Task Page05_Get_WithValidId_ShouldReturnViewWithModel()
    {
        var mockLobbyist = new LobbyistResponseDto(
            addresses: new List<AddressDtoModel>(),
            addressListId: 0,
            dateQualified: DateTime.UtcNow,
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 55,
            id: 55,
            name: "Test Lobbyist",
            phoneNumberListId: 0,
            phoneNumbers: new List<PhoneNumberDto>(),
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 3,
            agencies: [],
            completedCourseDate: DateTime.UtcNow,
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: DateTime.UtcNow,
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Test",
            middleName: "M",
            lastName: "User",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyistEmployerOrLobbyingFirmName: "Test Firm",
            lobbyOnlySpecifiedAgencies: false,
            photo: "photo.jpg",
            selfRegister: false,
            isSameAsCandidateAddress: false,
            withdrawnAt: null,
            terminatedAt: null,
            effectiveDateOfChanges: DateTime.UtcNow,
            type: "Lobbyist"
        );

        _lobbyistApiMock
            .Setup(api => api.GetLobbyistRegistration(55, It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockLobbyist);

        var result = await _controller.Page05(55, _lobbyistApiMock.Object) as ViewResult;

        Assert.That(result, Is.Not.Null);
        var model = result!.Model as LobbyistRegistrationStep02ViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(model!.Id, Is.EqualTo(55));
            Assert.That(model.Version, Is.EqualTo(mockLobbyist.Version));
        });
    }

    [Test]
    public async Task Page05_Get_WithNullId_ShouldReturnBadRequest()
    {
        var result = await _controller.Page05(null, _lobbyistApiMock.Object);
        Assert.That(result, Is.InstanceOf<BadRequestResult>());
    }

    [Test]
    public async Task Page05_Get_WithInvalidModelState_ShouldReturnNotFound()
    {
        _controller.ModelState.AddModelError("Error", "Invalid");

        var mockLobbyist = new LobbyistResponseDto(
            addresses: new List<AddressDtoModel>(),
            addressListId: 0,
            dateQualified: DateTime.UtcNow,
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 66,
            id: 66,
            name: "Test Lobbyist",
            phoneNumberListId: 0,
            phoneNumbers: new List<PhoneNumberDto>(),
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 1,
            agencies: [],
            completedCourseDate: DateTime.UtcNow,
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: DateTime.UtcNow,
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Test",
            middleName: "M",
            lastName: "User",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyistEmployerOrLobbyingFirmName: "Test Firm",
            lobbyOnlySpecifiedAgencies: false,
            photo: "photo.jpg",
            selfRegister: false,
            isSameAsCandidateAddress: false,
            withdrawnAt: null,
            terminatedAt: null,
            effectiveDateOfChanges: DateTime.UtcNow,
            type: "Lobbyist"
        );

        _lobbyistApiMock
            .Setup(api => api.GetLobbyistRegistration(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockLobbyist);

        var result = await _controller.Page05(66, _lobbyistApiMock.Object);
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    #endregion

    #region Page 06

    [Test]
    public void Page06_Get_WithValidModelState_ShouldReturnViewWithModel()
    {
        var mockModel = new LobbyistRegistrationStep02ViewModel
        {
            Id = 101
        };

        var result = _controller.Page06(mockModel) as ViewResult;

        Assert.That(result, Is.Not.Null);
        var model = result.Model as LobbyistRegistrationStep02ViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.That(model.Id, Is.EqualTo(101));
    }

    [Test]
    public void Page06_Get_WithInvalidModelState_ShouldReturnView()
    {
        _controller.ModelState.AddModelError("Error", "Invalid");

        var mockModel = new LobbyistRegistrationStep02ViewModel
        {
            Id = 101
        };

        var result = _controller.Page06(mockModel);

        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public void Page06_Post_WithPreviousAction_ShouldRedirectToPage05()
    {
        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = 303,
            Action = FormAction.Previous
        };
        var result = _controller.Page06(model) as RedirectToActionResult;
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page05"));
            Assert.That(result.RouteValues["id"], Is.EqualTo(303));
        });
    }

    [Test]
    public void Page06_Post_WithSaveAndCloseAction_ShouldRedirectToDashboard()
    {
        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = 303,
            Action = FormAction.SaveAndClose
        };
        var result = _controller.Page06(model) as RedirectToActionResult;
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public void Page06_Post_WithContinueAction_ShouldRedirectToPage05()
    {
        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = 303,
            Action = FormAction.Continue
        };
        var result = _controller.Page06(model) as RedirectToActionResult;
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page07"));
            Assert.That(result.RouteValues["id"], Is.EqualTo(303));
        });
    }

    [Test]
    public void Page06_Post_WithInvalidAction_ShouldReturnViewWithModelError()
    {
        var model = new LobbyistRegistrationStep02ViewModel
        {
            Id = 404,
            Action = (FormAction)999
        };
        var result = _controller.Page06(model) as ViewResult;
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.ContainsKey(string.Empty), Is.True);
        });
    }

    [Test]
    public async Task Page06_Get_WithValidId_ShouldReturnViewWithModel()
    {
        var mockLobbyist = new LobbyistResponseDto(
            addresses: new List<AddressDtoModel>(),
            addressListId: 0,
            dateQualified: DateTime.UtcNow,
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 77,
            id: 77,
            name: "Test Lobbyist",
            phoneNumberListId: 0,
            phoneNumbers: new List<PhoneNumberDto>(),
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 4,
            agencies: [],
            completedCourseDate: DateTime.UtcNow,
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: DateTime.UtcNow,
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Test",
            middleName: "M",
            lastName: "User",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyistEmployerOrLobbyingFirmName: "Test Firm",
            lobbyOnlySpecifiedAgencies: false,
            photo: "photo.jpg",
            selfRegister: false,
            isSameAsCandidateAddress: false,
            withdrawnAt: null,
            terminatedAt: null,
            effectiveDateOfChanges: DateTime.UtcNow,
            type: "Lobbyist"
        );

        _lobbyistApiMock
            .Setup(api => api.GetLobbyistRegistration(77, It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockLobbyist);

        var result = await _controller.Page06(77, _lobbyistApiMock.Object) as ViewResult;

        Assert.That(result, Is.Not.Null);
        var model = result!.Model as LobbyistRegistrationStep02ViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(model!.Id, Is.EqualTo(77));
            Assert.That(model.Version, Is.EqualTo(mockLobbyist.Version));
        });
    }

    [Test]
    public async Task Page06_Get_WithNullId_ShouldReturnBadRequest()
    {
        var result = await _controller.Page06(null, _lobbyistApiMock.Object);
        Assert.That(result, Is.InstanceOf<BadRequestResult>());
    }

    [Test]
    public async Task Page06_Get_WithInvalidModelState_ShouldReturnNotFound()
    {
        _controller.ModelState.AddModelError("Error", "Invalid");

        var mockLobbyist = new LobbyistResponseDto(
            addresses: new List<AddressDtoModel>(),
            addressListId: 0,
            dateQualified: DateTime.UtcNow,
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 88,
            id: 88,
            name: "Test Lobbyist",
            phoneNumberListId: 0,
            phoneNumbers: new List<PhoneNumberDto>(),
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 1,
            agencies: [],
            completedCourseDate: DateTime.UtcNow,
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: DateTime.UtcNow,
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Test",
            middleName: "M",
            lastName: "User",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyistEmployerOrLobbyingFirmName: "Test Firm",
            lobbyOnlySpecifiedAgencies: false,
            photo: "photo.jpg",
            selfRegister: false,
            isSameAsCandidateAddress: false,
            withdrawnAt: null,
            terminatedAt: null,
            effectiveDateOfChanges: DateTime.UtcNow,
            type: "Lobbyist"
        );

        _lobbyistApiMock
            .Setup(api => api.GetLobbyistRegistration(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockLobbyist);

        var result = await _controller.Page06(88, _lobbyistApiMock.Object);
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    #endregion

    #region Page 07

    [Test]
    public async Task Page07_Get_WithValidModelState_ShouldReturnViewWithModel()
    {
        var id = 101;
        var email = "<EMAIL>";
        var name = "sample name";
        var selfRegister = false;

        var model = new LobbyistRegistrationStep03ViewModel
        {
            Id = id,
            Email = email,
            Name = name,
            SelfRegister = selfRegister
        };

        _ = _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.GetPage07ViewModel(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(model);

        var result = await _controller.Page07(101) as ViewResult;
        Assert.That(result, Is.Not.Null);

        var resultModel = result.Model as LobbyistRegistrationStep03ViewModel;
        Assert.That(resultModel, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(resultModel.Id, Is.EqualTo(id));
            Assert.That(resultModel.Email, Is.EqualTo(email));
            Assert.That(resultModel.Name, Is.EqualTo(name));
            Assert.That(resultModel.SelfRegister, Is.EqualTo(selfRegister));
            Assert.That(resultModel.IsVerificationCertified, Is.EqualTo(false));
        });
    }

    [Test]
    public async Task Page07_Get_WithInvalidModelState_ShouldReturnNotFound()
    {
        _controller.ModelState.AddModelError("Error", "Invalid");
        var result = await _controller.Page07(101);
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task Page07_Post_WithSubmitAction_ShouldRedirectToPage08()
    {

        var model = new LobbyistRegistrationStep03ViewModel
        {
            Id = 303,
            Action = FormAction.Submit,
            SelfRegister = true,
        };

        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto
        {
            Id = 303,
            Valid = true,
            ValidationErrors = [],
            StatusId = CalAccess.Models.FilerRegistration.Registrations.RegistrationStatus.Accepted.Id,
            Notifications = [],
        };

        _ = _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.SubmitLobbyistRegistration(It.IsAny<LobbyistRegistrationStep03ViewModel>()))
            .ReturnsAsync(response);

        _controller.ModelState.Clear();

        var result = await _controller.Page07(model) as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo("Page08"));
            Assert.That(result!.RouteValues!["id"], Is.EqualTo(model.Id));
        });
    }

    [Test]
    public async Task Page07_Post_WithSubmitAction_ShouldRedirectToDashboardForAttestation()
    {

        var model = new LobbyistRegistrationStep03ViewModel
        {
            Id = 303,
            Action = FormAction.Submit,
            SelfRegister = false,
        };

        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto
        {
            Id = 303,
            Valid = true,
            ValidationErrors = [],
            StatusId = CalAccess.Models.FilerRegistration.Registrations.RegistrationStatus.Accepted.Id,
            Notifications = [],
        };

        _ = _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.SubmitLobbyistRegistration(It.IsAny<LobbyistRegistrationStep03ViewModel>()))
            .ReturnsAsync(response);

        _controller.ModelState.Clear();

        var result = await _controller.Page07(model) as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Page07_Post_WithSaveAndCloseAction_ShouldRedirectToDashboard()
    {
        var model = new LobbyistRegistrationStep03ViewModel
        {
            Id = 222,
            Action = FormAction.SaveAndClose,
            SelfRegister = false,
        };
        var result = await _controller.Page07(model) as RedirectToActionResult;
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Page07_Post_WithSubmitAction_AndValidationErrors_ReturnsView()
    {
        var model = new LobbyistRegistrationStep03ViewModel
        {
            Id = 303,
            Action = FormAction.Submit,
            SelfRegister = false,
        };

        var errors = new List<CalAccess.Models.Common.WorkFlowError>
        {
            new("sample error code", "sample error type", "sampleFieldName", "sample error message for {{Field Name}}!")
        };

        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto
        {
            Id = 303,
            Valid = true,
            ValidationErrors = errors,
            StatusId = CalAccess.Models.FilerRegistration.Registrations.RegistrationStatus.Accepted.Id,
            Notifications = [],
        };

        _ = _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.SubmitLobbyistRegistration(It.IsAny<LobbyistRegistrationStep03ViewModel>()))
            .ReturnsAsync(response);

        _controller.ModelState.Clear();

        var result = await _controller.Page07(model) as ViewResult;
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.ContainsKey(string.Empty), Is.True);
            Assert.That(_controller.ModelState[string.Empty]!.Errors, Has.Count.EqualTo(1));
            Assert.That(_controller.ModelState[string.Empty]!.Errors[0].ErrorMessage, Is.EqualTo("sample error message for sample error code!"));
        });
    }

    [Test]
    public async Task Page07_Post_WithPreviousAction_ShouldRedirectToPage06()
    {
        var model = new LobbyistRegistrationStep03ViewModel
        {
            Id = 303,
            Action = FormAction.Previous
        };
        var result = await _controller.Page07(model) as RedirectToActionResult;
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page06"));
            Assert.That(result.RouteValues["id"], Is.EqualTo(303));
        });
    }

    [Test]
    public async Task Page07_Post_WithInvalidAction_ShouldReturnViewWithModelError()
    {
        var model = new LobbyistRegistrationStep03ViewModel
        {
            Id = 404,
            Action = (FormAction)999
        };
        var result = await _controller.Page07(model) as ViewResult;
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.ContainsKey(string.Empty), Is.True);
        });
    }

    #endregion

    #region Page 08

    [Test]
    public void Page08_Get_WithValidModelState_ShouldReturnViewWithModel()
    {
        var before = DateTime.Now;
        var id = 101;

        var model = new FilerPortal.Models.Registrations.ConfirmationViewModel
        {
            Id = id,
            ExecutedOn = DateTime.Now,
            IsSubmission = true,
        };

        _ = _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.Page08GetViewModel(It.IsAny<long>()))
            .Returns(model);

        var result = _controller.Page08(id) as ViewResult;
        Assert.That(result, Is.Not.Null);

        var after = DateTime.Now;

        var resultModel = result.Model as FilerPortal.Models.Registrations.ConfirmationViewModel;
        Assert.That(resultModel, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(resultModel.Id, Is.EqualTo(id));
            Assert.That(resultModel.IsSubmission, Is.EqualTo(true));
            Assert.That(resultModel.ExecutedOn, Is.GreaterThanOrEqualTo(before).And.LessThanOrEqualTo(after));
        });
    }

    [Test]
    public void Page08_Get_WithInvalidModelState_ShouldReturnNotFound()
    {
        _controller.ModelState.AddModelError("Error", "Invalid");
        var result = _controller.Page08(101);
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void Page08_Post_WithValidModelState_ShouldShowToastAndRedirect()
    {
        // Arrange
        var model = new ConfirmationViewModel
        {
            Id = 123,
            IsSubmission = true
        };

        // Act
        var result = _controller.Page08(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });

        _toastServiceMock.Verify(
    t => t.Success(It.IsAny<string>(), true, "Right", "Bottom", 0), Times.Once);
    }

    [Test]
    public void Page08_Post_WithInvalidModelState_ShouldReturnView()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid");

        var model = new ConfirmationViewModel
        {
            Id = 456,
            IsSubmission = true
        };

        // Act
        var result = _controller.Page08(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    #endregion

    #region Amend

    [Test]
    public async Task Amend_WhenArgumentExceptionThrown_ShouldShowToastAndRedirect()
    {
        // Arrange
        long inputId = 123;
        var errorMessage = "Invalid ID";

        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.CreateAmendLobbyistRegistration(inputId))
            .ThrowsAsync(new ArgumentException(errorMessage));

        // Act
        var result = await _controller.Amend(inputId, _lobbyistApiMock.Object, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Amend_WhenModelStateInvalid_ShouldRedirectToDashboard()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.Amend(123, _lobbyistApiMock.Object, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Amend_WhenSuccessful_ShouldMapAndRedirectToPage02()
    {
        // Arrange
        long inputId = 123;
        long newRegistrationId = 456;

        var model = new LobbyistRegistrationStep01ViewModel();
        var amendLobbyist = new LobbyistResponseDto(
            addresses: new List<AddressDtoModel>(),
            addressListId: 1,
            dateQualified: DateTime.UtcNow,
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 789,
            id: newRegistrationId,
            name: "Test Lobbyist",
            phoneNumberListId: 1,
            phoneNumbers: new List<PhoneNumberDto>(),
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 1,
            agencies: [],
            completedCourseDate: DateTime.UtcNow,
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: DateTime.UtcNow,
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Test",
            middleName: "M",
            lastName: "User",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyistEmployerOrLobbyingFirmName: "Test Firm Name",
            lobbyOnlySpecifiedAgencies: false,
            photo: "",
            selfRegister: true,
            isSameAsCandidateAddress: true,
            withdrawnAt: null,
            terminatedAt: null,
            effectiveDateOfChanges: null,
            type: "Lobbyist"
        );

        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.CreateAmendLobbyistRegistration(inputId))
            .ReturnsAsync(newRegistrationId);

        _lobbyistApiMock
            .Setup(api => api.GetLobbyistRegistration(newRegistrationId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(amendLobbyist);

        // Act
        var result = await _controller.Amend(inputId, _lobbyistApiMock.Object, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page02"));
            Assert.That(result.RouteValues!["id"], Is.EqualTo(newRegistrationId));
        });

        _lobbyistRegistrationCtlSvcMock.Verify(
            svc => svc.MappingViewModelFromDto(It.IsAny<LobbyistRegistrationStep01ViewModel>(), amendLobbyist),
            Times.Once);
    }

    [Test]
    public async Task PopulateReferenceData_WithLobbyistEmployerId_ShouldSetContactOrganizationName()
    {
        // Arrange
        var model = new LobbyistRegistrationStep01ViewModel
        {
            LobbyistEmployerOrLobbyingFirmId = 999
        };

        var expectedName = "Test Organization";

        _referenceDataApiMock
            .Setup(api => api.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>());

        _filingsApiMock
            .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
            {
            new(
                id: 1,
                name: "Test Session",
                startDate: DateTime.Now.AddMonths(-1),
                endDate: DateTime.Now.AddMonths(1)
            )
            }));

        _lobbyistApiMock
            .Setup(api => api.SearchLobbyistEmployerOrLobbyingFirmByIdOrName("999", It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Generated.LobbyistEmployerOrLobbyistFirmSearchResultDto>
            {
            new(id: 999, name: expectedName)
            });

        var methodInfo = typeof(LobbyistRegistrationController)
            .GetMethod("PopulateReferenceData", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

        Assert.That(methodInfo, Is.Not.Null, "Could not find PopulateReferenceData method via reflection.");

        var task = (Task<LobbyistRegistrationStep01ViewModel>)methodInfo.Invoke(
            null,
            new object[] { model, _referenceDataApiMock.Object, _filingsApiMock.Object, _lobbyistApiMock.Object }
        )!;

        var updatedModel = await task;

        // Assert
        Assert.That(updatedModel.Contact, Is.Not.Null);
        Assert.That(updatedModel.Contact.OrganizationName, Is.EqualTo(expectedName));
    }

    #endregion

    #region Termination
    [Test]
    public void TerminationNotice_WhenArgumentExceptionThrown_ShouldShowToast()
    {
        // Arrange
        long id = 123;
        var errorMessage = "Invalid ID";
        Services.Business.FilerRegistration.Registrations.Models.LobbyistResponseDto? lobbyist = new()
        {
            Id = id,
            WithdrawnAt = DateTime.Now,
            LegislativeSessionId = 1,
        };

        _lobbyistRegistrationSvcMock.Setup(c => c.GetRegistrationById(id))
            .ThrowsAsync(new ArgumentException(errorMessage));

        // Act & Assert
        var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                 await _controller.TerminationNotice(id, _filingsApiMock.Object));

        Assert.That(exception, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(exception.Message, Is.EqualTo(errorMessage));
        });
    }

    [Test]
    public async Task TerminationNotice_WhenModelStateInvalid_ShouldShowError()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.TerminationNotice(123, _filingsApiMock.Object) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task TerminationNotice_WhenModelStateValid_ShouldShowNoticeData()
    {
        // Arrange
        long id = 1;

        Services.Business.FilerRegistration.Registrations.Models.LobbyistResponseDto? lobbyist = new()
        {
            Id = id,
            LegislativeSessionId = 1,
            Name = "test",
            FirstName = "test",
            LastName = "test",
        };

        LobbyistTerminationNoticeViewModel viewMode = new()
        {
            Id = id,
            LegislativeSessionId = 1,
            Name = "test",
            FirstName = "test",
            LastName = "test",
        };

        _lobbyistRegistrationSvcMock.Setup(c => c.GetRegistrationById(id))
                        .Returns(Task.FromResult<Services.Business.FilerRegistration.Registrations.Models.LobbyistResponseDto?>(lobbyist));
        _lobbyistRegistrationCtlSvcMock.Setup(c => c.MapTerminateLobbyistRegistrationViewModel(lobbyist))
                        .Returns(viewMode);

        _filingsApiMock
        .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
        .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
        {
                            new
                            (
                                id: 1,
                                name: "Test Session",
                                startDate : DateTime.Now,
                                endDate: DateTime.Now.AddYears(1)
                            )
        }));

        // Act
        var result = await _controller.TerminationNotice(id, _filingsApiMock.Object);

        var viewResult = result as ViewResult;
        var model = viewResult?.Model as LobbyistTerminationNoticeViewModel;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(model, Is.Not.Null);
            Assert.That(model!.LegislativeSessionId, Is.EqualTo(lobbyist.LegislativeSessionId));
            Assert.That(model.Name, Is.EqualTo(lobbyist.Name));
            Assert.That(model.FirstName, Is.EqualTo(lobbyist.FirstName));
            Assert.That(model.LastName, Is.EqualTo(lobbyist.LastName));
        });
    }

    [Test]
    public async Task TerminationNotice_WhenRegistrationNotFound_ShouldReturnNotFound()
    {
        // Arrange
        long id = 1;

        Services.Business.FilerRegistration.Registrations.Models.LobbyistResponseDto? lobbyist = new()
        {
            Id = id,
            LegislativeSessionId = 1,
            Name = "test",
            FirstName = "test",
            LastName = "test",
        };

        LobbyistTerminationNoticeViewModel viewMode = new()
        {
            Id = id,
            LegislativeSessionId = 1,
            Name = "test",
            FirstName = "test",
            LastName = "test",
        };

        _lobbyistRegistrationSvcMock.Setup(c => c.GetRegistrationById(id))
                        .Returns(Task.FromResult<Services.Business.FilerRegistration.Registrations.Models.LobbyistResponseDto?>(null));

        // Act
        var result = await _controller.TerminationNotice(id, _filingsApiMock.Object);

        var viewResult = result as BadRequestResult;

        // Assert
        Assert.That(viewResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult.StatusCode, Is.EqualTo(StatusCodes.Status400BadRequest));
        });
    }

    [Test]
    public async Task TerminationNoticePost_WhenModelStateInvalid_NoChanges()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model state");
        var model = new LobbyistTerminationNoticeViewModel
        {
            Id = 1,
            FirstName = "test",
            LastName = "test",
            LegislativeSessionId = 1,
            ExecutedOn = DateTime.Now,
            Action = FormAction.Continue
        };
        // Act
        var result = await _controller.TerminationNotice(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<LobbyistTerminationNoticeViewModel>());

    }

    [Test]
    public async Task TerminationNoticePost_RedirectToCloseAction()
    {
        // Arrange
        var model = new LobbyistTerminationNoticeViewModel
        {
            Id = 1,
            FirstName = "test",
            LastName = "test",
            LegislativeSessionId = 1,
            ExecutedOn = DateTime.Now,
            Action = FormAction.Close
        };
        // Act
        var result = await _controller.TerminationNotice(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        // Assert redirect to dashboard
        Assert.Multiple(() =>
        {
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
            Assert.That(result.ActionName, Is.EqualTo("Index"));
        });
    }

    [Test]
    [TestCase(FormAction.Continue)]
    [TestCase(FormAction.SaveAndClose)]
    [TestCase(FormAction.Close)]
    [TestCase(FormAction.Cancel)]
    public async Task TerminationNoticePost_ContinueNextPage(FormAction action)
    {
        // Arrange
        var id = 1;
        var draftId = 1;
        var model = new LobbyistTerminationNoticeViewModel
        {
            Id = id,
            FirstName = "test",
            LastName = "test",
            LegislativeSessionId = 1,
            ExecutedOn = DateTime.Now,
            Action = action,
            EffectiveDateOfTermination = DateTime.Now,
        };
        var reponse = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto();

        _lobbyistRegistrationSvcMock.Setup(c => c.SaveLobbyistRegistrationTermination(It.IsAny<long>(), It.IsAny<Services.Business.FilerRegistration.Registrations.Models.LobbyistRegistrationTerminationRequestDto>()))
                                    .Returns(Task.FromResult(reponse));

        // Act
        var result = await _controller.TerminationNotice(model);

        // Assert
        Assert.That(result, Is.Not.Null);
        switch (action)
        {
            case FormAction.SaveAndClose:
            case FormAction.Close:
                if (action == FormAction.SaveAndClose)
                {
                    _lobbyistRegistrationSvcMock
                        .Verify(c => c.SaveLobbyistRegistrationTermination(It.Is<long>(c => c == id), It.Is<Services.Business.FilerRegistration.Registrations.Models.LobbyistRegistrationTerminationRequestDto>(c => c.StatusId == draftId)), Times.Exactly(1));
                }

                Assert.Multiple(() =>
                {
                    var redirectAction = result as RedirectToActionResult;
                    Assert.That(redirectAction, Is.Not.Null);
                    Assert.That(redirectAction?.ActionName, Is.EqualTo("Index"));
                    Assert.That(redirectAction?.ControllerName, Is.EqualTo("Dashboard"));
                });
                break;
            case FormAction.Continue:
                Assert.Multiple(() =>
                {
                    var redirectAction = result as RedirectToActionResult;
                    Assert.That(redirectAction, Is.Not.Null);
                    Assert.That(redirectAction?.ActionName, Is.EqualTo("Verification"));
                });
                break;
            default:
                var viewResult = result as ViewResult;
                var resultModel = viewResult?.Model as LobbyistTerminationNoticeViewModel;
                Assert.Multiple(() =>
                {
                    Assert.That(viewResult, Is.Not.Null);
                    Assert.That(resultModel, Is.Not.Null);
                    Assert.That(resultModel?.Id, Is.EqualTo(model.Id));
                });
                break;
        }
    }

    [Test]
    public async Task TerminationNoticePost_InValidateOnContinue()
    {
        // Arrange
        var model = new LobbyistTerminationNoticeViewModel
        {
            Id = 1,
            FirstName = "test",
            LastName = "test",
            LegislativeSessionId = 1,
            ExecutedOn = DateTime.Now,
            Action = FormAction.Continue
        };
        // Act
        var result = await _controller.TerminationNotice(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var state = _controller.ModelState;
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.TypeOf<LobbyistTerminationNoticeViewModel>());
            Assert.That(state.ErrorCount, Is.EqualTo(1));
            Assert.That(_controller.ModelState.ContainsKey("EffectiveDateOfTermination"), Is.True);
            Assert.That(_controller.ModelState["EffectiveDateOfTermination"]!.Errors, Has.Count.EqualTo(1));
            Assert.That(_controller.ModelState["EffectiveDateOfTermination"]!.Errors[0].ErrorMessage, Is.EqualTo("FilerPortal.TerminateLobbyistRegistration.RequiredTerminatedAt"));

        });
    }

    [Test]
    public async Task TerminationNoticePost_SubmitPage()
    {
        // Arrange
        var model = new LobbyistTerminationNoticeViewModel
        {
            Id = 1,
            FirstName = "test",
            LastName = "test",
            LegislativeSessionId = 1,
            ExecutedOn = DateTime.Now,
            Action = FormAction.Submit
        };
        // Act
        var result = await _controller.TerminationNotice(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var state = _controller.ModelState;
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.TypeOf<LobbyistTerminationNoticeViewModel>());
            Assert.That(state.ErrorCount, Is.EqualTo(1));
            Assert.That(_controller.ModelState.ContainsKey(string.Empty), Is.True);
            Assert.That(_controller.ModelState[string.Empty]!.Errors, Has.Count.EqualTo(1));
            Assert.That(_controller.ModelState[string.Empty]!.Errors[0].ErrorMessage, Is.EqualTo("Common.InvalidSubmission"));

        });
    }


    [Test]
    public void TerminationVerification_WhenArgumentExceptionThrown_ShouldShowToast()
    {
        // Arrange
        long inputId = 123;
        var errorMessage = "Invalid ID";

        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.GetLobbyistVerificationViewModel(inputId))
            .ThrowsAsync(new ArgumentException(errorMessage));

        _authorizationSvcMock
            .Setup(au => au.IsAuthorized(It.IsAny<Services.Business.Authorization.AuthorizationRequest>()))
            .Returns(Task.FromResult(true));

        // Act & Assert
        var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                 await _controller.Verification(inputId));

        Assert.That(exception, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(exception.Message, Is.EqualTo(errorMessage));
        });
    }

    [Test]
    public async Task TerminationVerification_WhenModelStateInvalid_ShouldShowError()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.Verification(123) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task TerminationVerification_WhenModelStateValid_ShouldShowVerificationData()
    {
        // Arrange
        long inputId = 1;
        var current = DateTime.Now;
        var data = new LobbyistVerificationViewModel
        {
            Id = inputId,
            FirstName = "Jack",
            LastName = "Leee",
            Email = "<EMAIL>",
            ExecutedOn = current,
        };

        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.GetLobbyistVerificationViewModel(inputId))
            .Returns(Task.FromResult(data));

        _authorizationSvcMock
            .Setup(au => au.IsAuthorized(It.IsAny<Services.Business.Authorization.AuthorizationRequest>()))
            .Returns(Task.FromResult(true));

        // Act
        var result = await _controller.Verification(inputId);

        var viewResult = result as ViewResult;
        var model = viewResult?.Model as LobbyistVerificationViewModel;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(model, Is.Not.Null);
            Assert.That(model!.FirstName, Is.EqualTo(data.FirstName));
            Assert.That(model.LastName, Is.EqualTo(data.LastName));
            Assert.That(model.Email, Is.EqualTo(data.Email));
        });
    }

    [Test]
    public async Task TerminationConfirmation_WhenModelStateInvalid_ShouldShowError()
    {
        // Arrange
        var registrationType = "registrationType";
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.Confirmation(123, registrationType) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task TerminationConfirmation_WhenModelStateValid_ShouldShowConfirmationData()
    {
        // Arrange
        var id = 123;
        var registrationType = "registrationType";
        var pendingItems = new List<PendingItemDto> { };
        var data = new LobbyistConfirmationViewModel
        {
            Id = id,
            ExecutedOn = DateTime.Now,
            IsSubmission = true,
            PendingItems = [.. pendingItems.Select(x => new PendingItemSharedViewModel
                {
                    Item = x.Item,
                    Status = x.Status,
                })],
        };

        _lobbyistRegistrationCtlSvcMock
            .Setup(svc => svc.GetConfirmationViewModel(id))
            .Returns(Task.FromResult<LobbyistConfirmationViewModel?>(data));

        // Act
        var result = await _controller.Confirmation(123, registrationType) as ViewResult;
        var model = result?.Model as LobbyistConfirmationViewModel;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(model, Is.Not.Null);
            Assert.That(model!.Id, Is.EqualTo(data.Id));
            Assert.That(model.IsSubmission, Is.EqualTo(data.IsSubmission));
            Assert.That(model.ExecutedOn, Is.EqualTo(data.ExecutedOn));
        });
    }

    [Test]
    public async Task TerminationVerificationPost_WhenModelStateInvalid_NoChanges()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model state");
        long inputId = 1;
        var current = DateTime.Now;
        var model = new LobbyistVerificationViewModel
        {
            Id = inputId,
            FirstName = "Jack",
            LastName = "Leee",
            Email = "<EMAIL>",
            ExecutedOn = current,
            Action = FormAction.Continue
        };

        // Act
        var result = await _controller.Verification(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<LobbyistVerificationViewModel>());

    }

    [Test]
    public async Task TerminationVerificationPost_RedirectToCloseAction()
    {
        // Arrange
        var model = new LobbyistVerificationViewModel
        {
            Id = 1,
            FirstName = "Jack",
            LastName = "Leee",
            Email = "<EMAIL>",
            ExecutedOn = DateTime.Now,
            Action = FormAction.SaveAndClose
        };
        // Act
        var result = await _controller.Verification(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        // Assert redirect to dashboard
        Assert.Multiple(() =>
        {
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
            Assert.That(result.ActionName, Is.EqualTo("Index"));
        });
    }

    [Test]
    [TestCase(true)]
    [TestCase(false)]
    public async Task TerminationVerificationPost_Successfully(bool allowAttest)
    {
        // Arrange
        var model = new LobbyistVerificationViewModel
        {
            Id = 1,
            FirstName = "Jack",
            LastName = "Leee",
            Email = "<EMAIL>",
            ExecutedOn = DateTime.Now,
            Action = FormAction.Continue,
            EffectiveDateOfTermination = DateTime.UtcNow,
            LegislativeSessionId = 1,
            IsUserAuthorizedToAttest = allowAttest
        };
        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto()
        {
            Valid = true
        };

        _lobbyistRegistrationSvcMock.Setup(c => c.SendLobbyistRegistrationTermination(It.IsAny<long>(), It.IsAny<Services.Business.FilerRegistration.Registrations.Models.LobbyistRegistrationTerminationRequestDto>()))
                                    .Returns(Task.FromResult(response));

        // Act
        var result = await _controller.Verification(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            if (allowAttest)
            {
                Assert.That(result.ActionName, Is.EqualTo("Confirmation"));
            }
            else
            {
                Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
                Assert.That(result.ActionName, Is.EqualTo("Index"));
            }
        });
    }

    [Test]
    [TestCase(true)]
    [TestCase(false)]
    public async Task WithdrawVerificationPost_Successfully(bool allowAttest)
    {
        // Arrange
        var model = new LobbyistVerificationViewModel
        {
            Id = 1,
            FirstName = "Jack",
            LastName = "Leee",
            Email = "<EMAIL>",
            ExecutedOn = DateTime.Now,
            Action = FormAction.Continue,
            EffectiveDateOfTermination = DateTime.UtcNow,
            LegislativeSessionId = 1,
            IsUserAuthorizedToAttest = allowAttest,
            Type = RegistrationConstants.RegistrationType.LobbyistWithdrawal
        };
        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto()
        {
            Valid = true
        };

        _lobbyistRegistrationSvcMock.Setup(c => c.SendLobbyistRegistrationWithdrawal(It.IsAny<long>(), It.IsAny<Services.Business.FilerRegistration.Registrations.Models.WithdrawLobbyistRegistrationRequestDto>()))
            .Returns(Task.FromResult(response));

        // Act
        var result = await _controller.Verification(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            if (allowAttest)
            {
                Assert.That(result.ActionName, Is.EqualTo("Confirmation"));
            }
            else
            {
                Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
                Assert.That(result.ActionName, Is.EqualTo("Index"));
            }
        });
    }

    [Test]
    [TestCase(true)]
    [TestCase(false)]
    public async Task WithdrawVerificationPost_ReturnPrevious(bool allowAttest)
    {
        // Arrange
        var model = new LobbyistVerificationViewModel
        {
            Id = 1,
            FirstName = "Jack",
            LastName = "Leee",
            Email = "<EMAIL>",
            ExecutedOn = DateTime.Now,
            Action = FormAction.Previous,
            EffectiveDateOfTermination = DateTime.UtcNow,
            LegislativeSessionId = 1,
            IsUserAuthorizedToAttest = allowAttest,
            Type = RegistrationConstants.RegistrationType.LobbyistWithdrawal
        };
        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto()
        {
            Valid = true
        };

        _lobbyistRegistrationSvcMock.Setup(c => c.SendLobbyistRegistrationWithdrawal(It.IsAny<long>(), It.IsAny<Services.Business.FilerRegistration.Registrations.Models.WithdrawLobbyistRegistrationRequestDto>()))
            .Returns(Task.FromResult(response));

        // Act
        var result = await _controller.Verification(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("WithdrawRegistration"));
    }

    [Test]
    public async Task TerminationVerificationPost_ContinueNextPage_DecisionError()
    {
        // Arrange
        var model = new LobbyistVerificationViewModel
        {
            Id = 1,
            FirstName = "Jack",
            Email = "<EMAIL>",
            LastName = "Leee",
            ExecutedOn = DateTime.Now,
            EffectiveDateOfTermination = DateTime.UtcNow,
            Action = FormAction.Continue,
            LegislativeSessionId = 1,
        };

        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto()
        {
            Valid = false,
            ValidationErrors = new List<CalAccess.Models.Common.WorkFlowError>()
            {
                new("Field 1", "", "", "{{Field Name}} is required"),
                new("Field 2", "", "", "Field 2 is required")
            }
        };

        _lobbyistRegistrationSvcMock.Setup(c => c.SendLobbyistRegistrationTermination(It.IsAny<long>(), It.IsAny<Services.Business.FilerRegistration.Registrations.Models.LobbyistRegistrationTerminationRequestDto>()))
                                    .Returns(Task.FromResult(response));

        // Act
        var result = await _controller.Verification(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            var viewModel = result.Model as LobbyistVerificationViewModel;
            Assert.That(viewModel, Is.Not.Null);
            Assert.That(viewModel!.Email, Is.EqualTo(model.Email));
            Assert.That(_controller!.ModelState, Is.Not.Empty);
            Assert.That(_controller!.ModelState, Has.Count.EqualTo(response.ValidationErrors.Count));
        });
    }

    [Test]
    public async Task TerminationVerificationPost_WrongAction()
    {
        // Arrange
        var model = new LobbyistVerificationViewModel
        {
            Id = 1,
            FirstName = "Jack",
            LastName = "Leee",
            Email = "<EMAIL>",
            ExecutedOn = DateTime.Now,
            Action = FormAction.Submit
        };
        // Act
        var result = await _controller.Verification(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var state = _controller.ModelState;
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.TypeOf<LobbyistVerificationViewModel>());
            Assert.That(state.ErrorCount, Is.EqualTo(1));
            Assert.That(_controller.ModelState.ContainsKey(string.Empty), Is.True);
            Assert.That(_controller.ModelState[string.Empty]!.Errors, Has.Count.EqualTo(1));
            Assert.That(_controller.ModelState[string.Empty]!.Errors[0].ErrorMessage, Is.EqualTo("Common.InvalidSubmission"));

        });
    }

    #endregion

    #region Withdraw lobbyist registration
    [Test]
    public async Task WithdrawLobbyistRegistration_Get_ReturnView()
    {
        // Arrange
        DateTime withdrawnAt = DateTime.UtcNow;
        long id = 123;
        Services.Business.FilerRegistration.Registrations.Models.LobbyistResponseDto? lobbyist = new()
        {
            Id = id,
            WithdrawnAt = withdrawnAt,
            LegislativeSessionId = 1,
        };
        var model = new LobbyistRegistrationWithdrawalViewModel()
        {
            Id = id,
            EffectiveDateOfWithdrawal = withdrawnAt,
            LegislativeSessionId = 1,
        };

        _lobbyistRegistrationSvcMock.Setup(c => c.GetRegistrationById(id))
                                    .Returns(Task.FromResult<Services.Business.FilerRegistration.Registrations.Models.LobbyistResponseDto?>(lobbyist));
        _lobbyistRegistrationCtlSvcMock.Setup(c => c.MapWidthdrawLobbyistRegistrationViewModel(It.IsAny<Services.Business.FilerRegistration.Registrations.Models.LobbyistResponseDto>()))
                                   .Returns(model);
        _filingsApiMock
        .Setup(api => api.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
        .ReturnsAsync(new LegislativeSessionResponseList(new List<LegislativeSessionResponse>
        {
                    new
                    (
                        id: 1,
                        name: "Test Session",
                        startDate : DateTime.Now,
                        endDate: DateTime.Now.AddYears(1)
                    )
        }));

        // Act
        var result = await _controller.WithdrawRegistration(id, _filingsApiMock.Object) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            var model = result.Model as LobbyistRegistrationWithdrawalViewModel;
            Assert.That(model, Is.Not.Null);
            Assert.That(model?.Id, Is.EqualTo(id));
            Assert.That(model?.EffectiveDateOfWithdrawal, Is.EqualTo(withdrawnAt));
        });
    }

    [Test]
    public async Task WithdrawLobbyistRegistration_Get_ReturnNotFoundPage()
    {
        // Arrange
        long id = 123;
        Services.Business.FilerRegistration.Registrations.Models.LobbyistResponseDto? lobbyist = null;

        _lobbyistRegistrationSvcMock.Setup(c => c.GetRegistrationById(id)).Returns(Task.FromResult(lobbyist));

        // Act
        var result = await _controller.WithdrawRegistration(id, _filingsApiMock.Object) as NotFoundResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.StatusCode, Is.EqualTo(StatusCodes.Status404NotFound));
        });
    }

    [Test]
    [TestCase(FormAction.Continue)]
    [TestCase(FormAction.SaveAndClose)]
    [TestCase(FormAction.Cancel)]
    public async Task WithdrawLobbyistRegistration_Post_Valid(FormAction action)
    {
        // Arrange
        long id = 123;
        long draftId = 1;
        DateTime withdrawnAt = DateTime.UtcNow;
        var model = new LobbyistRegistrationWithdrawalViewModel()
        {
            Id = id,
            EffectiveDateOfWithdrawal = withdrawnAt,
            LegislativeSessionId = 1,
            Action = action
        };
        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto();
        response.ValidationErrors = new List<WorkFlowError>();

        _lobbyistRegistrationSvcMock.Setup(c => c.WithdrawLobbyistRegistration(It.IsAny<long>(), It.IsAny<Services.Business.FilerRegistration.Registrations.Models.WithdrawLobbyistRegistrationRequestDto>()))
                                    .Returns(Task.FromResult(response));

        // Act
        var result = await _controller.WithdrawRegistration(model);

        // Assert
        Assert.That(result, Is.Not.Null);
        switch (action)
        {
            case FormAction.SaveAndClose:
                _lobbyistRegistrationSvcMock
                    .Verify(c => c.WithdrawLobbyistRegistration(It.Is<long>(c => c == id), It.Is<Services.Business.FilerRegistration.Registrations.Models.WithdrawLobbyistRegistrationRequestDto>(c => c.StatusId == draftId)), Times.Exactly(1));

                Assert.Multiple(() =>
                {
                    var redirectAction = result as RedirectToActionResult;
                    Assert.That(redirectAction, Is.Not.Null);
                    Assert.That(redirectAction?.ActionName, Is.EqualTo("Index"));
                    Assert.That(redirectAction?.ControllerName, Is.EqualTo("Dashboard"));
                });
                break;
            case FormAction.Continue:
                _lobbyistRegistrationSvcMock
                    .Verify(c => c.WithdrawLobbyistRegistration(It.Is<long>(c => c == id), It.Is<Services.Business.FilerRegistration.Registrations.Models.WithdrawLobbyistRegistrationRequestDto>(c => c.StatusId == draftId)), Times.Exactly(1));

                Assert.Multiple(() =>
                {
                    var redirectAction = result as RedirectToActionResult;
                    Assert.That(redirectAction, Is.Not.Null);
                });
                break;
            case FormAction.Cancel:
                break;
            default:
                var viewResult = result as ViewResult;
                var resultModel = viewResult?.Model as LobbyistRegistrationWithdrawalViewModel;
                Assert.Multiple(() =>
                {
                    Assert.That(viewResult, Is.Not.Null);
                    Assert.That(resultModel, Is.Not.Null);
                    Assert.That(viewResult?.ViewName, Is.EqualTo("WithdrawRegistration"));
                    Assert.That(resultModel?.Id, Is.EqualTo(model.Id));
                    Assert.That(resultModel?.EffectiveDateOfWithdrawal, Is.EqualTo(model.EffectiveDateOfWithdrawal));
                });
                break;
        }
    }

    [Test]
    public async Task WithdrawLobbyistRegistration_Post_ModelInvalid()
    {
        // Arrange
        var model = new LobbyistRegistrationWithdrawalViewModel()
        {
            Id = 1,
            Action = FormAction.Continue
        };
        var response = new Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto
        {
            Id = 1,
            Valid = false,
            ValidationErrors =
            [
                new WorkFlowError(
                    FieldName: "Error",
                    ErrorCode: "Error",
                    ErrorType: "Error",
                    Message: "Error"
                ),
                new("Field 1", "", "", "{{Field Name}} is required"),
                new("Field 2", "", "", "Field 2 is required")
            ],
            StatusId = 3,
            ApprovedAt = null,
            FilerId = 1,
            Notifications = new(),
            AddressValidationResult = null
        };

        _lobbyistRegistrationSvcMock.Setup(c => c.WithdrawLobbyistRegistration(It.IsAny<long>(), It.IsAny<Services.Business.FilerRegistration.Registrations.Models.WithdrawLobbyistRegistrationRequestDto>()))
            .Returns(Task.FromResult(response));

        // Act
        var result = await _controller.WithdrawRegistration(model);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            var viewResult = result as ViewResult;
            var resultModel = viewResult?.Model as LobbyistRegistrationWithdrawalViewModel;
            Assert.That(viewResult, Is.Not.Null);
            Assert.That(resultModel, Is.Not.Null);
            Assert.That(resultModel?.Id, Is.EqualTo(model.Id));
        });
    }
    #endregion
}

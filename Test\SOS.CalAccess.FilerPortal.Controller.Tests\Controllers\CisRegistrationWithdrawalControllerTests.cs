using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Localization;
using Moq;
using NSubstitute;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices.CisRegistrationWithdrawalCtlSvc;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations.CisRegistrationWithdrawal;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(nameof(CisRegistrationWithdrawalController))]
internal sealed class CisRegistrationWithdrawalControllerTests : IDisposable
{
    private Mock<IStringLocalizer<SharedResources>> _localizer;
    private ICisRegistrationWithdrawalCtlSvc _cisRegistrationWithdrawalCtlSvc;
    private IAuthorizationSvc _authorizationSvc;
    private IToastService _toastService;

    private CisRegistrationWithdrawalController _controller;

    public void Dispose()
    {
        _controller.Dispose();
    }

    [SetUp]
    public void Setup()
    {
        _localizer = new Mock<IStringLocalizer<SharedResources>>();
        _cisRegistrationWithdrawalCtlSvc = Substitute.For<ICisRegistrationWithdrawalCtlSvc>();
        _authorizationSvc = Substitute.For<IAuthorizationSvc>();
        _toastService = Substitute.For<IToastService>();

        var localizedString = new LocalizedString("key", "text");
        _localizer.Setup(x => x[It.IsAny<string>()]).Returns(localizedString);

        _localizer.Setup(l => l[ResourceConstants.WithdrawalCisTitle]).Returns(new LocalizedString(ResourceConstants.WithdrawalCisTitle, "Withdraw Registration"));
        _localizer.Setup(l => l[ResourceConstants.FilerPortalTitle]).Returns(new LocalizedString(ResourceConstants.FilerPortalTitle, "Filer Portal"));
        _localizer.Setup(l => l[ResourceConstants.WithdrawalCisBreadcrumb]).Returns(new LocalizedString(ResourceConstants.WithdrawalCisBreadcrumb, "Candidate"));
        _localizer.Setup(l => l[ResourceConstants.WithdrawalCisStep01]).Returns(new LocalizedString(ResourceConstants.WithdrawalCisStep01, "Notice of withdrawal"));
        _localizer.Setup(l => l[ResourceConstants.WithdrawalCisStep02]).Returns(new LocalizedString(ResourceConstants.WithdrawalCisStep02, "Verification"));


        var tempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>());
        _controller = new CisRegistrationWithdrawalController(
            _cisRegistrationWithdrawalCtlSvc,
            _localizer.Object,
            _authorizationSvc,
            _toastService)
        {
            TempData = tempData
        };
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    #region Redirect to dashboard
    [Test]
    public void RedirectToDashboard_ReturnsRedirectToDashboardIndex()
    {
        // Act
        var result = _controller.RedirectToDashboard() as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }
    #endregion

    #region Index
    [Test]
    public async Task Index_Success_RedirectToPage01()
    {
        // Arrange
        long id = 1;
        _cisRegistrationWithdrawalCtlSvc.InitializeCisWithdrawal(Arg.Any<long>()).Returns(new MethodResult<long>(id));

        // Act
        var result = await _controller.Index(id) as RedirectToActionResult;

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page01"));
            Assert.That(result.RouteValues?["Id"], Is.EqualTo(1));
        });
    }

    [Test]
    public async Task Index_InvalidResult_RedirectsToDashboard()
    {
        // Arrange
        long id = 1;
        _cisRegistrationWithdrawalCtlSvc.InitializeCisWithdrawal(Arg.Any<long>()).Returns(new MethodResult<long>(new InvalidOperationException("Error")));

        // Act
        var result = await _controller.Index(id) as RedirectToActionResult;

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
        });
    }
    #endregion

    #region Edit
    [Test]
    public async Task Edit_Success_ReturnsRedirectToPage01()
    {
        // Arrange
        var id = 1;

        // Act
        var result = await _controller.Edit(id) as RedirectToActionResult;

        // Assert
        Assert.That(result!.ActionName, Is.EqualTo("Page01"));
    }

    [Test]
    public async Task Edit_InvalidModelState_ReturnsRedirectToDashboard()
    {
        // Arrange
        var id = 1;
        _controller.ModelState.AddModelError("Field", "Error");

        // Act
        var result = await _controller.Edit(id) as RedirectToActionResult;

        // Assert
        Assert.That(result!.ControllerName, Is.EqualTo("Dashboard"));
    }
    #endregion

    #region CompleteAttestation
    [Test]
    public async Task CompleteAttestation_AuthorizedAndValidModelState_RedirectsToPage02()
    {
        // Arrange
        const long registrationId = 123;
        _authorizationSvc.IsAuthorized(Arg.Any<AuthorizationRequest>()).Returns(true);
        _controller.ModelState.Clear();

        // Act
        var result = await _controller.CompleteAttestation(registrationId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.TypeOf<RedirectToActionResult>());
            var redirect = (RedirectToActionResult)result;
            Assert.That(redirect.ActionName, Is.EqualTo("Page02"));
            Assert.That(redirect.RouteValues["Id"], Is.EqualTo(registrationId));
        });
    }

    [Test]
    public async Task CompleteAttestation_InvalidModelState_RedirectsToDashboard()
    {
        // Arrange
        const long registrationId = 123;
        _authorizationSvc.IsAuthorized(Arg.Any<AuthorizationRequest>()).Returns(true);
        _controller.ModelState.AddModelError("key", "error");

        // Act
        var result = await _controller.CompleteAttestation(registrationId);

        // Assert
        Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task CompleteAttestation_Unauthorized_RedirectsToDashboardAndSetsToast()
    {
        // Arrange
        const long registrationId = 123;
        _authorizationSvc.IsAuthorized(Arg.Any<AuthorizationRequest>()).Returns(false);
        _controller.ModelState.Clear();

        // Act
        var result = await _controller.CompleteAttestation(registrationId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo("Index"));
            _toastService.Received(1).Error(_localizer.Object[CommonResourceConstants.UnauthorizedActionToast]);
        });
    }
    #endregion

    #region OnActionExecutting
    [Test]
    public void OnActionExecuting_SetsExpectedViewData()
    {
        // Arrange
        var httpContext = new DefaultHttpContext(); // Mock HttpContext
        var actionContext = new ActionContext(httpContext, new RouteData(), new ActionDescriptor());
        var context = new ActionExecutingContext(
            actionContext,
            new List<IFilterMetadata>(),
            new Dictionary<string, object?>(),
            _controller
        );

        // Act
        _controller.OnActionExecuting(context);

        // Assert
        var breadcrumbs = _controller.ViewData[LayoutConstants.Breadcrumbs] as List<Breadcrumb>;
        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(_controller.ViewData[LayoutConstants.Title], Is.EqualTo("Withdraw Registration"));
            Assert.That(_controller.ViewData["ProgressItem1Name"], Is.EqualTo("1. Notice of withdrawal"));
            Assert.That(_controller.ViewData["ProgressItem2Name"], Is.EqualTo("2. Verification"));
            Assert.That(breadcrumbs, Is.Not.Null);
            Assert.That(breadcrumbs?.Count, Is.EqualTo(2));
            if (breadcrumbs != null)
            {
                Assert.That(breadcrumbs[0].Name, Is.EqualTo("Filer Portal"));
                Assert.That(breadcrumbs[0].Url, Is.EqualTo("/FilerPortal"));
                Assert.That(breadcrumbs[1].Name, Is.EqualTo("Candidate"));
                Assert.That(breadcrumbs[1].Url, Is.EqualTo("/Candidate"));
            }
        });
    }

    #endregion

    #region Cancel
    [Test]
    public async Task Cancel_WhenModelStateIsInvalid_RedirectsToDashboard()
    {
        // Arrange
        _controller.ModelState.AddModelError("id", "Required");

        // Act
        var result = await _controller.Cancel(0);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirect, Is.Not.Null);
            Assert.That(redirect!.ActionName, Is.EqualTo("Index"));
            Assert.That(redirect.ControllerName, Is.Null.Or.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Cancel_WhenModelStateIsValid_RedirectsToDashboard()
    {
        // Arrange
        long id = 1;
        _cisRegistrationWithdrawalCtlSvc.CancelCisWithdrawal(Arg.Any<long>())
            .Returns(new MethodResult());

        // Act
        var result = await _controller.Cancel(id) as RedirectToActionResult;

        // Assert

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result!.ControllerName, Is.EqualTo("Dashboard"));
    }

    [Test]
    public async Task Cancel_InvalidResult_ReturnView()
    {
        // Arrange
        var id = 1;

        _cisRegistrationWithdrawalCtlSvc.CancelCisWithdrawal(Arg.Any<long>())
            .Returns(new MethodResult(new InvalidOperationException("Error")));

        // Act:
        var result = await _controller.Cancel(id) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result!.ControllerName, Is.EqualTo("Dashboard"));

    }
    #endregion

    #region Page01
    [Test]
    public async Task Page01_Success_ReturnsView()
    {
        // Arrange
        long id = 1;
        var model = new CisRegistrationWithdrawalPage01ViewModel();

        _cisRegistrationWithdrawalCtlSvc.Page01GetViewModel(Arg.Any<long>())
            .Returns(new MethodResult<CisRegistrationWithdrawalPage01ViewModel>(model));
        // Act
        var result = await _controller.Page01(id) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page01_ModelStateInvalid_ReturnsNotFound()
    {
        // Arrange
        var id = 1;
        _controller.ModelState.AddModelError("Field", "Error");

        // Act
        var result = await _controller.Page01(id);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task Page01_InvalidResult_ReturnView()
    {
        // Arrange
        var id = 1;
        _cisRegistrationWithdrawalCtlSvc.Page01GetViewModel(Arg.Any<long>())
            .Returns(new MethodResult<CisRegistrationWithdrawalPage01ViewModel>(new InvalidOperationException("Error")));
        // Act
        var result = await _controller.Page01(id);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page01_Post_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        var model = new CisRegistrationWithdrawalPage01ViewModel();
        _controller.ModelState.AddModelError("Field", "Error");

        // Act
        var result = await _controller.Page01(model);
        var viewResult = result as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(viewResult, Is.Not.Null);
            Assert.That(viewResult!.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task Page01_ActionContinue_ValidModelState_RedirectsToPage02()
    {
        // Arrange
        var id = 1;
        var model = new CisRegistrationWithdrawalPage01ViewModel { Action = FormAction.Continue, Id = id };
        // Act
        var result = await _controller.Page01(model);

        // Assert
        var redirect = (RedirectToActionResult)result;
        Assert.That(redirect.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(redirect.ActionName, Is.EqualTo("Page02"));
            Assert.That(redirect.RouteValues["Id"], Is.EqualTo(model.Id));
        });
    }

    [Test]
    public async Task Page01_ActionSaveAndClose_RedirectsToDashboard()
    {
        // Arrange
        var model = new CisRegistrationWithdrawalPage01ViewModel { Action = FormAction.SaveAndClose };

        // Act
        var res = await _controller.Page01(model);
        var result = res as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.Null.Or.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Page01_UnknownAction_AddsModelErrorAndReturnsView()
    {
        // Arrange
        var model = new CisRegistrationWithdrawalPage01ViewModel { Action = (FormAction)999 };

        // Act
        var res = await _controller.Page01(model);
        var result = res as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState[string.Empty]!.Errors, Has.Count.EqualTo(1));
            Assert.That(_controller.ModelState[string.Empty]!.Errors[0].ErrorMessage, Is.EqualTo("text"));
        });
    }
    #endregion

    #region Page02
    [Test]
    public async Task Page02_Get_ReturnsViewWithModel()
    {
        // Arrange
        var viewModel = new CisRegistrationWithdrawalPage02ViewModel { Id = 123, IsAttest = true };
        _cisRegistrationWithdrawalCtlSvc.Page02GetViewModel(123).Returns(new MethodResult<CisRegistrationWithdrawalPage02ViewModel>(viewModel));

        // Act
        var result = await _controller.Page02(123);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.Model, Is.EqualTo(viewModel));
    }

    [Test]
    public async Task Page02_Post_InvalidModel_ReturnsViewWithSameModel()
    {
        // Arrange
        var model = new CisRegistrationWithdrawalPage02ViewModel { Id = 123, IsAttest = true };
        _controller.ModelState.AddModelError("key", "error");

        // Act
        var result = await _controller.Page02(model);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page02_ActionContinue_InvalidResult_ReturnView()
    {
        // Arrange
        var id = 1;
        var model = new CisRegistrationWithdrawalPage02ViewModel { Action = FormAction.Continue, Id = id, IsAttest = false };
        _cisRegistrationWithdrawalCtlSvc.Page02Submit(Arg.Any<long>()).Returns(new MethodResult(new InvalidOperationException("Error")));
        // Act
        var result = await _controller.Page02(model);
        var viewResult = result as ViewResult;

        // Assert
        Assert.That(viewResult, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page02_Post_Previous_RedirectsToPage01()
    {
        // Arrange
        var model = new CisRegistrationWithdrawalPage02ViewModel
        {
            Id = 123,
            Action = FormAction.Previous,
            IsAttest = true
        };

        // Act
        var result = await _controller.Page02(model);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.That(redirect.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("Page01"));
            Assert.That(redirect.RouteValues["Id"], Is.EqualTo(123));
        });
    }

    [Test]
    public async Task Page02_Post_Continue_RedirectsToPage03()
    {
        // Arrange
        var model = new CisRegistrationWithdrawalPage02ViewModel
        {
            Id = 123,
            Action = FormAction.Continue,
            IsAttest = true
        };

        _cisRegistrationWithdrawalCtlSvc.Page02Submit(Arg.Any<long>())
            .Returns(new MethodResult());
        // Act
        var result = await _controller.Page02(model);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.That(redirect.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("Page03"));
            Assert.That(redirect.RouteValues["Id"], Is.EqualTo(123));
        });
    }

    [Test]
    public async Task Page02_Post_SaveAndClose_RedirectsToDashboard()
    {
        // Arrange
        var model = new CisRegistrationWithdrawalPage02ViewModel
        {
            Id = 123,
            Action = FormAction.SaveAndClose,
            IsAttest = true
        };

        // Act
        var result = await _controller.Page02(model);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.That(redirect.ActionName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task Page02_Post_InvalidAction_ReturnsViewWithModelError()
    {
        // Arrange
        var model = new CisRegistrationWithdrawalPage02ViewModel
        {
            Id = 123,
            Action = (FormAction)999,
            IsAttest = true
        };

        // Act
        var result = await _controller.Page02(model);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState[string.Empty].Errors, Has.Some.Matches<ModelError>(e => e.ErrorMessage == "text"));
        });
    }
    #endregion

    #region Page03
    [Test]
    public async Task Page03_Get_ReturnsViewWithModel()
    {
        // Arrange
        var id = 123;
        var viewModel = new CisRegistrationWithdrawalPage03ViewModel { Id = id, IsAttest = false };
        _cisRegistrationWithdrawalCtlSvc.Page03GetViewModel(Arg.Any<long>()).Returns(new MethodResult<CisRegistrationWithdrawalPage03ViewModel>(viewModel));

        // Act
        var result = await _controller.Page03(123);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.Model, Is.EqualTo(viewModel));
    }

    [Test]
    public async Task Page03_Get_InvalidResult_ReturnsViewWithModel()
    {
        // Arrange
        var id = 123;
        var viewModel = new CisRegistrationWithdrawalPage03ViewModel { Id = id, IsAttest = false };
        _cisRegistrationWithdrawalCtlSvc.Page03GetViewModel(Arg.Any<long>())
            .Returns(new MethodResult<CisRegistrationWithdrawalPage03ViewModel>(new InvalidOperationException("Error")));

        // Act
        var result = await _controller.Page03(123);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page03_Post_InvalidModel_ReturnsViewWithModel()
    {
        // Arrange
        var id = 1;
        _controller.ModelState.AddModelError("key", "error");

        // Act
        var result = await _controller.Page03(id);

        // Assert
        var viewResult = result;
        Assert.That(viewResult, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task Page03_Post_ActionClose_RedirectsToDashboard()
    {
        // Arrange
        var model = new CisRegistrationWithdrawalPage03ViewModel { Id = 123, Action = FormAction.Close, IsAttest = true };

        // Act
        var result = await _controller.Page03(model);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.That(redirect.ActionName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task Page03_Post_InvalidAction_ReturnsViewWithModelError()
    {
        // Arrange
        var model = new CisRegistrationWithdrawalPage03ViewModel { Id = 123, Action = (FormAction)999, IsAttest = true };

        // Act
        var result = await _controller.Page03(model);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.Model, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState[string.Empty].Errors, Has.Some.Matches<ModelError>(e => e.ErrorMessage == "text"));
        });
    }
    #endregion
}


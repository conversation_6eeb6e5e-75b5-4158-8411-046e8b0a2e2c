using System.Globalization;
using System.Net;
using System.Reflection;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using NSubstitute;
using Refit;
using SOS.CalAccess.FilerPortal.Constants;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Disclosure;
using SOS.CalAccess.FilerPortal.Models.Filings;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Transactions;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using FilerContactType = SOS.CalAccess.Models.FilerDisclosure.Contacts.FilerContactType;
using FilingSummaryStatus = SOS.CalAccess.FilerPortal.Generated.FilingSummaryStatus;
using FilingSummaryStatusModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryStatus;
using FilingSummaryTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;
using FilingType = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;


namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

[TestFixture]
public class DisclosureControllerTests
{
    private Mock<ILogger<DisclosureController>> _loggerMock;

    private Mock<ITempDataDictionaryFactory> _tempDataFactoryMock;
    private Mock<ITempDataDictionary> _tempDataMock;
    private Mock<IHttpContextAccessor> _httpContextAccessorMock;
    private Mock<IToastService> _toastService;
    private Mock<IStringLocalizer<SharedResources>> _localizerMock;
    private Mock<ILobbyistEmployerCoalitionApi> _lobbyistEmployerCoalitionApiMock;
    private Mock<IFilingsApi> _filingsApiMock;
    private Mock<IActivityExpenseApi> _activityExpenseApiMock;
    private Mock<IDisclosureCtlSvc> _disclosureCtlSvcMock;
    private DisclosureController _controller;

    private LobbyistEmployerReportResponse _sampleReport;

    [SetUp]
    public void Setup()
    {
        _loggerMock = new Mock<ILogger<DisclosureController>>();
        _tempDataFactoryMock = new Mock<ITempDataDictionaryFactory>();
        _tempDataMock = new Mock<ITempDataDictionary>();
        _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
        _toastService = new Mock<IToastService>();
        _localizerMock = new Mock<IStringLocalizer<SharedResources>>();
        _lobbyistEmployerCoalitionApiMock = new Mock<ILobbyistEmployerCoalitionApi>();
        _filingsApiMock = new Mock<IFilingsApi>();
        _activityExpenseApiMock = new Mock<IActivityExpenseApi>();
        _disclosureCtlSvcMock = new Mock<IDisclosureCtlSvc>();
        _toastService = new Mock<IToastService>();

        var httpContextMock = new Mock<HttpContext>();

        _tempDataFactoryMock.Setup(x => x.GetTempData(httpContextMock.Object)).Returns(_tempDataMock.Object);
        _httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContextMock.Object);

        // Mock the keys for Toast success/danger/warning
        _tempDataMock.Setup(td => td["ToastType"]).Returns("");
        _tempDataMock.Setup(td => td["ToastMessage"]).Returns("");
        _tempDataMock.Setup(td => td["ToastShowCloseButton"]).Returns("");
        _tempDataMock.Setup(td => td["ToastX"]).Returns("");
        _tempDataMock.Setup(td => td["ToastY"]).Returns("");
        _tempDataMock.Setup(td => td["ToastTimeOut"]).Returns("");

        _sampleReport = new LobbyistEmployerReportResponse(
            endDate: DateTime.Now,
            filerId: 12345,
            filerName: "Example Filer",
            id: 67890,
            isMemberOfLobbyingCoalition: true,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-6),
            status: 1,
            submittedDate: DateTime.Now.AddDays(-10),
            totalPaymentsToInHouseLobbyists: 50000.75,
            totalOverheadExpense: 1,
            totalUnderThresholdPayments: 2,
            diligenceStatementVerified: true,
            contributionsInExistingStatements: false,
            version: 2
        );

        _controller = new DisclosureController(_toastService.Object, _loggerMock.Object, _localizerMock.Object, _filingsApiMock.Object, _disclosureCtlSvcMock.Object);
        var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
        _controller.TempData = tempData;
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    [Test]
    public async Task Index_Invalid_ShouldReturnNotFound()
    {
        // Arrange
        var mockLobbyistEmployerCoalitionApi = new Mock<ILobbyistEmployerCoalitionApi>();
        var mockFilingsApi = new Mock<IFilingsApi>();
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.Index(filerId: null, filingId: null, viewName: null, reportType: null);

        // Assert
        Assert.That(result, Is.Not.Null, "Expected NotFoundResult when ModelState is invalid.");
    }

    [Test]
    public async Task Index_ShouldPopulateActivityExpensesGridModel()
    {
        // Arrange
        var filerId = "123";
        var longFilerId = long.Parse(filerId, CultureInfo.InvariantCulture);
        var filingId = "456";
        var cancellationToken = CancellationToken.None;
        var mockData = new List<ActivityExpenseItemResponse>
        {
            new(
                amount: 200.0,
                amountBenefiting: 200.0,
                contactId: null,
                filerId: longFilerId,
                id: 1,
                notes: "First activity expense",
                payeeName: "Payee person",
                payeeType: FilerContactType.Individual.Name,
                personBenefiting: "Who is benefiting",
                transactionDate: DateTime.Now
            ),
        };
        var expectedSubtotal = mockData.Sum(c => c.AmountBenefiting.GetValueOrDefault());

        var mockSession = new Mock<ISession>();

        var sessionKeys = new List<string>();

        mockSession.Setup(s => s.Keys).Returns(sessionKeys);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { Session = mockSession.Object }
        };

        _disclosureCtlSvcMock
        .Setup(x => x.BuildSummaryViewModel(
            FilingSummaryTypeModel.ActivityExpenseSummary.Name,
            filerId,
            filingId,
            "1",
            "1",
            null,
            It.IsAny<HttpContext>(),
            It.IsAny<ITempDataDictionary>(),
            cancellationToken))
        .ReturnsAsync(new DisclosureSummaryViewModel
        {
            ViewName = FilingSummaryTypeModel.ActivityExpenseSummary.Name,
            Id = long.Parse(filingId, CultureInfo.InvariantCulture),
            FilerId = long.Parse(filerId, CultureInfo.InvariantCulture),
            FilingSummaryId = long.Parse("1", CultureInfo.InvariantCulture),
            ActivityExpensesGridModel = new SmallDataGridModel
            {
                GridId = "ActivityExpensesGrid",
                DataSource = mockData,
                Columns = new List<DataGridColumn>
                {
                new() { Field = "TransactionDate" },
                new() { Field = "PayeeName" },
                new() { Field = "PayeeType" },
                new() { Field = "PersonBenefiting" },
                new() { Field = "AmountBenefiting" }
                }
            },
            Subtotal = expectedSubtotal
        });


        // Act
        var result = await _controller.Index(
            filerId,
            filingId,
            "1",
            "1",
            FilingSummaryTypeModel.ActivityExpenseSummary.Name,
            null,
            "1",
            cancellationToken
        ) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var model = result.Model as DisclosureSummaryViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.That(model.ActivityExpensesGridModel, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(model.ActivityExpensesGridModel.Columns.Any(c => c.Field == "TransactionDate"));
            Assert.That(model.ActivityExpensesGridModel.Columns.Any(c => c.Field == "PayeeName"));
            Assert.That(model.ActivityExpensesGridModel.Columns.Any(c => c.Field == "PayeeType"));
            Assert.That(model.ActivityExpensesGridModel.Columns.Any(c => c.Field == "PersonBenefiting"));
            Assert.That(model.ActivityExpensesGridModel.Columns.Any(c => c.Field == "AmountBenefiting"));
        });

        // Verify subtotal calculation
        Assert.That(result.ViewData["Subtotal"], Is.EqualTo(expectedSubtotal));
    }

    [Test]
    public async Task Index_ShouldPopulateActivityExpensesGridModel_RemoveSession()
    {
        // Arrange
        var filerId = "123";
        var longFilerId = long.Parse(filerId, CultureInfo.InvariantCulture);
        var filingId = "456";
        var longFilingId = long.Parse(filingId, CultureInfo.InvariantCulture);
        var cancellationToken = CancellationToken.None;
        var mockData = new List<ActivityExpenseItemResponse>
        {
            new(
                amount: 200.0,
                amountBenefiting: 200.0,
                contactId: null,
                filerId: longFilerId,
                id: 1,
                notes: "First activity expense",
                payeeName: "Payee person",
                payeeType: FilerContactType.Individual.Name,
                personBenefiting: "Who is benefiting",
                transactionDate: DateTime.Now
            ),
        };
        var expectedSubtotal = mockData.Sum(c => c.AmountBenefiting.GetValueOrDefault());

        var mockSession = new Mock<ISession>();
        var sessionKey = TransactionController.ActivityExpenseData;

        mockSession.Setup(s => s.Keys).Returns(new List<string> { sessionKey });

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { Session = mockSession.Object }
        };

        _disclosureCtlSvcMock
        .Setup(x => x.BuildSummaryViewModel(
            FilingSummaryTypeModel.ActivityExpenseSummary.Name,
            filerId,
            filingId,
            "1",
            "1",
            null,
            It.IsAny<HttpContext>(),
            It.IsAny<ITempDataDictionary>(),
            cancellationToken))
        .ReturnsAsync(new DisclosureSummaryViewModel
        {
            ViewName = FilingSummaryTypeModel.ActivityExpenseSummary.Name,
            Id = long.Parse(filingId, CultureInfo.InvariantCulture),
            FilerId = long.Parse(filerId, CultureInfo.InvariantCulture),
            FilingSummaryId = long.Parse("1", CultureInfo.InvariantCulture),
            ActivityExpensesGridModel = new SmallDataGridModel
            {
                GridId = "ActivityExpensesGrid",
                DataSource = mockData,
                Columns = new List<DataGridColumn>
                {
                        new() { Field = "TransactionDate" },
                        new() { Field = "PayeeName" },
                        new() { Field = "PayeeType" },
                        new() { Field = "PersonBenefiting" },
                        new() { Field = "AmountBenefiting" }
                }
            },
            Subtotal = expectedSubtotal
        });


        // Act
        var result = await _controller.Index(
            filerId,
            filingId,
            "1",
            "1",
            FilingSummaryTypeModel.ActivityExpenseSummary.Name,
            null,
            null,
            cancellationToken
        ) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var model = result.Model as DisclosureSummaryViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.That(model.ActivityExpensesGridModel, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(model.ActivityExpensesGridModel.Columns.Any(c => c.Field == "TransactionDate"));
            Assert.That(model.ActivityExpensesGridModel.Columns.Any(c => c.Field == "PayeeName"));
            Assert.That(model.ActivityExpensesGridModel.Columns.Any(c => c.Field == "PayeeType"));
            Assert.That(model.ActivityExpensesGridModel.Columns.Any(c => c.Field == "PersonBenefiting"));
            Assert.That(model.ActivityExpensesGridModel.Columns.Any(c => c.Field == "AmountBenefiting"));
        });

        // Verify subtotal calculation
        Assert.That(result.ViewData["Subtotal"], Is.EqualTo(expectedSubtotal));
    }

    [Test]
    public async Task Index_ShouldPopulateCoalitionPaymentsGridModel()
    {
        // Arrange
        var filerId = "12345";
        var filingId = "12345";
        var cancellationToken = CancellationToken.None;
        var mockData = new List<PaymentMadeToLobbyingCoalitionResponse>
        {
            new(amountThisPeriod: 500.00,
                coalitionName: "Coalition A",
                contactId: 1,
                cumulativeAmount: 1500.00,
                filerId: 1001,
                filingId: 1,
                id: 1,
                registrationId : 0),
            new(amountThisPeriod: 250.00,
                coalitionName: "Coalition B",
                contactId: 2,
                cumulativeAmount: 750.00,
                filerId: 1002,
                filingId: 1,
                id: 1,
                registrationId : 0)
        };

        var expectedSubtotal = mockData.Sum(x => x.AmountThisPeriod);

        _disclosureCtlSvcMock
        .Setup(x => x.BuildSummaryViewModel(
            FilingSummaryTypeModel.ToLobbyingCoalitionSummary.Name,
            filerId,
            filingId,
            "1",
            "1",
            null,
            It.IsAny<HttpContext>(),
            It.IsAny<ITempDataDictionary>(),
            cancellationToken))
        .ReturnsAsync(new DisclosureSummaryViewModel
        {
            ViewName = FilingSummaryTypeModel.ToLobbyingCoalitionSummary.Name,
            Id = long.Parse(filingId, CultureInfo.InvariantCulture),
            FilerId = long.Parse(filerId, CultureInfo.InvariantCulture),
            FilingSummaryId = long.Parse("1", CultureInfo.InvariantCulture),
            CoalitionPaymentsGridModel = new SmallDataGridModel
            {
                GridId = "CoalitionPaymentsGrid",
                DataSource = mockData,
                Columns = new List<DataGridColumn>
                {
                    new()
                    {
                        Field = nameof(PaymentMadeToLobbyingCoalitionResponse.CoalitionName),
                        HeaderText = "Lobbying Coalition"
                    },
                    new()
                    {
                        Field = nameof(PaymentMadeToLobbyingCoalitionResponse.FilerId),
                        HeaderText = "ID#"
                    },
                    new()
                    {
                        Field = nameof(PaymentMadeToLobbyingCoalitionResponse.AmountThisPeriod),
                        HeaderText = "Amount this Period",
                        IsCurrency = true
                    },
                    new()
                    {
                        Field = nameof(PaymentMadeToLobbyingCoalitionResponse.CumulativeAmount),
                        HeaderText = "Cumulative Amount",
                        IsCurrency = true
                    }
                },
            },
            Subtotal = expectedSubtotal
        });

        // Act
        var result =
            await _controller.Index(filerId, filingId, "1", "1", FilingSummaryTypeModel.ToLobbyingCoalitionSummary.Name, null, cancellationToken: cancellationToken);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);

        var model = viewResult.Model as DisclosureSummaryViewModel;
        Assert.That(model, Is.Not.Null);

        Assert.That(model.CoalitionPaymentsGridModel, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(model.CoalitionPaymentsGridModel.Columns.Any(c => c.HeaderText == "Lobbying Coalition"));
            Assert.That(model.CoalitionPaymentsGridModel.Columns.Any(c => c.HeaderText == "ID#"));
            Assert.That(model.CoalitionPaymentsGridModel.Columns.Any(c => c.HeaderText == "Amount this Period"));
            Assert.That(model.CoalitionPaymentsGridModel.Columns.Any(c => c.HeaderText == "Cumulative Amount"));
        });

        // Verify subtotal calculation
        Assert.That(viewResult.ViewData["Subtotal"], Is.EqualTo(expectedSubtotal));
    }

    [Test]
    public async Task Index_ShouldPopulateOtherPaymentsGridModel()
    {
        // Arrange
        var filerId = "12345";
        var filingId = "12345";
        var cancellationToken = CancellationToken.None;
        var mockData = new List<OtherPaymentsToInfluenceResponse>
        {
            new
            (
                paymentCodeName: "XYZ789",
                payeeName: "Smith & Partners",
                amount: 2500.00,
                cumulativeAmount: 7500.00,
                payeeSnapshot: null!,
                id: 1,
                otherActionsLobbied: "test",
                paymentCodeDescription: "test",
                paymentCodeId: 1
            ),
            new
            (
                paymentCodeName: "ABC123",
                payeeName: "Pacific Strategies Group",
                amount: 3200.00,
                cumulativeAmount: 10700.00,
                payeeSnapshot: null!,
                id: 1,
                otherActionsLobbied: "test",
                paymentCodeDescription: "test",
                paymentCodeId: 1
            )
        };

        var expectedSubtotal = mockData.Sum(x => x.Amount);

        _disclosureCtlSvcMock
        .Setup(x => x.BuildSummaryViewModel(
            FilingSummaryTypeModel.OtherPaymentsToInfluenceSummary.Name,
            filerId,
            filingId,
            "1",
            "1",
            null,
            It.IsAny<HttpContext>(),
            It.IsAny<ITempDataDictionary>(),
            cancellationToken))
        .ReturnsAsync(new DisclosureSummaryViewModel
        {
            ViewName = FilingSummaryTypeModel.OtherPaymentsToInfluenceSummary.Name,
            Id = long.Parse(filingId, CultureInfo.InvariantCulture),
            FilerId = long.Parse(filerId, CultureInfo.InvariantCulture),
            FilingSummaryId = long.Parse("1", CultureInfo.InvariantCulture),
            OtherPaymentsGridModel = new SmallDataGridModel
            {
                GridId = "OtherPaymentsGrid",
                DataSource = mockData,
                Columns = new List<DataGridColumn>
                {
                    new()
                    {
                        Field = nameof(OtherPaymentsToInfluenceResponse.PaymentCodeName),
                        HeaderText = "Code"
                    },
                    new()
                    {
                        Field = nameof(OtherPaymentsToInfluenceResponse.PayeeName),
                        HeaderText = "Payee"
                    },
                    new()
                    {
                        Field = nameof(OtherPaymentsToInfluenceResponse.Amount),
                        HeaderText = "Amount",
                        IsCurrency = true
                    },
                    new()
                    {
                        Field = nameof(OtherPaymentsToInfluenceResponse.CumulativeAmount),
                        HeaderText = "Cumulative Amount",
                        IsCurrency = true
                    }
                },
            },
            Subtotal = expectedSubtotal
        });

        // Act
        var result =
            await _controller.Index(filerId, filingId, "1", "1", FilingSummaryTypeModel.OtherPaymentsToInfluenceSummary.Name, null, cancellationToken: cancellationToken);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);

        var model = viewResult.Model as DisclosureSummaryViewModel;
        Assert.That(model, Is.Not.Null);

        Assert.That(model.OtherPaymentsGridModel, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(model.OtherPaymentsGridModel.Columns.Any(c => c.HeaderText == "Code"));
            Assert.That(model.OtherPaymentsGridModel.Columns.Any(c => c.HeaderText == "Payee"));
            Assert.That(model.OtherPaymentsGridModel.Columns.Any(c => c.HeaderText == "Amount"));
            Assert.That(model.OtherPaymentsGridModel.Columns.Any(c => c.HeaderText == "Cumulative Amount"));
        });

        // Verify subtotal calculation
        Assert.That(viewResult.ViewData["Subtotal"], Is.EqualTo(expectedSubtotal));
    }

    [Test]
    public async Task Index_ShouldReturnCampaignContributionsView_WhenViewNameIsCampaignContributions()
    {
        // Arrange
        var filerId = "123";
        var longFilerId = long.Parse(filerId, CultureInfo.InvariantCulture);
        var filingId = "456";
        var longFilingId = long.Parse(filingId, CultureInfo.InvariantCulture);
        var cancellationToken = CancellationToken.None;

        var mockFilerItemReponse = new Mock<FilerItemResponse>();

        var mockData = new List<LobbyistEmployerCampaignContributionItemResponse>
        {
            new(
                amount: 200.0,
                contactId: null,
                filerId: longFilerId,
                id: 1,
                notes: "First contribution",
                recipientFiler: null,
                recipientFilerId: 789,
                recipientName: "Recipient A",
                recipientType: "Candidate",
                transactionDate: DateTime.Now
            ),
            new(
                amount: 300.0,
                contactId: null,
                filerId: longFilerId,
                id: 2,
                notes: "Second contribution",
                recipientFiler: null,
                recipientFilerId: 790,
                recipientName: "Recipient B",
                recipientType: "Committee",
                transactionDate: DateTime.Now
            )
        };

        var expectedSubtotal = mockData.Sum(x => x.Amount);

        _disclosureCtlSvcMock
        .Setup(x => x.BuildSummaryViewModel(
            FilingSummaryTypeModel.CampaignContributionSummary.Name,
            filerId,
            filingId,
            "1",
            "1",
            null,
            It.IsAny<HttpContext>(),
            It.IsAny<ITempDataDictionary>(),
            cancellationToken))
        .ReturnsAsync(new DisclosureSummaryViewModel
        {
            ViewName = FilingSummaryTypeModel.CampaignContributionSummary.Name,
            Id = long.Parse(filingId, CultureInfo.InvariantCulture),
            FilerId = long.Parse(filerId, CultureInfo.InvariantCulture),
            FilingSummaryId = long.Parse("1", CultureInfo.InvariantCulture),
            CampaignContributionsGridModel = new SmallDataGridModel
            {
                GridId = "OtherPaymentsGrid",
                DataSource = mockData,
                Columns = new List<DataGridColumn>
                {
                    new()
                    {
                        Field = nameof(OtherPaymentsToInfluenceResponse.PaymentCodeName),
                        HeaderText = "Code"
                    },
                    new()
                    {
                        Field = nameof(OtherPaymentsToInfluenceResponse.PayeeName),
                        HeaderText = "Payee"
                    },
                    new()
                    {
                        Field = nameof(OtherPaymentsToInfluenceResponse.Amount),
                        HeaderText = "Amount",
                        IsCurrency = true
                    },
                    new()
                    {
                        Field = nameof(OtherPaymentsToInfluenceResponse.CumulativeAmount),
                        HeaderText = "Cumulative Amount",
                        IsCurrency = true
                    }
                },
            },
            Subtotal = expectedSubtotal
        });

        // Act
        var result = await _controller.Index(
            filerId,
            filingId,
            "1",
            "1",
            FilingSummaryTypeModel.CampaignContributionSummary.Name
        ) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.InstanceOf<DisclosureSummaryViewModel>());

        var model = (DisclosureSummaryViewModel)result.Model;
        Assert.Multiple(() =>
        {
            Assert.That(model.FilerId, Is.EqualTo(longFilerId), "Filer ID mismatch");
            Assert.That(model.CampaignContributionsGridModel, Is.Not.Null, "GridModel should not be null");
            Assert.That(model.CampaignContributionsGridModel, Is.InstanceOf<DataGridModel>(), "Data source mismatch");
            Assert.That(result.ViewData["Subtotal"], Is.EqualTo(500), "Subtotal calculation is incorrect");
        });
    }

    [Test]
    public async Task Index_ShouldReturnCoalitionReceivedView_WhenViewNameIsCoalitionReceived()
    {
        // Arrange
        var filerId = "123";
        var filingId = "456";
        var cancellationToken = CancellationToken.None;
        var longFilingId = long.Parse(filingId, CultureInfo.InvariantCulture);

        var mockData = new List<PaymentReceiveLobbyingCoalitionResponse>
        {
            new
            (
                amountThisPeriod: 500.00,
                coalitionName: "Coalition A",
                cumulativeAmount: 1500.00,
                contact: It.IsAny<ContactResponseDto>(),
                id: 1
            ),
            new
            (
                amountThisPeriod: 250.00,
                coalitionName: "Coalition B",
                cumulativeAmount: 750.00,
                contact: It.IsAny<ContactResponseDto>(),
                id: 2
            )
        };

        var expectedSubtotal = mockData.Sum(x => x.AmountThisPeriod);

        _disclosureCtlSvcMock
        .Setup(x => x.BuildSummaryViewModel(
            FilingSummaryTypeModel.RecieveLobbyingCoalitionSummary.Name,
            filerId,
            filingId,
            "1",
            "1",
            null,
            It.IsAny<HttpContext>(),
            It.IsAny<ITempDataDictionary>(),
            cancellationToken))
        .ReturnsAsync(new DisclosureSummaryViewModel
        {
            ViewName = FilingSummaryTypeModel.RecieveLobbyingCoalitionSummary.Name,
            Id = long.Parse(filingId, CultureInfo.InvariantCulture),
            FilerId = long.Parse(filerId, CultureInfo.InvariantCulture),
            FilingSummaryId = long.Parse("1", CultureInfo.InvariantCulture),
            CoalitionReceivedGridModel = new SmallDataGridModel
            {
                GridId = "CoalitionReceivedGrid",
                DataSource = mockData,
                Columns = new List<DataGridColumn>
                {
                    new()
                    {
                        Field = nameof(PaymentReceiveLobbyingCoalitionResponse.CoalitionName),
                        HeaderText = "Coalition Member"
                    },
                    new()
                    {
                        Field = nameof(PaymentReceiveLobbyingCoalitionResponse.AmountThisPeriod),
                        HeaderText = "Amount Received",
                        IsCurrency = true
                    },
                    new()
                    {
                        Field = nameof(PaymentReceiveLobbyingCoalitionResponse.CumulativeAmount),
                        HeaderText = "Cumulative Amount",
                        IsCurrency = true
                    }
                },
            },
            Subtotal = expectedSubtotal
        });

        // Act
        var result = await _controller.Index(
            filerId,
            filingId,
            "1",
            "1",
            FilingSummaryTypeModel.RecieveLobbyingCoalitionSummary.Name,
            null,
            "1",
            CancellationToken.None
        ) as ViewResult;

        // Assert

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.InstanceOf<DisclosureSummaryViewModel>());

        var model = (DisclosureSummaryViewModel)result.Model;
        Assert.Multiple(() =>
        {
            Assert.That(model.CoalitionReceivedGridModel, Is.Not.Null, "GridModel should not be null");
            Assert.That(model.CoalitionReceivedGridModel, Is.InstanceOf<DataGridModel>(), "Data source mismatch");
            Assert.That(result.ViewData["Subtotal"], Is.EqualTo(expectedSubtotal), "Subtotal calculation is incorrect");
        });
    }

    [Test]
    public async Task UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition_ShouldUpdate()
    {
        // Arrange
        var body = new UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalitionDto(true);
        var model = new DisclosureSummaryViewModel
        {
            StartDate = new DateTime(2024, 1, 1, 0, 0, 0, 0),
            EndDate = new DateTime(2024, 6, 30, 0, 0, 0, 0),
            Id = 7890,
            FilerId = 123456,
            FilingSummaryId = 1,
            IsMemberOfLobbyingCoalition = "false",
        };
        // Mocking the FilingsApi to return the updated report
        _filingsApiMock
            .Setup(api =>
                api.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(7890, body, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_sampleReport);

        _ = _disclosureCtlSvcMock.Setup(c => c.UpdateFilingSummaryById(It.IsAny<long>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .Callback(() => Task.Delay(10))
             .Returns(Task.CompletedTask);
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        // Act
        var result = await _controller.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(
            model,
            _filingsApiMock.Object,
            It.IsAny<CancellationToken>()
        );

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition_ShouldUpdate_WhenFilingSummaryIdNull()
    {
        // Arrange
        var body = new UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalitionDto(true);
        var model = new DisclosureSummaryViewModel
        {
            StartDate = new DateTime(2024, 1, 1, 0, 0, 0, 0),
            EndDate = new DateTime(2024, 6, 30, 0, 0, 0, 0),
            Id = 7890,
            FilerId = 123456,
            IsMemberOfLobbyingCoalition = "false",
        };
        // Mocking the FilingsApi to return the updated report
        _filingsApiMock
            .Setup(api =>
                api.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(7890, body, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_sampleReport);

        _ = _disclosureCtlSvcMock.Setup(c => c.UpdateFilingSummaryById(It.IsAny<long>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .Callback(() => Task.Delay(10))
             .Returns(Task.CompletedTask);

        var disclosureFilingReports = new List<DisclosureReport>
        {
            new(1, "Name", "ToLobbyingCoalitionSummary", "status", 1)
        };

        _ = _disclosureCtlSvcMock.Setup(svc => svc.GetDisclosureFilingReportsById(It.IsAny<long>(), CancellationToken.None))
            .ReturnsAsync(disclosureFilingReports);
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        // Act
        var result = await _controller.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(
            model,
            _filingsApiMock.Object,
            It.IsAny<CancellationToken>()
        );

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition_ShouldUpdate_WhenFilingSummaryIdZero()
    {
        // Arrange
        var body = new UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalitionDto(true);
        var model = new DisclosureSummaryViewModel
        {
            StartDate = new DateTime(2024, 1, 1, 0, 0, 0, 0),
            EndDate = new DateTime(2024, 6, 30, 0, 0, 0, 0),
            Id = 7890,
            FilerId = 123456,
            FilingSummaryId = 0,
            IsMemberOfLobbyingCoalition = "false",
        };
        // Mocking the FilingsApi to return the updated report
        _filingsApiMock
            .Setup(api =>
                api.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(7890, body, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_sampleReport);

        _ = _disclosureCtlSvcMock.Setup(c => c.UpdateFilingSummaryById(It.IsAny<long>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .Callback(() => Task.Delay(10))
             .Returns(Task.CompletedTask);

        var disclosureFilingReports = new List<DisclosureReport>
        {
            new(1, "Name", "ToLobbyingCoalitionSummary", "status", 1)
        };

        _ = _disclosureCtlSvcMock.Setup(svc => svc.GetDisclosureFilingReportsById(It.IsAny<long>(), CancellationToken.None))
            .ReturnsAsync(disclosureFilingReports);
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        // Act
        var result = await _controller.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(
            model,
            _filingsApiMock.Object,
            It.IsAny<CancellationToken>()
        );

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition_ReturnsViewOnException()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel { Id = 1, FilerId = 1001, FilingSummaryId = 1, IsMemberOfLobbyingCoalition = "true" };
        _filingsApiMock.Setup(x => x.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(
                7890,
                It.IsAny<UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalitionDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(_sampleReport);
        _ = _disclosureCtlSvcMock.Setup(c => c.UpdateFilingSummaryById(It.IsAny<long>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .Callback(() => Task.Delay(10))
             .Returns(Task.CompletedTask);
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        // Act
        var result =
            await _controller.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(model, _filingsApiMock.Object);

        // Assert
        var redirectToActionResult = result as RedirectToActionResult;
        Assert.That(redirectToActionResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectToActionResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectToActionResult.RouteValues, Is.Not.Null);

            Assert.That(redirectToActionResult.RouteValues?.ContainsKey("filerId"), Is.True);
            Assert.That(redirectToActionResult.RouteValues?["filerId"], Is.EqualTo(model.FilerId));

            Assert.That(redirectToActionResult.RouteValues?.ContainsKey("filingId"), Is.True);
            Assert.That(redirectToActionResult.RouteValues?["filingId"], Is.EqualTo(model.Id));

            Assert.That(redirectToActionResult.RouteValues?.ContainsKey("viewName"), Is.True);
            Assert.That(redirectToActionResult.RouteValues?["viewName"], Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition_ReturnsNotFoundIfModelStateInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model state");
        var model = new DisclosureSummaryViewModel();

        // Act
        var result =
            await _controller.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(model, _filingsApiMock.Object);

        // Assert
        Assert.That(result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task
        UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition_ApiThrowsException_ReturnsIndexViewWithError()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel { Id = 1, FilerId = 100, FilingSummaryId = 1, IsMemberOfLobbyingCoalition = "true" };
        _ = _disclosureCtlSvcMock.Setup(c => c.UpdateFilingSummaryById(It.IsAny<long>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .Callback(() => Task.Delay(10))
             .Returns(Task.CompletedTask);
        _filingsApiMock
            .Setup(api => api.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(It.IsAny<int>(),
                It.IsAny<UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalitionDto>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("API error"));
        var mockFilingItemResponse = new FilingItemResponse(
            id: 123,
            endDate: DateTime.Now,
            filerId: 456,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: FilingType.LobbyistEmployerReport.Name,
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        // Act
        var result =
            await _controller.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(model, _filingsApiMock.Object,
                CancellationToken.None);

        // Assert
        var redirectToActionResult = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirectToActionResult?.ActionName, Is.EqualTo("Index"));
        });
    }

    [Test]
    public async Task ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling_ReturnsRedirectToDashboard_WhenValidationSucceeds()
    {
        var filingId = "123";
        var model = new DisclosureSummaryViewModel { Id = 123, FilerId = 123, FilingSummaryId = 1 };
        var response = new ValidatedPaymentsReceivedLobbyingCoalitionTransactionResponse(123, true, []);

        _ = _disclosureCtlSvcMock.Setup(c => c.UpdateFilingSummaryById(It.IsAny<long>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .Callback(() => Task.Delay(10))
             .Returns(Task.CompletedTask);
        _lobbyistEmployerCoalitionApiMock.Setup(api => api.ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture),
            It.IsAny<CancellationToken>()))
        .ReturnsAsync(response);
        var mockFilingItemResponse = new FilingItemResponse(
            id: 123,
            endDate: DateTime.Now,
            filerId: 456,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: FilingType.LobbyistEmployerReport.Name,
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        var result = await _controller.ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(model, _lobbyistEmployerCoalitionApiMock.Object);

        Assert.That(result, Is.Not.Null);
        var redirectResult = (RedirectToActionResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues?["viewName"], Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling_ReturnsRedirectToPartialView_WhenValidationFails()
    {
        var filingId = "123";
        var model = new DisclosureSummaryViewModel { Id = 1, FilerId = 123 };
        var validationErrors = new List<WorkFlowError> { new("Test", "Test", "Test", "Test") };
        var response = new ValidatedPaymentsReceivedLobbyingCoalitionTransactionResponse(123, true, validationErrors);

        _lobbyistEmployerCoalitionApiMock.Setup(api => api.ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture),
            It.IsAny<CancellationToken>()))
        .ReturnsAsync(response);
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        var disclosureFilingReports = new List<DisclosureReport>
            {
                new(1, "Name", "RecieveLobbyingCoalitionSummary", "status", 1)
            };

        _ = _disclosureCtlSvcMock.Setup(svc => svc.GetDisclosureFilingReportsById(It.IsAny<long>(), CancellationToken.None))
            .ReturnsAsync(disclosureFilingReports);

        var result = await _controller.ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(model, _lobbyistEmployerCoalitionApiMock.Object);

        Assert.That(result, Is.Not.Null);
        var redirectResult = (RedirectToActionResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues?["viewName"], Is.EqualTo(FilingSummaryTypeModel.RecieveLobbyingCoalitionSummary.Name));
        });
    }

    [Test]
    public async Task ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling_LogsError_OnException()
    {
        var filingId = "123";
        var model = new DisclosureSummaryViewModel { Id = 1, FilerId = 123 };
        _lobbyistEmployerCoalitionApiMock.Setup(api => api.ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture),
            It.IsAny<CancellationToken>()))
        .ThrowsAsync(new InvalidOperationException("API error"));
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        var disclosureFilingReports = new List<DisclosureReport>
            {
                new(1, "Name", "RecieveLobbyingCoalitionSummary", "status", 1)
            };

        _ = _disclosureCtlSvcMock.Setup(svc => svc.GetDisclosureFilingReportsById(It.IsAny<long>(), CancellationToken.None))
            .ReturnsAsync(disclosureFilingReports);

        var result = await _controller.ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(model, _lobbyistEmployerCoalitionApiMock.Object);

        Assert.That(result, Is.Not.Null);
        var redirectResult = (RedirectToActionResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues?["viewName"], Is.EqualTo(FilingSummaryTypeModel.RecieveLobbyingCoalitionSummary.Name));
        });
    }

    [Test]
    public async Task ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling_ModelStateInvalid_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid ModelState");
        var model = new DisclosureSummaryViewModel();

        // Act
        var result = await _controller.ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(model, _lobbyistEmployerCoalitionApiMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling_ResponseInvalid_ReturnsRedirectWithErrors()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel { Id = 1, FilerId = 100 };
        var validationErrors = new List<WorkFlowError> { new("PeriodAmount", "ErrGlobal0002", "Validation", "{{Field Name}} is invalid") };
        var response = new ValidatedPaymentsReceivedLobbyingCoalitionTransactionResponse(1, false, validationErrors);

        _ = _lobbyistEmployerCoalitionApiMock
            .Setup(api => api.ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        var disclosureFilingReports = new List<DisclosureReport>
            {
                new(1, "Name", "RecieveLobbyingCoalitionSummary", "status", 1)
        };

        _ = _disclosureCtlSvcMock.Setup(svc => svc.GetDisclosureFilingReportsById(model.Id!.Value, CancellationToken.None))
            .ReturnsAsync(disclosureFilingReports);

        // Act
        var result = await _controller.ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(model, _lobbyistEmployerCoalitionApiMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        var redirectResult = (RedirectToActionResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues?["viewName"], Is.EqualTo(FilingSummaryTypeModel.RecieveLobbyingCoalitionSummary.Name));
        });
    }

    [Test]
    public async Task UpdateLobbyistEmployerCampaignContributions_ReturnsRedirect_WhenSuccessful()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel { Id = 1, ContributionsInExistingStatements = "True", RelatedFilerId = 1, FilerId = 1 };
        _filingsApiMock.Setup(api => api.UpdateLobbyistEmployerCampaignContributions(It.IsAny<long>(), It.IsAny<UpdateLobbyistEmployerCampaignContributionsDto>(), It.IsAny<CancellationToken>()))
                       .ReturnsAsync(_sampleReport);
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        // Act
        var result = await _controller.UpdateLobbyistEmployerCampaignContributions(model, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = (RedirectToActionResult)result;
        Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
    }
    [Test]
    public async Task Index_WithGeneralInfoViewAndLobbyistReportType_ReturnsCorrectlyPopulatedViewModel()
    {
        // Arrange
        const string filerId = "123";
        const string filingId = "456";
        const long longFilerId = 123L;
        const long longFilingId = 456L;
        var reportType = FilingType.LobbyistReport.Name;
        const string viewName = "GeneralInfo";

        var report = new LobbyistReportResponse(
            id: 123,
            filerId: 456,
            startDate: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            endDate: DateTime.Parse("2023-03-31", CultureInfo.InvariantCulture),
            diligenceStatementVerified: false,
            filerName: "Foo",
            parentId: 123,
            status: 1,
            submittedDate: DateTime.Parse("2023-03-31", CultureInfo.InvariantCulture),
            version: 1,
            filingTypeId: 16
        );

        var lobbyist = new LobbyistResponseDto(
            addresses: new List<AddressDtoModel>
            {
                new AddressDtoModel(
                    city: "Test City",
                    country: "USA",
                    purpose: "Mailing",
                    state: "CA",
                    street: "123 Test St",
                    street2: "",
                    type: "Home",
                    zip: "12345"
                )
            },
            addressListId: 789,
            dateQualified: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 456,
            id: 789,
            name: "Test Lobbyist",
            phoneNumberListId: 101,
            phoneNumbers:
            new List<PhoneNumberDto>
            {
                new("+1", "987", 1, false, "1011231234", 1, false, "Work"),
            },
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 1,
            agencies: [],
            completedCourseDate: new(),
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: new(),
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Walter",
            middleName: string.Empty,
            lastName: "White",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyOnlySpecifiedAgencies: false,
            photo: "sample.jpg",
            selfRegister: true,
            isSameAsCandidateAddress: true,
            lobbyistEmployerOrLobbyingFirmName: "test",
            withdrawnAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            terminatedAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            effectiveDateOfChanges: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            type: "Lobbyist"
        );

        var disclosureLobbyingGeneralInfoViewModelMock = new DisclosureLobbyingGeneralInfoViewModel(lobbyist, report)
        {
            ReportType = "Lobbyist report",
            FilingTypeId = FilingType.LobbyistReport.Id
        };

        _disclosureCtlSvcMock.Setup(svc => svc.GetGeneralInfoViewForLobbyist(longFilerId, longFilingId, It.IsAny<CancellationToken>())).ReturnsAsync(disclosureLobbyingGeneralInfoViewModelMock);

        // Act
        var result = await _controller.Index(filerId, filingId, "1", "1", viewName, reportType);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.Model, Is.InstanceOf<DisclosureLobbyingGeneralInfoViewModel>());

        var model = viewResult.Model as DisclosureLobbyingGeneralInfoViewModel;
        Assert.Multiple(() =>
        {
            Assert.That(model?.ReportType, Is.EqualTo("Lobbyist report"));
            Assert.That(model?.FilingTypeId, Is.EqualTo(FilingType.LobbyistReport.Id));
            Assert.That(model?.Name, Is.EqualTo("Test Lobbyist"));
            Assert.That(model?.EmployerName, Is.EqualTo("Test Employer"));
            Assert.That(model?.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(model!.StartDate, Is.EqualTo(new DateTime(2023, 1, 1)));
            Assert.That(model.EndDate, Is.EqualTo(new DateTime(2023, 3, 31)));
        });

        Assert.That(viewResult.ViewData["PartialView"], Is.EqualTo("GeneralInfo"));
    }

    [Test]
    public async Task UpdateLobbyistEmployerCampaignContributions_ReturnsNotFound_WhenModelIsInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid Model");
        var model = new DisclosureSummaryViewModel();

        // Act
        var result = await _controller.UpdateLobbyistEmployerCampaignContributions(model, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task Index_WithGeneralInfoViewAndLobbyistEmployerReportType_ReturnsCorrectlyPopulatedViewModel()
    {
        // Arrange
        const string filerId = "123";
        const string filingId = "456";
        var reportType = FilingType.LobbyistEmployerReport.Name;
        const string viewName = "GeneralInfo";
        const long longFilerId = 123L;
        const long longFilingId = 456L;

        var expectedModel = new DisclosureLobbyingGeneralInfoViewModel
        {
            ReportType = "Lobbyist employer report",
            FilingTypeId = FilingType.LobbyistEmployerReport.Id
        };

        _ = _disclosureCtlSvcMock
            .Setup(x => x.GetGeneralInfoViewForLobbyistEmployer(
                longFilerId,
                longFilingId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedModel);

        // Act
        var result = await _controller.Index(filerId, filingId, "1", "1", viewName, reportType);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.Model, Is.InstanceOf<DisclosureLobbyingGeneralInfoViewModel>());

        var model = viewResult.Model as DisclosureLobbyingGeneralInfoViewModel;
        Assert.Multiple(() =>
        {
            Assert.That(model?.ReportType, Is.EqualTo("Lobbyist employer report"));
            Assert.That(model?.FilingTypeId, Is.EqualTo(FilingType.LobbyistEmployerReport.Id));
        });

        Assert.That(viewResult.ViewData["PartialView"], Is.EqualTo("GeneralInfo"));

        _disclosureCtlSvcMock.Verify(
            x => x.GetGeneralInfoViewForLobbyistEmployer(
                longFilerId,
                longFilingId,
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Test]
    public async Task Index_WithGeneralInfoViewAndNullReportType_ReturnsDefaultViewModel()
    {
        // Arrange
        const string filerId = "123";
        const string filingId = "456";
        const string viewName = "GeneralInfo";
        const string reportType = "";

        // Act
        var result = await _controller.Index(filerId, filingId, "1", "1", viewName, reportType);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.Model, Is.InstanceOf<DisclosureLobbyingGeneralInfoViewModel>());

        var model = viewResult.Model as DisclosureLobbyingGeneralInfoViewModel;
        Assert.Multiple(() =>
        {
            Assert.That(model?.ReportType, Is.Null);
            Assert.That(viewResult.ViewData["PartialView"], Is.EqualTo("GeneralInfo"));
        });
    }

    [Test]
    public async Task Index_WithGeneralInfoViewAndValidIds_ReturnsCorrectView()
    {
        // Arrange
        var filerId = "123";
        var filingId = "456";
        var viewName = "GeneralInfo";
        var reportType = FilingType.LobbyistReport.Name;

        // Act
        var result = await _controller.Index(filerId, filingId, "1", "1", viewName, reportType);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewData["PartialView"], Is.EqualTo("GeneralInfo"));
    }

    [Test]
    public async Task Index_WithGeneralInfoViewAndLobbyingFirmReportType_ReturnsCorrectViewModel()
    {
        // Arrange
        var filerId = "123";
        var filingId = "456";
        var viewName = "GeneralInfo";
        var reportType = "LobbyingFirm";

        // Act
        var result = await _controller.Index(filerId, filingId, "1", "1", viewName, reportType);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.Model, Is.InstanceOf<DisclosureLobbyingGeneralInfoViewModel>());

        var model = viewResult.Model as DisclosureLobbyingGeneralInfoViewModel;
        Assert.That(model!.ReportType, Is.Null);
    }

    [Test]
    public async Task Index_WithGeneralInfoViewAndLobbyingCoalitionReportType_ReturnsCorrectViewModel()
    {
        // Arrange
        var filerId = "123";
        var filingId = "456";
        var viewName = "GeneralInfo";
        var reportType = "LobbyingCoalition";

        // Act
        var result = await _controller.Index(filerId, filingId, "1", "1", viewName, reportType);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.Model, Is.InstanceOf<DisclosureLobbyingGeneralInfoViewModel>());

        var model = viewResult.Model as DisclosureLobbyingGeneralInfoViewModel;
        Assert.That(model!.ReportType, Is.Null);
    }

    [Test]
    public async Task Index_WithGeneralInfoViewAndReport72h_ReturnsCorrectlyPopulatedViewModel()
    {
        // Arrange
        const string filerId = "123";
        const string filingId = "456";
        var reportType = FilingType.Report72h.Name;
        var cancellationToken = new CancellationToken();

        // Mock sessions returned by the GetLegistlativeSessions method
        var mockSessions = new List<LegislativeSessionResponse>
            {
                new(
                    DateTime.Now,
                    1,
                    "Session 2023",
                    DateTime.Now.AddMonths(6)
                )
            };
        _filingsApiMock.Setup(f => f.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList(mockSessions));
        _filingsApiMock.Setup(f => f.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1));

        // Use Reflection to invoke the private method
        var methodInfo = typeof(DisclosureController).GetMethod("HandleGeneralInfoView", BindingFlags.NonPublic | BindingFlags.Instance);

        if (methodInfo != null)
        {
            // Act 
            var taskResult = methodInfo.Invoke(_controller, new object[] { filerId, filingId, reportType, cancellationToken }) as Task<IActionResult>;

            Assert.That(taskResult, Is.Not.Null);

            IActionResult actionResult = await taskResult;

            // Assert
            Assert.That(actionResult, Is.InstanceOf<ViewResult>());

            var viewResult = actionResult as ViewResult;
            var model = viewResult?.Model as DisclosureLobbyingGeneralInfoViewModel;

            Assert.That(model, Is.Not.Null);
            Assert.Multiple(() =>
            {
                Assert.That(model.ReportType, Is.EqualTo("72h Report"));
                Assert.That(model.Id, Is.EqualTo(long.Parse(filingId, CultureInfo.InvariantCulture)));
                Assert.That(model.FilerId, Is.EqualTo(long.Parse(filerId, CultureInfo.InvariantCulture)));
                Assert.That(model.FilingTypeId, Is.EqualTo(FilingType.Report72h.Id));
                Assert.That(model.Sessions, Is.EqualTo(new LegislativeSessionResponseList(mockSessions)));
                Assert.That(model.LegislativeSessionId, Is.EqualTo(1));
            });
        }
        else
        {
            Assert.Fail("Method not found");
        }
    }

    [Test]
    public void CancelTransaction_WithValidParameters_ReturnsRedirectToIndexWithParameters()
    {
        // Arrange
        var filerId = "12345";
        var filingId = "67890";
        var reportType = "1";
        var viewName = "A";

        _localizerMock.Setup(l => l["FilerPortal.Disclosure.Dashboard.CancelTransactionSuccessMessage"])
            .Returns(new LocalizedString("FilerPortal.Disclosure.Dashboard.CancelTransactionSuccessMessage", "Transaction cancelled successfully"));

        // Act
        var result = _controller.CancelTransaction(filerId, filingId, reportType, viewName);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = (RedirectToActionResult)result;

        Assert.That(redirectResult.RouteValues, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));

            Assert.That(redirectResult.RouteValues.ContainsKey("filerId"), Is.True);
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(filerId));

            Assert.That(redirectResult.RouteValues.ContainsKey("filingId"), Is.True);
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(filingId));

            Assert.That(redirectResult.RouteValues.ContainsKey("reportType"), Is.True);
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(reportType));

            Assert.That(redirectResult.RouteValues.ContainsKey("viewName"), Is.True);
            Assert.That(redirectResult.RouteValues["viewName"], Is.EqualTo(viewName));
        });

        _toastService.Verify(ts => ts.Success(It.IsAny<string>(), true, "Right", "Bottom", 0), Times.AtLeastOnce);
    }

    [Test]
    public void CancelTransaction_WithNullParameters_ReturnsRedirectToIndexWithNullParameters()
    {
        // Arrange
        _localizerMock.Setup(l => l["FilerPortal.Disclosure.Dashboard.CancelTransactionSuccessMessage"])
            .Returns(new LocalizedString("FilerPortal.Disclosure.Dashboard.CancelTransactionSuccessMessage", "Transaction cancelled successfully"));

        // Act
        var result = _controller.CancelTransaction(null, null, null, null);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = (RedirectToActionResult)result;

        Assert.That(redirectResult.RouteValues, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);

            Assert.That(redirectResult.RouteValues.TryGetValue("filerId", out var filerId), Is.True);
            Assert.That(filerId, Is.Null);

            Assert.That(redirectResult.RouteValues.TryGetValue("filingId", out var filingId), Is.True);
            Assert.That(filingId, Is.Null);

            Assert.That(redirectResult.RouteValues.TryGetValue("reportType", out var reportType), Is.True);
            Assert.That(reportType, Is.Null);

            Assert.That(redirectResult.RouteValues.TryGetValue("viewName", out var viewName), Is.True);
            Assert.That(viewName, Is.Null);
        });

        _toastService.Verify(ts => ts.Success(It.IsAny<string>(), true, "Right", "Bottom", 0), Times.AtLeastOnce);
    }

    [Test]
    public void Cancel_WithValidParameters_ReturnsRedirectToIndexWithParameters()
    {
        // Arrange
        var filerId = "12345";
        var filingId = "67890";
        var reportType = "1";

        _localizerMock.Setup(l => l["FilerPortal.Disclosure.Dashboard.CancelSuccessMessage"])
            .Returns(new LocalizedString("FilerPortal.Disclosure.Dashboard.CancelSuccessMessage", "Cancelled successfully"));

        // Act
        var result = _controller.Cancel(filerId, filingId, reportType);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = (RedirectToActionResult)result;

        Assert.That(redirectResult.RouteValues, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));

            Assert.That(redirectResult.RouteValues.ContainsKey("filerId"), Is.True);
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(filerId));

            Assert.That(redirectResult.RouteValues.ContainsKey("filingId"), Is.True);
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(filingId));

            Assert.That(redirectResult.RouteValues.ContainsKey("reportType"), Is.True);
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(reportType));
        });

        _toastService.Verify(ts => ts.Success(It.IsAny<string>(), true, "Right", "Bottom", 0), Times.AtLeastOnce);
    }

    [Test]
    public void Cancel_WithNullParameters_ReturnsRedirectToIndexWithNullParameters()
    {
        // Arrange
        _localizerMock.Setup(l => l["FilerPortal.Disclosure.Dashboard.CancelSuccessMessage"])
            .Returns(new LocalizedString("FilerPortal.Disclosure.Dashboard.CancelSuccessMessage", "Cancelled successfully"));

        // Act
        var result = _controller.Cancel(null, null, null);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = (RedirectToActionResult)result;

        Assert.That(redirectResult.RouteValues, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);

            Assert.That(redirectResult.RouteValues.TryGetValue("filerId", out var filerId), Is.True);
            Assert.That(filerId, Is.Null);

            Assert.That(redirectResult.RouteValues.TryGetValue("filingId", out var filingId), Is.True);
            Assert.That(filingId, Is.Null);

            Assert.That(redirectResult.RouteValues.TryGetValue("reportType", out var reportType), Is.True);
            Assert.That(reportType, Is.Null);
        });

        _toastService.Verify(ts => ts.Success(It.IsAny<string>(), true, "Right", "Bottom", 0), Times.AtLeastOnce);
    }

    [Test]
    public async Task Index_ViewNameIsGeneralInfo_ReturnsCorrectViewModel()
    {
        // Arrange
        var filerId = "123";
        var filingId = "456";
        var filingSummaryId = "789";
        var viewName = "GeneralInfo";
        var reportType = FilingType.LobbyistReport.Name;

        var expectedModel = new DisclosureLobbyingGeneralInfoViewModel
        {
            ReportType = "Lobbyist report",
            FilingTypeId = FilingType.LobbyistReport.Id
        };

        _disclosureCtlSvcMock
            .Setup(x => x.GetGeneralInfoViewForLobbyist(
                long.Parse(filerId, CultureInfo.InvariantCulture),
                long.Parse(filingId, CultureInfo.InvariantCulture),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedModel);

        // Act
        var result = await _controller.Index(
            filerId: filerId,
            filingId: filingId,
            filingSummaryId: filingSummaryId,
            viewName: viewName,
            reportType: reportType,
            cancellationToken: CancellationToken.None);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);

        var model = viewResult.Model as DisclosureLobbyingGeneralInfoViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(model.FilingTypeId, Is.EqualTo(FilingType.LobbyistReport.Id));

            Assert.That(viewResult.ViewData["PartialView"], Is.EqualTo("GeneralInfo"));
            Assert.That(viewResult.ViewData["ReportType"], Is.EqualTo(reportType));
        });

        _disclosureCtlSvcMock.Verify(
            x => x.GetGeneralInfoViewForLobbyist(
                long.Parse(filerId, CultureInfo.InvariantCulture),
                long.Parse(filingId, CultureInfo.InvariantCulture),
                It.IsAny<CancellationToken>()),
            Times.Once);

        _disclosureCtlSvcMock.Verify(
            x => x.BuildSummaryViewModel(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<HttpContext>(),
                It.IsAny<ITempDataDictionary>(),
                It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Test]
    public async Task UpdateLegislativeSession_FilingNotFound_ReturnsRedirectToGeneralInfoWithAlert()
    {
        // Arrange
        var id = 1L;
        var filerId = 2L;
        var legislativeSessionId = 3L;
        var reportType = FilingType.Report72h.Name;
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);

        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        // Act
        var result = await _controller.UpdateLegislativeSession(id, filerId, legislativeSessionId, reportType, _filingsApiMock.Object);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ControllerName, Is.EqualTo("AmendDisclosure"));
            Assert.That(redirect.ActionName, Is.EqualTo("Index"));
            Assert.That(redirect.RouteValues!["filerId"], Is.EqualTo(filerId));
            Assert.That(redirect.RouteValues["filingId"], Is.EqualTo(id));
            Assert.That(redirect.RouteValues["reportType"], Is.EqualTo("Report72h"));
        });

        _toastService.Verify(t => t.Success(It.IsAny<string>(), true, "Right", "Bottom", 0), Times.Once);
    }

    [Test]
    public async Task UpdateLegislativeSession_ValidUpdate_ReturnsRedirectToIndex()
    {
        // Arrange
        var id = 1L;
        var filerId = 2L;
        var legislativeSessionId = 3L;
        var reportType = FilingType.Report72h.Name;
        var existingFiling = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 0);

        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(existingFiling);
        _filingsApiMock
            .Setup(x => x.UpdateFiling(id, It.IsAny<UpsertFilingRequest>(), CancellationToken.None))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.UpdateLegislativeSession(id, filerId, legislativeSessionId, reportType, _filingsApiMock.Object);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(redirect.ActionName, Is.EqualTo("Index"));
            Assert.That(redirect.RouteValues!["filerId"], Is.EqualTo(filerId));
            Assert.That(redirect.RouteValues["filingId"], Is.EqualTo(id));
            Assert.That(redirect.RouteValues["reportType"], Is.EqualTo("Report72h"));
        });

        _toastService.Verify(t => t.Success(It.IsAny<string>(), true, "Right", "Bottom", 0), Times.Once);
    }

    [Test]
    public async Task UpdateLegislativeSession_UpdateFilingThrowsException_ReturnsRedirectToGeneralInfoWithDangerAlert()
    {
        // Arrange
        var id = 1L;
        var filerId = 1;
        var legislativeSessionId = 1;
        var reportType = FilingType.Report72h.Name;
        var existingFiling = new FilingItemResponse(
            id: 1,
            endDate: DateTime.Now,
            filerId: 1,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 1,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: FilingType.LobbyistEmployerReport.Name,
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );
        var exception = new ArgumentException("Test exception");

        _filingsApiMock
            .Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingFiling);

        _filingsApiMock
            .Setup(api => api.UpdateFiling(id, It.IsAny<UpsertFilingRequest>(), CancellationToken.None))
            .ThrowsAsync(exception);

        // Act
        var result = await _controller.UpdateLegislativeSession(id, filerId, legislativeSessionId, reportType, _filingsApiMock.Object);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ControllerName, Is.EqualTo("AmendDisclosure"));
            Assert.That(redirect.ActionName, Is.EqualTo("Index"));
            Assert.That(redirect.RouteValues!["viewName"], Is.EqualTo("GeneralInfo"));
            Assert.That(redirect.RouteValues["filerId"], Is.EqualTo(filerId));
            Assert.That(redirect.RouteValues["filingId"], Is.EqualTo(id));
            Assert.That(redirect.RouteValues["reportType"], Is.EqualTo("Report72h"));
        });
    }


    [Test]
    public async Task UpdateInHousePayment_ModelStateInvalid_ReturnsNotFound()
    {
        // Arrange
        var model = new LobbyistEmployerReportViewModel();
        _controller.ModelState.AddModelError("SomeField", "Some error");

        // Act
        var result = await _controller.UpdateInHousePayment(model, 1, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task UpdateInHousePayment_SuccessfulUpdate_ReturnsRedirectToAction()
    {
        // Arrange
        var model = new LobbyistEmployerReportViewModel
        {
            Id = 1,
            FilerId = 1,
            TotalPaymentsToInHouseLobbyists = 1000
        };
        var filingSummaryId = 1;

        var apiResponse = new UpdateLobbyistEmployerReportResponseDto(1, 100, true, new List<WorkFlowError>());
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        var disclosureFilingReports = new List<DisclosureReport>
            {
                new(1, "Name", "PaymentsToInHouseLobbyists", "status", 1)
        };

        _ = _disclosureCtlSvcMock.Setup(svc => svc.GetDisclosureFilingReportsById(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(disclosureFilingReports);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);
        _filingsApiMock.Setup(api => api.UpdateLobbyistEmployerReport(It.IsAny<long>(), It.IsAny<UpdateLobbyistEmployerReportDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(apiResponse);

        _disclosureCtlSvcMock.Setup(service => service.UpdateFilingSummaryById(It.IsAny<long>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        var successMessage = "Success message";
        _localizerMock.Setup(localizer => localizer["FilerPortal.Disclosure.Dashboard.UpdateTransactionSuccessMessage"]).Returns(new LocalizedString("success", successMessage));

        // Act
        var result = await _controller.UpdateInHousePayment(model, filingSummaryId, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task UpdateInHousePayment_FailedUpdate_ReturnsViewWithErrors()
    {
        // Arrange
        var model = new LobbyistEmployerReportViewModel
        {
            Id = 1,
            FilerId = 1,
            TotalPaymentsToInHouseLobbyists = 1000
        };
        var filingSummaryId = 1;

        var apiResponse = new UpdateLobbyistEmployerReportResponseDto(1, 100, false, new List<WorkFlowError>() { new("123", "error", "TotalPaymentsToInHouseLobbyists", "unvalid") });
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        var disclosureFilingReports = new List<DisclosureReport>
            {
                new(1, "Name", "PaymentsToInHouseLobbyists", "status", 1)
        };

        _ = _disclosureCtlSvcMock.Setup(svc => svc.GetDisclosureFilingReportsById(It.IsAny<long>(), CancellationToken.None))
            .ReturnsAsync(disclosureFilingReports);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);
        _filingsApiMock.Setup(api => api.UpdateLobbyistEmployerReport(It.IsAny<long>(), It.IsAny<UpdateLobbyistEmployerReportDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(apiResponse);

        // Act
        var result = await _controller.UpdateInHousePayment(model, filingSummaryId, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task HandleUpdateStatusFilingSummary_ModelStateInvalid_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error");
        var mockFilingItemResponse = new FilingItemResponse(
            id: 123,
            endDate: DateTime.Now,
            filerId: 456,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: "Test Filing Type",
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        // Act
        var result = await _controller.HandleUpdateStatusFilingSummary(1, 1, 1, "viewName", "reportType", "status", CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task HandleUpdateStatusFilingSummary_SuccessfulUpdate_ReturnsRedirectToAction()
    {
        // Arrange
        var filerId = 1L;
        var filingId = 1L;
        var filingSummaryId = 1L;
        var viewName = "Dashboard";
        var reportType = "reportType";
        var filingSummaryStatusName = "InProgress";
        _localizerMock.Setup(l => l[$"{ResourceConstants.DisclosureSummaryBase}.{viewName}"]).Returns(new LocalizedString("key", "Filing Report"));
        _disclosureCtlSvcMock.Setup(s => s.UpdateFilingSummaryById(filingSummaryId, filingSummaryStatusName, It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.HandleUpdateStatusFilingSummary(filerId, filingId, filingSummaryId, viewName, reportType, filingSummaryStatusName, CancellationToken.None);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues!["filerId"], Is.EqualTo(filerId));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(filingId));
        });
    }

    [Test]
    public async Task HandleUpdateStatusFilingSummary_SuccessfulUpdate_ReturnsRedirectToAction_WhenFilingSummaryIdNull()
    {
        // Arrange
        var filerId = 1L;
        var filingId = 1L;
        var viewName = "Dashboard";
        var reportType = "reportType";
        var filingSummaryStatusName = "InProgress";
        var disclosureFilingReports = new List<DisclosureReport>
        {
            new(1, "Filing Report", "ToLobbyingCoalitionSummary", "status", 1)
        };

        _ = _disclosureCtlSvcMock.Setup(svc => svc.GetDisclosureFilingReportsById(It.IsAny<long>(), CancellationToken.None))
            .ReturnsAsync(disclosureFilingReports);
        _localizerMock.Setup(l => l[$"{ResourceConstants.DisclosureSummaryBase}.{viewName}"]).Returns(new LocalizedString("key", "Filing Report"));
        _disclosureCtlSvcMock.Setup(s => s.UpdateFilingSummaryById(It.IsAny<long>(), filingSummaryStatusName, It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.HandleUpdateStatusFilingSummary(filerId, filingId, null, viewName, reportType, filingSummaryStatusName, CancellationToken.None);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues!["filerId"], Is.EqualTo(filerId));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(filingId));
        });
    }

    [Test]
    public async Task UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition_ExceptionThrown_ReturnsViewWithError()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 1,
            FilingSummaryId = 123,
            FilerId = 456,
            IsMemberOfLobbyingCoalition = "true"
        };

        var exception = new ArgumentException("Test exception");

        _localizerMock.Setup(l => l[ResourceConstants.UpdateFailMessage]).Returns(new LocalizedString("key", "Update failed"));

        _filingsApiMock.Setup(api => api.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(It.IsAny<long>(), It.IsAny<UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalitionDto>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        // Act
        var result = await _controller.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(model, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult!.ViewName, Is.EqualTo("Index"));
            Assert.That(viewResult.Model, Is.EqualTo(model));
        });

        _loggerMock.Verify(log => log.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((obj, _) => obj.ToString()!.Contains("Update lobbyist employer payments made to lobbying coalition Failed")),
            It.Is<Exception>(ex => ex == exception),
            It.Is<Func<It.IsAnyType, Exception?, string>>((func, _) => func != null)
        ), Times.Once);

        Assert.That(_controller.ViewBag.PartialView, Is.EqualTo("CoalitionPayment"));
    }

    [Test]
    public async Task HandleUpdateStatusFilingSummary_ExceptionThrown_ReturnsRedirectToActionAndLogsError()
    {
        // Arrange
        var filerId = 1L;
        var filingId = 1L;
        var filingSummaryId = 1L;
        var viewName = "Dashboard";
        var reportType = "ReportType";
        var filingSummaryStatusName = "InProgress";
        var exception = new ArgumentException("Test exception");

        _localizerMock.Setup(l => l[ResourceConstants.UpdateFailMessage]).Returns(new LocalizedString("key", "Update failed"));
        _localizerMock.Setup(l => l[$"{ResourceConstants.DisclosureSummaryBase}.{viewName}"]).Returns(new LocalizedString("key", "Filing Report"));

        _disclosureCtlSvcMock.Setup(s => s.UpdateFilingSummaryById(filingSummaryId, filingSummaryStatusName, It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        // Act
        var result = await _controller.HandleUpdateStatusFilingSummary(filerId, filingId, filingSummaryId, viewName, reportType, filingSummaryStatusName, CancellationToken.None);

        // Assert
        _loggerMock.Verify(log => log.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((obj, _) => obj.ToString()!.Contains("Filing Report")),
            It.Is<Exception>(ex => ex == exception),
            It.Is<Func<It.IsAnyType, Exception?, string>>((func, _) => func != null)
        ), Times.Once);

        var redirectResult = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult!.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult!.RouteValues!["filerId"], Is.EqualTo(filerId));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(filingId));
            Assert.That(redirectResult.RouteValues["viewName"], Is.EqualTo(viewName));
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(reportType));
            Assert.That(redirectResult.RouteValues["filingSummaryId"], Is.EqualTo(filingSummaryId));
        });
    }

    [Test]
    public async Task UpdateLobbyistEmployerPaymentsToLobbyingFirms_RedirectsToIndexWithCorrectRouteValues()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 123,
            FilerId = 456,
            FilingSummaryId = 0
        };

        var fakeFilingSummaryId = 999;
        var fakeReportType = "Dashboard";

        _disclosureCtlSvcMock
            .Setup(s => s.UpdateFilingSummaryById(fakeFilingSummaryId, FilingSummaryStatusModel.InProgress.Name, false, It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask)
            .Verifiable();
        var mockFilingItemResponse = new FilingItemResponse(
            id: 123,
            endDate: DateTime.Now,
            filerId: 456,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: "Dashboard",
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );

        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        var disclosureFilingReports = new List<DisclosureReport>
            {
                new(1, "Name", "MadeToLobbyingFirmsSummary", "status", 1)
        };

        _ = _disclosureCtlSvcMock.Setup(svc => svc.GetDisclosureFilingReportsById(model.Id!.Value, CancellationToken.None))
            .ReturnsAsync(disclosureFilingReports);

        // Act
        var result = await _controller.UpdateLobbyistEmployerPaymentsToLobbyingFirms(model, _filingsApiMock.Object);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.Id));
            Assert.That(redirectResult.RouteValues["viewName"], Is.EqualTo("Dashboard"));
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(fakeReportType));
        });
    }

    [Test]
    public async Task UpdateLobbyistEmployerPaymentsToLobbyingFirms_ReturnsNotFoundIfModelStateInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model state");
        var model = new DisclosureSummaryViewModel();

        // Act
        var result = await _controller.UpdateLobbyistEmployerPaymentsToLobbyingFirms(model, _filingsApiMock.Object);

        // Assert
        Assert.That(result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task UpdateLobbyistEmployerPaymentsToLobbyingFirms_ThrowsException_ReturnsIndexViewWithError()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 1,
            FilerId = 1001,
            FilingSummaryId = 1
        };
        _disclosureCtlSvcMock
            .Setup(svc => svc.UpdateFilingSummaryById(It.IsAny<long>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Boom"));
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);

        // Act
        var result = await _controller.UpdateLobbyistEmployerPaymentsToLobbyingFirms(model, _filingsApiMock.Object);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult.ViewName, Is.EqualTo("Index"));
            Assert.That(viewResult.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task Index_ShouldPopulatePaymentsMadeToLobbyingFirmsGridModel()
    {
        // Arrange
        var filerId = "123";
        var filingId = "456";
        var longFilingId = long.Parse(filingId, CultureInfo.InvariantCulture);
        var cancellationToken = CancellationToken.None;
        var mockData = new List<PaymentMadeToLobbyingFirmsResponse>
        {
            new(amountThisPeriod: 100, cumulativeAmount: 100, filerId: longFilingId, firmName: "abc",
                advancesOrOtherPaymentsAmount: 0, advancesOrOtherPaymentsExplanation: string.Empty,
                contact: It.IsAny<ContactResponseDto>(), feesAndRetainersAmount: 1, reimbursementOfExpensesAmount: 1),
            new(amountThisPeriod: 150, cumulativeAmount: 150, filerId: longFilingId, firmName: "def",
                advancesOrOtherPaymentsAmount: 0, advancesOrOtherPaymentsExplanation: string.Empty,
                contact: It.IsAny<ContactResponseDto>(), feesAndRetainersAmount: 1, reimbursementOfExpensesAmount: 1)
        };

        var expectedSubtotal = mockData.Sum(x => x.AmountThisPeriod);

        _disclosureCtlSvcMock
        .Setup(x => x.BuildSummaryViewModel(
            FilingSummaryTypeModel.MadeToLobbyingFirmsSummary.Name,
            filerId,
            filingId,
            "1",
            "1",
            null,
            It.IsAny<HttpContext>(),
            It.IsAny<ITempDataDictionary>(),
            cancellationToken))
        .ReturnsAsync(new DisclosureSummaryViewModel
        {
            ViewName = FilingSummaryTypeModel.MadeToLobbyingFirmsSummary.Name,
            Id = long.Parse(filingId, CultureInfo.InvariantCulture),
            FilerId = long.Parse(filerId, CultureInfo.InvariantCulture),
            FilingSummaryId = long.Parse("1", CultureInfo.InvariantCulture),
            FirmPaymentsGridModel = new SmallDataGridModel
            {
                GridId = "OtherPaymentsGrid",
                DataSource = mockData,
                Columns = new List<DataGridColumn>
                {
                    new()
                    {
                        Field = nameof(PaymentMadeToLobbyingFirmsResponse.FilerId),
                        HeaderText = "Lobbying firm ID"
                    },
                    new()
                    {
                        Field = nameof(PaymentMadeToLobbyingFirmsResponse.FirmName),
                        HeaderText = "Name"
                    },
                    new()
                    {
                        Field = nameof(PaymentMadeToLobbyingFirmsResponse.AmountThisPeriod),
                        HeaderText = "Total this period",
                        IsCurrency = true
                    },
                    new()
                    {
                        Field = nameof(PaymentMadeToLobbyingFirmsResponse.CumulativeAmount),
                        HeaderText = "Cumulative total to date",
                        IsCurrency = true
                    }
                },
            },
            Subtotal = expectedSubtotal
        });

        // Act
        var result = await _controller.Index(
            filerId,
            filingId,
            filingSummaryId: "1",
            filingStatus: "1",
            viewName: FilingSummaryTypeModel.MadeToLobbyingFirmsSummary.Name,
            cancellationToken: cancellationToken
        ) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var vm = result.Model as DisclosureSummaryViewModel;
        Assert.That(vm, Is.Not.Null);
        var smallGrid = vm.FirmPaymentsGridModel as SmallDataGridModel;
        Assert.That(smallGrid, Is.Not.Null, "Expected the grid model to be a SmallDataGridModel");
        Assert.That(smallGrid.DataSource, Is.EqualTo(mockData));
        Assert.That(result.ViewData["Subtotal"], Is.EqualTo(expectedSubtotal));
    }

    [Test]
    public async Task UpdateLobbyistEmployerOtherPaymentsToInfluence_InvalidResponse_ReturnsIndexView()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 1,
            FilerId = 1001,
            FilingSummaryId = 99
        };

        _filingsApiMock
            .Setup(api => api.UpdateLobbyistEmployerLumpSums(
                1,
                It.IsAny<UpdateLobbyistEmployerReportDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new UpdateLobbyistEmployerLumpSumResponseDto(
                filingId: 1,
                valid: false,
                validationErrors: new List<WorkFlowError>
                {
                    new("", "FieldA", "Validation", "Bad A")
                },
                totalOverheadExpense: 0,
                totalUnderThresholdPayments: 0
            ));

        _filingsApiMock
            .Setup(api => api.GetFiling(1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new FilingItemResponse(
                id: 123,
                endDate: DateTime.Now,
                filerId: 456,
                parentId: null,
                startDate: DateTime.Now.AddMonths(-1),
                status: 2,
                filerName: "Test Filer",
                submittedDate: null,
                version: 1,
                filingType: "Test Filing Type",
                filingStatus: "Test Filing Status",
                amendmentExplanation: string.Empty,
                legislativeSessionId: 1,
                totalPaymentsPucActivity: 1,
                filingPeriodId: 1,
                filingTypeId: 1
            ));

        // Act
        var result = await _controller.UpdateLobbyistEmployerOtherPaymentsToInfluence(
            model,
            _filingsApiMock.Object,
            filingSummaryId: 99,
            cancellationToken: CancellationToken.None
        );

        // Assert
        var redirectResult = (RedirectToActionResult)result;
        Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
        Assert.That(_controller.ViewBag.PartialView,
            Is.EqualTo(FilingSummaryTypeModel.OtherPaymentsToInfluenceSummary.Name));
        Assert.That(redirectResult.RouteValues, Is.Not.Null);
        Assert.That(redirectResult.RouteValues["filingSummaryId"], Is.EqualTo(99));
    }

    [Test]
    public async Task UpdateLobbyistEmployerOtherPaymentsToInfluence_ValidResponse_RedirectsToDashboard()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 456,
            FilerId = 123,
            FilingSummaryId = 7
        };

        _filingsApiMock
          .Setup(api => api.UpdateLobbyistEmployerLumpSums(
              456,
              It.IsAny<UpdateLobbyistEmployerReportDto>(),
              It.IsAny<CancellationToken>()))
          .ReturnsAsync(new UpdateLobbyistEmployerLumpSumResponseDto(
              filingId: 456,
              valid: true,
              validationErrors: new List<WorkFlowError>(),
              totalOverheadExpense: 10,
              totalUnderThresholdPayments: 20
          ));

        _filingsApiMock
          .Setup(api => api.GetFiling(
              456,
              It.IsAny<CancellationToken>()))
          .ReturnsAsync(new FilingItemResponse(
              id: 123,
              endDate: DateTime.Now,
              filerId: 456,
              parentId: null,
              startDate: DateTime.Now.AddMonths(-1),
              status: 2,
              filerName: "Test Filer",
              submittedDate: null,
              version: 1,
              filingType: "Test Filing Type",
              filingStatus: "Test Filing Status",
              amendmentExplanation: string.Empty,
              legislativeSessionId: 1,
              totalPaymentsPucActivity: 1,
              filingPeriodId: 1,
              filingTypeId: 1
          ));

        _disclosureCtlSvcMock
          .Setup(svc => svc.UpdateFilingSummaryById(
              7,
              FilingSummaryStatusModel.InProgress.Name,
              false,
              It.IsAny<CancellationToken>()))
          .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.UpdateLobbyistEmployerOtherPaymentsToInfluence(
          model,
          _filingsApiMock.Object,
          filingSummaryId: null,
          cancellationToken: CancellationToken.None
        );

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirect = (RedirectToActionResult)result;
        Assert.That(redirect.RouteValues, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("Index"));
            Assert.That(redirect.RouteValues["viewName"], Is.EqualTo("Dashboard"));
            Assert.That(redirect.RouteValues["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirect.RouteValues["filingId"], Is.EqualTo(model.Id));
        });
    }

    [Test]
    public void SaveAndClose_ReturnsRedirectToTemporaryDashboard()
    {
        // Arrange
        _ = _localizerMock.Setup(l => l["FilerPortal.Disclosure.Dashboard.SaveSuccessMessage"])
            .Returns(new LocalizedString("FilerPortal.Disclosure.Dashboard.SaveSuccessMessage", "Report saved successfully."));

        // Act
        var result = _controller.SaveAndClose();

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult!.ActionName, Is.EqualTo("TemporaryDashboard"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Filing"));
        });
        _toastService.Verify(t => t.Success(It.IsAny<string>(), true, "Right", "Bottom", 0), Times.Once);
    }

    [Test]
    public async Task UpdatePucActivityPayment_ValidModel_UpdateSuccessful_RedirectsToDashboard()
    {
        var model = new DisclosureSummaryViewModel
        {
            Id = 1,
            FilerId = 1,
            FilingSummaryId = 1,
            TotalPaymentsPucActivity = 1000
        };

        var filing = new FilingItemResponse(
            id: 1,
            endDate: DateTime.Now,
            filerId: 1,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: "LobbyistEmployerReport",
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );

        var filingSummaries = new List<DisclosureReport>
        {
            new(model.FilingSummaryId.Value, FilingSummaryTypeModel.PucActivitySummary.Name, FilingSummaryTypeModel.PucActivitySummary.Name, FilingSummaryStatusModel.NotStarted.Name, FilingSummaryTypeModel.PucActivitySummary.Id)
        };
        var validationResponse = new UpdatePucActivityPaymentResponseDto(null, null, null, valid: true, validationErrors: []);

        _filingsApiMock.Setup(x => x.GetFiling(model.Id.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filing);

        _disclosureCtlSvcMock.Setup(x => x.GetDisclosureFilingReportsById(model.Id.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filingSummaries);

        _filingsApiMock.Setup(x => x.UpdatePucActivityPayment(model.Id.Value, It.IsAny<UpdatePucActivityPaymentRequestDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validationResponse);

        _disclosureCtlSvcMock.Setup(x => x.UpdateFilingSummaryById(model.FilingSummaryId.Value, FilingSummaryStatusModel.InProgress.Name, false, It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        var result = await _controller.UpdatePucActivityPayment(model, _filingsApiMock.Object, CancellationToken.None);

        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo(DisclosureConstants.Controller.IndexViewName));
            Assert.That(redirect.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirect.RouteValues!["filingId"], Is.EqualTo(model.Id));
            Assert.That(redirect.RouteValues!["viewName"], Is.EqualTo(DisclosureConstants.Controller.DashboardViewName));
            Assert.That(redirect.RouteValues!["reportType"], Is.EqualTo("LobbyistEmployerReport"));
        });
    }

    [Test]
    public async Task UpdatePucActivityPayment_InvalidModelState_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("TotalPaymentsPucActivity", "Required");

        var model = new DisclosureSummaryViewModel
        {
            Id = 67890,
            FilerId = 12345,
            TotalPaymentsPucActivity = 1000
        };

        // Act
        var result = await _controller.UpdatePucActivityPayment(model, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task UpdatePucActivityPayment_ValidationFails_ReturnsViewWithErrors()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 67890,
            FilerId = 12345,
            FilingSummaryId = null,
            TotalPaymentsPucActivity = 9999
        };

        var filing = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        var summaryId = 1111;
        var filingSummaries = new List<DisclosureReport>
        {
            new(summaryId, FilingSummaryTypeModel.PucActivitySummary.Name, FilingSummaryTypeModel.PucActivitySummary.Name, FilingSummaryStatusModel.NotStarted.Name, FilingSummaryTypeModel.PucActivitySummary.Id)
        };

        var validationErrors = new List<WorkFlowError>
        {
            new("123", "123", "TotalPaymentsPucActivity", "Invalid value")
        };

        var validationResponse = new UpdatePucActivityPaymentResponseDto(null, null, null, valid: false, validationErrors);

        _filingsApiMock.Setup(x => x.GetFiling(model.Id.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filing);

        _disclosureCtlSvcMock.Setup(x => x.GetDisclosureFilingReportsById(model.Id.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filingSummaries);

        _filingsApiMock.Setup(x => x.UpdatePucActivityPayment(model.Id.Value, It.IsAny<UpdatePucActivityPaymentRequestDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validationResponse);

        // Act
        var result = await _controller.UpdatePucActivityPayment(model, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult!.ViewName, Is.EqualTo(DisclosureConstants.Controller.IndexViewName));
            Assert.That(viewResult.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task UpdatePucActivityPayment_ExceptionThrown_ReturnsViewWithError()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 67890,
            FilerId = 12345,
            FilingSummaryId = null,
            TotalPaymentsPucActivity = 1500
        };

        var filing = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        var summaryId = 1111;
        var filingSummaries = new List<DisclosureReport>
        {
            new(summaryId, FilingSummaryTypeModel.PucActivitySummary.Name, FilingSummaryTypeModel.PucActivitySummary.Name, FilingSummaryStatusModel.NotStarted.Name, FilingSummaryTypeModel.PucActivitySummary.Id)
        };

        _filingsApiMock.Setup(x => x.GetFiling(model.Id.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filing);

        _disclosureCtlSvcMock.Setup(x => x.GetDisclosureFilingReportsById(model.Id.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filingSummaries);

        _filingsApiMock.Setup(x => x.UpdatePucActivityPayment(It.IsAny<long>(), It.IsAny<UpdatePucActivityPaymentRequestDto>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Simulated API failure"));

        // Act
        var result = await _controller.UpdatePucActivityPayment(model, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult!.ViewName, Is.EqualTo(DisclosureConstants.Controller.IndexViewName));
            Assert.That(viewResult.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task SaveAndCloseActionsLobbied_ValidModel_ReturnsRedirectToActionWithSuccess()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 1,
            FilerId = 1,
            AdvertLegislation = true,
            AdvertAdminActions = false,
            AdvertOther = true,
            AdvertOtherActionsLobbied = "Test lobbied action"
        };

        var filing = new FilingItemResponse(
            id: 1,
            endDate: DateTime.Now,
            filerId: 1,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 1,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: FilingType.LobbyistEmployerReport.Name,
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );
        var result = new ActionsLobbiedSummaryResponse(new List<ActionsLobbiedResponseDto>(),
             new List<ActionsLobbiedResponseDto>(), 1, 1, new FilingSummaryStatus(1, "Draft"), "a",
             new List<ActionsLobbiedResponseDto>(), true, new List<WorkFlowError>());

        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(filing);

        _filingsApiMock.Setup(api => api.UpdateLobbyistEmployerReportActionsLobbied(
                model.Id!.Value,
                It.IsAny<LobbyistEmployerActionsLobbiedRequest>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);

        // Act
        var response = await _controller.SaveAndCloseActionsLobbied(model, CancellationToken.None);

        // Assert
        var redirectResult = response as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult!.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.Id));
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(filing.FilingType));
        });
    }

    [Test]
    public async Task SaveAndCloseActionsLobbied_ValidationErrors_ReturnsViewWithValidationErrors()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 1,
            FilerId = 1,
            AdvertLegislation = true,
            AdvertAdminActions = false,
            AdvertOther = true,
            AdvertOtherActionsLobbied = "Test lobbied action"
        };

        var filing = new FilingItemResponse(
            id: 1,
            endDate: DateTime.Now,
            filerId: 1,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 1,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: FilingType.LobbyistEmployerReport.Name,
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );
        var validationErrors = new List<WorkFlowError>
        {
            new("Error1", "ErrorCode1", "OtherActionDescription", "Error message 1"),
            new("Error2", "ErrorCode2", "LobbyingAdvertisementSubjects", "Error message 2")
        };
        var result = new ActionsLobbiedSummaryResponse(new List<ActionsLobbiedResponseDto>(),
            new List<ActionsLobbiedResponseDto>(), 1, 1, new FilingSummaryStatus(1, "Draft"), "a",
            new List<ActionsLobbiedResponseDto>(), true, validationErrors);

        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(filing);

        _filingsApiMock.Setup(api => api.UpdateLobbyistEmployerReportActionsLobbied(
                It.IsAny<long>(),
                It.IsAny<LobbyistEmployerActionsLobbiedRequest>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);

        _disclosureCtlSvcMock.Setup(svc =>
                svc.GetDisclosureFilingReportsById(model.Id.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(
                [new DisclosureReport(1, "Name", FilingSummaryTypeModel.ActionsLobbiedSummary.Name, "status", 1)]);

        // Act
        var response = await _controller.SaveAndCloseActionsLobbied(model, CancellationToken.None);

        // Assert
        var redirectResult = response as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult!.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.Id));
        });
    }

    [Test]
    public async Task SaveAndCloseActionsLobbied_ApiException_LogsErrorAndReturnsViewWithError()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 1,
            FilerId = 1,
            AdvertLegislation = true,
            AdvertAdminActions = false,
            AdvertOther = true,
            AdvertOtherActionsLobbied = "Test lobbied action",
            FilingSummaryId = 100
        };

        var filing = new FilingItemResponse(
            id: 1,
            endDate: DateTime.Now,
            filerId: 1,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 1,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: FilingType.LobbyistEmployerReport.Name,
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );
        var fakeResponse = new HttpResponseMessage(HttpStatusCode.UnprocessableEntity)
        {
            Content = new StringContent(JsonConvert.SerializeObject(new[]
            {
                new { FieldName = "OtherActionDescription", Message = "Invalid", ErrorType = "Validation" },
                new { FieldName = "LobbyingAdvertisementSubjects", Message = "Invalid", ErrorType = "Validation" }
            }))
        };

        var apiException = await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Post,
            fakeResponse,
            new RefitSettings());


        _filingsApiMock.Setup(api => api.GetFiling(model.Id!.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filing);

        _filingsApiMock.Setup(api => api.UpdateLobbyistEmployerReportActionsLobbied(
                model.Id!.Value,
                It.IsAny<LobbyistEmployerActionsLobbiedRequest>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(apiException);

        _localizerMock.Setup(l => l[ResourceConstants.UpdateFailMessage])
            .Returns(new LocalizedString(ResourceConstants.UpdateFailMessage, "Update failed"));

        // Act
        var response = await _controller.SaveAndCloseActionsLobbied(model, CancellationToken.None);

        // Assert
        var redirectResult = response as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult!.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.Id));
            Assert.That(redirectResult.RouteValues["viewName"],
                Is.EqualTo(FilingSummaryTypeModel.ActionsLobbiedSummary.Name));
            Assert.That(redirectResult.RouteValues["filingSummaryId"], Is.EqualTo(model.FilingSummaryId));
        });

        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => true),
                It.IsAny<ApiException>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }

    [Test]
    public async Task SaveAndCloseActionsLobbied_IdIsNull_ReturnsNotFound()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel { Id = null };

        // Act
        var result = await _controller.SaveAndCloseActionsLobbied(model, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task SaveAndCloseActionsLobbied_Report72h_SuccessfulUpdate_RedirectsWithSuccess()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 1,
            FilerId = 1,
            AdvertLegislation = true,
            AdvertAdminActions = false,
            AdvertOther = false,
            FilingSummaryId = 100
        };

        var mockResponse = new ActionsLobbiedSummaryResponse(
            administrativeActions: new List<ActionsLobbiedResponseDto>(),

            assemblyBillActions: new List<ActionsLobbiedResponseDto>(),

            filingId: 1,

            filingSummaryId: 1,

            filingSummaryStatus: new FilingSummaryStatus(1, "In Progress"),

            otherActionsLobbied: "Some test value",

            senateBillActions: new List<ActionsLobbiedResponseDto>(),

            valid: true,

            validationErrors: new List<WorkFlowError>()
        );


        var filing = new FilingItemResponse(
            id: 1,
            endDate: DateTime.Now,
            filerId: 1,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 1,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: FilingType.Report72h.Name,
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );

        _filingsApiMock.Setup(api => api.GetFiling(model.Id!.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filing);

        _filingsApiMock.Setup(api => api.UpdateReport72HActionsLobbied(
                model.Id!.Value,
                It.IsAny<Report72HActionsLobbiedRequestDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockResponse);

        // Act
        var response = await _controller.SaveAndCloseActionsLobbied(model, CancellationToken.None);

        // Assert
        var redirectResult = response as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult!.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.Id));
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(FilingType.Report72h.Name));
        });
    }

    [Test]
    public async Task SaveAndCloseActionsLobbied_ModelStateInvalid_CallsHandleUpdateActionsLobbiedFailed()
    {
        // Arrange
        var filing = new FilingItemResponse(
            id: 123,
            endDate: DateTime.Now,
            filerId: 456,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: "Test Filing Type",
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );

        var model = new DisclosureSummaryViewModel { Id = 1, FilerId = 2 };
        _controller.ModelState.AddModelError("key", "error");

        _filingsApiMock.Setup(api => api.GetFiling(model.Id!.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filing);

        _localizerMock.Setup(l => l[ResourceConstants.UpdateFailMessage])
            .Returns(new LocalizedString(ResourceConstants.UpdateFailMessage, "Update failed"));

        // Act
        var result = await _controller.SaveAndCloseActionsLobbied(model, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task SaveAndCloseActionsLobbied_UnrecognizedFilingType_ReturnsNotFound()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 12345,
            FilerId = 6789,
            AdvertLegislation = true,
            AdvertAdminActions = false,
            AdvertOther = false,
            FilingSummaryId = 100
        };

        var filing = new FilingItemResponse(
            id: 123,
            endDate: DateTime.Now,
            filerId: 456,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: "Test Filing Type",
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );

        _filingsApiMock.Setup(api => api.GetFiling(model.Id!.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filing);

        // Act
        var response = await _controller.SaveAndCloseActionsLobbied(model, CancellationToken.None);

        // Assert
        Assert.That(response, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task UpsertNewNonRegisteredLobbyist_InvalidModelState_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model");
        var model = new NonRegisteredLobbyistFormModel() { FirstName = "FN", LastName = "LN" };

        // Act
        var result = await _controller.UpsertNewNonRegisteredLobbyist(model, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task UpsertNewNonRegisteredLobbyist_ValidCreate_RedirectsWithSuccessToast()
    {
        // Arrange
        var model = new NonRegisteredLobbyistFormModel
        {
            FilingSummaryId = 1,
            FirstName = "John",
            LastName = "Doe",
            IsEdit = false,
            Id = null,
            FilerId = 10,
            FilingId = 20
        };

        _disclosureCtlSvcMock
            .Setup(svc => svc.UpsertNonRegisteredLobbyist(
                It.IsAny<DisclosureFilingNonRegisteredLobbyistRequest>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new DisclosureFilingNonRegisteredLobbyistResponseDto(1, "FN", 1, "LN", "FN LN"));

        // Act
        var result = await _controller.UpsertNewNonRegisteredLobbyist(model, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task DeleteNonRegisteredLobbyist_InvalidModelState_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model");

        // Act
        var result = await _controller.DeleteNonRegisteredLobbyist(1, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.StatusCode, Is.EqualTo(404));
    }

    [Test]
    public async Task DeleteNonRegisteredLobbyist_ValidId_CallsServiceAndReturnsJsonResult()
    {
        // Arrange
        var id = 123;

        _disclosureCtlSvcMock
            .Setup(svc => svc.DeleteRegisteredLobbyist(id, It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.DeleteNonRegisteredLobbyist(id, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Value, Is.EqualTo(id));
        _disclosureCtlSvcMock.Verify(svc => svc.DeleteRegisteredLobbyist(id, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Test]
    public async Task ValidateReport72HActionsLobbiedAgencies_ModelStateInvalid_ReturnsEmptyDictionary()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error");
        var body = new List<AdministrativeActionViewModel>();

        // Act
        var result = await _controller.ValidateReport72HActionsLobbiedAgencies(body);

        // Assert
        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task ValidateReport72HActionsLobbiedAgencies_ValidationErrorsReturned_ReturnsFormattedValidationMessages()
    {
        // Arrange
        var body = new List<AdministrativeActionViewModel> { new() { AgencyId = 1, Name = "test" } };
        var errors = new List<WorkFlowError>
        {
            new("SomeField1", "E001", "FieldOne1", "Invalid value for {{Field Name}}"),
            new("SomeField2", "E002", "FieldTwo2", "Required {{Field Name}}")
        };
        var cancellationToken = CancellationToken.None;

        var response = new ValidateReport72HActionsLobbiedResponseDto(errors);
        _filingsApiMock.Setup(api => api.ValidateReport72HActionsLobbiedAgencies(It.IsAny<ValidateReport72HActionsLobbiedRequestDto>(), cancellationToken))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.ValidateReport72HActionsLobbiedAgencies(body);

        // Assert
        Assert.That(result, Has.Count.EqualTo(2));
        Assert.Multiple(() =>
        {
            Assert.That(result["FieldOne1"].Message, Does.Contain("Field One"));
            Assert.That(result["FieldTwo2"].Message, Does.Contain("Field Two"));
        });
    }

    [Test]
    public async Task ValidateReport72HActionsLobbiedAgencies_NoValidationErrors_ReturnsEmptyDictionary()
    {
        // Arrange
        var body = new List<AdministrativeActionViewModel> { new() { AgencyId = 1, Name = "test" } };
        var response = new ValidateReport72HActionsLobbiedResponseDto(new List<WorkFlowError>());
        var cancellationToken = CancellationToken.None;

        _filingsApiMock.Setup(api => api.ValidateReport72HActionsLobbiedAgencies(It.IsAny<ValidateReport72HActionsLobbiedRequestDto>(), cancellationToken))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.ValidateReport72HActionsLobbiedAgencies(body);

        // Assert
        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task ValidateReport72HActionsLobbiedBills_ModelStateInvalid_ReturnsEmptyDictionary()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error");
        var body = new List<LegislativeBillViewModel>();

        // Act
        var result = await _controller.ValidateReport72HActionsLobbiedBills(body);

        // Assert
        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task ValidateReport72HActionsLobbiedBills_ValidationErrorsReturned_ReturnsFormattedValidationMessages()
    {
        // Arrange
        var body = new List<LegislativeBillViewModel> { new() };
        var errors = new List<WorkFlowError>
        {
            new("BillName1", "E100", "BillField1", "Bad input on {{Field Name}}")
        };
        var cancellationToken = CancellationToken.None;


        var response = new ValidateReport72HActionsLobbiedResponseDto(errors);
        _filingsApiMock.Setup(api => api.ValidateReport72HActionsLobbiedBills(It.IsAny<ValidateReport72HActionsLobbiedRequestDto>(), cancellationToken))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.ValidateReport72HActionsLobbiedBills(body);

        // Assert
        Assert.That(result, Has.Count.EqualTo(1));
        Assert.That(result["BillField1"].Message, Does.Contain("Bill Field"));
    }

    [Test]
    public async Task ValidateReport72HActionsLobbiedBills_NoValidationErrors_ReturnsEmptyDictionary()
    {
        // Arrange
        var body = new List<LegislativeBillViewModel> { new() };
        var response = new ValidateReport72HActionsLobbiedResponseDto(new List<WorkFlowError>());
        var cancellationToken = CancellationToken.None;

        _filingsApiMock.Setup(api => api.ValidateReport72HActionsLobbiedBills(It.IsAny<ValidateReport72HActionsLobbiedRequestDto>(), cancellationToken))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.ValidateReport72HActionsLobbiedBills(body);

        // Assert
        Assert.That(result, Is.Empty);
    }

    [Test]
    public void BuildLobbyistEmployerActionsLobbiedRequest_ReturnsExpectedRequest()
    {
        // Arrange
        var httpContext = new DefaultHttpContext();
        var tempData = new TempDataDictionary(httpContext, Mock.Of<ITempDataProvider>())
        {
            ["LegislationSenateBillTempDataKey"] = new List<LegislativeBillViewModel> { new() },
            ["LegislationAssyBillTempDataKey"] = new List<LegislativeBillViewModel> { new() },
            ["AdministrativeActionTempDataKey"] = new List<AdministrativeActionViewModel> { new() { Name = "Test", AgencyId = 1 } }
        };

        _controller.ControllerContext = new ControllerContext { HttpContext = httpContext };
        _controller.TempData = tempData;

        var model = new DisclosureSummaryViewModel
        {
            AdvertLegislation = true,
            AdvertAdminActions = true,
            AdvertOther = true,
            AdvertOtherActionsLobbied = "SampleOtherAction"
        };

        // Use reflection to invoke the private method
        var method = typeof(BaseDisclosureController).GetMethod("BuildLobbyistEmployerActionsLobbiedRequest",
            BindingFlags.Instance | BindingFlags.NonPublic);

        var result = method?.Invoke(_controller, new object[] { model }) as LobbyistEmployerActionsLobbiedRequest;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public void BuildLobbyistEmployerActionsLobbiedRequest_AdvertFalse_ReturnsExpectedRequest()
    {
        // Arrange
        var httpContext = new DefaultHttpContext();
        var tempData = new TempDataDictionary(httpContext, Mock.Of<ITempDataProvider>())
        {
            ["LegislationSenateBillTempDataKey"] = new List<LegislativeBillViewModel> { new() },
            ["LegislationAssyBillTempDataKey"] = new List<LegislativeBillViewModel> { new() },
            ["AdministrativeActionTempDataKey"] = new List<AdministrativeActionViewModel> { new() { Name = "Test", AgencyId = 1 } }
        };

        _controller.ControllerContext = new ControllerContext { HttpContext = httpContext };
        _controller.TempData = tempData;

        var model = new DisclosureSummaryViewModel
        {
            AdvertLegislation = false,
            AdvertAdminActions = false,
            AdvertOther = false,
            AdvertOtherActionsLobbied = "SampleOtherAction"
        };

        // Use reflection to invoke the private method
        var method = typeof(BaseDisclosureController).GetMethod("BuildLobbyistEmployerActionsLobbiedRequest",
            BindingFlags.Instance | BindingFlags.NonPublic);

        var result = method?.Invoke(_controller, new object[] { model }) as LobbyistEmployerActionsLobbiedRequest;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.AssemblyBills, Is.Empty);
            Assert.That(result.SenateBills, Is.Empty);
            Assert.That(result.AdministrativeActions, Is.Empty);
        });
    }

    [Test]
    public void BuildReport72HActionsLobbiedRequest_ReturnsExpectedRequest()
    {
        // Arrange
        var httpContext = new DefaultHttpContext();
        var tempData = new TempDataDictionary(httpContext, Mock.Of<ITempDataProvider>())
        {
            ["LegislationSenateBillTempDataKey"] = new List<LegislativeBillViewModel> { new() },
            ["LegislationAssyBillTempDataKey"] = new List<LegislativeBillViewModel> { new() },
            ["AdministrativeActionTempDataKey"] = new List<AdministrativeActionViewModel> { new() { Name = "Test", AgencyId = 1 } }
        };

        _controller.ControllerContext = new ControllerContext { HttpContext = httpContext };
        _controller.TempData = tempData;

        var model = new DisclosureSummaryViewModel
        {
            AdvertLegislation = true,
            AdvertAdminActions = true,
            AdvertOther = true,
            AdvertOtherActionsLobbied = "SampleOtherAction"
        };

        var method = typeof(BaseDisclosureController).GetMethod("BuildReport72HActionsLobbiedRequest",
            BindingFlags.Instance | BindingFlags.NonPublic);

        var result = method?.Invoke(_controller, new object[] { model }) as Report72HActionsLobbiedRequestDto;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public void BuildReport72HActionsLobbiedRequest_AdvertFalse_ReturnsExpectedRequest()
    {
        // Arrange
        var httpContext = new DefaultHttpContext();
        var tempData = new TempDataDictionary(httpContext, Mock.Of<ITempDataProvider>())
        {
            ["LegislationSenateBillTempDataKey"] = new List<LegislativeBillViewModel> { new() },
            ["LegislationAssyBillTempDataKey"] = new List<LegislativeBillViewModel> { new() },
            ["AdministrativeActionTempDataKey"] = new List<AdministrativeActionViewModel> { new() { Name = "Test", AgencyId = 1 } }
        };

        _controller.ControllerContext = new ControllerContext { HttpContext = httpContext };
        _controller.TempData = tempData;

        var model = new DisclosureSummaryViewModel
        {
            AdvertLegislation = false,
            AdvertAdminActions = false,
            AdvertOther = false,
            AdvertOtherActionsLobbied = "SampleOtherAction"
        };

        var method = typeof(BaseDisclosureController).GetMethod("BuildReport72HActionsLobbiedRequest",
            BindingFlags.Instance | BindingFlags.NonPublic);

        var result = method?.Invoke(_controller, new object[] { model }) as Report72HActionsLobbiedRequestDto;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.AssemblyBills, Is.Empty);
            Assert.That(result.SenateBills, Is.Empty);
            Assert.That(result.AdministrativeActions, Is.Empty);
        });

    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_InvalidModelState_ReturnsNotFound()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel();
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.UpdateContributionsInExistingStatements(model, new List<long>(), "LobbyistEmployerReport", _filingsApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_InvalidModelData_ReturnsBadRequest()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel { Id = null };

        // Act
        var result = await _controller.UpdateContributionsInExistingStatements(model, new List<long>(), "LobbyistEmployerReport", _filingsApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_MissingRelatedFilerIds_ReturnsRedirectWithError()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 123,
            ContributionsInExistingStatements = "true"
        };

        // Act
        var result = await _controller.UpdateContributionsInExistingStatements(model, new List<long>(), "LobbyistEmployerReport", _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_SuccessfulUpdate_RedirectsToDisclosureIndex()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 123,
            ContributionsInExistingStatements = "true"
        };
        var relatedFilerIds = new List<long> { 456 };

        _filingsApiMock
            .Setup(api => api.UpdateContributionsInExistingStatements(model.Id.Value, It.IsAny<UpdateContributionsInExistingStatementsDto>(), CancellationToken.None))
            .ReturnsAsync(new UpdateContributionsInExistingStatementsResponseDto(1, true, []));

        // Act
        var result = await _controller.UpdateContributionsInExistingStatements(model, relatedFilerIds, "LobbyistEmployerReport", _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.Id));
        });
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_ExceptionThrown_RedirectsWithError()
    {
        // Arrange  
        var model = new DisclosureSummaryViewModel
        {
            Id = 123,
            ContributionsInExistingStatements = "true"
        };
        var relatedFilerIds = new List<long> { 456 };

        _filingsApiMock
            .Setup(api => api.UpdateContributionsInExistingStatements(model.Id.Value, It.IsAny<UpdateContributionsInExistingStatementsDto>(), CancellationToken.None))
            .ThrowsAsync(new InvalidOperationException("API error"));

        // Act  
        var result = await _controller.UpdateContributionsInExistingStatements(model, relatedFilerIds, "LobbyistEmployerReport", _filingsApiMock.Object, CancellationToken.None);

        // Assert  
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_ValidRequest_WithTrueFlag_RedirectsToIndex()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 123,
            FilerId = 456,
            ContributionsInExistingStatements = "TRUE"
        };
        var relatedFilerIds = new List<long> { 789, 101112 };
        var reportType = "LobbyistEmployerReport";

        var response = new UpdateContributionsInExistingStatementsResponseDto(123, true, new List<WorkFlowError>());

        _filingsApiMock
            .Setup(api => api.UpdateContributionsInExistingStatements(
                model.Id.Value,
                It.Is<UpdateContributionsInExistingStatementsDto>(dto =>
                    dto.ContributionsInExistingStatements == true &&
                    dto.RelatedFilerIds.SequenceEqual(relatedFilerIds)),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateContributionsInExistingStatements(
            model, relatedFilerIds, reportType, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.Id));
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(reportType));
        });
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_ValidRequest_WithFalseFlag_RedirectsToIndex()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 123,
            FilerId = 456,
            ContributionsInExistingStatements = "FALSE"
        };
        var relatedFilerIds = new List<long>();
        var reportType = "LobbyistEmployerReport";

        var response = new UpdateContributionsInExistingStatementsResponseDto(123, true, new List<WorkFlowError>());

        _filingsApiMock
            .Setup(api => api.UpdateContributionsInExistingStatements(
                model.Id.Value,
                It.Is<UpdateContributionsInExistingStatementsDto>(dto =>
                    dto.ContributionsInExistingStatements == false),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateContributionsInExistingStatements(
            model, relatedFilerIds, reportType, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.Id));
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(reportType));
        });
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_ValidationFails_ReturnsViewWithErrors()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 123,
            FilerId = 456,
            ContributionsInExistingStatements = "TRUE",
            ViewName = FilingSummaryTypeModel.CampaignContributionSummary.Name
        };
        var relatedFilerIds = new List<long>();
        var reportType = "LobbyistEmployerReport";

        var validationErrors = new List<WorkFlowError>
       {
           new("Error1", "ErrCode1", "IsContributionContainedInDisclosure", "Selection is required"),
           new("Error2", "ErrCode2", "RelatedFilerIds", "At least one major donor or recipient committee must be selected")
       };

        var response = new UpdateContributionsInExistingStatementsResponseDto(123, false, validationErrors);

        _filingsApiMock
            .Setup(api => api.UpdateContributionsInExistingStatements(
                model.Id.Value,
                It.IsAny<UpdateContributionsInExistingStatementsDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        _disclosureCtlSvcMock
            .Setup(svc => svc.ProcessCampaignContributionView(
                model,
                model.Id.Value.ToString(CultureInfo.InvariantCulture),
                reportType,
                It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.UpdateContributionsInExistingStatements(
            model, relatedFilerIds, reportType, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult.ViewName, Is.EqualTo("Index"));
            Assert.That(viewResult.Model, Is.EqualTo(model));
            Assert.That(_controller.ViewBag.PartialView, Is.EqualTo(FilingSummaryTypeModel.CampaignContributionSummary.Name));
        });

        _disclosureCtlSvcMock.Verify(
            svc => svc.ProcessCampaignContributionView(
                model,
                model.Id.Value.ToString(CultureInfo.InvariantCulture),
                reportType,
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_ApiThrowsException_RedirectsWithError()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 123,
            FilerId = 456,
            ContributionsInExistingStatements = "TRUE",
            ViewName = FilingSummaryTypeModel.CampaignContributionSummary.Name
        };
        var relatedFilerIds = new List<long> { 789 };
        var reportType = "LobbyistEmployerReport";

        _filingsApiMock
            .Setup(api => api.UpdateContributionsInExistingStatements(
                model.Id.Value,
                It.IsAny<UpdateContributionsInExistingStatementsDto>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("API error"));

        _localizerMock
            .Setup(l => l[ResourceConstants.UpdateFailMessage])
            .Returns(new LocalizedString(ResourceConstants.UpdateFailMessage, "Update failed"));

        // Act
        var result = await _controller.UpdateContributionsInExistingStatements(
            model, relatedFilerIds, reportType, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.Id));
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(reportType));
        });

        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => true),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_NullModelId_ReturnsBadRequest()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel { Id = null };
        var relatedFilerIds = new List<long>();
        var reportType = "LobbyistEmployerReport";

        // Act
        var result = await _controller.UpdateContributionsInExistingStatements(
            model, relatedFilerIds, reportType, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_TrueContributionsFlag_EmptyRelatedFilers_ReturnsRedirectWithError()
    {
        // Arrange
        var model = new DisclosureSummaryViewModel
        {
            Id = 123,
            FilerId = 456,
            ContributionsInExistingStatements = "TRUE",
            ViewName = FilingSummaryTypeModel.CampaignContributionSummary.Name
        };
        var relatedFilerIds = new List<long>();
        var reportType = "LobbyistEmployerReport";

        // Act
        var result = await _controller.UpdateContributionsInExistingStatements(
            model, relatedFilerIds, reportType, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.Id));
            Assert.That(redirectResult.RouteValues["viewName"], Is.EqualTo(FilingSummaryTypeModel.CampaignContributionSummary.Name));
        });
    }

    #region Lobbying Advertisement

    private static IEnumerable<TestCaseData> UpsertLobbyingAdvertisementTransaction_AmountInputs()
    {
        yield return new TestCaseData(null);
        yield return new TestCaseData(1500.75m);
    }

    [TestCaseSource(nameof(UpsertLobbyingAdvertisementTransaction_AmountInputs))]
    public async Task UpsertLobbyingAdvertisementTransaction_ShouldRedirectToIndex_WhenSubmissionIsValid(decimal? amount)
    {
        // Arrange
        var filerId = 1;
        var filingId = 1;
        var filingSummaryId = 303;

        var model = new LobbyingAdvertisementViewModel
        {
            Id = 1,
            FilingId = filingId,
            FilerId = filerId,
            FilingSummaryId = filingSummaryId,
            PublicationDate = DateTime.Today,
            DistributionMethodDescription = "Online ad campaign",
            AdditionalInformation = "Targeted social media campaign",
            Amount = amount,
            AdvertisementDistributionMethodId = 1,
            LegislatorId = 10,
            AdvertisementDistributionMethods = new List<SelectListItem>(),
            Legislators = new List<SelectListItem>(),
            Action = "EditLobbyingAdvertisement"
        };

        var existingFiling = new FilingItemResponse(
            id: 123,
            endDate: DateTime.Now,
            filerId: 456,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: FilingType.LobbyistEmployerReport.Name,
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );

        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(existingFiling);
        _filingsApiMock
            .Setup(x => x.UpdateFiling(filingId, It.IsAny<UpsertFilingRequest>(), CancellationToken.None))
            .Returns(Task.CompletedTask);

        var expectedResponse = new TransactionResponseDto(1, true, new List<WorkFlowError>());

        _filingsApiMock
            .Setup(api => api.GetFiling(model.Id.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingFiling);

        _disclosureCtlSvcMock
            .Setup(svc => svc.HandleLobbyingAdvertisementTransactionSubmit(
                It.IsAny<long>(),
                It.IsAny<LobbyingAdvertisementRequestDto>(),
                It.IsAny<ModelStateDictionary>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpsertLobbyingAdvertisementTransaction(model);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("AmendDisclosure"));
            Assert.That(redirectResult?.RouteValues?["filerId"], Is.EqualTo(filerId));
            Assert.That(redirectResult?.RouteValues?["filingId"], Is.EqualTo(filingId));
            Assert.That(redirectResult?.RouteValues?["reportType"], Is.EqualTo("Report72h"));
            Assert.That(redirectResult?.RouteValues?["filingSummaryId"], Is.EqualTo(filingSummaryId));
        });
    }


    [Test]
    public async Task UpsertLobbyingAdvertisementTransaction_ShouldReturnViewModel_WhenThrowException()
    {
        // Arrange
        var filerId = 1;
        var filingId = 1;

        var model = new LobbyingAdvertisementViewModel()
        {
            FilerId = filerId,
            FilingId = filingId,
        };

        var apiException = await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Post,
            new HttpResponseMessage(HttpStatusCode.InternalServerError),
            new RefitSettings());

        _ = _disclosureCtlSvcMock.Setup(x => x.HandleLobbyingAdvertisementTransactionSubmit(It.IsAny<long>(), It.IsAny<LobbyingAdvertisementRequestDto>(), _controller.ModelState, It.IsAny<CancellationToken>())).ThrowsAsync(apiException);

        // Act
        var result = await _controller.UpsertLobbyingAdvertisementTransaction(model, default) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));

            var typedModel = result.Model as LobbyingAdvertisementViewModel;
            Assert.That(typedModel, Is.Not.Null);
        });
    }

    [Test]
    public async Task UpsertLobbyingAdvertisementTransaction_ShouldReturnViewModel_WhenResponseInvalid()
    {
        // Arrange
        var filerId = 1;
        var filingId = 1;

        var model = new LobbyingAdvertisementViewModel()
        {
            FilerId = filerId,
            FilingId = filingId,
        };

        var expectedResponse = new TransactionResponseDto(1, false, new List<WorkFlowError>() { new("err001", "Validation", "Amount", "{{Field Name}} is required") });
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", DateTime.Now, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, DateTime.Now.AddMonths(-1), 1, DateTime.Now, 1, 1);
        _filingsApiMock.Setup(api => api.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingItemResponse);
        _ = _disclosureCtlSvcMock.Setup(x => x.HandleLobbyingAdvertisementTransactionSubmit(It.IsAny<long>(), It.IsAny<LobbyingAdvertisementRequestDto>(), _controller.ModelState, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpsertLobbyingAdvertisementTransaction(model, default) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));

            var typedModel = result.Model as LobbyingAdvertisementViewModel;
            Assert.That(typedModel, Is.Not.Null);
        });
    }

    [Test]
    public async Task UpsertLobbyingAdvertisementTransaction_ShouldReturnViewModel_WhenModelStateInvalid()
    {
        // Arrange
        var model = new LobbyingAdvertisementViewModel();
        _controller.ModelState.AddModelError("SomeProperty", "Some error");

        // Act
        var result = await _controller.UpsertLobbyingAdvertisementTransaction(model, default);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task UpdateFilingCustomPeriod_ModelStateInvalid_ReturnsErrorJson()
    {
        // Arrange
        var filingsApi = new Mock<IFilingsApi>();
        var payload = new FilingPeriodUpdateRequest { Id = 1, FilingPeriodId = 2, StartDate = "01/01/2024", EndDate = "12/31/2024" };
        _controller.ModelState.AddModelError("StartDate", "Invalid start date");
        _localizerMock.Setup(l => l["Invalid start date"]).Returns(new LocalizedString("Invalid start date", "Invalid start date"));

        // Act
        var result = await _controller.UpdateFilingCustomPeriod(payload, filingsApi.Object);

        // Assert
        var json = result as JsonResult;
        Assert.That(json, Is.Not.Null);
        Assert.That(json.Value, Is.InstanceOf<object>());
    }

    [Test]
    public async Task UpdateFilingCustomPeriod_InvalidStartDate_ReturnsErrorJson()
    {
        // Arrange
        var filingsApi = new Mock<IFilingsApi>();
        var payload = new FilingPeriodUpdateRequest { Id = 1, FilingPeriodId = 2, StartDate = "bad-date", EndDate = "12/31/2024" };

        // Act
        var result = await _controller.UpdateFilingCustomPeriod(payload, filingsApi.Object);

        // Assert
        var json = result as JsonResult;
        Assert.That(json, Is.Not.Null);
        Assert.That(json.Value, Is.InstanceOf<object>());
    }

    [Test]
    public async Task UpdateFilingCustomPeriod_InvalidEndDate_ReturnsErrorJson()
    {
        // Arrange
        var filingsApi = new Mock<IFilingsApi>();
        var payload = new FilingPeriodUpdateRequest { Id = 1, FilingPeriodId = 2, StartDate = "01/01/2024", EndDate = "bad-date" };

        // Act
        var result = await _controller.UpdateFilingCustomPeriod(payload, filingsApi.Object);

        // Assert
        var json = result as JsonResult;
        Assert.That(json, Is.Not.Null);
        Assert.That(json.Value, Is.InstanceOf<object>());
    }

    [Test]
    public async Task UpdateFilingCustomPeriod_ValidRequest_ReturnsApiResponseJson()
    {
        // Arrange
        var filingsApi = new Mock<IFilingsApi>();
        var payload = new FilingPeriodUpdateRequest { Id = 1, FilingPeriodId = 2, StartDate = "01/01/2024", EndDate = "12/31/2024" };
        var apiResponse = new FilingResponseDto(
            endDate: DateTime.Now,
            filerId: 1,
            filerName: "Jane Lobbyist",
            filingStatus: "Pending",
            filingType: FilingType.LobbyistEmployerReport.Name,
            id: 1,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 1,
            submittedDate: null,
            version: 1
        );

        filingsApi.Setup(f => f.UpdateFilingCustomPeriod(
            It.IsAny<long>(),
            It.IsAny<UpdateFilingCustomPeriodRequest>(),
            It.IsAny<CancellationToken>())).ReturnsAsync(apiResponse);

        // Act
        var result = await _controller.UpdateFilingCustomPeriod(payload, filingsApi.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task UpdateFilingCustomPeriod_ApiException_ReturnsErrorJson()
    {
        // Arrange
        var filingsApi = new Mock<IFilingsApi>();
        var payload = new FilingPeriodUpdateRequest
        {
            Id = 1,
            FilingPeriodId = 2,
            StartDate = "01/01/2024",
            EndDate = "12/31/2024"
        };

        filingsApi.Setup(f => f.UpdateFilingCustomPeriod(
            1,
            It.IsAny<UpdateFilingCustomPeriodRequest>(),
            It.IsAny<CancellationToken>())).ThrowsAsync(await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Post,
            new HttpResponseMessage(HttpStatusCode.InternalServerError),
            new RefitSettings()));

        // Act
        var result = await _controller.UpdateFilingCustomPeriod(payload, filingsApi.Object);

        // Assert
        var json = result as JsonResult;
        Assert.That(json, Is.Not.Null);
    }
    #endregion
}

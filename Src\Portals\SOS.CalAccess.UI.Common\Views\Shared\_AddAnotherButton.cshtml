@using Microsoft.AspNetCore.Mvc.Localization
@using SOS.CalAccess.UI.Common.Models;
@using SOS.CalAccess.UI.Common
@using SOS.CalAccess.UI.Common.Enums;
@using SOS.CalAccess.UI.Common.Localization;

@inject IHtmlLocalizer<SharedResources> Localizer

@model AddAnotherButtonModel

<button type="@Model.Type"
        name="@Model.Name"
        value="@Model.Value"
        class="@Model.CssClass">
    @if (!string.IsNullOrEmpty(Model.IconClass))
    {
        <i class="@Model.IconClass"></i>
    }
    @Model.Text
</button>



using NSubstitute;
using NSubstitute.ExceptionExtensions;
using NSubstitute.ReceivedExtensions;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.Efile.Model;
using SOS.CalAccess.Services.Business.FilerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Common.BusinessRules.Models;
using SOS.CalAccess.Services.Common.FileSystem;

namespace SOS.CalAccess.Services.Business.Tests.FilerRegistration.Registrations;
[TestFixture]
public class LobbyistRegistrationSvcTest
{
    private IDecisionsSvc _decisionsSvcMock;
    private IRegistrationRepository _registrationRepositoryMock;
    private IRegistrationRegistrationContactRepository _registrationRegistrationContactRepositoryMock;
    private IAuthorizationSvc _authorizationSvcMock;
    private IFilerSvc _filerSvcMock;
    private INotificationSvc _notificationSvcMock;
    private IUserMaintenanceSvc _userMaintenanceSvcMock;
    private IFilerLinkRepository _filerLinkRepositoryMock;
    private LobbyistRegistrationSvc _service;
    private LobbyistRegistrationSvcDependencies _dependencies;
    private IReferenceDataSvc _referenceDataSvcMock;
    private RegistrationModelMapper _modelMapper;
    private ILinkageSvc _linkageSvc;
    private IAttestationRepository _attestationRepository;
    private IUploadFileSvc _uploadFileSvcMock;

    [SetUp]
    public void SetUp()
    {
        _decisionsSvcMock = Substitute.For<IDecisionsSvc>();
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();
        _filerSvcMock = Substitute.For<IFilerSvc>();
        _notificationSvcMock = Substitute.For<INotificationSvc>();
        _userMaintenanceSvcMock = Substitute.For<IUserMaintenanceSvc>();
        _registrationRepositoryMock = Substitute.For<IRegistrationRepository>();
        _registrationRegistrationContactRepositoryMock = Substitute.For<IRegistrationRegistrationContactRepository>();
        _filerLinkRepositoryMock = Substitute.For<IFilerLinkRepository>();
        _referenceDataSvcMock = Substitute.For<IReferenceDataSvc>();
        _linkageSvc = Substitute.For<ILinkageSvc>();
        _attestationRepository = Substitute.For<IAttestationRepository>();
        _uploadFileSvcMock = Substitute.For<IUploadFileSvc>();

        _modelMapper = new RegistrationModelMapper(_referenceDataSvcMock);

        _dependencies = new LobbyistRegistrationSvcDependencies
        (
            _decisionsSvcMock,
            _authorizationSvcMock,
            _filerSvcMock,
            _notificationSvcMock,
            _userMaintenanceSvcMock,
            _registrationRepositoryMock,
            _registrationRegistrationContactRepositoryMock,
            _filerLinkRepositoryMock,
            _linkageSvc,
            _attestationRepository,
            _uploadFileSvcMock
        );

        _service = new LobbyistRegistrationSvc
        (
            _dependencies, _modelMapper
        );
    }

    private static IEnumerable<TestCaseData> CreateLobbyistRegistration_ValidRequest_IsNewCertification_Inputs()
    {
        yield return new TestCaseData(true);
        yield return new TestCaseData(false);
    }
    [TestCaseSource(nameof(CreateLobbyistRegistration_ValidRequest_IsNewCertification_Inputs))]
    public async Task CreateLobbyistRegistration_ValidRequest_ReturnsSuccessfulResponse(bool isNewCertification)
    {
        // Arrange
        var sampleOrgAddress = new AddressDto { Type = "sampleType", Street = "123 Road", Street2 = "APT A", City = "Test City", State = "HI", Country = "US", Zip = "12345", Purpose = "random purpose" };
        var sampleMailAddress = new AddressDto { Type = "sampleType", Street = "456 Road", Street2 = "APT B", City = "Sample City", State = "HI", Country = "US", Zip = "67890", Purpose = "random purpose" };
        var sampleRegistrationAgencies = new List<RegistrationAgencyDto>()
        {
            new() { AgencyId = 1 },
            new() { AgencyId = 2 }
        };
        var request = new LobbyistRegistrationRequestDto { FirstName = "FirstName", MiddleName = "MiddleName", LastName = "LastName", BusinessAddress = sampleOrgAddress, MailingAddress = sampleMailAddress, LobbyistEmployerOrLobbyingFirmId = 2, IsNewCertification = isNewCertification };
        var mappedModel = _modelMapper.MapLobbyistRegistrationRequestToModel(request);
        var updatedMappedModel = new Lobbyist { Name = "FirstName", EmployerName = string.Empty, StatusId = RegistrationStatus.Draft.Id, FilerId = 1 };
        var linkedFiler = new Filer { Id = 1, FilerTypeId = FilerType.LobbyingFirm.Id };

        _ = _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(), Arg.Any<bool>())
            .Returns(new List<WorkFlowError>());

        _ = _ = _registrationRepositoryMock.IsLobbyistNameUnique(Arg.Any<string>()).Returns(Task.FromResult(true));

        _ = _dependencies.AuthorizationSvc
            .GetInitiatingUserId()
            .Returns(Task.FromResult<long?>(1));

        _ = _filerSvcMock
            .AddFilerAsync(Arg.Any<Filer>())
            .Returns(Task.FromResult(1L));

        _ = _registrationRepositoryMock.CreateLobbyist(Arg.Any<Lobbyist>())
            .Returns(mappedModel);

        _ = _dependencies.RegistrationRepository.Update(Arg.Any<Registration>())
            .Returns(Task.FromResult<Registration>(updatedMappedModel));

        _ = _filerSvcMock.GetFiler(Arg.Any<long>()).Returns(linkedFiler);
        // Act
        var result = await _service.CreateLobbyistRegistrationPage03(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.FilerId, Is.EqualTo(1));
        });

        _ = _filerLinkRepositoryMock.Received(1).LinkEntityTypeToFiler(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<FilerLinkType>(), Arg.Any<long>(), Arg.Any<List<FilerLinkType>>());
    }


    [Test]
    public async Task CreateLobbyistRegistration_WithPhoto_UpdatesRelationship()
    {
        // Arrange
        var sampleOrgAddress = new AddressDto { Type = "sampleType", Street = "123 Road", Street2 = "APT A", City = "Test City", State = "HI", Country = "US", Zip = "12345", Purpose = "random purpose" };
        var sampleMailAddress = new AddressDto { Type = "sampleType", Street = "456 Road", Street2 = "APT B", City = "Sample City", State = "HI", Country = "US", Zip = "67890", Purpose = "random purpose" };
        var sampleRegistrationAgencies = new List<RegistrationAgencyDto>()
        {
            new() { AgencyId = 1 },
            new() { AgencyId = 2 }
        };
        var request = new LobbyistRegistrationRequestDto { FirstName = "FirstName", MiddleName = "MiddleName", LastName = "LastName", BusinessAddress = sampleOrgAddress, MailingAddress = sampleMailAddress, LobbyistEmployerOrLobbyingFirmId = 2, Photo = "upload.txt" };
        var mappedModel = _modelMapper.MapLobbyistRegistrationRequestToModel(request);
        var updatedMappedModel = new Lobbyist { Name = "FirstName", EmployerName = string.Empty, StatusId = RegistrationStatus.Draft.Id, FilerId = 1 };
        var linkedFiler = new Filer { Id = 1, FilerTypeId = FilerType.LobbyingFirm.Id };
        var uploadedFile = new UploadedFile
        {
            OriginalFileName = "upload.txt",
            RelationshipType = RelationshipType.RegistrationFiling.ToString(),
            RelationshipId = null,
            Path = "myblobcontainer"
        };

        _ = _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(), Arg.Any<bool>())
            .Returns(new List<WorkFlowError>());

        _ = _ = _registrationRepositoryMock.IsLobbyistNameUnique(Arg.Any<string>()).Returns(Task.FromResult(true));

        _ = _dependencies.AuthorizationSvc
            .GetInitiatingUserId()
            .Returns(Task.FromResult<long?>(1));

        _ = _filerSvcMock
            .AddFilerAsync(Arg.Any<Filer>())
            .Returns(Task.FromResult(1L));

        _ = _registrationRepositoryMock.CreateLobbyist(Arg.Any<Lobbyist>())
            .Returns(mappedModel);

        _ = _dependencies.UploadFileSvc.FindUploadByFileNameGuid(request.Photo).Returns(uploadedFile);

        _ = _dependencies.RegistrationRepository.Update(Arg.Any<Registration>())
            .Returns(Task.FromResult<Registration>(updatedMappedModel));

        _ = _filerSvcMock.GetFiler(Arg.Any<long>()).Returns(linkedFiler);
        // Act
        var result = await _service.CreateLobbyistRegistrationPage03(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.FilerId, Is.EqualTo(1));
        });

        _ = _filerLinkRepositoryMock.Received(1).LinkEntityTypeToFiler(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<FilerLinkType>(), Arg.Any<long>(), Arg.Any<List<FilerLinkType>>());

        _ = _dependencies.UploadFileSvc.Received(1).UpdateUploadedFile(Arg.Any<UploadedFile>());
    }

    [Test]
    public async Task GetLobbyistByFilerId_ShouldReturnLobbyistResponseDto_WhenLobbyistExists()
    {
        // Arrange
        var filerId = 123;
        var lobbyist = new Lobbyist
        {
            Name = "Test Lobbyist",
            StatusId = 1,
            EmployerName = "Test Employer"
        };
        _registrationRepositoryMock.FindLobbyingRegistrationByFilerId<Lobbyist>(filerId)!.Returns(Task.FromResult(lobbyist));
        // Act
        var result = await _service.GetLobbyistByFilerId(filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<LobbyistResponseDto>());
        Assert.That(result, Is.InstanceOf<LobbyistResponseDto>());
        await _registrationRepositoryMock.Received(1).FindLobbyingRegistrationByFilerId<Lobbyist>(filerId);

    }

    [Test]
    public void GetLobbyistByFilerId_ShouldThrowKeyNotFoundException_WhenLobbyistDoesNotExist()
    {
        // Arrange
        const int filerId = 123;
        _registrationRepositoryMock.FindLobbyingRegistrationByFilerId<Lobbyist>(filerId)!.Returns(Task.FromResult<Lobbyist>(null!));

        // Act & Assert
        Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.GetLobbyistByFilerId(filerId));
    }

    [Test]
    public void CancelLobbyistRegistration_ShouldThrowExceptionWhenRecordStatusIsNotDraft()
    {
        // Arrange
        long id = 1;
        var record = new Lobbyist
        {
            Name = "Name",
            Email = "<EMAIL>",
            StatusId = RegistrationStatus.Submitted.Id
        };
        _ = _registrationRepositoryMock.FindById(Arg.Any<long>()).Returns(record);

        // Act
        // Assert
        _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(InvalidOperationException)), async () => await _service.CancelLobbyistRegistration(id));
    }

    [Test]
    public void CancelLobbyistRegistration_ShouldThrowExceptionWhenRegistrationNotFound()
    {
        // Arrange
        long id = 1;

        _ = _registrationRepositoryMock.FindById(Arg.Any<long>()).Returns((Registration)null!);

        // Act
        // Assert
        _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(KeyNotFoundException)), async () => await _service.CancelLobbyistRegistration(id));
    }

    [Test]
    public void CancelLobbyistRegistrationn_ShouldSucceedWhenStatusIsDraft()
    {
        // Arrange
        long id = 1;
        var record = new Lobbyist
        {
            Name = "Name",
            Email = "<EMAIL>",
            StatusId = RegistrationStatus.Draft.Id
        };
        _ = _registrationRepositoryMock.FindById(Arg.Any<long>()).Returns(record);

        // Act
        // Assert
        Assert.DoesNotThrowAsync(async () => await _service.CancelLobbyistRegistration(id));
        _registrationRepositoryMock.Received()
            .Update(Arg.Any<Lobbyist>());
    }

    [Test]
    public void CancelLobbyistRegistration_ShouldThrowExceptionWhenLobbyistNotFound()
    {
        // Arrange
        long id = 1;
        _ = _registrationRepositoryMock.FindLobbyistById(Arg.Any<long>()).Returns((Lobbyist?)null);

        // Act
        // Assert
        Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.CancelLobbyistRegistration(id));
    }

    /// <summary>
    /// UpdateLobbyistRegistration should throw if no registration exists with that id.
    /// </summary>
    [Test]
    public void UpdateLobbyistRegistration_ThrowsWhenRegistrationNotFound()
    {
        const long id = 202;
        _registrationRepositoryMock.FindLobbyistById(id).Returns((Lobbyist?)null);
        var request = new LobbyistRegistrationRequestDto();

        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _service.UpdateLobbyistRegistration(id, request)
        );
        Assert.That(ex.Message, Is.EqualTo($"Registration not Found Id={id}"));
    }

    /// <summary>
    /// UpdateLobbyistRegistration should return Valid=true and call Update(...) when decisions return no errors.
    /// </summary>
    [Test]
    public async Task UpdateLobbyistRegistration_ReturnsValidAndUpdatesWhenNoDecisionErrors()
    {
        const long id = 404;
        var existing = new Lobbyist { Id = id, StatusId = 7, Name = "Test", FirstName = "Tom", LastName = "And Jerry" };

        _ = _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(), Arg.Any<bool>())
            .Returns(new List<WorkFlowError>());

        _ = _registrationRepositoryMock.IsLobbyistNameUnique(Arg.Any<string>()).Returns(Task.FromResult(true));

        _ = _registrationRepositoryMock
            .FindLobbyistById(id)
            .Returns(existing);

        Registration asBase = existing;
        Task<Registration> updateResult = Task.FromResult(asBase);

        _registrationRepositoryMock
            .Update(Arg.Any<Lobbyist>())
            .Returns(updateResult);

        // Act
        var result = await _service.UpdateLobbyistRegistration(
            id,
            new LobbyistRegistrationRequestDto()
            {
                FirstName = "Tom",
                LastName = "Hanks"
            }
        );

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.StatusId, Is.EqualTo(existing.StatusId));
        });

        // And verify Update was called
        await _registrationRepositoryMock.Received(1)
            .Update(Arg.Is<Lobbyist>(r => r.Id == id));
    }

    [Test]
    public async Task UpdateLobbyistRegistration_ReturnsNotValidWhenNameNotUnique()
    {
        const long id = 404;
        var existing = new Lobbyist { Id = id, StatusId = 7, Name = "Test", FirstName = "Tom", LastName = "And Jerry" };

        _ = _registrationRepositoryMock
            .FindLobbyistById(id)
            .Returns(existing);

        _ = _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(), Arg.Any<bool>())
            .Returns(new List<WorkFlowError>());

        _ = _registrationRepositoryMock.IsLobbyistNameUnique(Arg.Any<string>()).Returns(Task.FromResult(false));

        // Act
        var result = await _service.UpdateLobbyistRegistration(
            id,
            new LobbyistRegistrationRequestDto()
        );

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.Not.Empty);
            Assert.That(result.StatusId, Is.EqualTo(existing.StatusId));
        });
    }

    [Test]
    public async Task SubmitLobbyistRegistrationForEfile_ValidRequest_ReturnsSuccessResponse()
    {
        // Arrange
        var lobbyist = new Lobbyist { Name = "UniqueName", StatusId = RegistrationStatus.Draft.Id };
        var registration = new Lobbyist { Id = 10, Name = "UniqueName", StatusId = RegistrationStatus.Draft.Id };

        var request = new LobbyistRegistrationSubmissionDto()
        {
            Amendment = new EfileAmendment { SupercededFilingId = null },
            Attestation = new EfileAttestation(),
            IsSubmission = true,
            Lobbyist = lobbyist,
            UserId = 20,
            LobbyistEmployerOrLobbyingFirmId = 100,
        };

        var validationErrors = new List<WorkFlowError>();
        var linkedFiler = new Filer { Id = 100, FilerTypeId = FilerType.LobbyistEmployer.Id };

        _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, List<WorkFlowError>>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(),
            Arg.Any<bool>()
        ).Returns(validationErrors);

        _registrationRepositoryMock.IsLobbyistNameUnique(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));
        _registrationRepositoryMock.CreateLobbyist(Arg.Any<Lobbyist>()).Returns(Task.FromResult(registration));
        _filerSvcMock.AddFilerAsync(Arg.Any<Filer>()).Returns(Task.FromResult(100L));
        _filerSvcMock.GetFiler(Arg.Any<long>()).Returns(linkedFiler);
        _registrationRepositoryMock.Update(Arg.Any<Lobbyist>()).Returns(Task.FromResult<Registration>(registration));

        // Act
        var result = await _service.SubmitLobbyistRegistrationForEfile(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.FilerId, Is.EqualTo(100));
        });
        await _filerLinkRepositoryMock.Received(1).LinkEntityTypeToFiler(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<FilerLinkType>(), Arg.Any<long>(), Arg.Any<List<FilerLinkType>>());
    }

    [Test]
    public async Task SubmitLobbyistRegistrationForEfile_ValidationErrors_ReturnsInvalidResponse()
    {
        // Arrange
        var lobbyist = new Lobbyist { Name = "DuplicateName", StatusId = RegistrationStatus.Draft.Id };
        var request = new LobbyistRegistrationSubmissionDto()
        {
            Amendment = new EfileAmendment { SupercededFilingId = null },
            Attestation = new EfileAttestation(),
            IsSubmission = true,
            Lobbyist = lobbyist,
            UserId = 20,
            LobbyistEmployerOrLobbyingFirmId = 100,
        };

        var validationErrors = new List<WorkFlowError> { new("Name", "E001", "Validation", "Duplicate name") };

        _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, List<WorkFlowError>>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(),
            Arg.Any<bool>()
        ).Returns(validationErrors);

        _registrationRepositoryMock.IsLobbyistNameUnique(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(false));

        // Act
        var result = await _service.SubmitLobbyistRegistrationForEfile(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.Not.Empty);
            Assert.That(result.FilerId, Is.Null);
        });
        await _registrationRepositoryMock.DidNotReceive().CreateLobbyist(Arg.Any<Lobbyist>());
    }

    [Test]
    public async Task SubmitLobbyistRegistrationForEfile_RegistrationIsNull_ReturnsInvalidResponse()
    {
        // Arrange
        var lobbyist = new Lobbyist { Name = "UniqueName", StatusId = RegistrationStatus.Draft.Id };
        var request = new LobbyistRegistrationSubmissionDto()
        {
            Amendment = new EfileAmendment { SupercededFilingId = null },
            Attestation = new EfileAttestation(),
            IsSubmission = true,
            Lobbyist = lobbyist,
            UserId = 20,
            LobbyistEmployerOrLobbyingFirmId = 100,
        };
        var validationErrors = new List<WorkFlowError>();

        _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, List<WorkFlowError>>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(),
            Arg.Any<bool>()
        ).Returns(validationErrors);

        _registrationRepositoryMock.IsLobbyistNameUnique(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));
        _registrationRepositoryMock.CreateLobbyist(Arg.Any<Lobbyist>()).Returns(Task.FromResult<Lobbyist>(null!));

        // Act
        var result = await _service.SubmitLobbyistRegistrationForEfile(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.FilerId, Is.Null);
        });
        await _registrationRepositoryMock.Received(1).CreateLobbyist(Arg.Any<Lobbyist>());
    }

    [Test]
    public async Task SubmitLobbyistRegistration_NonLobbyist_Valid()
    {
        //Arrange
        var id = 1982;
        var lobbyist = new Lobbyist() { Id = id, FilerId = 1, Name = "Name", Email = "Email", StatusId = RegistrationStatus.Draft.Id, SelfRegister = false };

        _registrationRepositoryMock.FindLobbyistById(id).Returns(lobbyist);
        _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(DecisionsWorkflow.FRLOBFilingLobbyistSendAttestation, Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(), true)
            .Returns(new StandardDecisionsSubmissionResponse() { Status = RegistrationStatus.Pending.ToString()!, Errors = new List<WorkFlowError>() });
        await _linkageSvc.SendLinkageRequestToPerson(Arg.Any<SendLinkageRequestToPersonDto>());

        //Act
        var result = await _service.SubmitLobbyistRegistration(id);

        //Assert
        await _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(DecisionsWorkflow.FRLOBFilingLobbyistSendAttestation, Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(), true);
        await _registrationRepositoryMock.Received(1).Update(
           Arg.Is<Lobbyist>(x => x.Version == 0 && x.StatusId == RegistrationStatus.Pending.Id)
        );

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(0));
        });
    }

    [Test]
    public async Task SubmitLobbyistRegistration_NotDraft_Invalid()
    {
        //Arrange
        var id = 1982;
        var lobbyist = new Lobbyist() { Id = id, FilerId = 1, Name = "Name", Email = "Email", StatusId = RegistrationStatus.Accepted.Id, SelfRegister = false };

        _registrationRepositoryMock.FindLobbyistById(id).Returns(lobbyist);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() =>
            _service.SubmitLobbyistRegistration(id));

        Assert.That(ex.Message, Is.EqualTo(
            $"Cannot submit a registration that is not in 'Draft' status. Id={id} Status={RegistrationStatus.Accepted.Id}"));

        // Verify no other processing occurred
        await _attestationRepository.DidNotReceive().Create(Arg.Any<Attestation>());
        await _registrationRepositoryMock.DidNotReceive().Update(Arg.Any<Lobbyist>());
        await _linkageSvc.DidNotReceive().SendLinkageRequestToPerson(Arg.Any<SendLinkageRequestToPersonDto>());
    }

    [Test]
    public void SubmitLobbyistRegistration_NotFound_ThrowsKeyNotFoundException()
    {
        // Arrange
        var nonExistentId = 9999; // An ID that doesn't exist

        // Mock the repository to return null for this ID
        _registrationRepositoryMock.FindLobbyistById(nonExistentId).Returns((Lobbyist?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() =>
            _service.SubmitLobbyistRegistration(nonExistentId));

        // Verify the exception message
        Assert.That(ex.Message, Is.EqualTo($"Registration not Found Id={nonExistentId}"));
    }

    [Test]
    public async Task SubmitLobbyistRegistration_NoNotifications_ShouldNotCallNotificationService()
    {
        // Arrange
        var id = 1982;
        var filerId = 12345;
        var lobbyist = new Lobbyist()
        {
            Id = id,
            Name = "Name",
            StatusId = RegistrationStatus.Draft.Id,
            SelfRegister = true,
            FilerId = filerId
        };

        var response = new StandardDecisionsSubmissionResponse()
        {
            Status = RegistrationStatus.Accepted.ToString()!,
            Errors = new List<WorkFlowError>(),
            Notifications = new List<NotificationTrigger>
            {
                // All notifications have SendNotification = false
                new(false, null, null)
            }
        };

        _registrationRepositoryMock.FindLobbyistById(id).Returns(lobbyist);
        _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.FRLOBFilingLobbyistFinalSubmission,
                Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(),
                true)
            .Returns(response);

        // Act
        var result = await _service.SubmitLobbyistRegistration(id);

        // Assert
        await _attestationRepository.Received(1).Create(Arg.Any<Attestation>());
        await _decisionsSvcMock.Received(1)
            .InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.FRLOBFilingLobbyistFinalSubmission,
                Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(),
                true);
        await _registrationRepositoryMock.Received(1).Update(
            Arg.Is<Lobbyist>(x => x.Version == 0 && x.StatusId == RegistrationStatus.Accepted.Id));

        // Ensure notification service was NOT called
        await _notificationSvcMock.DidNotReceive().SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(0));
        });
    }

    [Test]
    public async Task SubmitLobbyistRegistration_Amend_NoNotifications_ShouldNotCallNotificationService()
    {
        // Arrange
        var id = 1982;
        var filerId = 12345;
        var lobbyist = new Lobbyist()
        {
            Id = id,
            ParentId = 1,
            Version = 1,
            Name = "Name",
            StatusId = RegistrationStatus.Draft.Id,
            SelfRegister = true,
            FilerId = filerId
        };

        var response = new StandardDecisionsSubmissionResponse()
        {
            Status = RegistrationStatus.Accepted.ToString()!,
            Errors = new List<WorkFlowError>(),
            Notifications = new List<NotificationTrigger>
            {
                // All notifications have SendNotification = false
                new(false, null, null)
            }
        };

        _registrationRepositoryMock.FindLobbyistById(id).Returns(lobbyist);
        _decisionsSvcMock
            .InitiateWorkflow<DecisionsAmendLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.FRLOBFilingAmendLobbyistFinalSubmission,
                Arg.Any<DecisionsAmendLobbyistRegistrationGeneralInfo>(),
                true)
            .Returns(response);

        // Act
        var result = await _service.SubmitLobbyistRegistration(id);

        // Assert
        await _attestationRepository.Received(1).Create(Arg.Any<Attestation>());
        await _decisionsSvcMock.Received(1)
            .InitiateWorkflow<DecisionsAmendLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.FRLOBFilingAmendLobbyistFinalSubmission,
                Arg.Any<DecisionsAmendLobbyistRegistrationGeneralInfo>(),
                true);
        await _registrationRepositoryMock.Received(1).Update(
            Arg.Is<Lobbyist>(x => x.Version == 1 && x.StatusId == RegistrationStatus.Accepted.Id));

        // Ensure notification service was NOT called
        await _notificationSvcMock.DidNotReceive().SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(0));
        });
    }

    [Test]

    public async Task SubmitLobbyistRegistration_Notifications_ShouldCallNotificationService()
    {
        // Arrange
        var id = 1982;
        var filerId = 12345;
        var lobbyist = new Lobbyist()
        {
            Id = id,
            Name = "Name",
            StatusId = RegistrationStatus.Draft.Id,
            SelfRegister = true,
            FilerId = filerId
        };

        var response = new StandardDecisionsSubmissionResponse()
        {
            Status = RegistrationStatus.Accepted.ToString()!,
            Errors = new List<WorkFlowError>(),
            Notifications = new List<NotificationTrigger>
            {
                // All notifications have SendNotification = false
                new(true, 1, DateTime.UtcNow)
            }
        };

        _registrationRepositoryMock.FindLobbyistById(id).Returns(lobbyist);
        _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.FRLOBFilingLobbyistFinalSubmission,
                Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(),
                true)
            .Returns(response);

        // Act
        var result = await _service.SubmitLobbyistRegistration(id);

        // Assert
        await _attestationRepository.Received(1).Create(Arg.Any<Attestation>());
        await _decisionsSvcMock.Received(1)
            .InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.FRLOBFilingLobbyistFinalSubmission,
                Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(),
                true);
        await _registrationRepositoryMock.Received(1).Update(
            Arg.Is<Lobbyist>(x => x.Version == 0 && x.StatusId == RegistrationStatus.Accepted.Id));

        // Ensure notification service was NOT called
        await _notificationSvcMock.Received(1).SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(0));
        });
    }

    [Test]

    public async Task SubmitLobbyistRegistration_SendFilerNotificationThrowException_ShouldReturnSuccessResponse()
    {
        // Arrange
        var id = 1982;
        var filerId = 12345;
        var lobbyist = new Lobbyist()
        {
            Id = id,
            Name = "Name",
            StatusId = RegistrationStatus.Draft.Id,
            SelfRegister = true,
            FilerId = filerId
        };

        var response = new StandardDecisionsSubmissionResponse()
        {
            Status = RegistrationStatus.Accepted.ToString()!,
            Errors = new List<WorkFlowError>(),
            Notifications = new List<NotificationTrigger>
            {
                // All notifications have SendNotification = false
                new(true, 1, DateTime.UtcNow)
            }
        };

        _registrationRepositoryMock.FindLobbyistById(id).Returns(lobbyist);
        _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.FRLOBFilingLobbyistFinalSubmission,
                Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(),
                true)
            .Returns(response);
        _notificationSvcMock.SendFilerNotification(Arg.Any<SendFilerNotificationRequest>())
            .Throws(new InvalidOperationException("Test exception"));

        // Act
        var result = await _service.SubmitLobbyistRegistration(id);

        // Assert
        await _attestationRepository.Received(1).Create(Arg.Any<Attestation>());
        await _decisionsSvcMock.Received(1)
            .InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.FRLOBFilingLobbyistFinalSubmission,
                Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(),
                true);
        await _registrationRepositoryMock.Received(1).Update(
            Arg.Is<Lobbyist>(x => x.Version == 0 && x.StatusId == RegistrationStatus.Accepted.Id));

        // Ensure notification service was NOT called
        await _notificationSvcMock.Received(1).SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(0));
        });
    }

    [Test]
    public async Task CreateAmendment_WhenRegistrationIsAccepted_ShouldCloneAndReturn()
    {
        // Arrange
        var original = new Lobbyist
        {
            Id = 100,
            Name = "test Lobbyist",
            StatusId = RegistrationStatus.Accepted.Id,
            Version = 1,
            AddressList = new AddressList
            {
                Id = 50,
                Addresses = new List<Address>
            {
                new()
                {
                    Id = 501,
                    City = "CityA",
                    Street = "123 Main St",
                    State = "HI",
                    Zip = "96815",
                    Country = "USA",
                    Type = "Home",
                    Purpose = "Mailing"
                }
            }
            },
            PhoneNumberList = new PhoneNumberList
            {
                Id = 60,
                PhoneNumbers = new List<PhoneNumber>
            {
                new()
                {
                    Id = 601,
                    Number = "1234567",
                    Type = "Work",
                    CountryCode = "+1",
                    CountryId = 1
                }
            }
            }
        };

        _registrationRepositoryMock.FindLobbyistById(Arg.Any<long>())
            .Returns(original);

        _authorizationSvcMock.GetInitiatingUserId()
            .Returns(1L);

        Lobbyist? createdLobbyist = null;

        _registrationRepositoryMock
            .Create(Arg.Do<Lobbyist>(x => createdLobbyist = x))
            .Returns(Task.FromResult((Registration)original)); // just to satisfy signature

        // Act
        var result = await _service.CreateLobbyistAmendmentRegistrationAsync(original.Id);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(createdLobbyist, Is.Not.Null);
        });

        Assert.Multiple(() =>
        {
            Assert.That(createdLobbyist!.Id, Is.EqualTo(0));
            Assert.That(createdLobbyist!.Version, Is.EqualTo(2));
            Assert.That(createdLobbyist!.ParentId, Is.EqualTo(original.Id));
            Assert.That(createdLobbyist!.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));
            Assert.That(createdLobbyist!.AddressList!.Id, Is.EqualTo(0));
            Assert.That(createdLobbyist!.AddressList!.Addresses!.First().Id, Is.EqualTo(0));
            Assert.That(createdLobbyist!.PhoneNumberList!.Id, Is.EqualTo(0));
            Assert.That(createdLobbyist!.PhoneNumberList!.PhoneNumbers!.First().Id, Is.EqualTo(0));
        });
    }

    [Test]
    public async Task CreateAmendment_WhenRegistrationIsRejected_ShouldCloneAndReturn()
    {
        // Arrange
        var original = new Lobbyist
        {
            Id = 101,
            Name = "test Lobbyist",
            StatusId = RegistrationStatus.Rejected.Id,
            Version = 3
        };

        _registrationRepositoryMock.FindLobbyistById(original.Id)
            .Returns(original);

        _authorizationSvcMock.GetInitiatingUserId()
            .Returns(5L);

        Lobbyist? createdLobbyist = null;

        _registrationRepositoryMock
            .Create(Arg.Do<Lobbyist>(x => createdLobbyist = x))
            .Returns(Task.FromResult((Registration)original)); // satisfy interface

        // Act
        var result = await _service.CreateLobbyistAmendmentRegistrationAsync(original.Id);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(createdLobbyist, Is.Not.Null);
        });

        Assert.Multiple(() =>
        {
            Assert.That(createdLobbyist!.Id, Is.EqualTo(0));
            Assert.That(createdLobbyist!.Version, Is.EqualTo(4));
            Assert.That(createdLobbyist!.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));
            Assert.That(createdLobbyist!.CreatedBy, Is.EqualTo(5));
        });
    }

    [Test]
    public async Task CreateAmendment_WhenStatusNotAcceptedOrRejected_ShouldReturnOriginal()
    {
        // Arrange
        var original = new Lobbyist
        {
            Id = 200,
            Name = "test Lobbysit",
            StatusId = RegistrationStatus.Draft.Id // Not Accepted or Rejected
        };

        _registrationRepositoryMock.FindLobbyistById(original.Id)
            .Returns(original);

        // Act
        var result = await _service.CreateLobbyistAmendmentRegistrationAsync(original.Id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(original.Id));
        await _registrationRepositoryMock.DidNotReceive().Create(Arg.Any<Lobbyist>());
    }

    [Test]
    public void CreateAmendment_WhenRegistrationNotFound_ShouldThrow()
    {
        // Arrange
        _registrationRepositoryMock.FindLobbyistById(Arg.Any<long>())
            .Returns((Lobbyist?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.CreateLobbyistAmendmentRegistrationAsync(999));
        Assert.That(ex!.Message, Does.Contain("Registration not found"));
    }

    [Test]
    [TestCase(nameof(Lobbyist))]
    [TestCase(nameof(LobbyistWithdrawal))]
    public async Task WithdrawLobbyistRegistration_Valid(string type)
    {
        //Arrange
        var id = 1982;
        var withdrawnAt = DateTime.UtcNow;
        var lobbyist = new Lobbyist()
        {
            Id = id,
            FilerId = 1,
            Name = "Name",
            Email = "Email",
            StatusId = RegistrationStatus.Accepted.Id,
            SelfRegister = false
        };
        var lobbyistWithdraw =
            new LobbyistWithdrawal() { Id = id + 1, Name = lobbyist.Name, StatusId = RegistrationStatus.Draft.Id };
        var payload = new WithdrawLobbyistRegistrationRequestDto()
        {
            StatusId = RegistrationStatus.Draft.Id,
            WithdrawnAt = withdrawnAt,
        };

        _registrationRepositoryMock.FindLobbyistById(id).Returns(lobbyist);
        _registrationRepositoryMock.Create(Arg.Any<LobbyistWithdrawal>()).Returns(lobbyistWithdraw);
        _registrationRepositoryMock.GetRegistrationDiscriminatorById(id).Returns(type);
        _registrationRepositoryMock.FindLobbyistWithdrawalById(id).Returns(lobbyistWithdraw);

        //Act
        var result = await _service.WithdrawLobbyistRegistration(id, payload);

        //Assert
        var assertId = type == nameof(Lobbyist) ? id + 1 : id; // For Lobbyist, we expect a new ID
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(assertId));
        });
    }

    [Test]
    public async Task WithdrawLobbyistRegistration_Invalid_DecisionErrors()
    {
        //Arrange
        var id = 1982;
        var withdrawnAt = DateTime.UtcNow;
        var lobbyist = new Lobbyist() { Id = id, FilerId = 1, Name = "Name", Email = "Email", StatusId = RegistrationStatus.Accepted.Id, SelfRegister = false };
        var lobbyistWithdraw = new LobbyistWithdrawal() { Id = id, Name = lobbyist.Name, StatusId = RegistrationStatus.Draft.Id };
        var payload = new WithdrawLobbyistRegistrationRequestDto()
        {
            StatusId = RegistrationStatus.Draft.Id,
            WithdrawnAt = withdrawnAt,
        };

        _registrationRepositoryMock.FindLobbyistById(id).Returns(lobbyist);
        _registrationRepositoryMock.Create(Arg.Any<LobbyistWithdrawal>()).Returns(lobbyistWithdraw);

        var decisionResponse = new StandardDecisionsSubmissionResponse()
        {
            Errors = new List<WorkFlowError>()
            {
                new("EffectiveDateOfWithdrawal", "1", "Error", "Required")
            }
        };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistRegistrationWithdrawal, StandardDecisionsSubmissionResponse>
                (DecisionsWorkflow.LobbyistRegistrationSubmitWithdrawal, Arg.Any<DecisionsLobbyistRegistrationWithdrawal>(), true)
            .Returns(Task.FromResult(decisionResponse));

        //Act
        var result = await _service.WithdrawLobbyistRegistration(id, payload);

        //Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(lobbyistWithdraw.Id));
        });
    }

    [Test]
    public void WithLobbyistRegistration_NonLobbyist_ThrowsKeyNotFoundException()
    {
        // Arrange
        long id = 123;
        var request = new WithdrawLobbyistRegistrationRequestDto
        {
            StatusId = 1,
            WithdrawnAt = DateTime.UtcNow
        };

        // Mock FindLobbyistById to return null
        _registrationRepositoryMock.FindLobbyistById(id).Returns(Task.FromResult<Lobbyist?>(null));

        // Act & Assert
        var result = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.WithdrawLobbyistRegistration(id, request));

        Assert.That(result.Message, Is.EqualTo($"Lobbyist withdrawal with ID {id} not found."));
    }

    [Test]
    public async Task GetRegistrationById_LobbyistType()
    {
        // Arrange
        long id = 123;
        var lobbyist = new Lobbyist() { Id = id, FilerId = 1, Name = "Name", Email = "Email", StatusId = RegistrationStatus.Accepted.Id, SelfRegister = false };

        _registrationRepositoryMock.GetRegistrationDiscriminatorById(id).Returns(nameof(Lobbyist));
        _registrationRepositoryMock.FindLobbyistById(id).Returns(lobbyist);

        // Act
        var result = await _service.GetRegistrationById(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result?.Id, Is.EqualTo(id));
            Assert.That(result?.Name, Is.EqualTo(lobbyist.Name));
        });
    }

    [Test]
    public async Task GetRegistrationById_LobbyistWithdrawType()
    {
        // Arrange
        long id = 123;
        DateTime withdrawnAt = DateTime.UtcNow;
        var lobbyistWithdrawnal = new LobbyistWithdrawal() { Id = id, FilerId = 1, Name = "Name", Email = "Email", StatusId = RegistrationStatus.Accepted.Id, WithdrawnAt = withdrawnAt };

        _registrationRepositoryMock.GetRegistrationDiscriminatorById(id).Returns(nameof(LobbyistWithdrawal));
        _registrationRepositoryMock.FindLobbyistWithdrawalById(id).Returns(lobbyistWithdrawnal);

        // Act
        var result = await _service.GetRegistrationById(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result?.Id, Is.EqualTo(id));
            Assert.That(result?.Name, Is.EqualTo(lobbyistWithdrawnal.Name));
            Assert.That(result?.WithdrawnAt, Is.EqualTo(withdrawnAt));
        });
    }

    [Test]
    public async Task GetRegistrationById_LobbyistTerminationType()
    {
        // Arrange
        long id = 123;
        DateTime terminatedAt = DateTime.UtcNow;
        var lobbyistTermination = new LobbyistTermination()
        {
            Id = id,
            LegislativeSessionId = 1,
            FirstName = "FirstName",
            MiddleName = "MiddleName",
            LastName = "LastName",
            Name = "Name",
            Email = "Email",
            FilerId = 1,
            StatusId = RegistrationStatus.Accepted.Id,
            TerminatedAt = terminatedAt,
            AddressList = new AddressList
            {
                Addresses = new List<Address>
                {
                    new()
                    {
                        Street = "123 Main St",
                        City = "Sacramento",
                        State = "CA",
                        Zip = "95814",
                        Country = "USA",
                        Purpose = "Business",
                        Type = "Office"
                    }
                }
            },
            PhoneNumberList = new PhoneNumberList
            {
                Id = 1,
                PhoneNumbers = new List<PhoneNumber>
                {
                    new()
                    {
                        Id = 1,
                        CountryCode = "+1",
                        CountryId = 1,
                        Number = "8081234567",
                        Type = "Work"
                    }
                }
            }
        };

        _registrationRepositoryMock.GetRegistrationDiscriminatorById(id).Returns(nameof(LobbyistTermination));
        _registrationRepositoryMock.FindLobbyistTerminationById(id).Returns(lobbyistTermination);

        // Act
        var result = await _service.GetRegistrationById(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result?.Id, Is.EqualTo(id));
            Assert.That(result?.Name, Is.EqualTo(lobbyistTermination.Name));
            Assert.That(result?.FirstName, Is.EqualTo(lobbyistTermination.FirstName));
            Assert.That(result?.MiddleName, Is.EqualTo(lobbyistTermination.MiddleName));
            Assert.That(result?.LastName, Is.EqualTo(lobbyistTermination.LastName));
            Assert.That(result?.LegislativeSessionId, Is.EqualTo(lobbyistTermination.LegislativeSessionId));
        });
    }

    [Test]
    public void GetRegistrationById_LobbyistType_NotFound()
    {
        // Arrange
        long id = 1213;
        DateTime withdrawnAt = DateTime.UtcNow;
        var lobbyistWithdrawnal = new LobbyistWithdrawal() { Id = id, FilerId = 1, Name = "Name", Email = "Email", StatusId = RegistrationStatus.Accepted.Id, WithdrawnAt = withdrawnAt };

        _registrationRepositoryMock.GetRegistrationDiscriminatorById(id).Returns(nameof(Lobbyist));
        _registrationRepositoryMock.FindLobbyistById(id).Returns(Task.FromResult<Lobbyist?>(null));

        // Act & Assert
        var result = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.GetRegistrationById(id));

        Assert.That(result.Message, Is.EqualTo($"Lobbyist registration with Filer ID {id} not found."));
    }

    [Test]
    public void GetRegistrationById_LobbyistWithdrawType_NotFound()
    {
        // Arrange
        long id = 123;
        DateTime withdrawnAt = DateTime.UtcNow;
        var lobbyistWithdrawnal = new LobbyistWithdrawal() { Id = id, FilerId = 1, Name = "Name", Email = "Email", StatusId = RegistrationStatus.Accepted.Id, WithdrawnAt = withdrawnAt };

        _registrationRepositoryMock.GetRegistrationDiscriminatorById(id).Returns(nameof(LobbyistWithdrawal));
        _registrationRepositoryMock.FindLobbyistWithdrawalById(id).Returns(Task.FromResult<LobbyistWithdrawal?>(null));

        // Act & Assert
        var result = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetRegistrationById(id));

        Assert.That(result.Message, Is.EqualTo($"Lobbyist withdrawal with ID {id} not found."));
    }

    [Test]
    public void GetRegistrationById_LobbyistTermination_NotFound()
    {
        // Arrange
        long id = 1213;
        DateTime withdrawnAt = DateTime.UtcNow;
        var lobbyistWithdrawnal = new LobbyistWithdrawal() { Id = id, FilerId = 1, Name = "Name", Email = "Email", StatusId = RegistrationStatus.Accepted.Id, WithdrawnAt = withdrawnAt };

        _registrationRepositoryMock.GetRegistrationDiscriminatorById(id).Returns(nameof(LobbyistTermination));
        _registrationRepositoryMock.FindLobbyistTerminationById(id).Returns(Task.FromResult<LobbyistTermination?>(null));

        // Act & Assert
        var result = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.GetRegistrationById(id));

        Assert.That(result.Message, Is.EqualTo($"Lobbyist termination with ID {id} not found."));
    }

    [Test]
    public async Task GetRegistrationById_NotMapType()
    {
        // Arrange
        long id = 123;

        _registrationRepositoryMock.GetRegistrationDiscriminatorById(id).Returns(nameof(Candidate));

        // Act
        var result = await _service.GetRegistrationById(id);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task SendLobbyistRegistrationTermination_HasError()
    {
        // Arrange
        long id = 123;
        var terminatedAt = DateTime.UtcNow;
        var request = new LobbyistRegistrationTerminationRequestDto()
        {
            TerminatedAt = terminatedAt,
            LegislativeSessionId = 1,
            StatusId = RegistrationStatus.Accepted.Id
        };
        var decisionResponse = new StandardDecisionsSubmissionResponse()
        {
            Errors = new List<WorkFlowError>()
            {
                new("EffectiveDateOfTermination", "1", "Error", "Required")
            }
        };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistRegistrationTermination, StandardDecisionsSubmissionResponse>
                    (DecisionsWorkflow.FRLOBFilingSubmitLobbyistRegistrationTermination, Arg.Any<DecisionsLobbyistRegistrationTermination>(), true)
                        .Returns(Task.FromResult(decisionResponse));

        // Act
        var result = await _service.SendLobbyistRegistrationTermination(id, request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.EqualTo(false));
            Assert.That(result?.ValidationErrors?.Count, Is.EqualTo(1));
        });
    }

    [Test]
    public void SendLobbyistRegistrationTermination_NotFoundLobbyist()
    {
        // Arrange
        long id = 123;
        var terminatedAt = DateTime.UtcNow;
        var request = new LobbyistRegistrationTerminationRequestDto()
        {
            TerminatedAt = terminatedAt,
            LegislativeSessionId = 2,
            StatusId = RegistrationStatus.Accepted.Id
        };
        var decisionResponse = new StandardDecisionsSubmissionResponse()
        {
            Errors = [],
        };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistRegistrationTermination, StandardDecisionsSubmissionResponse>
                    (DecisionsWorkflow.FRLOBFilingSubmitLobbyistRegistrationTermination, Arg.Any<DecisionsLobbyistRegistrationTermination>(), true)
                        .Returns(Task.FromResult(decisionResponse));

        _registrationRepositoryMock.FindLobbyistTerminationById(id).Returns(Task.FromResult<LobbyistTermination?>(null));

        // Act & Assert
        var result = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.SendLobbyistRegistrationTermination(id, request));

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Message, Is.EqualTo($"Lobbyist registration with Filer ID {id} not found."));
    }

    [Test]
    [TestCase(true)]
    [TestCase(false)]
    public async Task SendLobbyistRegistrationTermination_SendSuccessfully(bool allowAttest)
    {
        // Arrange
        long id = 123;
        var terminatedAt = DateTime.UtcNow;
        var request = new LobbyistRegistrationTerminationRequestDto()
        {
            TerminatedAt = terminatedAt,
            LegislativeSessionId = 1,
            StatusId = allowAttest ? RegistrationStatus.Accepted.Id : RegistrationStatus.Pending.Id,
        };
        var decisionResponse = new StandardDecisionsSubmissionResponse()
        {
            Errors = [],
            Notifications = new List<NotificationTrigger>()
            {
                new(true, 1, DateTime.UtcNow),
                new(true, 88, DateTime.UtcNow),
            }
        };

        var lobbyist = new LobbyistTermination()
        {
            Id = id,
            LegislativeSessionId = 1,
            FirstName = "FirstName",
            MiddleName = "MiddleName",
            LastName = "LastName",
            Name = "Name",
            Email = "Email",
            FilerId = 1,
            StatusId = RegistrationStatus.Accepted.Id,
            TerminatedAt = terminatedAt,
            AddressList = new AddressList
            {
                Addresses = new List<Address>
                {
                    new()
                    {
                        Street = "123 Main St",
                        City = "Sacramento",
                        State = "CA",
                        Zip = "95814",
                        Country = "USA",
                        Purpose = "Business",
                        Type = "Office"
                    }
                }
            },
            PhoneNumberList = new PhoneNumberList
            {
                Id = 1,
                PhoneNumbers = new List<PhoneNumber>
                {
                    new()
                    {
                        Id = 1,
                        CountryCode = "+1",
                        CountryId = 1,
                        Number = "8081234567",
                        Type = "Work"
                    }
                }
            }
        };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistRegistrationTermination, StandardDecisionsSubmissionResponse>
                    (DecisionsWorkflow.FRLOBFilingSubmitLobbyistRegistrationTermination, Arg.Any<DecisionsLobbyistRegistrationTermination>(), true)
                        .Returns(Task.FromResult(decisionResponse));
        _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistRegistrationTermination, StandardDecisionsSubmissionResponse>
                    (DecisionsWorkflow.FRLOBFilingTerminalRegistrationSendForAttestation, Arg.Any<DecisionsLobbyistRegistrationTermination>(), true)
                        .Returns(Task.FromResult(decisionResponse));

        _registrationRepositoryMock.FindLobbyistTerminationById(id).Returns(Task.FromResult<LobbyistTermination?>(lobbyist));

        // Act
        var result = await _service.SendLobbyistRegistrationTermination(id, request);

        // Assert
        var _ = _notificationSvcMock.Received(decisionResponse.Notifications.Count).SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.EqualTo(true));
        });
    }

    [Test]
    public async Task UpdateLobbyistRegistration_WithVersionGreaterThanZero_CallsAmendmentWorkflow()
    {
        // Arrange
        const long id = 1001;

        var existing = new Lobbyist
        {
            Id = id,
            Version = 1,
            Name = "AmendName",
            FirstName = "First",
            LastName = "Last",
            StatusId = RegistrationStatus.Accepted.Id
        };

        var request = new LobbyistRegistrationRequestDto
        {
            EffectiveDateOfChanges = DateTime.UtcNow,
            FirstName = "First",
            LastName = "Last",
            CheckRequiredFieldsFlag = true,
            LobbyistEmployerOrLobbyingFirmId = 10,
            FilerId = 200
        };

        _registrationRepositoryMock.FindLobbyistById(id)
            .Returns(existing);

        _registrationRepositoryMock
            .IsLobbyistNameUnique(Arg.Any<string>(), Arg.Any<long?>())
            .Returns(Task.FromResult(true));

        _decisionsSvcMock
            .InitiateWorkflow<DecisionsAmendLobbyistRegistrationGeneralInfo, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsAmendLobbyistRegistrationGeneralInfo>(),
                Arg.Any<bool>())
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        _registrationRepositoryMock
            .Update(Arg.Any<Lobbyist>())
            .Returns(Task.FromResult<Registration>(existing));

        // Act
        var result = await _service.UpdateLobbyistRegistration(id, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.Id, Is.EqualTo(id));
        });

        await _decisionsSvcMock.Received(1)
            .InitiateWorkflow<DecisionsAmendLobbyistRegistrationGeneralInfo, List<WorkFlowError>>(
                DecisionsWorkflow.FRLOBFilingAmendRegistrationStep1GeneralInfo,
                Arg.Any<DecisionsAmendLobbyistRegistrationGeneralInfo>(),
                Arg.Any<bool>());
    }

    [Test]
    [TestCase(nameof(Lobbyist))]
    [TestCase(nameof(LobbyistTermination))]
    public async Task SaveLobbyistRegistrationTermination_Successfully(string type)
    {
        // Arrange
        long id = 123;
        var terminatedAt = DateTime.UtcNow;
        var request = new LobbyistRegistrationTerminationRequestDto()
        {
            LegislativeSessionId = 1,
            TerminatedAt = terminatedAt,
            StatusId = RegistrationStatus.Accepted.Id
        };
        var decisionResponse = new StandardDecisionsSubmissionResponse()
        {
            Errors = [],
            Notifications = new List<NotificationTrigger>()
            {
                new(true, 1, DateTime.UtcNow),
            }
        };

        var lobbyist = new Lobbyist()
        {
            Id = id,
            LegislativeSessionId = 1,
            FirstName = "FirstName",
            MiddleName = "MiddleName",
            LastName = "LastName",
            Name = "Name",
            Email = "Email",
            FilerId = 1,
            StatusId = RegistrationStatus.Accepted.Id,
            TerminatedAt = terminatedAt,
            AddressList = new AddressList
            {
                Addresses = new List<Address>
                {
                    new()
                    {
                        Street = "123 Main St",
                        City = "Sacramento",
                        State = "CA",
                        Zip = "95814",
                        Country = "USA",
                        Purpose = "Business",
                        Type = "Office"
                    }
                }
            },
            PhoneNumberList = new PhoneNumberList
            {
                Id = 1,
                PhoneNumbers = new List<PhoneNumber>
                {
                    new()
                    {
                        Id = 1,
                        CountryCode = "+1",
                        CountryId = 1,
                        Number = "8081234567",
                        Type = "Work"
                    }
                }
            }
        };
        var lobbyistTermination = new LobbyistTermination() { Id = id, StatusId = 1, Name = "Lobbyist" };
        var registration = lobbyistTermination as Registration;

        _registrationRepositoryMock.GetRegistrationDiscriminatorById(id).Returns(Task.FromResult<string?>(type));

        _registrationRepositoryMock.FindLobbyistById(id).Returns(Task.FromResult<Lobbyist?>(lobbyist));
        _registrationRepositoryMock.FindLobbyistTerminationById(id).Returns(Task.FromResult<LobbyistTermination?>(lobbyistTermination));

        _registrationRepositoryMock.Create(Arg.Any<LobbyistTermination>()).Returns(Task.FromResult(registration));

        // Act
        var result = await _service.SaveLobbyistRegistrationTermination(id, request);

        // Assert
        if (type == nameof(Lobbyist))
        {
            var _ = _registrationRepositoryMock.Received(1).Create(Arg.Any<LobbyistTermination>());
        }
        else if (type == nameof(Registration))
        {
            var _ = _registrationRepositoryMock.Received(1).Update(Arg.Any<LobbyistTermination>());
        }
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(id));
    }

    [Test]
    [TestCase(nameof(Lobbyist))]
    [TestCase(nameof(LobbyistTermination))]
    public void SaveLobbyistRegistrationTermination_ThrowError(string type)
    {
        // Arrange
        long id = 123;
        _registrationRepositoryMock.GetRegistrationDiscriminatorById(id).Returns(Task.FromResult<string?>(type));
        var request = new LobbyistRegistrationTerminationRequestDto()
        {
            LegislativeSessionId = 1,
            TerminatedAt = DateTime.UtcNow,
            StatusId = RegistrationStatus.Accepted.Id
        };

        // Act & Assert
        Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.SaveLobbyistRegistrationTermination(id, request));
    }

    [Test]
    public async Task SubmitLobbyistRegistration_WithNoEmployerFilerLink_ShouldReturnSuccessResponse()
    {
        //Arrange
        var id = 1982;
        var lobbyist = new Lobbyist()
        {
            Id = id,
            FilerId = 1,
            Name = "Name",
            Email = "Email",
            StatusId = RegistrationStatus.Draft.Id,
            SelfRegister = false,
            Filer = new Filer()
            {
                FilerLinks = new List<FilerLink>
                {
                    new()
                    {
                        CreatedBy = 0,
                        ModifiedBy = 0,
                        EffectiveDate = DateTime.UtcNow,
                        FilerId = 1,
                        FilerLinkTypeId = FilerLinkType.LobbyistEmployer.Id,
                        LinkedEntityId = 0
                    },
                    new()
                    {
                        CreatedBy = 0,
                        ModifiedBy = 0,
                        EffectiveDate = DateTime.UtcNow,
                        FilerId = 1,
                        FilerLinkTypeId = FilerLinkType.LobbyingFirm.Id,
                        LinkedEntityId = 0
                    },
                }
            }
        };

        _registrationRepositoryMock.FindLobbyistById(id).Returns(lobbyist);
        _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(DecisionsWorkflow.FRLOBFilingLobbyistSendAttestation, Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(), true)
            .Returns(new StandardDecisionsSubmissionResponse() { Status = RegistrationStatus.Pending.ToString()!, Errors = new List<WorkFlowError>() });
        await _linkageSvc.SendLinkageRequestToPerson(Arg.Any<SendLinkageRequestToPersonDto>());

        //Act
        var result = await _service.SubmitLobbyistRegistration(id);

        //Assert
        await _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(DecisionsWorkflow.FRLOBFilingLobbyistSendAttestation, Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(), true);
        await _registrationRepositoryMock.Received(1).Update(
           Arg.Is<Lobbyist>(x => x.Version == 0 && x.StatusId == RegistrationStatus.Pending.Id)
        );

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(0));
        });
    }

    [Test]
    public async Task SendLobbyistRegistrationWithdrawal_WhenNoValidationErrors_UpdatesRegistrationAndSendsNotifications()
    {
        // Arrange
        var id = 1L;
        var request = new WithdrawLobbyistRegistrationRequestDto
        {
            StatusId = RegistrationStatus.Accepted.Id,
            WithdrawnAt = DateTime.UtcNow,
            ExecutedOn = DateTime.UtcNow,
            LegislativeSessionId = 2024
        };
        var decisionsResponse = new StandardDecisionsSubmissionResponse
        {
            Errors = [],
            Notifications = new List<NotificationTrigger>
            {
                new() { SendNotification = true, NotificationTemplateId = 10 }
            }
        };
        var withdrawLobbyist = new LobbyistWithdrawal
        {
            Id = id,
            FilerId = 100,
            StatusId = RegistrationStatus.Pending.Id,
            Name = "Test"
        };
        var lobbyist = new Lobbyist()
        {
            Id = id,
            FilerId = 1,
            Name = "Name",
            Email = "Email",
            StatusId = RegistrationStatus.Draft.Id,
            SelfRegister = false,
            Filer = new Filer()
            {
                FilerLinks = new List<FilerLink>
                {
                    new()
                    {
                        CreatedBy = 0,
                        ModifiedBy = 0,
                        EffectiveDate = DateTime.UtcNow,
                        FilerId = 1,
                        FilerLinkTypeId = FilerLinkType.LobbyistEmployer.Id,
                        LinkedEntityId = 0
                    },
                    new()
                    {
                        CreatedBy = 0,
                        ModifiedBy = 0,
                        EffectiveDate = DateTime.UtcNow,
                        FilerId = 1,
                        FilerLinkTypeId = FilerLinkType.LobbyingFirm.Id,
                        LinkedEntityId = 0
                    },
                }
            }
        };

        var lobbyistEmployer = new LobbyistEmployer()
        {
            Name = "Name",
            StatusId = RegistrationStatus.Submitted.Id,
            OriginalId = 2,
            Id = 1,
        };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistRegistrationWithdrawal, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.LobbyistRegistrationSubmitWithdrawal, Arg.Any<DecisionsLobbyistRegistrationWithdrawal>(), true)
            .Returns(Task.FromResult(decisionsResponse));
        _dependencies.RegistrationRepository.FindLobbyistWithdrawalById(id)
            .Returns(Task.FromResult<LobbyistWithdrawal?>(withdrawLobbyist));
        _dependencies.NotificationSvc.SendFilerNotification(Arg.Any<SendFilerNotificationRequest>())
            .Returns(Task.CompletedTask);
        _registrationRepositoryMock.FindLobbyistById(Arg.Any<long>()).Returns(lobbyist);
        _registrationRepositoryMock.FindLobbyistEmployerById(Arg.Any<long>()).Returns(lobbyistEmployer);

        // Act
        var result = await _service.SendLobbyistRegistrationWithdrawal(id, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.True);
            Assert.That(withdrawLobbyist.StatusId, Is.EqualTo(RegistrationStatus.PendingFilerAction.Id));
            Assert.That(withdrawLobbyist.WithdrawnAt, Is.EqualTo(request.WithdrawnAt));
            Assert.That(withdrawLobbyist.SubmittedAt, Is.EqualTo(request.ExecutedOn));
        });
    }

    [Test]
    public async Task SendLobbyistRegistrationWithdrawal_WhenValidationErrorsExist_ReturnsFailureResponse()
    {
        // Arrange
        var id = 1;
        var request = new WithdrawLobbyistRegistrationRequestDto
        {
            StatusId = RegistrationStatus.Pending.Id,
            WithdrawnAt = DateTime.UtcNow,
            ExecutedOn = DateTime.UtcNow,
            LegislativeSessionId = 2024
        };
        var decisionsResponse = new StandardDecisionsSubmissionResponse
        {
            Errors = new List<WorkFlowError> { new("Field", "Code", "Type", "Message") }
        };
        var lobbyist = new LobbyistWithdrawal
        {
            Id = id,
            FilerId = 100,
            StatusId = RegistrationStatus.Pending.Id,
            Name = "Test"
        };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistRegistrationWithdrawal, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.LobbyistRegistrationSendForAttestationWithdrawal, Arg.Any<DecisionsLobbyistRegistrationWithdrawal>(), true)
            .Returns(Task.FromResult(decisionsResponse));
        _dependencies.RegistrationRepository.FindLobbyistWithdrawalById(id)
            .Returns(Task.FromResult<LobbyistWithdrawal?>(lobbyist));

        // Act
        var result = await _service.SendLobbyistRegistrationWithdrawal(id, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.Not.Empty);
        });
    }

    [Test]
    public void SendLobbyistRegistrationWithdrawal_WhenLobbyistNotFound_ThrowsKeyNotFoundException()
    {
        // Arrange
        var id = 3L;
        var request = new WithdrawLobbyistRegistrationRequestDto
        {
            StatusId = RegistrationStatus.Accepted.Id,
            WithdrawnAt = DateTime.UtcNow,
            ExecutedOn = DateTime.UtcNow,
            LegislativeSessionId = 2024
        };
        var decisionsResponse = new StandardDecisionsSubmissionResponse { Errors = [] };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistRegistrationWithdrawal, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.LobbyistRegistrationSendForAttestationWithdrawal, Arg.Any<DecisionsLobbyistRegistrationWithdrawal>(), true)
            .Returns(Task.FromResult(decisionsResponse));
        _dependencies.RegistrationRepository.FindLobbyistWithdrawalById(id)
            .Returns(Task.FromResult<LobbyistWithdrawal?>(null));

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _service.SendLobbyistRegistrationWithdrawal(id, request));

        Assert.That(ex!.Message, Does.Contain($"Lobbyist registration with Filer ID {id} not found."));
    }

    [Test]
    public async Task SendLobbyistRegistrationWithdrawal_WhenNoNotifications_DoesNotSendNotification()
    {
        // Arrange
        var id = 4L;
        var request = new WithdrawLobbyistRegistrationRequestDto
        {
            StatusId = RegistrationStatus.Accepted.Id,
            WithdrawnAt = DateTime.UtcNow,
            ExecutedOn = DateTime.UtcNow,
            LegislativeSessionId = 2024
        };
        var decisionsResponse = new StandardDecisionsSubmissionResponse
        {
            Errors = [],
            Notifications = new List<NotificationTrigger>
            {
                new() { SendNotification = false, NotificationTemplateId = 10 }
            }
        };
        var lobbyistWithdraw = new LobbyistWithdrawal
        {
            Id = id,
            FilerId = 100,
            StatusId = RegistrationStatus.Draft.Id,
            Name = "Name"
        };

        var lobbyist = new Lobbyist()
        {
            Id = id,
            FilerId = 1,
            Name = "Name",
            Email = "Email",
            StatusId = RegistrationStatus.Draft.Id,
            SelfRegister = false,
            Filer = new Filer()
            {
                FilerLinks = new List<FilerLink>
                {
                    new()
                    {
                        CreatedBy = 0,
                        ModifiedBy = 0,
                        EffectiveDate = DateTime.UtcNow,
                        FilerId = 1,
                        FilerLinkTypeId = FilerLinkType.LobbyistEmployer.Id,
                        LinkedEntityId = 0
                    },
                    new()
                    {
                        CreatedBy = 0,
                        ModifiedBy = 0,
                        EffectiveDate = DateTime.UtcNow,
                        FilerId = 1,
                        FilerLinkTypeId = FilerLinkType.LobbyingFirm.Id,
                        LinkedEntityId = 0
                    },
                }
            }
        };

        var lobbyistEmployer = new LobbyistEmployer()
        {
            Name = "Name",
            StatusId = RegistrationStatus.Submitted.Id,
            OriginalId = 2,
            Id = 1,
        };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistRegistrationWithdrawal, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.LobbyistRegistrationSubmitWithdrawal,
                Arg.Any<DecisionsLobbyistRegistrationWithdrawal>(), true)
            .Returns(Task.FromResult(decisionsResponse));
        _dependencies.RegistrationRepository.FindLobbyistWithdrawalById(id)
            .Returns(Task.FromResult<LobbyistWithdrawal?>(lobbyistWithdraw));
        _registrationRepositoryMock.FindLobbyistById(Arg.Any<long>()).Returns(lobbyist);
        _registrationRepositoryMock.FindLobbyistEmployerById(Arg.Any<long>()).Returns(lobbyistEmployer);

        // Act
        var result = await _service.SendLobbyistRegistrationWithdrawal(id, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    private static IEnumerable<TestCaseData> SubmitLobbyistRegistration_WithEmployerFilerLinkAndTerminationDate()
    {
        yield return new TestCaseData(new LobbyistEmployer()
        {
            EmployerName = "Employer Name",
            Name = "Name",
            StatusId = RegistrationStatus.Draft.Id,
            OriginalId = 1,
            Id = 1,
        }, null);
        yield return new TestCaseData(new LobbyistEmployer()
        {
            Name = "Name",
            EmployerName = "Employer Name",
            StatusId = RegistrationStatus.Draft.Id,
            OriginalId = 2,
            Id = 1,
            FilerId = 1
        }, null);
        yield return new TestCaseData(new LobbyistEmployer()
        {
            Name = "Name",
            EmployerName = "Employer Name",
            StatusId = RegistrationStatus.Accepted.Id,
            OriginalId = 2,
            Id = 1,
        }, null);
        yield return new TestCaseData(new LobbyistEmployer()
        {
            Name = "Name",
            EmployerName = "Employer Name",
            StatusId = RegistrationStatus.Pending.Id,
            OriginalId = 2,
            Id = 1,
            FilerId = 1
        }, DateTime.Now);
        yield return new TestCaseData(new LobbyistEmployer()
        {
            Name = "Name",
            EmployerName = "Employer Name",
            StatusId = RegistrationStatus.PendingFilerAction.Id,
            OriginalId = 2,
            Id = 1,
        }, DateTime.Now);
        yield return new TestCaseData(new LobbyistEmployer()
        {
            Name = "Name",
            EmployerName = "Employer Name",
            StatusId = RegistrationStatus.Submitted.Id,
            OriginalId = 2,
            Id = 1,
            FilerId = 1
        }, null);
    }
    [TestCaseSource(nameof(SubmitLobbyistRegistration_WithEmployerFilerLinkAndTerminationDate))]
    public async Task SubmitLobbyistRegistration_WithEmployerFilerLink_ShouldReturnSuccessResponse(LobbyistEmployer lobbyistEmployer, DateTime terminationDate)
    {
        //Arrange
        var id = 1982;
        var lobbyist = new Lobbyist()
        {
            Id = id,
            FilerId = 1,
            Name = "Name",
            Email = "Email",
            StatusId = RegistrationStatus.Draft.Id,
            SelfRegister = false,
            Filer = new Filer()
            {
                FilerLinks = new List<FilerLink>
                {
                    new()
                    {
                        CreatedBy = 0,
                        ModifiedBy = 0,
                        EffectiveDate = DateTime.UtcNow,
                        FilerId = 1,
                        FilerLinkTypeId = FilerLinkType.LobbyingFirm.Id,
                        LinkedEntityId = 1,
                        TerminationDate = terminationDate
                    },
                    new()
                    {
                        CreatedBy = 0,
                        ModifiedBy = 0,
                        EffectiveDate = DateTime.UtcNow,
                        FilerId = 1,
                        FilerLinkTypeId = FilerLinkType.LobbyistEmployer.Id,
                        LinkedEntityId = 1
                    },
                }
            }
        };

        _registrationRepositoryMock.FindLobbyistById(id).Returns(lobbyist);
        _registrationRepositoryMock.FindLobbyistEmployerById(1).Returns(lobbyistEmployer);
        _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(DecisionsWorkflow.FRLOBFilingLobbyistSendAttestation, Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(), true)
            .Returns(new StandardDecisionsSubmissionResponse()
            {
                Status = RegistrationStatus.Pending.ToString()!,
                Errors = new List<WorkFlowError>(),
                Notifications = new List<NotificationTrigger>
                {
                    new()
                    {
                        SendNotification = false,
                        DueDate = DateTime.Now,
                        NotificationTemplateId = 1,
                        Recipient = nameof(NotificationRecipient.InitiatingUser)
                    },
                    new()
                    {
                        SendNotification = true,
                        DueDate = DateTime.Now,
                        NotificationTemplateId = 2,
                        Recipient = nameof(NotificationRecipient.FilerUsers)
                    },
                    new()
                    {
                        SendNotification = true,
                        DueDate = DateTime.Now,
                        NotificationTemplateId = 3,
                        Recipient = nameof(NotificationRecipient.LinkedFilerUser)
                    },
                }
            });

        await _linkageSvc.SendLinkageRequestToPerson(Arg.Any<SendLinkageRequestToPersonDto>());

        //Act
        var result = await _service.SubmitLobbyistRegistration(id);

        //Assert
        await _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(DecisionsWorkflow.FRLOBFilingLobbyistSendAttestation, Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(), true);

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(0));
        });
    }

    [Test]
    public void GetLobbyistRegistration_WhenLobbyistNotFound_ShouldThrowNotFoundException()
    {
        //Arrange
        var lobbyistId = 1;

        _registrationRepositoryMock.GetRegistrationDiscriminatorById(Arg.Any<long>())
            .Returns(nameof(Lobbyist));
        _registrationRepositoryMock.FindLobbyistById(Arg.Any<long>())
            .Returns((Lobbyist?)null);

        // Act & Assert
        Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.GetLobbyistRegistration(lobbyistId));
    }

    [Test]
    public async Task GetLobbyistRegistration_WhenLobbyistExisted_ShouldReturnLobbyistResponseDto()
    {
        //Arrange
        var lobbyist = new Lobbyist { Id = 1, Name = "FirstName", EmployerName = string.Empty, StatusId = RegistrationStatus.Draft.Id, FilerId = 1 };
        _registrationRepositoryMock.GetRegistrationDiscriminatorById(Arg.Any<long>())
                    .Returns(nameof(Lobbyist));
        _dependencies.RegistrationRepository.FindLobbyistById(1).Returns(lobbyist);

        // Act
        var result = await _service.GetLobbyistRegistration(1);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.FilerId, Is.EqualTo(lobbyist.FilerId));
            Assert.That(result.Name, Is.EqualTo(lobbyist.Name));
            Assert.That(result.EmployerName, Is.EqualTo(lobbyist.EmployerName));
            Assert.That(result.StatusId, Is.EqualTo(lobbyist.StatusId));
        });
    }
}

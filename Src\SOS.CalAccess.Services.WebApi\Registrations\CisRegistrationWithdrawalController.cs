using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.WebApi.Shared;

namespace SOS.CalAccess.Services.WebApi.Registrations;

/// <summary>
/// API controller responsible for handling CIS registration withdrawals.
/// </summary>
[ApiController]
[AllowAnonymous]
[Route("/")]
public sealed class CisRegistrationWithdrawalController(
    IAuthorizationService authorization,
    ICisRegistrationWithdrawalSvc cisRegistrationWithdrawalService)
    : AuthorizationAwareControllerBase(authorization), ICisRegistrationWithdrawalSvc
{
    /// <summary>
    /// Creates a new CIS registration withdrawal.
    /// The associated registration must have a status of 'Accepted'.
    /// </summary>
    /// <param name="registrationId">The ID of the registration to withdraw.</param>
    /// <returns>Task representing the asynchronous operation.</returns>
    [HttpPost(ICisRegistrationWithdrawalSvc.CreateWithdrawalPath, Name = nameof(CreateCisWithdrawal))]
    public async Task<long> CreateCisWithdrawal([FromRoute] long registrationId)
    {
        return await cisRegistrationWithdrawalService.CreateCisWithdrawal(registrationId);
    }

    /// <summary>
    /// Retrieves a CIS registration withdrawal by its registration ID.
    /// </summary>
    /// <param name="registrationId">The unique identifier of the withdrawal registration.</param>
    /// <returns>Returns the CIS withdrawal details.</returns>
    [HttpGet(ICisRegistrationWithdrawalSvc.GetWithdrawalPath, Name = nameof(GetCisWithdrawal))]
    public async Task<CisRegistrationWithdrawalDto> GetCisWithdrawal([FromRoute] long registrationId)
    {
        return await cisRegistrationWithdrawalService.GetCisWithdrawal(registrationId);
    }

    /// <summary>
    /// Cancels (soft deletes) a CIS registration withdrawal.
    /// Allowed only when the record is in 'Draft' or 'Pending' status.
    /// </summary>
    /// <param name="registrationId">The unique identifier of the withdrawal registration to cancel.</param>
    /// <returns>Task representing the asynchronous operation.</returns>
    [HttpDelete(ICisRegistrationWithdrawalSvc.CancelWithdrawalPath, Name = nameof(CancelCisWithdrawal))]
    public async Task CancelCisWithdrawal([FromRoute] long registrationId)
    {
        await cisRegistrationWithdrawalService.CancelCisWithdrawal(registrationId);
    }

    // <summary>
    /// Handles the send for attestation of Candidate Intention Statement (CIS) withdrawal for a given registration.
    /// </summary>
    /// <param name="registrationId">The unique ID of the CIS registration to withdraw.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    [HttpPost(ICisRegistrationWithdrawalSvc.SendForAttestationCisWithdrawalPath, Name = nameof(SendForAttestationCisWithdrawal))]
    public async Task<RegistrationResponseDto> SendForAttestationCisWithdrawal([FromRoute] long registrationId)
    {
        return await cisRegistrationWithdrawalService.SendForAttestationCisWithdrawal(registrationId);
    }

    // <summary>
    /// Handles the attestation of Candidate Intention Statement (CIS) withdrawal for a given registration.
    /// </summary>
    /// <param name="registrationId">The unique ID of the CIS registration to withdraw.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    [HttpPut(ICisRegistrationWithdrawalSvc.AttestationCisWithdrawalPath, Name = nameof(AttestationCisWithdrawal))]
    public async Task<RegistrationResponseDto> AttestationCisWithdrawal(CisRegistrationAttestationWithdrawalDto cisRegistrationAttestationWithdrawalDto)
    {
        return await cisRegistrationWithdrawalService.AttestationCisWithdrawal(cisRegistrationAttestationWithdrawalDto);
    }
    // <summary>
    /// Gets the Pending items of Candidate Intention Statement (CIS) withdrawal for a given registration.
    /// </summary>
    /// <param name="registrationId">The unique ID of the CIS registration to withdraw.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    [HttpGet(ICisRegistrationWithdrawalSvc.GetPendingItemsPath, Name = nameof(GetPendingItems))]
    public async Task<List<PendingItemDto>> GetPendingItems([FromRoute] long registrationId)
    {
        return await cisRegistrationWithdrawalService.GetPendingItems(registrationId);
    }
}


using SOS.CalAccess.FilerPortal.Models.Registrations.CisRegistrationWithdrawal;
using SOS.CalAccess.FilerPortal.Models.SharedModels;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.UI.Common.Constants;
using SOS.CalAccess.UI.Common.Models;

namespace SOS.CalAccess.FilerPortal.ControllerServices.CisRegistrationWithdrawalCtlSvc;

public class CisRegistrationWithdrawalCtlSvc(
    ICisRegistrationWithdrawalSvc cisRegistrationWithdrawalSvc,
    IDateTimeSvc dateTimeSvc
    ) : ICisRegistrationWithdrawalCtlSvc
{

    #region Core
    public async Task<MethodResult> CancelCisWithdrawal(long id)
    {
        try
        {
            await cisRegistrationWithdrawalSvc.CancelCisWithdrawal(id);
            return new();
        }
        catch (Exception ex)
        {
            return new(ex);
        }
    }
    #endregion

    #region InitializeCisWithdrawal
    /// <inheritdoc /
    public async Task<MethodResult<long>> InitializeCisWithdrawal(long id)
    {
        try
        {
            var cisWithdrawal = await cisRegistrationWithdrawalSvc.GetCisWithdrawal(id);

            if (cisWithdrawal.Id == null)
            {
                var cisWithdrawalId = await cisRegistrationWithdrawalSvc.CreateCisWithdrawal(id);
                return new(cisWithdrawalId);
            }

            return new(id);
        }
        catch (Exception ex)
        {
            return new(ex);
        }
    }

    #endregion

    #region Page01
    /// <inheritdoc />
    public async Task<MethodResult<CisRegistrationWithdrawalPage01ViewModel>> Page01GetViewModel(long id)
    {
        try
        {
            var cisWithdrawal = await cisRegistrationWithdrawalSvc.GetCisWithdrawal(id);

            var candidateAddress = cisWithdrawal.Addresses?
                .FirstOrDefault(x => x.Purpose == CommonConstants.Address.PurposeCandidate);

            var model = new CisRegistrationWithdrawalPage01ViewModel
            {
                Id = id,
                Name = $"{cisWithdrawal.Candidate?.FirstName} {cisWithdrawal.Candidate?.LastName}",
                Email = cisWithdrawal.Candidate?.Email,
                Address = new AddressViewModel(candidateAddress),
                Office = cisWithdrawal.ElectionOfficeSought,
                District = cisWithdrawal.ElectionRace?.District?.Name,
                ElectionDate = cisWithdrawal.ElectionRace?.Election?.ElectionDate
            };
            return new(model);
        }
        catch (Exception ex)
        {
            return new(ex);
        }
    }

    #endregion

    #region Page02
    /// <inheritdoc />
    public async Task<MethodResult<CisRegistrationWithdrawalPage02ViewModel>> Page02GetViewModel(long id)
    {
        try
        {
            var registration = await cisRegistrationWithdrawalSvc.GetCisWithdrawal(id);
            var model = new CisRegistrationWithdrawalPage02ViewModel
            {
                Id = id,
                Name = $"{registration?.Candidate?.FirstName} {registration?.Candidate?.LastName}",
                ExecutedOn = dateTimeSvc.GetCurrentDateTime(),
                IsAttest = true
            };
            return new(model);
        }
        catch (Exception ex)
        {
            return new(ex);
        }
    }
    /// <inheritdoc />
    public async Task<MethodResult> Page02Submit(long id)
    {
        try
        {
            await cisRegistrationWithdrawalSvc.CancelCisWithdrawal(id);
            return new();
        }
        catch (Exception ex)
        {
            return new(ex);
        }
    }

    #endregion

    #region Page03
    /// <inheritdoc />
    public async Task<MethodResult<CisRegistrationWithdrawalPage03ViewModel>> Page03GetViewModel(long id)
    {
        try
        {
            var registration = await cisRegistrationWithdrawalSvc.GetCisWithdrawal(id);

            var model = new CisRegistrationWithdrawalPage03ViewModel
            {
                ExecutedOn = registration.ExecutedOn ?? dateTimeSvc.GetCurrentDateTime(),
                PendingItems = new List<PendingItemSharedViewModel>
                {
                    new() {
                        Item = "test1",
                        Status = "pending"
                    }
                },
                IsAttest = false
            };
            return new(model);
        }
        catch (Exception ex)
        {
            return new(ex);
        }
    }
    #endregion
}
